"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { DashboardStats } from "./DashboardStats";
import { VolunteerQuickActions } from "./VolunteerQuickActions";
import { EventSelector } from "./EventSelector";

export const DashboardMain: React.FC = () => {
  const [selectedEventId, setSelectedEventId] = useState<string | null>(null);
  const router = useRouter();

  // Handle event selection - immediately navigate to categories page with selected event
  const handleEventSelect = (eventId: string) => {
    setSelectedEventId(eventId);
    if (eventId) {
      router.push(`/volunteer/dashboard/categories?eventId=${eventId}`);
    }
  };

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-white mb-2">Dashboard</h1>
        <p className="text-gray-400">
          Welcome to the volunteer coordinator dashboard. Manage categories,
          shifts, and payments.
        </p>
      </div>

      {/* Stats Section */}
      <DashboardStats />

      {/* Quick Actions */}
      <div className="mb-6">
        <VolunteerQuickActions />
      </div>

      {/* Event Selector */}
      <div className="mb-6">
        <EventSelector onEventSelect={handleEventSelect} />
      </div>
    </div>
  );
};
