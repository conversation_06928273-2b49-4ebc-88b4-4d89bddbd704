import { prisma } from "@/lib/prisma";
import { TicketStatus } from "@prisma/client";

// ============================================================================
// EVENT CAPACITY FUNCTIONS
// ============================================================================

/**
 * Get available capacity for an event
 * Considers both sold tickets and current holds
 */
export async function getEventAvailableCapacity(eventId: string, tx?: any): Promise<number> {
  const prismaClient = tx || prisma;
  
  const event = await prismaClient.event.findUnique({
    where: { id: eventId },
    select: { capacity: true }
  });
  
  if (!event?.capacity) {
    return Infinity; // No capacity limit
  }
  
  const [soldTickets, heldCapacity] = await Promise.all([
    // Count sold tickets from associated products that affect capacity
    prismaClient.ticket.count({
      where: {
        product: { 
          eventId,
          affectsCapacity: true
        },
        status: TicketStatus.SOLD
      }
    }),
    // Count current event capacity holds
    prismaClient.eventCapacityHold.aggregate({
      where: {
        eventId,
        expiresAt: { gt: new Date() }
      },
      _sum: { quantity: true }
    })
  ]);
  
  const used = soldTickets + (heldCapacity._sum.quantity || 0);
  return Math.max(0, event.capacity - used);
}

/**
 * Check if an event has enough available capacity
 */
export async function isEventCapacityAvailable(
  eventId: string,
  requestedQuantity: number,
  tx?: any
): Promise<boolean> {
  const availableCapacity = await getEventAvailableCapacity(eventId, tx);
  return availableCapacity >= requestedQuantity;
}

/**
 * Get event capacity statistics
 */
export async function getEventCapacityStats(eventId: string) {
  const event = await prisma.event.findUnique({
    where: { id: eventId },
    select: { capacity: true }
  });
  
  if (!event?.capacity) {
    return {
      total: null,
      available: Infinity,
      sold: 0,
      held: 0,
      hasLimit: false
    };
  }
  
  const [soldTickets, heldCapacity] = await Promise.all([
    prisma.ticket.count({
      where: {
        product: { 
          eventId,
          affectsCapacity: true
        },
        status: TicketStatus.SOLD
      }
    }),
    prisma.eventCapacityHold.aggregate({
      where: {
        eventId,
        expiresAt: { gt: new Date() }
      },
      _sum: { quantity: true }
    })
  ]);
  
  const held = heldCapacity._sum.quantity || 0;
  const available = Math.max(0, event.capacity - soldTickets - held);
  
  return {
    total: event.capacity,
    available,
    sold: soldTickets,
    held,
    hasLimit: true
  };
}

// ============================================================================
// EVENT CAPACITY HOLD FUNCTIONS
// ============================================================================

/**
 * Create an event capacity hold for a cart item
 */
export async function createEventCapacityHold(
  userId: string,
  eventId: string,
  cartItemId: string,
  quantity: number,
  tx?: any // Optional transaction context
) {
  const prismaClient = tx || prisma;
  
  // Check available capacity
  const event = await prismaClient.event.findUnique({
    where: { id: eventId },
    select: { capacity: true }
  });
  
  if (!event?.capacity) {
    throw new Error("Event has no capacity limit");
  }
  
  // Get current usage
  const [soldTickets, heldCapacity] = await Promise.all([
    prismaClient.ticket.count({
      where: {
        product: { 
          eventId,
          affectsCapacity: true
        },
        status: TicketStatus.SOLD
      }
    }),
    prismaClient.eventCapacityHold.aggregate({
      where: {
        eventId,
        expiresAt: { gt: new Date() }
      },
      _sum: { quantity: true }
    })
  ]);
  
  const used = soldTickets + (heldCapacity._sum.quantity || 0);
  const available = event.capacity - used;
  
  if (available < quantity) {
    throw new Error(`Only ${available} spots available for this event`);
  }
  
  // Create the hold
  const hold = await prismaClient.eventCapacityHold.create({
    data: {
      eventId,
      userId,
      cartItemId,
      quantity,
      expiresAt: new Date(Date.now() + 15 * 60 * 1000), // 15 minutes
    },
  });
  
  return hold;
}

/**
 * Release an event capacity hold
 */
export async function releaseEventCapacityHold(holdId: string) {
  return await prisma.$transaction(async (tx) => {
    const hold = await tx.eventCapacityHold.findUnique({
      where: { id: holdId }
    });
    
    if (!hold) {
      return { released: 0 };
    }
    
    await tx.eventCapacityHold.delete({
      where: { id: holdId }
    });
    
    return { released: hold.quantity };
  });
}

/**
 * Release expired event capacity holds
 */
export async function releaseExpiredEventCapacityHolds() {
  const now = new Date();
  
  return await prisma.$transaction(async (tx) => {
    // Find expired holds
    const expiredHolds = await tx.eventCapacityHold.findMany({
      where: {
        expiresAt: { lt: now }
      }
    });
    
    if (expiredHolds.length === 0) {
      return { holdsReleased: 0, capacityReleased: 0 };
    }
    
    // Delete expired holds
    await tx.eventCapacityHold.deleteMany({
      where: {
        expiresAt: { lt: now }
      }
    });
    
    const capacityReleased = expiredHolds.reduce((sum, hold) => sum + hold.quantity, 0);
    
    return {
      holdsReleased: expiredHolds.length,
      capacityReleased
    };
  });
}

/**
 * Convert event capacity holds to sold when purchase completes
 */
export async function convertEventCapacityHoldsToSold(
  eventId: string,
  userId: string,
  cartId: string
) {
  return await prisma.$transaction(async (tx) => {
    // Find all event capacity holds for this user's cart items
    const cartItems = await tx.cartItem.findMany({
      where: {
        cartId,
        eventCapacityHold: { 
          userId,
          eventId
        }
      },
      include: { 
        eventCapacityHold: true,
        product: true
      }
    });
    
    if (cartItems.length === 0) {
      return { converted: 0 };
    }
    
    let totalConverted = 0;
    
    // For each cart item with event capacity hold, we need to create individual sold tickets
    for (const cartItem of cartItems) {
      if (cartItem.eventCapacityHold && cartItem.product.affectsCapacity) {
        const quantity = cartItem.eventCapacityHold.quantity;
        
        // Create individual sold tickets for this product
        const soldTickets = Array(quantity).fill(null).map(() => ({
          productId: cartItem.productId,
          status: TicketStatus.SOLD,
        }));
        
        await tx.ticket.createMany({
          data: soldTickets
        });
        
        // Delete the event capacity hold
        await tx.eventCapacityHold.delete({
          where: { id: cartItem.eventCapacityHold.id }
        });
        
        totalConverted += quantity;
      }
    }
    
    return { converted: totalConverted };
  });
}

/**
 * Update event capacity hold expiration time (extend hold)
 */
export async function extendEventCapacityHold(holdId: string, additionalMinutes: 15) {
  const newExpirationTime = new Date(Date.now() + additionalMinutes * 60 * 1000);
  
  return await prisma.eventCapacityHold.update({
    where: { id: holdId },
    data: { expiresAt: newExpirationTime }
  });
}

/**
 * Get all products associated with an event that affect capacity
 */
export async function getEventCapacityProducts(eventId: string) {
  return await prisma.product.findMany({
    where: {
      eventId,
      affectsCapacity: true,
      isActive: true
    },
    include: {
      category: true
    }
  });
}

/**
 * Check if a product is associated with an event and affects capacity
 */
export async function isProductEventCapacityBased(
  productId: string, 
  tx?: any
): Promise<{
  isEventBased: boolean;
  eventId?: string;
  affectsCapacity?: boolean;
}> {
  const prismaClient = tx || prisma;
  
  const product = await prismaClient.product.findUnique({
    where: { id: productId },
    select: { 
      eventId: true, 
      affectsCapacity: true 
    }
  });
  
  if (!product) {
    return { isEventBased: false };
  }
  
  return {
    isEventBased: !!product.eventId && product.affectsCapacity,
    eventId: product.eventId || undefined,
    affectsCapacity: product.affectsCapacity
  };
}

/**
 * Get user's current event capacity holds
 */
export async function getUserEventCapacityHolds(userId: string) {
  return await prisma.eventCapacityHold.findMany({
    where: {
      userId,
      expiresAt: { gt: new Date() }
    },
    include: {
      event: {
        select: {
          id: true,
          name: true,
          startDate: true,
          endDate: true
        }
      },
      cartItem: {
        include: {
          product: {
            select: {
              id: true,
              name: true,
              price: true
            }
          }
        }
      }
    }
  });
}