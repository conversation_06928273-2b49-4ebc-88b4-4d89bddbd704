// This script seeds the database for development purposes
// It implements the same seeding logic as the first-time-seed endpoint

const { PrismaClient } = require("@prisma/client");

// Simple Lorem Ipsum generator for the seed script
function generateLoremArticle(paragraphs = 4) {
  let article = "";
  
  // Add a main heading
  article += `<h2>Lorem Ipsum Heading</h2>\n`;
  
  // Add first paragraph
  article += `<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl.</p>\n`;
  
  // Add second paragraph
  article += `<p>Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>\n`;
  
  // Add a subheading
  article += `<h3>Subheading</h3>\n`;
  
  // Add a list
  article += `<ul>\n`;
  for (let i = 0; i < 4; i++) {
    article += `  <li>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</li>\n`;
  }
  article += `</ul>\n`;
  
  // Add remaining paragraphs
  for (let i = 0; i < paragraphs - 2; i++) {
    article += `<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl.</p>\n`;
  }
  
  return article;
}

const seedDb = async () => {
  try {
    // First check if we need to seed by checking if categories exist
    const prisma = new PrismaClient();
    console.log("Connected to database. Checking if seeding is needed...");

    const categoriesCount = await prisma.newsCategory.count();
    const articlesCount = await prisma.newsArticle.count();

    if (categoriesCount > 0 && articlesCount > 0) {
      console.log("Database already has categories and articles. No seeding needed.");
      console.log(`Found ${categoriesCount} categories and ${articlesCount} articles.`);
      await prisma.$disconnect();
      return;
    }

    console.log("Database needs seeding. Creating categories and sample articles...");

    // Create categories if they don't exist
    let categories = [];
    if (categoriesCount === 0) {
      // Create the four default categories
      await prisma.newsCategory.createMany({
        data: [
          {
            name: "Featured",
            slug: "featured",
            description: "Featured articles from the Bank of Styx",
          },
          {
            name: "News",
            slug: "news",
            description: "Latest news and updates",
          },
          {
            name: "Announcements",
            slug: "announcements",
            description: "Official announcements from the Bank of Styx",
          },
          {
            name: "Events",
            slug: "events",
            description: "Upcoming and past events",
          },
        ],
        skipDuplicates: true,
      });

      // Fetch the created categories to get their IDs
      categories = await prisma.newsCategory.findMany();
      console.log(`Created ${categories.length} categories`);
    } else {
      // Fetch existing categories
      categories = await prisma.newsCategory.findMany();
      console.log(`Using ${categories.length} existing categories`);
    }

    // Create sample articles if they don't exist
    if (articlesCount === 0 && categories.length > 0) {
      // Find an admin user to be the author
      let adminUser = await prisma.user.findFirst({
        where: { isAdmin: true }
      });

      // Find the "Featured" and "News" categories
      const featuredCategory = categories.find(c => c.slug === "featured") || categories[0];
      const newsCategory = categories.find(c => c.slug === "news") || categories[0];
      
      // First, create uploaded image records for the news articles
      const welcomeImage = await prisma.uploadedImage.create({
        data: {
          filename: "bank-building.jpg",
          path: "/public/images/bank-building.jpg",
          url: "/images/bank-building.jpg",
          mimeType: "image/jpeg",
          size: 250000, // Approximate size
          entityType: "news",
          upload_type: "news",
          upload_config: JSON.stringify({
            maxSize: ********,
            allowedTypes: ["image/jpeg", "image/png", "image/gif", "image/webp"],
            processImage: true,
            quality: 0.9
          }),
          dimensions: JSON.stringify({
            width: 1200,
            height: 675
          })
        }
      });

      const comingSoonImage = await prisma.uploadedImage.create({
        data: {
          filename: "coming-soon.jpg", 
          path: "/public/images/coming-soon.jpg",
          url: "/images/coming-soon.jpg",
          mimeType: "image/jpeg",
          size: 180000, // Approximate size
          entityType: "news",
          upload_type: "news",
          upload_config: JSON.stringify({
            maxSize: ********,
            allowedTypes: ["image/jpeg", "image/png", "image/gif", "image/webp"],
            processImage: true,
            quality: 0.9
          }),
          dimensions: JSON.stringify({
            width: 1200,
            height: 675
          })
        }
      });
      
      // Create two sample articles
      const now = new Date();
      
      // Article 1 - Featured
      const welcomeArticle = await prisma.newsArticle.create({
        data: {
          title: "Welcome to the Bank of Styx",
          slug: "welcome-to-bank-of-styx",
          content: `<h2>Welcome to the Bank of Styx!</h2>
<p>Welcome to the official website of the Bank of Styx, your trusted financial institution for the Pirate Rinfair community.</p>
<p>We're excited to have you here and look forward to serving all your banking needs.</p>
<h3>Our Services</h3>
<ul>
  <li>Secure banking for all your needs</li>
  <li>Easy transfers between accounts</li>
  <li>Pay code system for quick payments</li>
  <li>Merchant integration</li>
</ul>
<p>Visit our help section to learn more about how to use our services!</p>
${generateLoremArticle(2)}`,
          excerpt: "Welcome to the Bank of Styx, your trusted financial institution for the Pirate Rinfair community.",
          image: welcomeImage.url,
          authorId: adminUser.id,
          categoryId: featuredCategory.id,
          status: "published",
          featured: true,
          publishedAt: now,
          views: 0
        }
      });

      // Update the uploaded image with the article entityId
      await prisma.uploadedImage.update({
        where: { id: welcomeImage.id },
        data: { entityId: welcomeArticle.id }
      });
      
      // Article 2 - News
      const newsArticle = await prisma.newsArticle.create({
        data: {
          title: "New Features Coming Soon",
          slug: "new-features-coming-soon",
          content: `<h2>Exciting New Features Coming Soon!</h2>
<p>We're constantly working to improve your banking experience at the Bank of Styx.</p>
<p>Here's a preview of some exciting new features we're developing for you.</p>
<h3>Coming Soon</h3>
<ul>
  <li>Enhanced security features</li>
  <li>Mobile app for on-the-go banking</li>
  <li>Improved user interface</li>
  <li>New merchant partnerships</li>
</ul>
<p>Stay tuned for more updates!</p>
${generateLoremArticle(3)}`,
          excerpt: "We're working on exciting new features to enhance your banking experience.",
          image: comingSoonImage.url,
          authorId: adminUser.id,
          categoryId: newsCategory.id,
          status: "published",
          featured: false,
          publishedAt: new Date(now.getTime() - ********), // 1 day ago
          views: 0
        }
      });

      // Update the uploaded image with the article entityId
      await prisma.uploadedImage.update({
        where: { id: comingSoonImage.id },
        data: { entityId: newsArticle.id }
      });
      
      console.log("Created 2 sample articles with uploaded image records");
    } else {
      console.log(`Using ${articlesCount} existing articles`);
    }

    console.log("Database seeding completed successfully");
    await prisma.$disconnect();
  } catch (error) {
    console.error("Error seeding database:", error);
    process.exit(1);
  }
};

seedDb();
