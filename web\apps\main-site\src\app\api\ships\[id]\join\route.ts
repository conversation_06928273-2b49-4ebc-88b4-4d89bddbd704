import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth";

export const dynamic = 'force-dynamic';

interface Params {
  params: {
    id: string;
  };
}

export async function POST(request: Request, { params }: Params) {
  try {
    const { id: shipId } = params;
    const currentUser = await getCurrentUser(request);

    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Check if ship exists and is active
    const ship = await prisma.ship.findUnique({
      where: { 
        id: shipId,
        status: 'active',
      },
      include: {
        captain: {
          select: { id: true, displayName: true },
        },
      },
    });

    if (!ship) {
      return NextResponse.json({ error: "Ship not found" }, { status: 404 });
    }

    // Check if user is already a member of any ship
    const existingMembership = await prisma.shipMember.findFirst({
      where: {
        userId: currentUser.id,
        status: 'active',
      },
    });

    if (existingMembership) {
      return NextResponse.json(
        { error: "You are already a member of a ship" },
        { status: 400 }
      );
    }

    // Check if there's already a pending request
    const existingRequest = await prisma.shipJoinRequest.findFirst({
      where: {
        userId: currentUser.id,
        shipId: shipId,
        status: 'pending',
      },
    });

    if (existingRequest) {
      return NextResponse.json(
        { error: "You already have a pending request for this ship" },
        { status: 400 }
      );
    }

    // Create join request
    const joinRequest = await prisma.shipJoinRequest.create({
      data: {
        userId: currentUser.id,
        shipId: shipId,
        requestedById: currentUser.id, // User is requesting to join
        type: 'request',
        status: 'pending',
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
        ship: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      message: `Join request sent to ${ship.captain.displayName}`,
      request: {
        id: joinRequest.id,
        status: joinRequest.status,
        createdAt: joinRequest.createdAt.toISOString(),
      },
    });
  } catch (error) {
    console.error("Error creating join request:", error);
    return NextResponse.json(
      { error: "Failed to create join request" },
      { status: 500 }
    );
  }
}