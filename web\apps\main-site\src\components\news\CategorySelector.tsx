"use client";

import React, { useState, memo, useCallback } from "react";
import { Category } from "../../services/newsService";
import { useCreateCategory } from "../../hooks/useNews";
import { EMPTY_ARRAY, deepEqual } from "../../lib/performance";

interface CategorySelectorProps {
  selectedCategory: string;
  onChange: (category: string) => void;
  error?: boolean;
  errorMessage?: string;
  className?: string;
  disabled?: boolean;
  categories?: Category[];
}

const CategorySelectorComponent: React.FC<CategorySelectorProps> = ({
  selectedCategory,
  onChange,
  error = false,
  errorMessage = "",
  className = "",
  disabled = false,
  categories = EMPTY_ARRAY, // Use stable empty array reference
}) => {
  // Remove excessive logging - only log when categories actually change
  // console.log("CategorySelector received categories:", categories);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newCategory, setNewCategory] = useState("");
  const [createError, setCreateError] = useState("");

  const createCategoryMutation = useCreateCategory();

  // Handle new category creation
  const handleCreateCategory = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newCategory.trim()) {
      setCreateError("Category name is required");
      return;
    }

    // Check if category already exists
    const categoryExists = categories.some(
      (cat) => cat.name.toLowerCase() === newCategory.toLowerCase(),
    );

    if (categoryExists) {
      setCreateError("A category with this name already exists");
      return;
    }

    try {
      // Create the new category using the mutation
      const createdCategory = await createCategoryMutation.mutateAsync({
        name: newCategory,
        description: "", // Optional description
      });

      // Select the newly created category
      onChange(createdCategory.id);

      // Reset form and close it
      setNewCategory("");
      setCreateError("");
      setShowCreateForm(false);
    } catch (error) {
      console.error("Error creating category:", error);
      setCreateError("Failed to create category. Please try again.");
    }
  };

  return (
    <div className={className}>
      {showCreateForm ? (
        <div
          className={`p-3 bg-secondary rounded-md border ${
            createError ? "border-accent" : "border-gray-600"
          }`}
        >
          <form onSubmit={handleCreateCategory} className="space-y-3">
            <div>
              <input
                type="text"
                placeholder="Category name"
                className={`w-full px-3 py-2 bg-secondary-dark border ${
                  createError ? "border-accent" : "border-gray-600"
                } rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary`}
                value={newCategory}
                onChange={(e) => setNewCategory(e.target.value)}
                disabled={createCategoryMutation.isPending || disabled}
              />
              {createError && (
                <p className="mt-1 text-sm text-accent">{createError}</p>
              )}
            </div>
            <div className="flex justify-end space-x-2">
              <button
                type="button"
                className="px-3 py-1 bg-secondary-dark text-white rounded-md hover:bg-secondary"
                onClick={() => {
                  setShowCreateForm(false);
                  setCreateError("");
                  setNewCategory("");
                }}
                disabled={createCategoryMutation.isPending || disabled}
              >
                Cancel
              </button>
              <button
                type="submit"
                className={`px-3 py-1 ${
                  createCategoryMutation.isPending || disabled
                    ? "bg-gray-600 cursor-not-allowed"
                    : "bg-primary hover:bg-primary-dark"
                } text-white rounded-md flex items-center`}
                disabled={createCategoryMutation.isPending || disabled}
              >
                {createCategoryMutation.isPending && (
                  <svg
                    className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                )}
                Create
              </button>
            </div>
          </form>
        </div>
      ) : (
        <div className="flex flex-col space-y-2">
          <div className="relative">
            <select
              className={`w-full px-4 py-2 bg-secondary border ${
                error ? "border-accent" : "border-gray-600"
              } rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary appearance-none`}
              value={selectedCategory}
              onChange={(e) => onChange(e.target.value)}
              disabled={disabled}
            >
              {" "}
              <option value="">Select a category</option>
              {Array.isArray(categories) && categories.length > 0 ? (
                categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))
              ) : (
                <option value="" disabled>
                  No categories available
                </option>
              )}
            </select>
            <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
              <svg
                className="w-5 h-5 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M19 9l-7 7-7-7"
                ></path>
              </svg>
            </div>
          </div>
          {error && errorMessage && (
            <p className="mt-1 text-sm text-accent">{errorMessage}</p>
          )}
        </div>
      )}
    </div>
  );
};

// Memoize the component to prevent unnecessary re-renders
export const CategorySelector = memo(
  CategorySelectorComponent,
  (prevProps, nextProps) => {
    // Custom comparison function to prevent re-renders when props haven't meaningfully changed
    return (
      prevProps.selectedCategory === nextProps.selectedCategory &&
      prevProps.error === nextProps.error &&
      prevProps.errorMessage === nextProps.errorMessage &&
      prevProps.className === nextProps.className &&
      prevProps.disabled === nextProps.disabled &&
      prevProps.categories?.length === nextProps.categories?.length &&
      deepEqual(prevProps.categories, nextProps.categories)
    );
  },
);
