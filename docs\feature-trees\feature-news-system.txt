﻿# Bank of Styx Website - News & Content System
# Generated on 08/07/2025 12:35:25
# Priority: Medium
# News article management, content publishing, and news dashboard
# Root directory: C:\Users\<USER>\projects\test\web

## Directories and Files

### Files
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\news\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\news\analytics\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\news\analytics\route.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\news\articles\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\news\articles\[id]\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\news\articles\route.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\news\categories\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\news\categories\[id]\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\news\categories\route.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\news\public\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\news\public\[slug]\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\news\public\route.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\news\README.md
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\news\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\news\[slug]\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\news\dashboard\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\news\dashboard\articles\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\news\dashboard\articles\[id]\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\news\dashboard\articles\new\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\news\dashboard\articles\new\page.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\news\dashboard\articles\page.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\news\dashboard\categories\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\news\dashboard\categories\page.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\news\dashboard\featured\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\news\dashboard\featured\page.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\news\dashboard\page.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\news\layout.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\news\page.tsx

