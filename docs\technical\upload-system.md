# File Upload System Documentation

## Overview
The Bank of Styx file upload system provides secure, organized, and optimized file handling across the entire platform. It supports multiple upload types, automatic image processing, database tracking, and organized storage with both v1 and v2 API implementations.

## System Architecture

### Upload API Endpoints
```
/api/uploads/
├── route.ts                    # v1 General upload endpoint
├── avatar/route.ts             # Dedicated avatar upload
├── [type]/[filename]/route.ts  # File serving endpoint  
└── v2/route.ts                 # Enhanced v2 upload system
```

### Storage Structure
```
public/uploads/
├── avatars/        # User profile images
├── deposits/       # Banking deposit receipts
├── news/          # News article images
├── products/      # Product images
├── ships/         # Ship logos and images
├── general/       # General purpose uploads
└── events/        # Event-related media
```

## Upload System Versions

### Version 1 (Legacy)
- **Endpoint**: `POST /api/uploads`
- **Service**: `imageUploadService.ts`
- **Features**: Basic file validation, simple storage
- **Usage**: General uploads with basic type handling

### Version 2 (Current)
- **Endpoint**: `POST /api/uploads/v2`
- **Service**: `uploadServiceV2.ts`
- **Features**: Enhanced validation, image processing, metadata tracking
- **Usage**: All new upload implementations

## Supported Upload Types

### Image Types
- **Formats**: JPEG, PNG, GIF, WebP
- **Max Size**: 10MB (configurable per type)
- **Processing**: Auto-optimization, format conversion, dimension detection
- **Validation**: Header verification, mime type checking

### Upload Categories

#### Avatar Uploads (`/api/uploads/avatar`)
- **Directory**: `uploads/avatars/`
- **Size Limit**: 5MB
- **Processing**: Auto-resize, optimization
- **Integration**: User profile system
- **Special Features**: Discord avatar sync

#### News Images (`type: "news"`)
- **Directory**: `uploads/news/`
- **Size Limit**: 10MB
- **Processing**: Web optimization, thumbnail generation
- **Integration**: News article system
- **Usage**: Featured images, inline content

#### Deposit Receipts (`type: "deposit"`)
- **Directory**: `uploads/deposits/`
- **Size Limit**: 10MB
- **Processing**: Document optimization
- **Integration**: Banking transaction system
- **Security**: Admin-only access for verification

#### Product Images (`type: "product"`)
- **Directory**: `uploads/products/`
- **Size Limit**: 8MB
- **Processing**: E-commerce optimization, multiple sizes
- **Integration**: Shopping system
- **Features**: Gallery support, zoom optimization

#### Ship Logos (`type: "ship-logo"`)
- **Directory**: `uploads/ships/`
- **Size Limit**: 5MB
- **Processing**: Logo optimization, consistent sizing
- **Integration**: Ship management system
- **Usage**: Ship profiles, directory listings

## File Processing Pipeline

### V2 Processing Flow
```typescript
1. File Reception & Validation
   ↓
2. Security Checks (type, size, content)
   ↓
3. Unique Filename Generation
   ↓
4. Image Processing (if applicable)
   ↓
5. Storage to Filesystem
   ↓
6. Database Record Creation
   ↓
7. URL Generation & Response
```

### Image Processing Features
- **Optimization**: Automatic compression and format conversion
- **Dimension Detection**: Width/height extraction and storage
- **Quality Control**: Configurable compression levels
- **Format Conversion**: WebP conversion for web optimization
- **Thumbnail Generation**: Future-ready for thumbnail systems

## Database Integration

### UploadedImage Model
```typescript
interface UploadedImage {
  id: string;              // UUID primary key
  filename: string;        // Original sanitized filename
  path: string;           // Full filesystem path
  url: string;            // Public access URL
  mimeType: string;       // File mime type
  size: number;           // File size in bytes
  entityType?: string;    // Upload category/type
  entityId?: string;      // Related entity ID
  upload_type: string;    // V2 upload type
  upload_config: object;  // Upload configuration
  dimensions: object;     // Image dimensions {width, height}
  createdAt: DateTime;    // Upload timestamp
  updatedAt: DateTime;    // Last modification
}
```

### Database Relationships
- **Users**: Avatar uploads linked to user profiles
- **NewsArticles**: Featured images linked to articles
- **Products**: Product images linked to catalog items
- **Transactions**: Deposit receipts linked to banking transactions

## Upload Configuration

### Type-Specific Configuration
```typescript
// Example configuration in uploadConfig
const uploadConfig = {
  avatar: {
    maxSize: 5 * 1024 * 1024,        // 5MB
    allowedTypes: ["image/jpeg", "image/png", "image/webp"],
    processImage: true,
    quality: 85
  },
  news: {
    maxSize: 10 * 1024 * 1024,       // 10MB
    allowedTypes: ["image/jpeg", "image/png", "image/webp"],
    processImage: true,
    quality: 90
  }
};
```

### Environment Variables
```env
# File upload limits
MAX_FILE_SIZE=********                    # 10MB default
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp

# Storage paths
UPLOAD_PATH=public/uploads                # Base upload directory
```

## Security Features

### File Validation
- **Type Verification**: MIME type and file header validation
- **Size Limits**: Configurable per upload type
- **Content Scanning**: Basic image header validation
- **Filename Sanitization**: Special character removal and normalization

### Access Control
- **Authentication Required**: Most upload endpoints require valid JWT
- **Role-Based Permissions**: Admin-only for certain upload types
- **Entity Ownership**: Users can only upload for owned entities
- **Rate Limiting**: Built-in request throttling

### Storage Security
- **Unique Filenames**: UUID-based naming prevents conflicts
- **Directory Organization**: Type-based folder structure
- **Public Access**: Controlled via API endpoints only
- **Path Traversal Protection**: Sanitized paths and validation

## API Usage Examples

### V2 Upload (Recommended)
```typescript
// Upload user avatar
const formData = new FormData();
formData.append('file', file);
formData.append('type', 'avatar');
formData.append('entityId', userId);

const response = await fetch('/api/uploads/v2', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`
  },
  body: formData
});

const result = await response.json();
// Returns: { success: true, file: { id, url, dimensions, ... } }
```

### Legacy V1 Upload
```typescript
// General upload
const formData = new FormData();
formData.append('file', file);
formData.append('entityType', 'news');
formData.append('entityId', articleId);

const response = await fetch('/api/uploads', {
  method: 'POST',
  body: formData
});
```

### Avatar Upload (Dedicated Endpoint)
```typescript
const formData = new FormData();
formData.append('file', file);

const response = await fetch('/api/uploads/avatar', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`
  },
  body: formData
});
```

## File Serving

### Access Patterns
- **Direct Access**: `/uploads/[type]/[filename]` (public directory)
- **API Access**: `/api/images/[id]` (database-routed access)
- **Type Access**: `/api/uploads/[type]/[filename]` (organized access)

### Caching Strategy
- **Static Files**: Served by Next.js static file handler
- **Browser Caching**: Standard HTTP cache headers
- **CDN Ready**: Compatible with CDN deployment patterns

## Error Handling

### Common Error Responses
```typescript
// File too large
{ error: "File size exceeds 10MB limit", status: 400 }

// Invalid file type
{ error: "File type not supported", status: 400 }

// Missing file
{ error: "No file provided", status: 400 }

// Invalid upload type
{ error: "Invalid upload type", status: 400 }

// Processing error
{ error: "Upload failed", status: 500 }
```

### Error Recovery
- **Graceful Degradation**: Fallback to original file if processing fails
- **Partial Uploads**: Cleanup of failed uploads
- **Database Consistency**: Transactional upload recording
- **Logging**: Comprehensive error logging for debugging

## Performance Considerations

### Optimization Strategies
- **Image Processing**: Sharp-based optimization for fast processing
- **Async Processing**: Non-blocking upload handling
- **Memory Management**: Stream-based file handling for large files
- **Database Indexing**: Optimized queries for upload lookups

### Scalability Features
- **Configurable Limits**: Per-type size and count limits
- **Directory Sharding**: Future-ready for large-scale deployments
- **CDN Integration**: Static file serving optimization
- **Monitoring Ready**: Built-in metrics and logging

## Development Workflow

### Adding New Upload Type
1. **Update Configuration**: Add type to `uploadConfig`
2. **Create Directory**: Ensure upload directory exists
3. **Update Service**: Add type handling in `getDirectoryForType()`
4. **Add Validation**: Type-specific validation rules
5. **Test Integration**: Verify with frontend components
6. **Update Documentation**: Document new type usage

### Local Development
```bash
# Ensure upload directories exist
mkdir -p web/apps/main-site/public/uploads/{avatars,news,products,deposits,ships,general,events}

# Set appropriate permissions
chmod 755 web/apps/main-site/public/uploads/

# Test upload functionality
curl -X POST http://localhost:3000/api/uploads/v2 \
  -H "Authorization: Bearer $TOKEN" \
  -F "file=@test-image.jpg" \
  -F "type=general"
```

## Troubleshooting

### Common Issues

#### Upload Directory Permissions
```bash
# Fix permissions issue
chmod -R 755 public/uploads/
chown -R $USER:$USER public/uploads/
```

#### Large File Uploads
```javascript
// Next.js config adjustment
module.exports = {
  api: {
    bodyParser: {
      sizeLimit: '10mb',
    },
  },
}
```

#### Database Connection Issues
```bash
# Verify Prisma client generation
npx prisma generate

# Check database connection
npx prisma db pull
```

## Migration Notes

### V1 to V2 Migration
- **Database Schema**: V2 adds additional metadata fields
- **API Changes**: Enhanced response format and validation
- **Processing**: Improved image optimization pipeline
- **Compatibility**: V1 endpoints remain functional for backward compatibility

---

**File Locations:**
- **API Routes**: `/src/app/api/uploads/`
- **Services**: `/src/services/imageUploadService.ts`, `/src/services/uploadServiceV2.ts`
- **Configuration**: `/src/lib/uploadConfig.ts`
- **Storage**: `/public/uploads/`
- **Database Model**: `UploadedImage` in `prisma/schema.prisma`

**Dependencies:**
- **Sharp**: Image processing library
- **UUID**: Unique identifier generation
- **Prisma**: Database ORM
- **Next.js**: File serving and API handling