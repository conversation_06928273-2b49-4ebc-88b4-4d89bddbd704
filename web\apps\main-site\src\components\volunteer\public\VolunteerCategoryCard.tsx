"use client";

import React from "react";
import { VolunteerCategory } from "@/hooks/usePublicVolunteer";

interface VolunteerCategoryCardProps {
  category: VolunteerCategory;
  onClick: () => void;
}

export const VolunteerCategoryCard: React.FC<VolunteerCategoryCardProps> = ({
  category,
  onClick,
}) => {
  return (
    <div
      className="bg-secondary-light rounded-lg shadow-md overflow-hidden border border-gray-600 hover:shadow-lg transition-shadow duration-300 cursor-pointer"
      onClick={onClick}
    >
      {/* Card Header */}
      <div className="p-4 border-b border-gray-600 bg-secondary">
        <h3 className="text-lg font-bold text-white truncate">
          {category.name}
        </h3>
      </div>

      {/* Card Body */}
      <div className="p-4">
        {/* Description */}
        <div className="mb-4">
          <p className="text-gray-300 line-clamp-3">
            {category.description || "No description provided."}
          </p>
        </div>

        {/* Pay Rate */}
        <div className="mb-4">
          <span className="text-sm font-medium text-gray-400">Pay Rate:</span>
          <span className="ml-2 text-white">
            £{category.payRate.toFixed(2)}/hr
          </span>
        </div>

        {/* Available Slots */}
        <div className="bg-secondary p-3 rounded-md">
          <div className="flex justify-between items-center">
            <span className="text-gray-400">Available Slots:</span>
            <span className="text-lg font-bold text-primary">
              {category.stats.availableSlots}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};
