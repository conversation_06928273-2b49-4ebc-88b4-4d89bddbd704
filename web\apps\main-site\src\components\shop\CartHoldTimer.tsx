"use client";

import { useState, useEffect } from "react";
import { Button } from "@bank-of-styx/ui";
import { useRefreshHolds } from "@/hooks/useCart";
import { toast } from "react-hot-toast";

interface CartHoldTimerProps {
  expiresAt: string | null;
  ticketCount: number;
  onExpired?: () => void;
}

export function CartHoldTimer({
  expiresAt,
  ticketCount,
  onExpired,
}: CartHoldTimerProps) {
  const [remainingTime, setRemainingTime] = useState<string>("");
  const [isExpiring, setIsExpiring] = useState<boolean>(false);
  const [isExpired, setIsExpired] = useState<boolean>(false);
  const refreshHolds = useRefreshHolds();

  useEffect(() => {
    if (!expiresAt) return;

    const expires = new Date(expiresAt);

    const updateTimer = () => {
      const now = new Date();
      const diff = expires.getTime() - now.getTime();

      if (diff <= 0) {
        setRemainingTime("Expired");
        setIsExpiring(false);
        setIsExpired(true);
        onExpired?.();
        return;
      }

      const minutes = Math.floor(diff / 60000);
      const seconds = Math.floor((diff % 60000) / 1000);
      setRemainingTime(`${minutes}:${seconds.toString().padStart(2, "0")}`);

      // Set expiring flag when < 2 minutes left
      setIsExpiring(diff < 120000);
    };

    updateTimer();
    const interval = setInterval(updateTimer, 1000);

    return () => clearInterval(interval);
  }, [expiresAt, onExpired]);

  const handleRefresh = async () => {
    try {
      await refreshHolds.mutateAsync();
      toast.success("Hold extended for 15 more minutes");
      setIsExpiring(false);
      setIsExpired(false);
    } catch (error) {
      toast.error("Failed to extend hold");
    }
  };

  if (!expiresAt) return null;

  return (
    <div
      className={`flex items-center justify-between rounded-md p-3 mb-4 ${
        isExpired
          ? "bg-red-50 border border-red-200"
          : isExpiring
          ? "bg-amber-50 border border-amber-200"
          : "bg-blue-50 border border-blue-200"
      }`}
    >
      <div className="flex-grow">
        <p
          className={`text-sm font-medium ${
            isExpired
              ? "text-red-800"
              : isExpiring
              ? "text-amber-800"
              : "text-blue-800"
          }`}
        >
          {isExpired
            ? `${ticketCount} ${
                ticketCount === 1 ? "ticket" : "tickets"
              } hold expired`
            : isExpiring
            ? `Hold expiring soon: ${remainingTime}`
            : `${ticketCount} ${
                ticketCount === 1 ? "ticket" : "tickets"
              } reserved for: ${remainingTime}`}
        </p>
      </div>
      {!isExpired && (
        <Button
          variant="outline"
          size="sm"
          onClick={handleRefresh}
          loading={refreshHolds.isPending}
          disabled={refreshHolds.isPending}
          className={
            isExpiring
              ? "border-amber-300 text-amber-700 hover:bg-amber-100"
              : "border-blue-300 text-blue-700 hover:bg-blue-100"
          }
        >
          Extend
        </Button>
      )}
    </div>
  );
}
