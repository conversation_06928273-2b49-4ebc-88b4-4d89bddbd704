"use client";

import { useState } from "react";
import { useSalesProducts, useDeleteProduct } from "@/hooks/useProducts";
import { useProductCategories } from "@/hooks/useProductCategories";
import { Product } from "@/services/productService";
import { <PERSON><PERSON>, Spinner } from "@bank-of-styx/ui";
import { Select, Badge } from "@/components/shared";
import Link from "next/link";
import { toast } from "react-hot-toast";
import { useConfirm } from "@/hooks/useConfirm";
import { ProductCategory } from "@/services/productService";

export const ProductList: React.FC = () => {
  // State for filters
  const [categoryFilter, setCategoryFilter] = useState<string>("");
  const [activeFilter, setActiveFilter] = useState<string>("all");

  // Fetch products with filters
  const { data, isLoading, error } = useSalesProducts({
    categoryId: categoryFilter || undefined,
    isActive:
      activeFilter === "active"
        ? true
        : activeFilter === "inactive"
        ? false
        : undefined,
  });

  // Fetch categories for filter dropdown
  const { data: categoriesData } = useProductCategories();

  // Delete product mutation
  const deleteProduct = useDeleteProduct();
  const { confirm, ConfirmationDialog } = useConfirm();

  // Handle delete product
  const handleDeleteProduct = async (id: string, name: string) => {
    try {
      const confirmed = await confirm({
        title: "Delete Product",
        message: `Are you sure you want to delete "${name}"? This will also remove all related redemption codes, tickets, and cart items. This action cannot be undone.`,
        confirmText: "Delete",
        cancelText: "Cancel",
      });

      if (confirmed) {
        try {
          const result = await deleteProduct.mutateAsync(id);
          
          // Show detailed success message
          if (result && typeof result === 'object' && 'deletedItems' in result) {
            const deletedItems = result.deletedItems as any;
            let details = [];
            if (deletedItems.redemptionCodes > 0) details.push(`${deletedItems.redemptionCodes} redemption codes`);
            if (deletedItems.tickets > 0) details.push(`${deletedItems.tickets} tickets`);
            if (deletedItems.cartItems > 0) details.push(`${deletedItems.cartItems} cart items`);
            if (deletedItems.orderItems > 0) details.push(`${deletedItems.orderItems} pending order items`);
            
            const detailMessage = details.length > 0 ? ` (Also removed: ${details.join(', ')})` : '';
            toast.success(`Product "${name}" deleted successfully${detailMessage}`);
          } else {
            toast.success(`Product "${name}" deleted successfully`);
          }
        } catch (error: any) {
          console.error("Error deleting product:", error);
          
          // Show specific error message if available
          const errorMessage = error?.response?.data?.error || "Failed to delete product";
          toast.error(errorMessage);
        }
      }
    } catch (error) {
      console.error("Error in handleDeleteProduct:", error);
      toast.error("An error occurred while trying to delete the product");
    }
  };

  // Handle filter changes
  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setCategoryFilter(e.target.value);
  };

  const handleActiveFilterChange = (
    e: React.ChangeEvent<HTMLSelectElement>,
  ) => {
    setActiveFilter(e.target.value);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center p-8">
        <Spinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 text-center">
        <p className="text-accent mb-4">Error loading products</p>
        <Button onClick={() => window.location.reload()} variant="secondary">
          Try Again
        </Button>
      </div>
    );
  }

  const products = data || [];

  return (
    <div>
      {/* Filters */}
      <div className="flex flex-col md:flex-row gap-4 mb-6 p-4 bg-secondary-light rounded-lg">
        <div className="w-full md:w-1/3">
          <Select
            label="Category"
            value={categoryFilter}
            onChange={handleCategoryChange}
            fullWidth
          >
            <option value="">All Categories</option>
            {categoriesData?.categories?.map((category: ProductCategory) => (
              <option key={category.id} value={category.id}>
                {category.name}
              </option>
            ))}
          </Select>
        </div>
        <div className="w-full md:w-1/3">
          <Select
            label="Status"
            value={activeFilter}
            onChange={handleActiveFilterChange}
            fullWidth
          >
            <option value="all">All Status</option>
            <option value="active">Active Only</option>
            <option value="inactive">Inactive Only</option>
          </Select>
        </div>
      </div>

      {/* Products Table */}
      {products.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-text-muted mb-4">
            No products found matching your filters.
          </p>
          <Link href="/sales/products/create">
            <Button variant="primary">Create New Product</Button>
          </Link>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-secondary-dark">
                <th className="px-4 py-3 text-left">Name</th>
                <th className="px-4 py-3 text-left">Category</th>
                <th className="px-4 py-3 text-right">Price</th>
                <th className="px-4 py-3 text-center">Inventory</th>
                <th className="px-4 py-3 text-center">Status</th>
                <th className="px-4 py-3 text-right">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-border-subtle">
              {products.map((product: Product) => (
                <tr key={product.id} className="hover:bg-secondary-light">
                  <td className="px-4 py-3">
                    <div className="font-medium flex items-center gap-2">
                      {product.name}
                      {product.isFree && (
                        <Badge variant="success" className="text-xs">
                          Free
                        </Badge>
                      )}
                    </div>
                    <div className="text-sm text-text-muted">
                      {product.event
                        ? `Event: ${product.event.name}`
                        : "General Product"}
                    </div>
                  </td>
                  <td className="px-4 py-3">{product.category.name}</td>
                  <td className="px-4 py-3 text-right">
                    {product.isFree ? (
                      <span className="text-success font-semibold">FREE</span>
                    ) : (
                      `$${product.price.toFixed(2)}`
                    )}
                  </td>
                  <td className="px-4 py-3 text-center">
                    {product.inventory === null ? (
                      <span className="text-text-muted">Unlimited</span>
                    ) : product.inventory <= 5 ? (
                      <span className="text-accent">{product.inventory}</span>
                    ) : (
                      product.inventory
                    )}
                  </td>
                  <td className="px-4 py-3 text-center">
                    <Badge variant={product.isActive ? "success" : "warning"}>
                      {product.isActive ? "Active" : "Inactive"}
                    </Badge>
                  </td>
                  <td className="px-4 py-3 text-right">
                    <div className="flex justify-end space-x-2">
                      <Link href={`/sales/products/${product.id}/stats`}>
                        <Button variant="secondary" size="sm">
                          Stats
                        </Button>
                      </Link>
                      <Link href={`/sales/products/${product.id}/edit`}>
                        <Button variant="secondary" size="sm">
                          Edit
                        </Button>
                      </Link>
                      <Button
                        variant="secondary"
                        size="sm"
                        onClick={() =>
                          handleDeleteProduct(product.id, product.name)
                        }
                        loading={deleteProduct.isPending}
                      >
                        Delete
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
      <ConfirmationDialog />
    </div>
  );
};
