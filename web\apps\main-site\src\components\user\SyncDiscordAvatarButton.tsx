"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@bank-of-styx/ui";
import fetchClient from "@/lib/fetchClient";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "react-hot-toast";

interface SyncDiscordAvatarButtonProps {
  className?: string;
  variant?: "primary" | "secondary" | "outline" | "accent";
  size?: "sm" | "md" | "lg";
}

export const SyncDiscordAvatarButton: React.FC<
  SyncDiscordAvatarButtonProps
> = ({ className = "", variant = "primary", size = "md" }) => {
  const [isSyncing, setIsSyncing] = useState(false);
  const { refreshUserData, user, connectDiscord } = useAuth();

  const handleSync = async () => {
    try {
      // Check if user has Discord connected
      if (!user?.connectedAccounts?.discord) {
        toast.error("You need to connect your Discord account first");
        // Offer to connect Discord
        if (confirm("Would you like to connect your Discord account now?")) {
          connectDiscord();
        }
        return;
      }

      setIsSyncing(true);

      // Call the sync endpoint
      const response = await fetchClient.request<{
        success: boolean;
        avatarUrl: string;
      }>("/api/users/sync-discord-avatar", {
        method: "POST",
      });

      if (response.success) {
        // Refresh user data to update the avatar in the UI
        await refreshUserData();
        toast.success("Discord avatar synced successfully");
      } else {
        toast.error("Failed to sync Discord avatar");
      }
    } catch (error) {
      console.error("Error syncing Discord avatar:", error);

      // Handle specific error cases
      if (error instanceof Error) {
        if (error.message === "Discord account not connected") {
          toast.error("You need to connect your Discord account first");
          // Offer to connect Discord
          if (confirm("Would you like to connect your Discord account now?")) {
            connectDiscord();
          }
        } else if (error.message === "Discord user has no avatar") {
          toast.error("Your Discord account doesn't have an avatar set");
        } else {
          toast.error(`Error: ${error.message}`);
        }
      } else {
        toast.error("Failed to sync Discord avatar");
      }
    } finally {
      setIsSyncing(false);
    }
  };

  return (
    <Button
      onClick={handleSync}
      disabled={isSyncing}
      variant={variant}
      size={size}
      className={className}
    >
      {isSyncing ? "Syncing..." : "Sync Discord Avatar"}
    </Button>
  );
};
