"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";

interface NavigationProps {
  isMobileNavOpen: boolean;
  closeMobileNav: () => void;
}

const navItems = [
  { name: "Home", path: "/" },
  { name: "Auctions", path: "/auctions" },
  { name: "Merchants", path: "/merchants" },
  { name: "Bank", path: "/bank" },
  { name: "Chat", path: "/chat" },
  { name: "News", path: "/news" },
  { name: "Rules", path: "/rules" },
  { name: "Help", path: "/help" },
  { name: "Settings", path: "/settings" },
  { name: "About", path: "/about" },
];

export default function Navigation({
  isMobileNavOpen,
  closeMobileNav,
}: NavigationProps) {
  const pathname = usePathname();

  // Function to check if a path is active
  const isActive = (path: string) => {
    if (path === "/") {
      return pathname === "/";
    }
    return pathname.startsWith(path);
  };

  return (
    <>
      {/* Overlay for mobile - only shown when mobile nav is open */}
      {isMobileNavOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
          onClick={closeMobileNav}
          aria-hidden="true"
        />
      )}

      <nav
        className={`
          bg-secondary flex-shrink-0 shadow-md
          md:w-40 md:sticky md:top-12 md:self-start md:block md:z-10
          ${
            isMobileNavOpen
              ? "block fixed z-50 h-full right-0 top-0 w-32"
              : "hidden"
          }
        `}
      >
        <div className="p-3 md:p-3 space-y-2 md:space-y-3">
          <div className="border-b border-gray-600 pb-2 flex justify-between items-center">
            <h2 className=" text-md md:text-xl pl-1 font-bold">Navigation</h2>

            {/* Close button - only visible on mobile */}
            <button
              className="md:hidden text-text-secondary hover:text-text-primary"
              onClick={closeMobileNav}
              aria-label="Close navigation"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6 md:h-6 md:w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          <ul className="space-y-2 md:space-y-4">
            {navItems.map((item) => (
              <li key={item.path}>
                <Link
                  href={item.path}
                  onClick={() => {
                    // Only close on mobile devices
                    if (window.innerWidth < 768) {
                      closeMobileNav();
                    }
                  }}
                  className={`
                  block px-2 md:px-3 py-2 md:py-2 text-sm md:text-base rounded-md transition-colors
                  ${
                    isActive(item.path)
                      ? "border-2 border-primary font-medium"
                      : "border-2 border-gray-600 hover:bg-secondary-light"
                  }
                `}
                >
                  {item.name}
                </Link>
              </li>
            ))}
          </ul>
        </div>
      </nav>
    </>
  );
}
