"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "../../../../contexts/AuthContext";
import {
  CashierDashboardLayout,
  LedgerDisplay,
} from "../../../../components/cashier";
import { toast } from "react-hot-toast";
import {
  useLedgerEntries,
  useCreateLedgerEntry,
  useVerifyLedgerEntry,
} from "../../../../hooks/useBank";

export default function LedgerPage() {
  const { user, isLoading } = useAuth();
  const router = useRouter();
  const [isAuthorized, setIsAuthorized] = useState(false);

  // Use hooks to fetch ledger entries and handle verification
  const {
    data: ledgerEntries,
    isLoading: isLoadingEntries,
    error,
  } = useLedgerEntries();
  const createLedgerMutation = useCreateLedgerEntry();
  const verifyLedgerMutation = useVerifyLedgerEntry();

  // Check authorization after auth is loaded
  useEffect(() => {
    // Only proceed with checks if auth loading is complete
    if (!isLoading) {
      if (!user || !user.roles?.banker) {
        router.push("/");
      } else {
        // User is authorized
        setIsAuthorized(true);
      }
    }
  }, [user, router, isLoading]);

  // Function to verify a ledger entry
  const handleVerifyLedger = async (id: string) => {
    try {
      await verifyLedgerMutation.mutateAsync(id);
      return Promise.resolve();
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to verify ledger entry";
      toast.error(errorMessage);
      return Promise.reject(error);
    }
  };

  // Function to create a new ledger entry
  const handleCreateEntry = async (data: {
    description: string;
    totalDeposits: number;
    totalWithdrawals: number;
    totalTransfers: number;
    netChange: number;
  }) => {
    try {
      await createLedgerMutation.mutateAsync(data);
      return Promise.resolve();
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to create ledger entry";
      toast.error(errorMessage);
      return Promise.reject(error);
    }
  };

  // Show loading state or nothing while checking authorization
  if (isLoading || !isAuthorized) {
    return (
      <div className="min-h-screen bg-secondary-dark flex items-center justify-center">
        <div className="bg-secondary-light p-6 rounded-lg shadow-md border border-gray-600">
          <p className="text-white text-lg">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <CashierDashboardLayout>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-white mb-2">
          Ledger Management
        </h1>
        <p className="text-gray-400">
          Create and verify ledger entries to track bank financial activity.
        </p>
      </div>

      {isLoadingEntries ? (
        <div className="bg-secondary-light rounded-lg shadow-md p-4 sm:p-6 border border-gray-600 text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-gray-400">Loading ledger entries...</p>
        </div>
      ) : error ? (
        <div className="bg-secondary-light rounded-lg shadow-md p-4 sm:p-6 border border-gray-600 text-center py-8">
          <div className="text-error text-4xl mb-2">!</div>
          <p className="text-error font-medium">Error loading ledger entries</p>
          <p className="text-gray-400 mt-2">
            {error instanceof Error ? error.message : "Unknown error"}
          </p>
        </div>
      ) : (
        <div className="bg-secondary-light rounded-lg shadow-md p-4 sm:p-6 border border-gray-600">
          <LedgerDisplay
            ledgerEntries={ledgerEntries || []}
            onVerify={handleVerifyLedger}
            onCreateEntry={handleCreateEntry}
          />
        </div>
      )}
    </CashierDashboardLayout>
  );
}
