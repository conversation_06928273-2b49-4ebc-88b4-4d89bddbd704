/* Dark mode overrides for Auth Modal */

/* Modal container */
.auth-modal-container {
  background-color: var(--color-secondary-light) !important;
  color: var(--color-text-primary) !important;
}

/* Modal header */
.auth-modal-header {
  background-color: var(--color-secondary) !important;
  border-color: var(--color-border-dark) !important;
}

.auth-modal-header h3 {
  color: var(--color-text-primary) !important;
}

/* Input labels */
.auth-modal-container label {
  color: var(--color-text-primary) !important;
}

/* Inputs */
.auth-modal-container input {
  background-color: var(--color-secondary) !important;
  border-color: var(--color-border-dark) !important;
  color: var(--color-text-primary) !important;
}

.auth-modal-container input:focus {
  border-color: var(--color-primary) !important;
  box-shadow: 0 0 0 2px rgba(88, 101, 242, 0.2) !important;
}

/* Divider */
.auth-modal-divider {
  color: var(--color-text-secondary) !important;
}

/* Discord button */
.auth-discord-button {
  background-color: rgba(88, 101, 242, 0.1) !important;
  border-color: var(--color-primary) !important;
}

.auth-discord-button:hover {
  background-color: rgba(88, 101, 242, 0.2) !important;
}

/* Toggle text */
.auth-toggle-text {
  color: var(--color-text-secondary) !important;
}

.auth-toggle-button {
  color: var(--color-primary) !important;
}

.auth-toggle-button:hover {
  color: var(--color-primary-light) !important;
}
