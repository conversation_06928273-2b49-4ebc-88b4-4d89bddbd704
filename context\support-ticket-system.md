# Support Ticket System

## Overview
A comprehensive help desk system with ticket management, role-based access control, bidirectional email integration, and internal note capabilities for customer support.

## Core Components

### Ticket Lifecycle
The support ticket system follows a structured workflow with multiple status transitions:

#### Ticket Status Flow
```typescript
// Status progression: open → in_progress → resolved → closed
const statusFlow = {
  open: "Initial submission state",
  in_progress: "<PERSON><PERSON> is actively working on the ticket", 
  resolved: "Issue resolved with solution provided",
  closed: "Ticket fully completed and archived"
};
```

#### Priority Levels
```typescript
// Priority determines handling urgency and admin notification level
const priorityLevels = {
  low: "Standard response time",
  medium: "Default priority for new tickets",
  high: "Escalated priority with faster response",
  urgent: "Critical issues requiring immediate attention"
};
```

### Database Schema

#### SupportTicket Model
```sql
SupportTicket {
  id: String (UUID)
  subject: String (required)
  message: String (required, LongText)
  status: String (default: "open")
  priority: String (default: "medium") 
  category: String (default: "general")
  
  // Contact Information
  name: String? (auto-filled from user if authenticated)
  email: String (required, validated)
  phone: String?
  
  // User Association
  userId: String? (if submitted by logged-in user)
  user: User? (relationship)
  
  // Assignment System
  assignedToId: String? (admin assigned to handle ticket)
  assignedTo: User? (relationship)
  assignedAt: DateTime?
  
  // Resolution Tracking
  resolvedById: String? (admin who resolved)
  resolvedBy: User? (relationship)
  resolvedAt: DateTime?
  resolution: String? (resolution description)
  
  // Timestamps
  createdAt: DateTime
  updatedAt: DateTime
  
  // Related Records
  notes: TicketNote[] (conversation history)
}
```

#### TicketNote Model
```sql
TicketNote {
  id: String (UUID)
  content: String (required, LongText)
  isInternal: Boolean (default: false)
  
  // Relationships
  ticketId: String (required)
  ticket: SupportTicket (relationship)
  authorId: String (required)
  author: User (relationship)
  
  // Timestamps
  createdAt: DateTime
  updatedAt: DateTime
}
```

### Role-based Access Control

#### Permission Matrix
```typescript
const permissions = {
  // Public (Unauthenticated)
  public: {
    create: true,        // Can submit tickets
    read: false,         // Cannot view tickets
    update: false,       // Cannot modify tickets
    notes: false         // Cannot add notes
  },
  
  // Authenticated Users
  user: {
    create: true,        // Can submit tickets
    read: "own",         // Can view only their tickets
    update: false,       // Cannot modify ticket status/priority
    notes: "own",        // Can add public notes to their tickets
    internal: false      // Cannot see internal notes
  },
  
  // Admin Users
  admin: {
    create: true,        // Can create tickets
    read: "all",         // Can view all tickets
    update: true,        // Can modify status, priority, assignment
    notes: "all",        // Can add notes to any ticket
    internal: true,      // Can create and view internal notes
    assign: true,        // Can assign tickets to other admins
    resolve: true        // Can mark tickets as resolved
  }
};
```

#### Access Validation Patterns
```typescript
// Ticket ownership validation
const hasTicketAccess = (user: User, ticket: SupportTicket, isAdmin: boolean) => {
  return isAdmin || ticket.userId === user.id;
};

// Note visibility filtering
const filterNotes = (notes: TicketNote[], isAdmin: boolean) => {
  return isAdmin ? notes : notes.filter(note => !note.isInternal);
};
```

## API Architecture

### Ticket Management Endpoints

#### GET /api/support/tickets
**Admin-only endpoint** for retrieving tickets with advanced filtering:

```typescript
// Query Parameters
interface TicketFilters {
  page?: number;           // Pagination (default: 1)
  limit?: number;          // Items per page (default: 10)
  status?: string;         // Filter by status
  priority?: string;       // Filter by priority
  category?: string;       // Filter by category
  search?: string;         // Search subject, message, email, name
  assignedToMe?: boolean;  // Show only tickets assigned to current admin
}

// Response includes pagination metadata
interface TicketListResponse {
  tickets: SupportTicket[];
  pagination: {
    total: number;
    pages: number;
    page: number;
    limit: number;
  };
}
```

#### POST /api/support/tickets
**Public endpoint** for ticket creation with automatic email notifications:

```typescript
// Accepts submissions from both authenticated and anonymous users
const createTicket = async (data: {
  subject: string;         // Required
  message: string;         // Required
  email: string;          // Required, validated with regex
  name?: string;          // Optional, auto-filled from user
  phone?: string;         // Optional
  category?: string;      // Default: "general"
}) => {
  // Auto-fills name from authenticated user if available
  // Sends immediate email notification to admin
  // Returns ticket ID and basic info
};
```

#### GET /api/support/tickets/[id]
**Role-based endpoint** with different data visibility:

```typescript
// Returns different note sets based on user role
const getTicket = async (id: string, user: User, isAdmin: boolean) => {
  const ticket = await prisma.supportTicket.findUnique({
    include: {
      notes: {
        where: isAdmin ? {} : { isInternal: false },  // Filter internal notes
        orderBy: { createdAt: "asc" }
      }
    }
  });
  
  // Validate access permissions
  if (!isAdmin && ticket.userId !== user.id) {
    throw new Error("Access denied");
  }
};
```

#### PATCH /api/support/tickets/[id]
**Admin-only endpoint** for ticket updates with state management:

```typescript
const updateTicket = async (id: string, updates: {
  status?: "open" | "in_progress" | "resolved" | "closed";
  priority?: "low" | "medium" | "high" | "urgent";
  category?: string;
  assignedToId?: string;
  resolution?: string;
}) => {
  // Auto-assigns resolved timestamp and user when status = "resolved"
  // Sends email notification to user when resolved
  // Updates assignment timestamp when assignedToId changes
};
```

### Note Management Endpoints

#### GET /api/support/tickets/[id]/notes
**Role-filtered endpoint** for conversation history:

```typescript
// Returns notes with role-based filtering
const getNotes = async (ticketId: string, user: User, isAdmin: boolean) => {
  return prisma.ticketNote.findMany({
    where: {
      ticketId,
      // Non-admins only see public notes
      ...(isAdmin ? {} : { isInternal: false })
    },
    include: { author: true },
    orderBy: { createdAt: "asc" }
  });
};
```

#### POST /api/support/tickets/[id]/notes
**Bidirectional communication** with automatic email notifications:

```typescript
const addNote = async (ticketId: string, data: {
  content: string;
  isInternal?: boolean;  // Only admins can set to true
}) => {
  // Email notification logic:
  // - Admin public note → Email to user
  // - User note → Email to admin
  // - Internal notes → No external email
};
```

## Email Integration System

### Bidirectional Email Communication
The system maintains email conversations that mirror the ticket system:

#### Admin Email Notifications
```typescript
// New ticket submission
const notifyAdminNewTicket = async (ticket: SupportTicket) => {
  await sendEmail({
    to: ADMIN_EMAIL,
    subject: `New Support Ticket: ${ticket.subject}`,
    html: `
      <h2>New Support Ticket Submission</h2>
      <p><strong>Ticket ID:</strong> ${ticket.id}</p>
      <p><strong>From:</strong> ${ticket.name} (${ticket.email})</p>
      <p><strong>Category:</strong> ${ticket.category}</p>
      <hr>
      <h3>Message:</h3>
      <p>${ticket.message.replace(/\n/g, "<br>")}</p>
    `,
    replyTo: ticket.email  // Enables direct email replies
  });
};

// User response notification
const notifyAdminUserResponse = async (ticket: SupportTicket, note: TicketNote) => {
  await sendEmail({
    to: ADMIN_EMAIL,
    subject: `User Response on Ticket: ${ticket.subject}`,
    html: `
      <h2>New User Response</h2>
      <p><strong>Ticket ID:</strong> ${ticket.id}</p>
      <p><strong>Status:</strong> ${ticket.status}</p>
      <hr>
      <h3>User Response:</h3>
      <p>${note.content.replace(/\n/g, "<br>")}</p>
    `
  });
};
```

#### User Email Notifications
```typescript
// Admin response notification
const notifyUserResponse = async (ticket: SupportTicket, note: TicketNote) => {
  await sendEmail({
    to: ticket.email,
    subject: `Re: Support Ticket: ${ticket.subject}`,
    html: `
      <h2>New Response to Your Support Ticket</h2>
      <p><strong>Ticket ID:</strong> ${ticket.id}</p>
      <hr>
      <h3>Response:</h3>
      <p>${note.content.replace(/\n/g, "<br>")}</p>
      <p>You can reply to this email or log in to view the full conversation.</p>
    `
  });
};

// Resolution notification
const notifyUserResolution = async (ticket: SupportTicket) => {
  await sendEmail({
    to: ticket.email,
    subject: `Support Ticket Resolved: ${ticket.subject}`,
    html: `
      <h2>Your Support Ticket Has Been Resolved</h2>
      <p><strong>Resolution:</strong></p>
      <p>${ticket.resolution?.replace(/\n/g, "<br>")}</p>
      <p>If you have further questions, please reply or create a new ticket.</p>
    `
  });
};
```

### Email Threading
- **Consistent Subject Lines**: Maintains email thread continuity
- **Reply-To Headers**: Enables direct email responses
- **HTML + Text**: Dual format for compatibility
- **Unique Identifiers**: Ticket IDs for tracking

## Frontend Service Integration

### TypeScript Service Layer
```typescript
// services/ticketService.ts provides typed API interactions
export interface SupportTicket {
  id: string;
  subject: string;
  status: "open" | "in_progress" | "resolved" | "closed";
  priority: "low" | "medium" | "high" | "urgent";
  category: string;
  // ... complete type definitions
}

// Centralized API functions with error handling
export const ticketService = {
  getTickets: (filters?: TicketFilters) => Promise<TicketListResponse>,
  getTicketById: (id: string) => Promise<SupportTicket>,
  createTicket: (data: CreateTicketData) => Promise<CreateTicketResponse>,
  updateTicket: (id: string, updates: TicketUpdates) => Promise<SupportTicket>,
  addNote: (id: string, note: NoteData) => Promise<TicketNote>
};
```

### Contact Form Integration
```typescript
// Contact forms automatically create support tickets
export const submitContactForm = async (data: {
  name?: string;
  email: string;
  phone?: string;
  message: string;
}) => {
  return ticketService.createTicket({
    subject: "Contact Form Submission",
    message: data.message,
    name: data.name,
    email: data.email,
    phone: data.phone,
    category: "general"
  });
};
```

## Security Features

### Input Validation
```typescript
// Email validation with regex
const emailRegex = /^\S+@\S+\.\S+$/;

// Content sanitization
const sanitizeContent = (content: string) => {
  return content.trim().substring(0, 10000); // Limit content length
};

// Role verification for sensitive operations
const requireAdmin = async (request: NextRequest) => {
  const user = await getCurrentUser(request);
  const isAdmin = await userHasRole(request, "admin");
  
  if (!user || !isAdmin) {
    throw new Error("Admin privileges required");
  }
  
  return user;
};
```

### Access Control Enforcement
```typescript
// Ticket access validation
const validateTicketAccess = async (ticketId: string, user: User, isAdmin: boolean) => {
  const ticket = await prisma.supportTicket.findUnique({
    where: { id: ticketId },
    select: { userId: true }
  });
  
  if (!ticket) {
    throw new Error("Ticket not found");
  }
  
  if (!isAdmin && ticket.userId !== user.id) {
    throw new Error("Access denied");
  }
  
  return ticket;
};

// Internal note restrictions
const validateInternalNotePermission = (isInternal: boolean, isAdmin: boolean) => {
  if (isInternal && !isAdmin) {
    throw new Error("Only admins can create internal notes");
  }
};
```

### Data Privacy
- **Personal Information**: Limited exposure of user data
- **Internal Notes**: Hidden from non-admin users
- **Email Privacy**: Controlled visibility and notifications
- **Access Logging**: Audit trail for sensitive operations

## Advanced Features

### Ticket Assignment System
```typescript
// Admins can assign tickets to specific team members
const assignTicket = async (ticketId: string, assignedToId: string) => {
  await prisma.supportTicket.update({
    where: { id: ticketId },
    data: {
      assignedToId,
      assignedAt: new Date()
    }
  });
  
  // Optional: Notify assigned admin
};

// Filter tickets assigned to current admin
const getMyAssignedTickets = async (adminId: string) => {
  return prisma.supportTicket.findMany({
    where: { assignedToId: adminId }
  });
};
```

### Category System
```typescript
// Predefined categories for ticket organization
const ticketCategories = {
  general: "General inquiries",
  account: "Account-related issues", 
  banking: "Banking and transaction problems",
  technical: "Technical support",
  other: "Other issues"
};

// Category-based filtering and routing
const getCategorizedTickets = async (category: string) => {
  return prisma.supportTicket.findMany({
    where: { category }
  });
};
```

### Search and Filtering
```typescript
// Multi-field search functionality
const searchTickets = async (searchTerm: string) => {
  return prisma.supportTicket.findMany({
    where: {
      OR: [
        { subject: { contains: searchTerm } },
        { message: { contains: searchTerm } },
        { email: { contains: searchTerm } },
        { name: { contains: searchTerm } }
      ]
    }
  });
};
```

## Integration Patterns

### Contact Form → Support Ticket
The contact form at `/api/contact` creates support tickets instead of standalone emails:

```typescript
// Unified contact handling
const contactSubmission = await ticketService.createTicket({
  subject: "Contact Form Submission",
  message: formData.message,
  email: formData.email,
  name: formData.name,
  phone: formData.phone,
  category: "general"
});
```

### User Dashboard Integration
- **My Tickets**: Users can view their submitted tickets
- **Ticket Status**: Real-time status updates
- **Response Notifications**: In-app and email notifications
- **Conversation History**: Full conversation thread access

### Admin Dashboard Features
- **Ticket Queue**: Prioritized ticket listing
- **Assignment Management**: Assign tickets to team members  
- **Bulk Operations**: Update multiple tickets
- **Performance Metrics**: Response time and resolution statistics

## Important Files
- `src/app/api/support/tickets/route.ts` - Main ticket CRUD operations
- `src/app/api/support/tickets/[id]/route.ts` - Individual ticket management
- `src/app/api/support/tickets/[id]/notes/route.ts` - Note/conversation management
- `src/services/ticketService.ts` - Frontend service layer
- `src/app/api/contact/route.ts` - Contact form integration
- `prisma/schema.prisma` - SupportTicket and TicketNote models

<function_calls>
<invoke name="TodoWrite">
<parameter name="todos">[{"id": "1", "content": "Examine support ticket API routes and implementation", "status": "completed", "priority": "high"}, {"id": "2", "content": "Review database schema for support ticket models", "status": "completed", "priority": "high"}, {"id": "3", "content": "Analyze frontend components for ticket management", "status": "completed", "priority": "medium"}, {"id": "4", "content": "Document notification integration patterns", "status": "completed", "priority": "medium"}, {"id": "5", "content": "Create comprehensive support ticket system documentation", "status": "completed", "priority": "high"}]