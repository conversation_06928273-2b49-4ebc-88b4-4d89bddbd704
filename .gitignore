



# Dependencies
delete/node_modules/
node_modules/


.pnpm-store/
.npm
yarn.lock

# Build outputs
.next/
build/
dist/
out/
nametag/
# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env*.local

# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# IDE and editor files
.idea/
.vscode/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Testing
/coverage
documents/
# Misc
*.log
.cache
.temp
*.bak

# Production uploads
/uploads

# Typescript
*.tsbuildinfo

# PWA files
public/sw.js
public/workbox-*.js

web/apps/main-site/public/uploads/
.config/
