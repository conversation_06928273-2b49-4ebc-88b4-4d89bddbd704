-- AlterTable
ALTER TABLE `products` ADD COLUMN `isFree` BO<PERSON><PERSON>N NOT NULL DEFAULT false;

-- CreateTable
CREATE TABLE `redemption_codes` (
    `id` VARCHAR(191) NOT NULL,
    `code` VARCHAR(191) NOT NULL,
    `productId` VARCHAR(191) NOT NULL,
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    UNIQUE INDEX `redemption_codes_code_key`(`code`),
    INDEX `redemption_codes_productId_idx`(`productId`),
    INDEX `redemption_codes_code_idx`(`code`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `redemption_codes` ADD CONSTRAINT `redemption_codes_productId_fkey` FOREI<PERSON>N KEY (`productId`) REFERENCES `products`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
