/**
 * Send an email
 * Note: This is a placeholder implementation. In a real application,
 * you would integrate with an email service like SendGrid, Mailgun, etc.
 */
export async function sendEmail(
  to: string,
  subject: string,
  message: string,
): Promise<void> {
  // In development, log the email to the console
  if (process.env.NODE_ENV === "development") {
    console.log("==== EMAIL SENT ====");
    console.log(`To: ${to}`);
    console.log(`Subject: ${subject}`);
    console.log(`Message: ${message}`);
    console.log("====================");
  }

  // In production, you would use an email service
  // Example with SendGrid:
  // await sendgrid.send({
  //   to,
  //   from: '<EMAIL>',
  //   subject,
  //   text: message,
  // });

  // For now, just return a resolved promise
  return Promise.resolve();
}
