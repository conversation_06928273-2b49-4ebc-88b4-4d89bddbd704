import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth";

export const dynamic = 'force-dynamic';

export async function GET(request: Request) {
  try {
    const currentUser = await getCurrentUser(request);

    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    if (!currentUser.isAdmin && !currentUser.isLandSteward) {
      return NextResponse.json(
        { error: "Land Steward or Admin access required" },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status') || 'pending';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;

    // Get applications with pagination
    const [applications, total] = await Promise.all([
      prisma.captainApplication.findMany({
        where: { status },
        include: {
          user: {
            select: {
              id: true,
              username: true,
              displayName: true,
              avatar: true,
              email: true,
            },
          },
          reviewedBy: {
            select: {
              id: true,
              username: true,
              displayName: true,
            },
          },
        },
        orderBy: [
          { previouslyRejected: 'desc' }, // Previously rejected first
          { appliedAt: 'asc' }, // Oldest first
        ],
        skip,
        take: limit,
      }),
      prisma.captainApplication.count({
        where: { status },
      }),
    ]);

    // Get pending count for the dashboard
    const pendingCount = await prisma.captainApplication.count({
      where: { status: 'pending' },
    });

    const transformedApplications = applications.map(app => ({
      id: app.id,
      shipName: app.shipName,
      description: app.description,
      tags: app.tags as string[] || [],
      logoPath: app.logoPath,
      status: app.status,
      previouslyRejected: app.previouslyRejected,
      rejectionReason: app.rejectionReason,
      appliedAt: app.appliedAt.toISOString(),
      reviewedAt: app.reviewedAt?.toISOString(),
      user: app.user,
      reviewedBy: app.reviewedBy,
    }));

    return NextResponse.json({
      success: true,
      applications: transformedApplications,
      pagination: {
        page,
        limit,
        totalCount: total,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
      pendingCount,
    });
  } catch (error) {
    console.error("Error fetching captain applications:", error);
    return NextResponse.json(
      { error: "Failed to fetch applications" },
      { status: 500 }
    );
  }
}