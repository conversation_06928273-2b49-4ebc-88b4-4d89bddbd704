"use client";

import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useBankUser } from "../../hooks/useBank";
import { Toaster } from "react-hot-toast";

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export const DashboardLayout: React.FC<DashboardLayoutProps> = ({
  children,
}) => {
  const pathname = usePathname();
  const { data: user, isLoading, error } = useBankUser();

  const navItems = [
    { name: "Dashboard", path: "/bank/dashboard" },
    { name: "Transfer", path: "/bank/dashboard/transfer" },
    { name: "Deposit", path: "/bank/dashboard/deposit" },
    { name: "Withdraw", path: "/bank/dashboard/withdraw" },
    { name: "Pay-Code", path: "/bank/dashboard/pay-code" },
    { name: "Transactions", path: "/bank/dashboard/transactions" },
    { name: "Donate", path: "/bank/dashboard/donate" },
  ];

  // Function to check if a path is active
  const isActive = (path: string) => {
    if (path === "/bank/dashboard") {
      return pathname === "/bank/dashboard";
    }
    return pathname.startsWith(path);
  };

  // Format balance
  const formatBalance = (balance?: number) => {
    if (balance === undefined) return "Loading...";
    return `NS ${balance.toFixed(0)}`;
  };

  return (
    <div className="min-h-screen bg-secondary-dark">
      <Toaster
        position="top-right"
        toastOptions={{
          style: {
            background: "var(--color-secondary)",
            color: "var(--color-text-primary)",
            border: "1px solid var(--color-border-dark)",
            boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
          },
          success: {
            style: {
              background: "var(--color-success)",
              color: "white",
              fontWeight: "500",
            },
            icon: "✓",
          },
          error: {
            style: {
              background: "var(--color-error)",
              color: "white",
              fontWeight: "500",
            },
            icon: "✕",
          },
        }}
      />
      <div className="container mx-auto px-2 py-6">
        <div className="flex flex-col md:flex-row gap-3">
          {/* Sidebar Navigation - Hidden on mobile */}
          <aside className="hidden md:block md:w-40 flex-shrink-0">
            <div className="bg-secondary rounded-lg shadow-md p-4">
              <nav>
                <ul className="space-y-5">
                  {navItems.map((item) => (
                    <li key={item.path}>
                      <Link
                        href={item.path}
                        className={`
                          block px-4 py-2 rounded-md transition-colors
                          ${
                            isActive(item.path)
                              ? "border-2 border-primary font-medium"
                              : "border-2 border-gray-600 hover:bg-secondary-light"
                          }
                        `}
                      >
                        {item.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </nav>
            </div>

            {/* Balance Card */}
            <div className="bg-secondary rounded-lg shadow-md p-4 mt-4">
              <h2 className="text-lg font-semibold mb-2">Current Balance</h2>
              {isLoading ? (
                <div className="animate-pulse h-8 bg-gray-600 rounded w-3/4"></div>
              ) : error ? (
                <p className="text-error">Error loading balance</p>
              ) : (
                <p className="text-3xl font-bold text-success">
                  {formatBalance(user?.balance)}
                </p>
              )}
            </div>
          </aside>

          {/* Mobile Balance Card - Only visible on mobile */}
          <div className="md:hidden bg-secondary rounded-lg shadow-md p-4 mb-0">
            <h2 className="text-lg font-semibold mb-2">Current Balance</h2>
            {isLoading ? (
              <div className="animate-pulse h-8 bg-gray-600 rounded w-3/4"></div>
            ) : error ? (
              <p className="text-error">Error loading balance</p>
            ) : (
              <p className="text-3xl font-bold text-success">
                {formatBalance(user?.balance)}
              </p>
            )}
          </div>

          {/* Main Content */}
          <main className="flex-1">{children}</main>
        </div>
      </div>
    </div>
  );
};
