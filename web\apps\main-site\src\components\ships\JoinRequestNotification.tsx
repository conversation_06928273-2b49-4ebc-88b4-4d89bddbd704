"use client";

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { Button } from '@bank-of-styx/ui';

interface JoinRequest {
  id: string;
  type: string; // 'invite' | 'request'
  status: string;
  message?: string;
  createdAt: string;
  user: {
    id: string;
    username: string;
    displayName: string;
    avatar: string;
  };
  ship: {
    id: string;
    name: string;
    description: string;
    logo?: string;
    captain: {
      id: string;
      username: string;
      displayName: string;
      avatar: string;
    };
  };
  requestedBy: {
    id: string;
    username: string;
    displayName: string;
    avatar: string;
  };
}

interface JoinRequestNotificationProps {
  currentUserId: string;
  onRefresh?: () => void;
}

export default function JoinRequestNotification({ 
  currentUserId, 
  onRefresh 
}: JoinRequestNotificationProps) {
  const [joinRequests, setJoinRequests] = useState<JoinRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  const fetchJoinRequests = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/user/ship-invites', {
        credentials: 'include'
      });

      if (response.ok) {
        const data = await response.json();
        setJoinRequests(data.invites || []);
      } else {
        console.error('Error fetching join requests:', response.statusText);
      }
    } catch (error) {
      console.error('Error fetching join requests:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRespondToInvite = async (inviteId: string, response: 'accepted' | 'declined') => {
    setActionLoading(inviteId);
    try {
      const apiResponse = await fetch(`/api/user/ship-invites/${inviteId}/respond`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ response })
      });

      if (apiResponse.ok) {
        const data = await apiResponse.json();
        
        // Remove the processed invite from the list
        setJoinRequests(prev => prev.filter(req => req.id !== inviteId));
        
        // Call refresh callback if provided
        if (onRefresh) {
          onRefresh();
        }

        // Show success message (could be a toast in a real app)
        console.log(data.message);
      } else {
        const errorData = await apiResponse.json();
        console.error('Error responding to invite:', errorData.error);
      }
    } catch (error) {
      console.error('Error responding to invite:', error);
    } finally {
      setActionLoading(null);
    }
  };

  useEffect(() => {
    fetchJoinRequests();
  }, [currentUserId]);

  if (loading) {
    return (
      <div className="space-y-4">
        {[1, 2].map(i => (
          <div key={i} className="bg-white rounded-lg shadow p-4 animate-pulse">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (joinRequests.length === 0) {
    return null; // Don't show anything if no pending requests
  }

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900">
        Ship Invitations ({joinRequests.length})
      </h3>
      
      {joinRequests.map((request) => (
        <div
          key={request.id}
          className="bg-white rounded-lg shadow border-l-4 border-blue-500 p-4"
        >
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0">
                <Image
                  src={request.ship.logo || '/images/default-ship.png'}
                  alt={request.ship.name}
                  width={48}
                  height={48}
                  className="w-12 h-12 rounded-lg object-cover"
                />
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2">
                  <h4 className="text-lg font-medium text-gray-900 truncate">
                    {request.ship.name}
                  </h4>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    Invitation
                  </span>
                </div>
                
                <div className="mt-1 flex items-center space-x-2 text-sm text-gray-600">
                  <Image
                    src={request.requestedBy.avatar}
                    alt={request.requestedBy.displayName}
                    width={20}
                    height={20}
                    className="w-5 h-5 rounded-full object-cover"
                  />
                  <span>
                    Invited by <strong>{request.requestedBy.displayName}</strong>
                  </span>
                </div>
                
                {request.message && (
                  <p className="mt-2 text-sm text-gray-700 bg-gray-50 rounded p-2">
                    "{request.message}"
                  </p>
                )}
                
                <p className="mt-2 text-sm text-gray-600 line-clamp-2">
                  {request.ship.description}
                </p>
                
                <div className="mt-2 text-xs text-gray-500">
                  Received {new Date(request.createdAt).toLocaleDateString()} at{' '}
                  {new Date(request.createdAt).toLocaleTimeString()}
                </div>
              </div>
            </div>
            
            <div className="flex flex-col space-y-2 ml-4">
              <Button
                size="sm"
                onClick={() => handleRespondToInvite(request.id, 'accepted')}
                disabled={actionLoading === request.id}
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                {actionLoading === request.id ? 'Processing...' : 'Accept'}
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleRespondToInvite(request.id, 'declined')}
                disabled={actionLoading === request.id}
                className="text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                Decline
              </Button>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}