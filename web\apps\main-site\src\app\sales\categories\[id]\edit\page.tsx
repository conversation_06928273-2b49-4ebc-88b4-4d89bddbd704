"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { SalesDashboardLayout } from "@/components/sales/SalesDashboardLayout";
import { CategoryForm } from "@/components/sales/CategoryForm";
import { useSalesProductCategory } from "@/hooks/useProductCategories";
import { Spinner } from "@bank-of-styx/ui";

export default function EditCategoryPage({
  params,
}: {
  params: { id: string };
}) {
  const { id } = params;
  const { user, isLoading: authLoading } = useAuth();
  const [isAuthorized, setIsAuthorized] = useState(false);
  const router = useRouter();

  // Fetch category data
  const {
    data: categoryData,
    isLoading: categoryLoading,
    error,
  } = useSalesProductCategory(id);

  // Check if user is authorized to access this page
  useEffect(() => {
    if (!authLoading) {
      if (!user || !user.roles?.salesManager) {
        router.push("/");
      } else {
        setIsAuthorized(true);
      }
    }
  }, [user, authLoading, router]);

  if (authLoading || categoryLoading) {
    return (
      <SalesDashboardLayout>
        <div className="flex justify-center items-center min-h-[50vh]">
          <Spinner size="lg" />
        </div>
      </SalesDashboardLayout>
    );
  }

  if (!isAuthorized) {
    return null; // Don't render anything while redirecting
  }

  if (error || !categoryData) {
    return (
      <SalesDashboardLayout>
        <div className="p-8 text-center">
          <h2 className="text-xl font-bold mb-4">Category Not Found</h2>
          <p className="mb-4">
            The category you're trying to edit doesn't exist or has been
            removed.
          </p>
          <button
            onClick={() => router.push("/sales/categories")}
            className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark"
          >
            Back to Categories
          </button>
        </div>
      </SalesDashboardLayout>
    );
  }

  return (
    <SalesDashboardLayout>
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Edit Category</h1>
      </div>

      <CategoryForm initialData={categoryData} isEditing={true} />
    </SalesDashboardLayout>
  );
}
