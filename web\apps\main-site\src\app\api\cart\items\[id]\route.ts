import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth";
import { TicketStatus } from "@prisma/client";
import { 
  isProductEventCapacityBased, 
  isEventCapacityAvailable, 
  createEventCapacityHold,
  releaseEventCapacityHold
} from "@/lib/event-capacity-system";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
interface Params {
  params: {
    id: string;
  };
}

// PUT /api/cart/items/[id] - Update cart item
export async function PUT(req: NextRequest, { params }: Params) {
  try {
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    const { id } = params;
    const { quantity } = await req.json();

    if (!quantity || quantity < 1) {
      return NextResponse.json({ error: "Invalid quantity" }, { status: 400 });
    }

    return await prisma.$transaction(async (tx) => {
      // Verify the cart item belongs to the user and get related holds
      const cartItem = await tx.cartItem.findFirst({
        where: {
          id,
          cart: {
            userId: user.id,
          },
        },
        include: {
          product: true,
          ticketHold: {
            include: {
              tickets: true,
            },
          },
          eventCapacityHold: true,
        },
      });

      if (!cartItem) {
        return NextResponse.json(
          { error: "Cart item not found" },
          { status: 404 },
        );
      }

      // Check if this product uses event capacity system
      const eventCapacityInfo = await isProductEventCapacityBased(cartItem.productId, tx);
      
      // Calculate difference in quantity
      const quantityDifference = quantity - cartItem.quantity;

      if (eventCapacityInfo.isEventBased && eventCapacityInfo.eventId) {
        // Handle event capacity system
        if (quantityDifference > 0) {
          // Increasing quantity - check event capacity
          const hasCapacity = await isEventCapacityAvailable(
            eventCapacityInfo.eventId, 
            quantityDifference, 
            tx
          );
          
          if (!hasCapacity) {
            return NextResponse.json(
              { error: "Not enough event capacity available" },
              { status: 400 },
            );
          }

          // If there's an existing event capacity hold, we need to release it and create a new one
          // with the updated quantity (since we can't directly update the quantity)
          if (cartItem.eventCapacityHold) {
            await tx.eventCapacityHold.delete({
              where: { id: cartItem.eventCapacityHold.id }
            });
          }

          // Create new hold with updated quantity
          await createEventCapacityHold(
            user.id,
            eventCapacityInfo.eventId,
            cartItem.id,
            quantity, // Total quantity, not just the difference
            tx
          );
        } else if (quantityDifference < 0) {
          // Decreasing quantity - update the hold
          if (cartItem.eventCapacityHold) {
            await tx.eventCapacityHold.delete({
              where: { id: cartItem.eventCapacityHold.id }
            });
            
            // Create new hold with reduced quantity
            if (quantity > 0) {
              await createEventCapacityHold(
                user.id,
                eventCapacityInfo.eventId,
                cartItem.id,
                quantity,
                tx
              );
            }
          }
        }
        // If quantityDifference === 0, no change needed
      } else {
        // Handle individual ticket system (existing logic)
        if (quantityDifference > 0) {
          const additionalTicketsNeeded = quantityDifference;

          // Find additional available tickets
          const additionalAvailableTickets = await tx.ticket.findMany({
            where: {
              productId: cartItem.productId,
              status: TicketStatus.AVAILABLE,
            },
            take: additionalTicketsNeeded,
          });

          if (additionalAvailableTickets.length < additionalTicketsNeeded) {
            return NextResponse.json(
              { error: "Not enough tickets available" },
              { status: 400 },
            );
          }

          // Hold the additional tickets
          if (additionalAvailableTickets.length > 0) {
            let holdId = cartItem.ticketHold?.id;

            if (!holdId) {
              const newHold = await tx.ticketHold.create({
                data: {
                  userId: user.id,
                  cartItemId: cartItem.id,
                  expiresAt: new Date(Date.now() + 15 * 60 * 1000),
                },
              });
              holdId = newHold.id;
            } else {
              await tx.ticketHold.update({
                where: { id: holdId },
                data: {
                  expiresAt: new Date(Date.now() + 15 * 60 * 1000),
                },
              });
            }

            await tx.ticket.updateMany({
              where: {
                id: {
                  in: additionalAvailableTickets.map((t) => t.id),
                },
              },
              data: {
                status: TicketStatus.HELD,
                holdId,
              },
            });
          }
        } else if (quantityDifference < 0) {
          const ticketsToRelease = Math.abs(quantityDifference);

          if (cartItem.ticketHold && cartItem.ticketHold.tickets.length > 0) {
            const heldTickets = cartItem.ticketHold.tickets
              .filter((t) => t.status === TicketStatus.HELD)
              .sort(
                (a, b) =>
                  new Date(b.updatedAt).getTime() -
                  new Date(a.updatedAt).getTime(),
              )
              .slice(0, ticketsToRelease);

            if (heldTickets.length > 0) {
              await tx.ticket.updateMany({
                where: {
                  id: {
                    in: heldTickets.map((t) => t.id),
                  },
                },
                data: {
                  status: TicketStatus.AVAILABLE,
                  holdId: null,
                },
              });
            }
          }
        }
      }

      // Update the cart item quantity
      const updatedItem = await tx.cartItem.update({
        where: { id },
        data: {
          quantity,
          updatedAt: new Date(),
        },
        include: {
          product: true,
          ticketHold: {
            include: {
              tickets: {
                where: {
                  status: TicketStatus.HELD,
                },
              },
            },
          },
          eventCapacityHold: true,
        },
      });

      return NextResponse.json({ 
        item: updatedItem,
        holdType: eventCapacityInfo.isEventBased ? "event" : "individual"
      });
    });
  } catch (error) {
    console.error("Error updating cart item:", error);
    return NextResponse.json(
      { error: "Failed to update cart item" },
      { status: 500 },
    );
  }
}

// DELETE /api/cart/items/[id] - Remove item from cart
export async function DELETE(req: NextRequest, { params }: Params) {
  try {
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    const itemId = params.id;

    return await prisma.$transaction(async (tx) => {
      // Verify the cart item belongs to the user and get related holds
      const cartItem = await tx.cartItem.findFirst({
        where: {
          id: itemId,
          cart: {
            userId: user.id,
          },
        },
        include: {
          ticketHold: true,
          eventCapacityHold: true,
        },
      });

      if (!cartItem) {
        return NextResponse.json(
          { error: "Cart item not found" },
          { status: 404 },
        );
      }

      let releasedTickets = 0;
      let releasedCapacity = 0;

      // Release individual ticket holds
      if (cartItem.ticketHold) {
        const heldTickets = await tx.ticket.findMany({
          where: {
            holdId: cartItem.ticketHold.id,
            status: TicketStatus.HELD,
          },
        });

        releasedTickets = heldTickets.length;

        if (releasedTickets > 0) {
          await tx.ticket.updateMany({
            where: {
              holdId: cartItem.ticketHold.id,
              status: TicketStatus.HELD,
            },
            data: {
              status: TicketStatus.AVAILABLE,
              holdId: null,
            },
          });
        }
      }

      // Release event capacity holds
      if (cartItem.eventCapacityHold) {
        releasedCapacity = cartItem.eventCapacityHold.quantity;
        await tx.eventCapacityHold.delete({
          where: { id: cartItem.eventCapacityHold.id }
        });
      }

      // Delete the cart item
      await tx.cartItem.delete({
        where: { id: itemId },
      });

      return NextResponse.json({ 
        success: true, 
        releasedTickets,
        releasedCapacity 
      });
    });
  } catch (error) {
    console.error("Error removing cart item:", error);
    return NextResponse.json(
      { error: "Failed to remove cart item" },
      { status: 500 },
    );
  }
}
