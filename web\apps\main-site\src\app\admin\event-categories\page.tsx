"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useColorTheme } from "@/contexts/ColorThemeContext";
import { useAuth } from "@/contexts/AuthContext";
import fetchClient from "@/lib/fetchClient";
import { AdminDashboardLayout } from "@/components/admin";

// Define types
interface Category {
  id: string;
  name: string;
  description: string | null;
  color: string | null;
  createdAt: string;
  updatedAt: string;
}

export default function AdminEventCategoriesPage() {
  const router = useRouter();
  const { isDarkMode } = useColorTheme();
  const { user, isAuthenticated } = useAuth();
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Form state for new/edit category
  const [formData, setFormData] = useState({
    id: "",
    name: "",
    description: "",
    color: "#3B82F6", // Default blue color
  });

  const [isEditing, setIsEditing] = useState(false);
  const [showForm, setShowForm] = useState(false);

  // Fetch categories
  const fetchCategories = async () => {
    setLoading(true);
    try {
      const data = await fetchClient.get<Category[]>(
        "/api/admin/event-categories",
      );
      setCategories(data);
      setError(null);
    } catch (err) {
      console.error("Error loading categories:", err);
      setError("Error loading categories. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Handle form input changes
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      id: "",
      name: "",
      description: "",
      color: "#3B82F6",
    });
    setIsEditing(false);
  };

  // Open form for creating a new category
  const handleNewCategory = () => {
    resetForm();
    setShowForm(true);
  };

  // Open form for editing a category
  const handleEditCategory = (category: Category) => {
    setFormData({
      id: category.id,
      name: category.name,
      description: category.description || "",
      color: category.color || "#3B82F6",
    });
    setIsEditing(true);
    setShowForm(true);
  };

  // Handle form submission for creating/updating a category
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      const categoryData = {
        name: formData.name,
        description: formData.description || null,
        color: formData.color,
      };

      if (isEditing) {
        // Update existing category
        await fetchClient.put(
          `/api/admin/event-categories/${formData.id}`,
          categoryData,
        );
        setSuccess("Category updated successfully!");
      } else {
        // Create new category
        await fetchClient.post("/api/admin/event-categories", categoryData);
        setSuccess("Category created successfully!");
      }

      // Refresh categories and reset form
      fetchCategories();
      resetForm();
      setShowForm(false);

      // Clear success message after a delay
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err: any) {
      console.error("Error saving category:", err);
      setError(err.message || "Failed to save category. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Handle category deletion
  const handleDeleteCategory = async (id: string) => {
    if (
      !confirm(
        "Are you sure you want to delete this category? This action cannot be undone.",
      )
    ) {
      return;
    }

    setLoading(true);
    try {
      await fetchClient.delete(`/api/admin/event-categories/${id}`);

      setSuccess("Category deleted successfully!");
      fetchCategories();

      // Clear success message after a delay
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err: any) {
      console.error("Error deleting category:", err);
      setError(err.message || "Failed to delete category. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Cancel form
  const handleCancelForm = () => {
    resetForm();
    setShowForm(false);
  };

  // Initialize
  useEffect(() => {
    fetchCategories();
  }, []);

  return (
    <AdminDashboardLayout>
      <div className="px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Event Categories</h1>
        <div className="flex space-x-4">
          <button
            onClick={handleNewCategory}
            className="px-4 py-2 rounded"
            style={{
              backgroundColor: "var(--color-primary)",
              color: "var(--color-text-primary)",
            }}
          >
            Add New Category
          </button>
          <Link
            href="/admin/events"
            className="px-4 py-2 rounded"
            style={{
              backgroundColor: "var(--color-secondary)",
              color: "var(--color-text-primary)",
            }}
          >
            Back to Events
          </Link>
        </div>
      </div>

      {/* Error message */}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {/* Success message */}
      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          {success}
        </div>
      )}

      {/* Category form */}
      {showForm && (
        <div
          className="rounded-lg p-6 mb-8"
          style={{
            backgroundColor: "var(--color-bg-card)",
            boxShadow: "0 1px 3px rgba(0,0,0,0.12)",
          }}
        >
          <h2 className="text-xl font-semibold mb-4">
            {isEditing ? "Edit Category" : "Create New Category"}
          </h2>
          <form onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Category name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Category Name *
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                  className="w-full border border-gray-300 rounded px-3 py-2"
                  placeholder="Enter category name"
                />
              </div>

              {/* Category color */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Color
                </label>
                <div className="flex items-center">
                  <input
                    type="color"
                    name="color"
                    value={formData.color}
                    onChange={handleInputChange}
                    className="h-10 w-10 border border-gray-300 rounded mr-2"
                  />
                  <input
                    type="text"
                    name="color"
                    value={formData.color}
                    onChange={handleInputChange}
                    className="w-full border border-gray-300 rounded px-3 py-2"
                    placeholder="#RRGGBB"
                    pattern="^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$"
                  />
                </div>
              </div>

              {/* Category description */}
              <div className="col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full border border-gray-300 rounded px-3 py-2"
                  placeholder="Category description (optional)"
                />
              </div>
            </div>

            {/* Form buttons */}
            <div className="mt-6 flex space-x-4">
              <button
                type="submit"
                disabled={loading}
                className="px-4 py-2 rounded font-medium"
                style={{
                  backgroundColor: loading
                    ? "var(--color-primary-light)"
                    : "var(--color-primary)",
                  color: "var(--color-text-primary)",
                  cursor: loading ? "not-allowed" : "pointer",
                }}
              >
                {loading
                  ? isEditing
                    ? "Updating..."
                    : "Creating..."
                  : isEditing
                  ? "Update Category"
                  : "Create Category"}
              </button>
              <button
                type="button"
                onClick={handleCancelForm}
                className="px-4 py-2 rounded"
                style={{
                  backgroundColor: "var(--color-secondary)",
                  color: "var(--color-text-primary)",
                }}
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Categories list */}
      <div
        className="rounded-lg overflow-hidden"
        style={{
          backgroundColor: "var(--color-bg-card)",
          boxShadow: "0 1px 3px rgba(0,0,0,0.12)",
        }}
      >
        <table className="min-w-full divide-y divide-gray-200">
          <thead
            style={{
              backgroundColor: "var(--color-secondary)",
              color: "var(--color-text-primary)",
            }}
          >
            <tr>
              <th
                className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider"
                style={{ color: "var(--color-text-muted)" }}
              >
                Name
              </th>
              <th
                className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider"
                style={{ color: "var(--color-text-muted)" }}
              >
                Color
              </th>
              <th
                className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider"
                style={{ color: "var(--color-text-muted)" }}
              >
                Description
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody
            style={{
              backgroundColor: "var(--color-bg-card)",
              color: "var(--color-text-primary)",
            }}
            className="divide-y divide-gray-200"
          >
            {loading && categories.length === 0 ? (
              <tr>
                <td colSpan={4} className="px-6 py-4 text-center">
                  Loading categories...
                </td>
              </tr>
            ) : categories.length === 0 ? (
              <tr>
                <td colSpan={4} className="px-6 py-4 text-center">
                  No categories found. Create your first category!
                </td>
              </tr>
            ) : (
              categories.map((category) => (
                <tr key={category.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div
                      className="text-sm font-medium"
                      style={{ color: "var(--color-text-primary)" }}
                    >
                      {category.name}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div
                        className="h-6 w-6 rounded-full mr-2"
                        style={{ backgroundColor: category.color || "#e5e7eb" }}
                      ></div>
                      <span
                        className="text-sm"
                        style={{ color: "var(--color-text-muted)" }}
                      >
                        {category.color || "None"}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div
                      className="text-sm truncate max-w-xs"
                      style={{ color: "var(--color-text-muted)" }}
                    >
                      {category.description || "No description"}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button
                      onClick={() => handleEditCategory(category)}
                      className="mr-4"
                      style={{ color: "var(--color-primary)" }}
                    >
                      Edit
                    </button>
                    <button
                      onClick={() => handleDeleteCategory(category.id)}
                      style={{ color: "var(--color-error)" }}
                    >
                      Delete
                    </button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
      </div>
    </AdminDashboardLayout>
  );
}
