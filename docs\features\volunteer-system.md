# Volunteer System

## Overview
The Volunteer System is a comprehensive event-based volunteer management platform that handles shift scheduling, time tracking, check-ins, payments, and ship-based hour credit tracking. It provides a multi-tiered approach with public volunteer interfaces, lead manager dashboards, coordinator tools, and administrative oversight, all integrated with the banking and ship management systems.

## Directory Structure
```
volunteer-system/
├── src/app/volunteer/                 # Public volunteer interface
│   ├── page.tsx                       # Volunteer landing page
│   ├── layout.tsx                     # Shared volunteer layout
│   └── dashboard/                     # User volunteer dashboard
│       └── page.tsx                   # Personal shift management
├── src/app/volunteer/lead/            # Lead Manager interface
│   └── dashboard/                     # Lead manager operations
│       └── page.tsx                   # Category-specific shift management
├── src/app/api/volunteer/             # Volunteer API endpoints
│   ├── public/                        # Public volunteer operations
│   │   ├── events/                    # Event browsing
│   │   ├── categories/                # Category and shift access
│   │   ├── shifts/                    # Shift signup and check-in
│   │   └── user/                      # User's volunteer data
│   ├── lead/                          # Lead manager operations
│   │   ├── shifts/                    # Shift attendance management
│   │   └── payments/                  # Payment initiation
│   ├── management/                    # Coordinator operations
│   │   └── payments/                  # Bulk payment processing
│   └── events/                        # Event and category administration
├── src/components/volunteer/          # Volunteer UI components
│   ├── public/                        # Public volunteer components
│   │   ├── VolunteerSignupModal.tsx   # Comprehensive signup form
│   │   ├── ShipSearch.tsx             # Ship search with auto-complete
│   │   ├── UserVolunteerQuickActions.tsx # Check-in and shift management
│   │   ├── VolunteerEventCard.tsx     # Event browsing interface
│   │   └── VolunteerShiftCard.tsx     # Shift display and actions
│   └── lead/                          # Lead manager components
│       ├── LeadDashboardMain.tsx      # Main dashboard with date navigation
│       ├── LeadDashboardStats.tsx     # Statistics and metrics
│       └── ShiftCard.tsx              # Detailed shift management
├── src/services/                      # Volunteer business logic
│   ├── volunteerService.ts            # Core volunteer operations
│   ├── volunteerCheckinService.ts     # Check-in system logic
│   └── volunteerPaymentService.ts     # Payment processing
├── src/hooks/                         # Volunteer React hooks
│   ├── usePublicVolunteer.ts          # Public volunteer operations
│   ├── useVolunteerShifts.ts          # Shift management
│   ├── useVolunteerPayments.ts        # Payment operations
│   └── useVolunteerCategories.ts      # Category management
└── src/types/volunteer.ts             # Comprehensive TypeScript definitions
```

## Key Files & Components

### Frontend Components
- **`VolunteerSignupModal.tsx`** - Comprehensive signup form with ship integration, theme fields, and accessibility options
- **`ShipSearch.tsx`** - Real-time ship search with auto-complete, captain details, and "None/Open Camping" option
- **`UserVolunteerQuickActions.tsx`** - Check-in buttons with time-based availability and real-time status updates
- **`VolunteerEventCard.tsx`** - Event browsing interface with volunteer opportunity summaries
- **`VolunteerShiftCard.tsx`** - Individual shift display with capacity, requirements, and signup actions
- **`LeadDashboardMain.tsx`** - Lead manager interface with date navigation, shift filtering, and attendance management
- **`LeadDashboardStats.tsx`** - Statistics dashboard with volunteer metrics and category performance
- **`ShiftCard.tsx`** - Detailed shift management with volunteer status controls and bulk operations

### API Endpoints
- **`GET /api/volunteer/public/events`** - Available events with volunteer opportunities and capacity information
- **`GET /api/volunteer/public/events/[id]/categories`** - Categories for specific events with shift counts
- **`GET /api/volunteer/public/categories/[id]/shifts`** - Available shifts with vacancy counts and requirements
- **`POST /api/volunteer/public/shifts/[id]/signup`** - Comprehensive volunteer signup with metadata and ship tracking
- **`POST /api/volunteer/public/shifts/[id]/checkin`** - Time-based check-in system with 15-minute windows
- **`GET /api/volunteer/public/shifts/[id]/checkin`** - Check-in status and availability window information
- **`GET /api/volunteer/public/user/shifts`** - User's upcoming and completed volunteer assignments
- **`GET /api/volunteer/lead/shifts`** - Date-filtered shifts for lead manager's assigned category
- **`PATCH /api/volunteer/lead/shifts/[id]/attendance`** - Update volunteer attendance status with ship credit tracking
- **`POST /api/volunteer/lead/payments/initiate`** - Process completed shifts for payment with banking integration
- **`GET /api/volunteer/management/payments`** - Pending volunteer payments with comprehensive filtering
- **`POST /api/volunteer/management/payments/process`** - Bulk payment processing with transaction creation
- **`GET /api/volunteer/management/payments/history`** - Payment history with date range and status filtering
- **`POST /api/volunteer/events/[id]/categories/[categoryId]/shifts`** - Batch shift creation for events

### Database Models
- **`VolunteerCategory`** - Volunteer work organization with pay rates, lead manager assignments, and event relationships
- **`VolunteerShift`** - Specific time slots with capacity management, requirements, and category relationships
- **`VolunteerAssignment`** - User-shift relationships with comprehensive metadata and status tracking
- **`VolunteerHours`** - Completed work tracking with payment information and ship credit assignment
- **`VolunteerSlot`** - Individual positions within shifts with hold system integration
- **`ShipVolunteerRequirement`** - Ship-based volunteer hour requirements with progress tracking

### Services
- **`volunteerService.ts`** - Core volunteer operations including signup, shift management, and data retrieval
- **`volunteerCheckinService.ts`** - Time-based check-in system with window calculations and status management
- **`volunteerPaymentService.ts`** - Payment processing with rate calculations and banking system integration

### Hooks
- **`usePublicVolunteer()`** - Public volunteer operations including event browsing, signup, and check-in functionality
- **`useVolunteerShifts()`** - Shift CRUD operations with optimistic updates and error handling
- **`useVolunteerPayments()`** - Payment processing and history with TanStack Query integration
- **`useShipSearch(query)`** - Debounced ship search with auto-complete and captain information

## Common Tasks

### Task 1: How to Sign Up for a Volunteer Shift
1. User browses available events at `/volunteer` page
2. Selects event to view volunteer categories and opportunities
3. Clicks on shift card to view details and requirements
4. Clicks "Sign Up" button to open comprehensive signup modal
5. Fills out form including:
   - Personal information (first name, last name, email, pirate name)
   - Accommodation details (staying with, ship association)
   - Classifications (dock access, land grant work)
   - Accessibility options (pronouns, special participation)
   - Notification preferences
6. Ship search auto-populates with user's current ship or allows manual selection
7. System creates VolunteerAssignment with pending status
8. Metadata stored for comprehensive record keeping
9. User receives confirmation and shift appears in personal dashboard
10. Check-in becomes available 15 minutes before shift start time

### Task 2: How to Check In for a Volunteer Shift
1. User navigates to volunteer dashboard or shift-specific page
2. System displays check-in button 15 minutes before shift start
3. Real-time countdown shows time until check-in window opens
4. User clicks "Check In" button when available
5. System validates:
   - Current time is within check-in window (15 min before to shift start)
   - User assignment status is pending/assigned/confirmed
   - User hasn't already checked in
6. Check-in updates assignment status to "checked_in"
7. Real-time notification sent to lead manager
8. User interface updates to show checked-in status
9. Check-in window expires when shift begins
10. Late check-ins require lead manager manual override

### Task 3: How to Manage Volunteer Attendance (Lead Manager)
1. Lead manager accesses `/volunteer/lead/dashboard` 
2. Views shifts filtered by assigned category and selected date
3. Sees real-time volunteer status for each shift (pending, checked-in, completed, etc.)
4. Updates attendance status via ShiftCard interface:
   - Mark as "completed" for volunteers who finished full shift
   - Mark as "abandoned" for early departures
   - Mark as "no-show" for non-attendees
   - Reverse status changes if corrections needed
5. For completed volunteers:
   - System calculates hours based on shift duration
   - Pay amount calculated using category pay rate
   - Ship credit assigned based on volunteer's ship association
6. Initiates payment processing via "Process Payments" button
7. System creates VolunteerHours records with banking integration
8. Payments appear in coordinator queue for final processing
9. Real-time updates notify volunteers of status changes

## API Integration

### Authentication Requirements
- **Public browsing**: Event and shift listing requires no authentication
- **Signup/Check-in**: Valid JWT token required for volunteer operations
- **Lead Manager**: JWT token + `isLeadManager` role + category permission match
- **Coordinator**: JWT token + `isVolunteerCoordinator` role for management operations
- **Land Steward**: JWT token + `isLandSteward` role for ship requirement monitoring
- **Token format**: `Bearer <jwt-token>` in Authorization header

### Request/Response Examples
```typescript
// Volunteer Signup Request
interface VolunteerSignupRequest {
  firstName: string;
  lastName: string;
  email: string;
  pirateName: string;              // Theme-specific identity
  stayingWith: string;             // Accommodation information
  stayingWithShipId?: string;      // Ship association for credit tracking
  isDock: boolean;                 // Dock access classification
  isLandGrant: boolean;            // Land grant work classification
  landGrantCreditShipId?: string;  // Override ship for land grant credit
  pronouns: string;                // Inclusivity feature
  addToDeedsLottery: boolean;      // Special event participation
  emailNotification: boolean;      // Communication preferences
  websiteNotification: boolean;
}

// Volunteer Assignment Response
interface VolunteerAssignmentResponse {
  id: string;
  status: 'pending' | 'assigned' | 'confirmed' | 'checked_in' | 'completed' | 'abandoned' | 'no_show' | 'cancelled';
  shift: {
    id: string;
    name: string;
    startTime: string;
    endTime: string;
    category: {
      id: string;
      name: string;
      payRate: number;
    };
    event: {
      id: string;
      name: string;
      startDate: string;
      endDate: string;
    };
  };
  user: {
    id: string;
    username: string;
    displayName: string;
  };
  metadata: {
    firstName: string;
    lastName: string;
    pirateName: string;
    stayingWith: string;
    // ... other signup form data
  };
  checkInWindow?: {
    opensAt: string;
    closesAt: string;
    isOpen: boolean;
  };
  createdAt: string;
  updatedAt: string;
}

// Check-In Status Response
interface CheckInStatusResponse {
  canCheckIn: boolean;
  isCheckedIn: boolean;
  timeUntilCheckIn?: number;       // minutes until window opens
  timeUntilExpiry?: number;        // minutes until window closes
  status: 'not_ready' | 'available' | 'expired' | 'checked_in';
  shift: {
    id: string;
    name: string;
    startTime: string;
    endTime: string;
  };
}

// Payment Processing Request
interface PaymentProcessingRequest {
  assignments: Array<{
    id: string;
    status: 'completed' | 'abandoned' | 'no_show';
    payMultiplier?: number;          // 1x-4x for special circumstances
    notes?: string;
  }>;
}

// Ship Search Response
interface ShipSearchResponse {
  ships: Array<{
    id: string;
    name: string;
    memberCount: number;
    captain: {
      id: string;
      username: string;
      displayName: string;
    };
    isRecruiting: boolean;
  }>;
  hasMore: boolean;
}
```

## Database Schema

### Primary Models
```sql
-- VolunteerCategory table (volunteer work organization)
CREATE TABLE VolunteerCategory (
  id VARCHAR(191) PRIMARY KEY,
  eventId VARCHAR(191) NOT NULL,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  payRate DECIMAL(10,2) NOT NULL,
  leadManagerId VARCHAR(191), -- User with leadManager role
  requiresTraining BOOLEAN DEFAULT false,
  isActive BOOLEAN DEFAULT true,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (eventId) REFERENCES Event(id),
  FOREIGN KEY (leadManagerId) REFERENCES User(id),
  INDEX idx_category_event (eventId),
  INDEX idx_category_lead (leadManagerId)
);

-- VolunteerShift table (specific time slots)
CREATE TABLE VolunteerShift (
  id VARCHAR(191) PRIMARY KEY,
  categoryId VARCHAR(191) NOT NULL,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  startTime DATETIME NOT NULL,
  endTime DATETIME NOT NULL,
  capacity INT NOT NULL,
  filledSlots INT DEFAULT 0,
  requirements TEXT,
  location VARCHAR(255),
  isActive BOOLEAN DEFAULT true,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (categoryId) REFERENCES VolunteerCategory(id),
  INDEX idx_shift_category (categoryId),
  INDEX idx_shift_time (startTime, endTime),
  INDEX idx_shift_capacity (capacity, filledSlots)
);

-- VolunteerAssignment table (user-shift relationships)
CREATE TABLE VolunteerAssignment (
  id VARCHAR(191) PRIMARY KEY,
  userId VARCHAR(191) NOT NULL,
  shiftId VARCHAR(191) NOT NULL,
  status ENUM('pending', 'assigned', 'confirmed', 'checked_in', 'completed', 'abandoned', 'no_show', 'cancelled') DEFAULT 'pending',
  metadata JSON, -- Comprehensive form data storage
  checkedInAt DATETIME,
  completedAt DATETIME,
  assignedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  notes TEXT,
  FOREIGN KEY (userId) REFERENCES User(id),
  FOREIGN KEY (shiftId) REFERENCES VolunteerShift(id),
  UNIQUE KEY unique_user_shift (userId, shiftId),
  INDEX idx_assignment_user (userId),
  INDEX idx_assignment_shift (shiftId),
  INDEX idx_assignment_status (status)
);

-- VolunteerHours table (completed work and payment tracking)
CREATE TABLE VolunteerHours (
  id VARCHAR(191) PRIMARY KEY,
  userId VARCHAR(191) NOT NULL,
  assignmentId VARCHAR(191) NOT NULL,
  categoryId VARCHAR(191) NOT NULL,
  shiftId VARCHAR(191) NOT NULL,
  hoursWorked DECIMAL(4,2) NOT NULL,
  payRate DECIMAL(10,2) NOT NULL,
  payMultiplier DECIMAL(3,2) DEFAULT 1.00,
  totalPay DECIMAL(10,2) NOT NULL,
  paymentStatus ENUM('pending', 'processing', 'paid', 'cancelled') DEFAULT 'pending',
  creditShipId VARCHAR(191), -- Ship receiving credit for these hours
  isDockHours BOOLEAN DEFAULT false,
  isLandGrant BOOLEAN DEFAULT false,
  processedAt DATETIME,
  processedBy VARCHAR(191), -- Lead manager who processed
  paidAt DATETIME,
  paidBy VARCHAR(191), -- Coordinator who paid
  transactionId VARCHAR(191), -- Banking system transaction
  notes TEXT,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (userId) REFERENCES User(id),
  FOREIGN KEY (assignmentId) REFERENCES VolunteerAssignment(id),
  FOREIGN KEY (categoryId) REFERENCES VolunteerCategory(id),
  FOREIGN KEY (shiftId) REFERENCES VolunteerShift(id),
  FOREIGN KEY (creditShipId) REFERENCES Ship(id),
  FOREIGN KEY (processedBy) REFERENCES User(id),
  FOREIGN KEY (paidBy) REFERENCES User(id),
  FOREIGN KEY (transactionId) REFERENCES Transaction(id),
  INDEX idx_hours_user (userId),
  INDEX idx_hours_ship (creditShipId),
  INDEX idx_hours_status (paymentStatus)
);

-- ShipVolunteerRequirement table (ship hour requirements)
CREATE TABLE ShipVolunteerRequirement (
  id VARCHAR(191) PRIMARY KEY,
  shipId VARCHAR(191) NOT NULL,
  requiredHours INT NOT NULL,
  currentHours INT DEFAULT 0,
  deadline DATETIME,
  status ENUM('pending', 'in_progress', 'completed', 'overdue') DEFAULT 'pending',
  description TEXT,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (shipId) REFERENCES Ship(id),
  INDEX idx_ship_req_ship (shipId),
  INDEX idx_ship_req_status (status),
  INDEX idx_ship_req_deadline (deadline)
);

-- VolunteerSlot table (individual positions with hold system)
CREATE TABLE VolunteerSlot (
  id VARCHAR(191) PRIMARY KEY,
  shiftId VARCHAR(191) NOT NULL,
  userId VARCHAR(191),
  status ENUM('available', 'held', 'filled') DEFAULT 'available',
  heldAt DATETIME,
  heldBy VARCHAR(191),
  assignedAt DATETIME,
  FOREIGN KEY (shiftId) REFERENCES VolunteerShift(id),
  FOREIGN KEY (userId) REFERENCES User(id),
  FOREIGN KEY (heldBy) REFERENCES User(id),
  INDEX idx_slot_shift (shiftId),
  INDEX idx_slot_status (status),
  INDEX idx_slot_hold (heldAt, status)
);
```

### Relationships
- **VolunteerCategory ↔ Event**: Many-to-one (categories belong to events)
- **VolunteerCategory ↔ User**: Many-to-one (lead manager relationship)
- **VolunteerShift ↔ VolunteerCategory**: Many-to-one (shifts belong to categories)
- **VolunteerAssignment ↔ User/Shift**: Many-to-one relationships with unique constraints
- **VolunteerHours ↔ Ship**: Many-to-one (hours credited to specific ships)
- **ShipVolunteerRequirement ↔ Ship**: One-to-many (ships can have multiple requirements)

## Related Features
- **[Authentication System](./authentication-system.md)** - Role-based access control for lead managers, coordinators, and Land Stewards
- **[Banking System](./banking-system.md)** - Payment processing with automatic transaction creation and balance updates
- **[Ship Management System](./ship-management-system.md)** - Ship credit system for volunteer hour requirements and progress tracking
- **[Notification System](./notification-system.md)** - Real-time updates for check-ins, status changes, and payment processing
- **[Event Management System](./event-management-system.md)** - Event-based volunteer opportunity creation and management

## User Roles & Permissions
- **Volunteers**: Event browsing, shift signup, check-in capabilities, personal dashboard access
- **Lead Managers**: Category-specific shift management, attendance tracking, payment initiation for assigned category
- **Volunteer Coordinators**: Full system management including event/category creation, bulk payment processing, system oversight
- **Land Stewards**: Ship volunteer requirement monitoring, progress tracking, compliance oversight
- **Admins**: Complete volunteer system administration including role assignments and system configuration

## Recent Changes
- **v5.1.0** - Volunteer Ship Tracking System (2025-01-07)
  - Enhanced volunteer signup form with ship search functionality
  - Implemented automatic ship hour tracking and credit assignment
  - Created Land Steward dashboard for monitoring ship volunteer requirements
  - Updated database schema with ship volunteer hour relationships
  - Built ship search component with auto-populate and dropdown selection
- **v5.0.0** - Enhanced Check-In System (July 2025)
  - Implemented 15-minute check-in window system
  - Added real-time status updates with 60-second refresh
  - Introduced "abandoned" status for early departures
  - Enhanced lead manager status reversal capabilities
- **v4.8.0** - Payment System Integration
  - Direct banking system integration for volunteer payments
  - Automated transaction creation and balance updates
  - Bulk payment processing with coordinator approval workflow
- **v4.7.0** - Hold System Integration
  - Integrated with ticket hold system to prevent volunteer slot overselling
  - 15-minute holds during signup process with automatic cleanup

## Troubleshooting

### Common Issues
1. **Check-in not available**: Verify current time is within 15-minute window before shift start. Check assignment status is pending/assigned/confirmed.
2. **Ship search not working**: Ensure minimum 2 characters entered, check network connectivity, verify ship database has active ships.
3. **Payment processing failures**: Check banking system integration, verify lead manager has processed shifts as completed, ensure coordinator permissions.
4. **Volunteer hour credit not appearing**: Verify ship assignment in signup form, check VolunteerHours record has creditShipId, ensure requirement tracking is active.
5. **Capacity issues**: Check shift capacity vs filled slots, verify hold system cleanup, ensure slot availability calculations are accurate.

### Debug Information
- **Log locations**: Volunteer operation logs in server console, payment processing logs in banking service
- **Environment variables**: Email service for notifications, banking integration settings
- **Database queries**: Use Prisma Studio to inspect volunteer assignments, hours, and ship requirements
- **Debugging commands**:
  ```bash
  # Check volunteer assignments
  curl -H "Authorization: Bearer <token>" http://localhost:3000/api/volunteer/public/user/shifts
  
  # Verify check-in status
  curl -H "Authorization: Bearer <token>" http://localhost:3000/api/volunteer/public/shifts/{id}/checkin
  
  # Check payment status
  curl -H "Authorization: Bearer <token>" http://localhost:3000/api/volunteer/management/payments
  ```

### Performance Considerations
- **Real-time Updates**: Check-in status refreshes every 60 seconds to prevent server overload
- **Search Optimization**: Ship search debounced at 300ms with minimum 2-character requirement
- **Database Indexing**: Comprehensive indexes on time-based queries and status fields
- **Pagination**: Large volunteer lists use cursor-based pagination for performance
- **Hold System**: 15-minute holds with automatic cleanup prevent resource locking

### Security Measures
- **Role-based Access**: Strict permission checking for lead manager category assignments
- **Time-based Validation**: Check-in windows prevent fraudulent early/late check-ins
- **Assignment Validation**: Prevents duplicate signups and capacity overflow
- **Payment Security**: Multi-tier approval process with audit trails
- **Data Privacy**: Metadata stored securely with access control based on user roles

### Integration Patterns
- **Banking Integration**: Seamless payment processing with transaction creation and balance updates
- **Ship Integration**: Automatic hour credit assignment with progress tracking and requirement monitoring
- **Notification Integration**: Real-time updates for all volunteer status changes and payment processing
- **Hold System Integration**: Prevents overselling while allowing time for signup completion

---

**File Locations:**
- Pages: `/src/app/volunteer/`
- Components: `/src/components/volunteer/`
- API: `/src/app/api/volunteer/`
- Services: `/src/services/volunteerService.ts`, `/src/services/volunteerCheckinService.ts`
- Hooks: `/src/hooks/usePublicVolunteer.ts`, `/src/hooks/useVolunteerShifts.ts`
- Types: `/src/types/volunteer.ts`