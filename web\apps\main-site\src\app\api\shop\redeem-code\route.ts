import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth";

export const dynamic = 'force-dynamic';

// POST /api/shop/redeem-code - Validate and apply a redemption code
export async function POST(req: NextRequest) {
  try {
    // Check if user is authenticated
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    // Get request body
    const { code } = await req.json();

    if (!code || typeof code !== 'string') {
      return NextResponse.json(
        { error: "Redemption code is required" },
        { status: 400 },
      );
    }

    // Normalize the code (uppercase, trim)
    const normalizedCode = code.trim().toUpperCase();

    // Find the redemption code
    const redemptionCode = await prisma.redemptionCode.findUnique({
      where: { code: normalizedCode },
      include: {
        product: {
          include: {
            category: true,
            event: true,
          },
        },
      },
    });

    if (!redemptionCode) {
      return NextResponse.json(
        { error: "Invalid redemption code" },
        { status: 404 },
      );
    }

    if (!redemptionCode.isActive) {
      return NextResponse.json(
        { error: "This redemption code is no longer active" },
        { status: 400 },
      );
    }

    const product = redemptionCode.product;

    // Check if product is active and free
    if (!product.isActive) {
      return NextResponse.json(
        { error: "This product is no longer available" },
        { status: 400 },
      );
    }

    if (!product.isFree) {
      return NextResponse.json(
        { error: "This redemption code is not valid for a free product" },
        { status: 400 },
      );
    }

    // Get or create user's cart
    let cart = await prisma.cart.findFirst({
      where: { userId: user.id },
      include: {
        items: {
          include: {
            product: true,
          },
        },
      },
    });

    if (!cart) {
      cart = await prisma.cart.create({
        data: { userId: user.id },
        include: {
          items: {
            include: {
              product: true,
            },
          },
        },
      });
    }

    // Check if this product is already in the cart
    const existingCartItem = cart.items.find(item => item.productId === product.id);
    if (existingCartItem) {
      return NextResponse.json(
        { error: "This product is already in your cart" },
        { status: 400 },
      );
    }

    // Check capacity constraints if product affects capacity
    if (product.affectsCapacity && product.eventId) {
      // For products that affect event capacity, we need to check and hold capacity
      const event = await prisma.event.findUnique({
        where: { id: product.eventId },
      });

      if (event && event.capacity) {
        // Get current capacity usage
        const currentCapacityHolds = await prisma.eventCapacityHold.aggregate({
          where: {
            eventId: product.eventId,
            expiresAt: {
              gt: new Date(),
            },
          },
          _sum: {
            quantity: true,
          },
        });

        const currentCapacityUsed = currentCapacityHolds._sum.quantity || 0;

        if (currentCapacityUsed >= event.capacity) {
          return NextResponse.json(
            { error: "Event is at full capacity" },
            { status: 400 },
          );
        }
      }
    }

    // Use transaction to add item to cart with proper holds
    const result = await prisma.$transaction(async (tx) => {
      // Create cart item with redemption tracking
      const cartItem = await tx.cartItem.create({
        data: {
          cartId: cart.id,
          productId: product.id,
          quantity: 1,
          isCodeRedemption: true,
          redemptionCodeId: redemptionCode.id,
        },
      });

      // Handle capacity holds if needed
      if (product.affectsCapacity && product.eventId) {
        // Create event capacity hold
        await tx.eventCapacityHold.create({
          data: {
            eventId: product.eventId,
            userId: user.id,
            cartItemId: cartItem.id,
            quantity: 1,
            expiresAt: new Date(Date.now() + 15 * 60 * 1000), // 15 minutes
          },
        });
      } else {
        // For products with individual tickets, create ticket hold
        if (product.inventory && product.inventory > 0) {
          // Find an available ticket
          const availableTicket = await tx.ticket.findFirst({
            where: {
              productId: product.id,
              status: 'AVAILABLE',
            },
          });

          if (availableTicket) {
            // Create ticket hold
            const ticketHold = await tx.ticketHold.create({
              data: {
                userId: user.id,
                cartItemId: cartItem.id,
                expiresAt: new Date(Date.now() + 15 * 60 * 1000), // 15 minutes
              },
            });

            // Update ticket status to HELD
            await tx.ticket.update({
              where: { id: availableTicket.id },
              data: {
                status: 'HELD',
                holdId: ticketHold.id,
              },
            });
          }
        }
      }

      // Deactivate the redemption code so it can't be used again
      await tx.redemptionCode.update({
        where: { id: redemptionCode.id },
        data: { isActive: false },
      });

      return cartItem;
    });

    return NextResponse.json({
      success: true,
      product: {
        id: product.id,
        name: product.name,
        price: 0, // Free product
      },
      message: `${product.name} has been added to your cart`,
    });

  } catch (error) {
    console.error("Error redeeming code:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}