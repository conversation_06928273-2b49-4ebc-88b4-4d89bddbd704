-- AlterTable
ALTER TABLE `event_forms` ADD COLUMN `requiredVolunteerHours` DOUBLE NULL;

-- CreateTable
CREATE TABLE `ship_volunteer_requirements` (
    `id` VARCHAR(191) NOT NULL,
    `shipId` VARCHAR(191) NOT NULL,
    `formSubmissionId` VARCHAR(191) NOT NULL,
    `requiredHours` DOUBLE NOT NULL,
    `completedHours` DOUBLE NOT NULL DEFAULT 0,
    `status` VARCHAR(191) NOT NULL DEFAULT 'pending',
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `ship_volunteer_requirements_formSubmissionId_key`(`formSubmissionId`),
    INDEX `ship_volunteer_requirements_shipId_idx`(`shipId`),
    INDEX `ship_volunteer_requirements_formSubmissionId_idx`(`formSubmissionId`),
    INDEX `ship_volunteer_requirements_status_idx`(`status`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `ship_volunteer_requirements` ADD CONSTRAINT `ship_volunteer_requirements_shipId_fkey` FOREIGN KEY (`shipId`) REFERENCES `ships`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ship_volunteer_requirements` ADD CONSTRAINT `ship_volunteer_requirements_formSubmissionId_fkey` FOREIGN KEY (`formSubmissionId`) REFERENCES `form_submissions`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
