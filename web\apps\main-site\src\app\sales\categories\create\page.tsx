"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { SalesDashboardLayout } from "@/components/sales/SalesDashboardLayout";
import { CategoryForm } from "@/components/sales/CategoryForm";

export default function CreateCategoryPage() {
  const { user, isLoading } = useAuth();
  const [isAuthorized, setIsAuthorized] = useState(false);
  const router = useRouter();

  // Check if user is authorized to access this page
  useEffect(() => {
    if (!isLoading) {
      if (!user || !user.roles?.salesManager) {
        router.push("/");
      } else {
        setIsAuthorized(true);
      }
    }
  }, [user, isLoading, router]);

  if (isLoading) {
    return <div className="p-8 text-center">Loading...</div>;
  }

  if (!isAuthorized) {
    return null; // Don't render anything while redirecting
  }

  return (
    <SalesDashboardLayout>
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Create New Category</h1>
      </div>

      <CategoryForm />
    </SalesDashboardLayout>
  );
}
