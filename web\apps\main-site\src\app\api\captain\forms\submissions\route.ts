import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth";
import prisma from "@/lib/prisma";

export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get the user's ship
    const shipMember = await prisma.shipMember.findFirst({
      where: {
        userId: user.id,
        status: "active",
      },
      include: {
        ship: {
          select: {
            id: true,
            name: true,
            captainId: true,
          },
        },
      },
    });

    if (!shipMember) {
      return NextResponse.json(
        { error: "You must be a member of a ship to view submissions" },
        { status: 403 }
      );
    }

    // Check if user is the captain
    const isCaptain = shipMember.ship.captainId === user.id;
    
    if (!isCaptain) {
      return NextResponse.json(
        { error: "Only ship captains can view form submissions" },
        { status: 403 }
      );
    }

    // Get form submissions for this ship
    const submissions = await prisma.formSubmission.findMany({
      where: {
        shipId: shipMember.ship.id,
      },
      include: {
        form: {
          select: {
            id: true,
            name: true,
            description: true,
            status: true,
            submissionDeadline: true,
            event: {
              select: {
                id: true,
                name: true,
                startDate: true,
                endDate: true,
              },
            },
          },
        },
        reviewedBy: {
          select: {
            id: true,
            username: true,
            displayName: true,
          },
        },
      },
      orderBy: {
        submittedAt: "desc",
      },
    });

    return NextResponse.json({
      ship: shipMember.ship,
      submissions,
    });
  } catch (error) {
    console.error("Error fetching form submissions:", error);
    return NextResponse.json(
      { error: "Failed to fetch form submissions" },
      { status: 500 }
    );
  }
}