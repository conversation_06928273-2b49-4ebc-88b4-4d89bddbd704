"use client";

import React, { useState } from "react";
import { Button, Input } from "@bank-of-styx/ui";
import Link from "next/link";
import { useAuth } from "@/contexts/AuthContext";
import { useRouter } from "next/navigation";
import { ShipLogoUploader } from "@/components/upload";
import { uploadShipLogo, validateShipLogo } from "@/lib/shipLogoUpload";

export default function CaptainApplicationPage() {
  const { user } = useAuth();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    shipName: "",
    description: "",
    tags: "",
    logoFile: null as File | null,
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Redirect if not authenticated
  if (!user) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-12">
            <h1 className="text-3xl font-bold text-white mb-4">
              Apply to be a Captain
            </h1>
            <p className="text-gray-400 mb-6">
              You need to be signed in to apply for captaincy.
            </p>
            <Link href="/auth/login">
              <Button variant="primary">Sign In</Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.shipName.trim()) {
      newErrors.shipName = "Ship name is required";
    } else if (formData.shipName.length < 3) {
      newErrors.shipName = "Ship name must be at least 3 characters";
    } else if (formData.shipName.length > 50) {
      newErrors.shipName = "Ship name must be less than 50 characters";
    }

    if (!formData.description.trim()) {
      newErrors.description = "Description is required";
    } else if (formData.description.length < 50) {
      newErrors.description = "Description must be at least 50 characters";
    } else if (formData.description.length > 1000) {
      newErrors.description = "Description must be less than 1000 characters";
    }

    if (formData.logoFile) {
      const logoValidationError = validateShipLogo(formData.logoFile);
      if (logoValidationError) {
        newErrors.logo = logoValidationError;
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      let logoPath = null;

      // Upload logo if provided using ship logo upload utility
      if (formData.logoFile) {
        const uploadResult = await uploadShipLogo(formData.logoFile);
        
        if (!uploadResult.success) {
          throw new Error(uploadResult.error || "Failed to upload logo");
        }
        
        logoPath = uploadResult.logoUrl;
      }

      // Parse tags
      const tags = formData.tags
        .split(",")
        .map((tag) => tag.trim())
        .filter((tag) => tag.length > 0);

      // Submit application
      const response = await fetch("/api/ships/apply", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
        },
        body: JSON.stringify({
          shipName: formData.shipName,
          description: formData.description,
          tags,
          logoPath,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to submit application");
      }

      // Success - redirect to ships page with success message
      alert("Captain application submitted successfully! Land Stewards will review your application.");
      router.push("/ships");
    } catch (error) {
      console.error("Error submitting application:", error);
      alert(error instanceof Error ? error.message : "Failed to submit application");
    } finally {
      setLoading(false);
    }
  };

  const handleLogoFileSelected = (file: File | null) => {
    setFormData({ ...formData, logoFile: file });
    // Clear any previous logo errors when a new file is selected
    if (errors.logo) {
      setErrors({ ...errors, logo: "" });
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <nav className="mb-6">
            <Link href="/ships" className="text-primary hover:text-primary-light">
              ← Back to Ships
            </Link>
          </nav>
          
          <h1 className="text-3xl font-bold text-white mb-2">
            Apply to be a Captain
          </h1>
          <p className="text-gray-400">
            Create your own ship and lead a crew in the Bank of Styx community.
          </p>
        </div>

        {/* Application Form */}
        <div className="max-w-2xl">
          <form onSubmit={handleSubmit} className="bg-secondary rounded-lg shadow-lg p-6">
            {/* Ship Name */}
            <div className="mb-6">
              <label htmlFor="shipName" className="block text-white font-medium mb-2">
                Ship Name *
              </label>
              <Input
                id="shipName"
                type="text"
                value={formData.shipName}
                onChange={(e) => setFormData({ ...formData, shipName: e.target.value })}
                placeholder="Enter your ship's name"
                className={errors.shipName ? "border-red-500" : ""}
                maxLength={50}
              />
              {errors.shipName && (
                <p className="text-red-400 text-sm mt-1">{errors.shipName}</p>
              )}
              <p className="text-gray-400 text-sm mt-1">
                Choose a unique name for your ship (3-50 characters)
              </p>
            </div>

            {/* Description */}
            <div className="mb-6">
              <label htmlFor="description" className="block text-white font-medium mb-2">
                Ship Description *
              </label>
              <textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Describe your ship's purpose, goals, and what makes it unique..."
                className={`w-full px-4 py-2 bg-secondary-dark border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary h-32 resize-vertical ${
                  errors.description ? "border-red-500" : ""
                }`}
                maxLength={1000}
              />
              {errors.description && (
                <p className="text-red-400 text-sm mt-1">{errors.description}</p>
              )}
              <p className="text-gray-400 text-sm mt-1">
                Tell us about your ship's mission and what makes it special (50-1000 characters).
                This will be shown to potential members.
              </p>
            </div>

            {/* Tags */}
            <div className="mb-6">
              <label htmlFor="tags" className="block text-white font-medium mb-2">
                Tags (Optional)
              </label>
              <Input
                id="tags"
                type="text"
                value={formData.tags}
                onChange={(e) => setFormData({ ...formData, tags: e.target.value })}
                placeholder="gaming, trading, exploration, social"
                className={errors.tags ? "border-red-500" : ""}
              />
              {errors.tags && (
                <p className="text-red-400 text-sm mt-1">{errors.tags}</p>
              )}
              <p className="text-gray-400 text-sm mt-1">
                Separate tags with commas. These help members find your ship.
              </p>
            </div>

            {/* Logo Upload */}
            <div className="mb-6">
              <label className="block text-white font-medium mb-2">
                Ship Logo (Optional)
              </label>
              <ShipLogoUploader
                onFileSelected={handleLogoFileSelected}
                selectedFile={formData.logoFile}
                error={errors.logo}
              />
            </div>

            {/* Guidelines */}
            <div className="mb-6">
              <div className="bg-secondary-dark rounded-lg p-4">
                <h3 className="text-white font-medium mb-2">Application Guidelines</h3>
                <ul className="text-gray-300 text-sm space-y-1">
                  <li>• Ship names must be unique and appropriate</li>
                  <li>• Applications are reviewed by Land Stewards</li>
                  <li>• You can only be a captain of one ship at a time</li>
                  <li>• Rejected applications can be resubmitted with improvements</li>
                  <li>• Once approved, you can start recruiting members</li>
                </ul>
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex gap-4">
              <Button
                type="submit"
                variant="primary"
                disabled={loading}
                className="flex-1"
              >
                {loading ? "Submitting..." : "Submit Application"}
              </Button>
              <Link href="/ships">
                <Button variant="outline" disabled={loading}>
                  Cancel
                </Button>
              </Link>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}