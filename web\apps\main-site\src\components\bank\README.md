# Banking Components

User-facing banking interface components for account management and transactions.

## Components

- **DashboardLayout.tsx** - Main layout component for the banking dashboard
- **AccountSummary.tsx** - Account balance and summary information display
- **QuickActions.tsx** - Quick action buttons for common banking operations
- **RecentTransactions.tsx** - List component for displaying recent transaction history
- **TransactionItem.tsx** - Individual transaction display component with details
- **index.ts** - Component exports for the banking module

These components create the user banking experience, providing account information, transaction history, and banking operations in an intuitive interface.
