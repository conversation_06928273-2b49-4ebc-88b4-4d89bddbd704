import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth";
import prisma from "@/lib/prisma";

export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get the user's ship
    const shipMember = await prisma.shipMember.findFirst({
      where: {
        userId: user.id,
        status: "active",
      },
      include: {
        ship: {
          select: {
            id: true,
            name: true,
            captainId: true,
          },
        },
      },
    });

    if (!shipMember) {
      return NextResponse.json(
        { error: "You must be a member of a ship to access forms" },
        { status: 403 }
      );
    }

    // Check if user is the captain
    const isCaptain = shipMember.ship.captainId === user.id;
    
    if (!isCaptain) {
      return NextResponse.json(
        { error: "Only ship captains can access forms" },
        { status: 403 }
      );
    }

    // Get available forms (active status and deadline not passed)
    const now = new Date();
    const availableForms = await prisma.eventForm.findMany({
      where: {
        status: "active",
        OR: [
          { submissionDeadline: null },
          { submissionDeadline: { gte: now } },
        ],
      },
      include: {
        event: {
          select: {
            id: true,
            name: true,
            startDate: true,
            endDate: true,
          },
        },
        submissions: {
          where: {
            shipId: shipMember.ship.id,
          },
          select: {
            id: true,
            status: true,
            submittedAt: true,
          },
        },
      },
      orderBy: [
        { submissionDeadline: "asc" },
        { createdAt: "desc" },
      ],
    });

    // Add submission status for each form
    const formsWithStatus = availableForms.map(form => ({
      ...form,
      hasSubmission: form.submissions.length > 0,
      submissionStatus: form.submissions[0]?.status || null,
      submittedAt: form.submissions[0]?.submittedAt || null,
    }));

    return NextResponse.json({
      ship: shipMember.ship,
      forms: formsWithStatus,
    });
  } catch (error) {
    console.error("Error fetching available forms:", error);
    return NextResponse.json(
      { error: "Failed to fetch available forms" },
      { status: 500 }
    );
  }
}