"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, Mo<PERSON> } from "@bank-of-styx/ui";
import { useAuth } from "@/contexts/AuthContext";
import Link from "next/link";

interface CaptainApplication {
  id: string;
  shipName: string;
  description: string;
  tags: string[];
  logoPath: string | null;
  status: 'pending' | 'approved' | 'rejected';
  appliedAt: string;
  reviewedAt: string | null;
  rejectionReason: string | null;
  previouslyRejected: boolean;
  user: {
    id: string;
    username: string;
    displayName: string;
    avatar: string | null;
    email: string;
  };
}

interface Pagination {
  page: number;
  limit: number;
  totalCount: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export default function LandStewardApplications() {
  const { user } = useAuth();
  const [applications, setApplications] = useState<CaptainApplication[]>([]);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [selectedApp, setSelectedApp] = useState<CaptainApplication | null>(null);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [showApproveModal, setShowApproveModal] = useState(false);
  const [rejectionReason, setRejectionReason] = useState("");
  const [shipSlogan, setShipSlogan] = useState("");
  const [activeTab, setActiveTab] = useState<'pending' | 'approved' | 'rejected'>('pending');
  const [pagination, setPagination] = useState<Pagination | null>(null);
  const [currentPage, setCurrentPage] = useState(1);

  const fetchApplications = async (status: string = 'pending', page: number = 1) => {
    try {
      setLoading(true);
      const response = await fetch(
        `/api/land-steward/applications?status=${status}&page=${page}&limit=10`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error("Failed to fetch applications");
      }

      const data = await response.json();
      setApplications(data.applications);
      setPagination(data.pagination);
    } catch (error) {
      console.error("Error fetching applications:", error);
      alert("Failed to load applications");
    } finally {
      setLoading(false);
    }
  };

  const handleApprove = async () => {
    if (!selectedApp) return;

    try {
      setActionLoading(selectedApp.id);
      const response = await fetch(
        `/api/land-steward/applications/${selectedApp.id}/approve`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
          },
          body: JSON.stringify({ slogan: shipSlogan }),
        }
      );

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to approve application");
      }

      alert("Application approved successfully! Ship has been created.");
      setShowApproveModal(false);
      setSelectedApp(null);
      setShipSlogan("");
      fetchApplications(activeTab, currentPage);
    } catch (error) {
      console.error("Error approving application:", error);
      alert(error instanceof Error ? error.message : "Failed to approve application");
    } finally {
      setActionLoading(null);
    }
  };

  const handleReject = async () => {
    if (!selectedApp || !rejectionReason.trim()) return;

    try {
      setActionLoading(selectedApp.id);
      const response = await fetch(
        `/api/land-steward/applications/${selectedApp.id}/reject`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
          },
          body: JSON.stringify({ reason: rejectionReason }),
        }
      );

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to reject application");
      }

      alert("Application rejected successfully.");
      setShowRejectModal(false);
      setSelectedApp(null);
      setRejectionReason("");
      fetchApplications(activeTab, currentPage);
    } catch (error) {
      console.error("Error rejecting application:", error);
      alert(error instanceof Error ? error.message : "Failed to reject application");
    } finally {
      setActionLoading(null);
    }
  };

  const handleTabChange = (tab: 'pending' | 'approved' | 'rejected') => {
    setActiveTab(tab);
    setCurrentPage(1);
    fetchApplications(tab, 1);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    fetchApplications(activeTab, page);
  };

  useEffect(() => {
    fetchApplications('pending', 1);
  }, []);

  // Check permissions after all hooks
  if (!user || (!user.roles?.admin && !user.roles?.landSteward)) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-12">
            <h1 className="text-3xl font-bold text-white mb-4">Access Denied</h1>
            <p className="text-gray-400 mb-6">
              You need Land Steward or Admin permissions to access this page.
            </p>
            <Link href="/ships">
              <Button variant="primary">Back to Ships</Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-3xl font-bold text-white mb-2">
                Captain Applications
              </h1>
              <p className="text-gray-400">
                Review and manage captain applications for the Ships system.
              </p>
            </div>
            <div className="flex gap-2">
              <Link href="/land-steward">
                <Button variant="outline">Back to Dashboard</Button>
              </Link>
              <Link href="/ships">
                <Button variant="outline">Back to Ships</Button>
              </Link>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="mb-6">
          <div className="flex space-x-1 bg-secondary rounded-lg p-1">
            {[
              { key: 'pending', label: 'Pending', count: activeTab === 'pending' ? pagination?.totalCount : 0 },
              { key: 'approved', label: 'Approved', count: activeTab === 'approved' ? pagination?.totalCount : 0 },
              { key: 'rejected', label: 'Rejected', count: activeTab === 'rejected' ? pagination?.totalCount : 0 },
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => handleTabChange(tab.key as any)}
                className={`flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                  activeTab === tab.key
                    ? 'bg-primary text-white'
                    : 'text-gray-400 hover:text-white hover:bg-secondary-dark'
                }`}
              >
                {tab.label}
                {activeTab === tab.key && pagination && (
                  <span className="ml-2 bg-white bg-opacity-20 px-2 py-0.5 rounded-full text-xs">
                    {pagination.totalCount}
                  </span>
                )}
              </button>
            ))}
          </div>
        </div>

        {/* Applications List */}
        {loading ? (
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="bg-secondary rounded-lg p-6 animate-pulse">
                <div className="flex items-start gap-4">
                  <div className="w-16 h-16 bg-secondary-dark rounded-lg"></div>
                  <div className="flex-1">
                    <div className="h-6 bg-secondary-dark rounded mb-2 w-1/3"></div>
                    <div className="h-4 bg-secondary-dark rounded mb-4 w-full"></div>
                    <div className="flex gap-2">
                      <div className="h-6 bg-secondary-dark rounded w-16"></div>
                      <div className="h-6 bg-secondary-dark rounded w-20"></div>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <div className="h-8 bg-secondary-dark rounded w-20"></div>
                    <div className="h-8 bg-secondary-dark rounded w-20"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : applications.length === 0 ? (
          <div className="text-center py-12">
            <div className="w-24 h-24 bg-secondary rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">
              No {activeTab} applications
            </h3>
            <p className="text-gray-400">
              {activeTab === 'pending' 
                ? "There are no pending captain applications to review."
                : `There are no ${activeTab} captain applications.`
              }
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {applications.map((app) => (
              <div key={app.id} className="bg-secondary rounded-lg shadow-lg overflow-hidden">
                <div className="p-6">
                  <div className="flex items-start gap-4">
                    {/* Logo */}
                    <div className="flex-shrink-0">
                      {app.logoPath ? (
                        <img
                          src={app.logoPath}
                          alt={`${app.shipName} logo`}
                          className="w-16 h-16 object-contain rounded-lg bg-secondary-dark"
                        />
                      ) : (
                        <div className="w-16 h-16 bg-primary rounded-lg flex items-center justify-center">
                          <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        </div>
                      )}
                    </div>

                    {/* Application Details */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between mb-2">
                        <div>
                          <h3 className="text-xl font-bold text-white">{app.shipName}</h3>
                          <p className="text-gray-400 text-sm">
                            Applied by {app.user.displayName} (@{app.user.username})
                          </p>
                        </div>
                        <div className="flex items-center gap-2">
                          {app.previouslyRejected && (
                            <span className="bg-yellow-900 text-yellow-100 px-2 py-1 rounded text-xs">
                              Previously Rejected
                            </span>
                          )}
                          <span className={`px-2 py-1 rounded text-xs ${
                            app.status === 'pending' ? 'bg-blue-900 text-blue-100' :
                            app.status === 'approved' ? 'bg-green-900 text-green-100' :
                            'bg-red-900 text-red-100'
                          }`}>
                            {app.status.charAt(0).toUpperCase() + app.status.slice(1)}
                          </span>
                        </div>
                      </div>

                      <p className="text-gray-300 mb-4 line-clamp-3">{app.description}</p>

                      {/* Tags */}
                      {app.tags && app.tags.length > 0 && (
                        <div className="flex flex-wrap gap-2 mb-4">
                          {app.tags.map((tag, index) => (
                            <span
                              key={index}
                              className="bg-primary text-white px-2 py-1 rounded text-sm"
                            >
                              #{tag}
                            </span>
                          ))}
                        </div>
                      )}

                      <div className="flex items-center justify-between">
                        <div className="text-sm text-gray-400">
                          Applied: {new Date(app.appliedAt).toLocaleString()}
                          {app.reviewedAt && (
                            <span className="ml-4">
                              Reviewed: {new Date(app.reviewedAt).toLocaleString()}
                            </span>
                          )}
                        </div>

                        {/* Actions */}
                        {app.status === 'pending' && (
                          <div className="flex gap-2">
                            <Button
                              variant="primary"
                              size="sm"
                              onClick={() => {
                                setSelectedApp(app);
                                setShowApproveModal(true);
                              }}
                              disabled={actionLoading === app.id}
                            >
                              {actionLoading === app.id ? "Processing..." : "Approve"}
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                setSelectedApp(app);
                                setShowRejectModal(true);
                              }}
                              disabled={actionLoading === app.id}
                            >
                              Reject
                            </Button>
                          </div>
                        )}

                        {app.status === 'rejected' && app.rejectionReason && (
                          <div className="text-red-400 text-sm max-w-md">
                            <strong>Rejection reason:</strong> {app.rejectionReason}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Pagination */}
        {pagination && pagination.totalPages > 1 && (
          <div className="flex justify-center items-center gap-2 mt-8">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={!pagination.hasPrev}
            >
              Previous
            </Button>
            
            {Array.from({ length: pagination.totalPages }, (_, i) => i + 1).map((page) => (
              <Button
                key={page}
                variant={page === currentPage ? "primary" : "outline"}
                size="sm"
                onClick={() => handlePageChange(page)}
              >
                {page}
              </Button>
            ))}
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={!pagination.hasNext}
            >
              Next
            </Button>
          </div>
        )}

        {/* Approve Modal */}
        <Modal
          isOpen={showApproveModal}
          onClose={() => setShowApproveModal(false)}
          size="md"
        >
          <div className="p-6">
            <h2 className="text-xl font-bold text-white mb-4">Approve Captain Application</h2>
            <p className="text-gray-300 mb-4">
              Are you sure you want to approve <strong>{selectedApp?.shipName}</strong>?
              This will create the ship and make {selectedApp?.user.displayName} the captain.
            </p>
            
            <div className="mb-4">
              <label htmlFor="slogan" className="block text-white font-medium mb-2">
                Ship Slogan (Optional)
              </label>
              <input
                id="slogan"
                type="text"
                value={shipSlogan}
                onChange={(e) => setShipSlogan(e.target.value)}
                placeholder="Enter a slogan for the ship..."
                className="w-full px-4 py-2 bg-secondary-dark border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary"
                maxLength={500}
              />
              <p className="text-gray-400 text-sm mt-1">
                Optional slogan that will be displayed on the ship page.
              </p>
            </div>

            <div className="flex justify-end gap-3">
              <Button
                variant="outline"
                onClick={() => setShowApproveModal(false)}
                disabled={actionLoading === selectedApp?.id}
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                onClick={handleApprove}
                disabled={actionLoading === selectedApp?.id}
              >
                {actionLoading === selectedApp?.id ? "Approving..." : "Approve Application"}
              </Button>
            </div>
          </div>
        </Modal>

        {/* Reject Modal */}
        <Modal
          isOpen={showRejectModal}
          onClose={() => setShowRejectModal(false)}
          size="md"
        >
          <div className="p-6">
            <h2 className="text-xl font-bold text-white mb-4">Reject Captain Application</h2>
            <p className="text-gray-300 mb-4">
              Please provide a reason for rejecting <strong>{selectedApp?.shipName}</strong>.
              This will help the applicant improve their future submissions.
            </p>
            
            <div className="mb-4">
              <label htmlFor="reason" className="block text-white font-medium mb-2">
                Rejection Reason *
              </label>
              <textarea
                id="reason"
                value={rejectionReason}
                onChange={(e) => setRejectionReason(e.target.value)}
                placeholder="Explain why this application is being rejected..."
                className="w-full px-4 py-2 bg-secondary-dark border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary h-24 resize-vertical"
                required
                minLength={10}
              />
              <p className="text-gray-400 text-sm mt-1">
                Minimum 10 characters required. Be constructive and specific.
              </p>
            </div>

            <div className="flex justify-end gap-3">
              <Button
                variant="outline"
                onClick={() => setShowRejectModal(false)}
                disabled={actionLoading === selectedApp?.id}
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                onClick={handleReject}
                disabled={actionLoading === selectedApp?.id || rejectionReason.trim().length < 10}
              >
                {actionLoading === selectedApp?.id ? "Rejecting..." : "Reject Application"}
              </Button>
            </div>
          </div>
        </Modal>
      </div>
    </div>
  );
}