  Phase 1: Database Schema Updates
I just want to make sure that these are being added to the create template form creation , it can be added as a selectable element to be added and then amount can be configured.
 Using the pencil element like the other elements when they are added. if the volunteer requirement element gets added to the form, It adds the correct flag to do what we need it to do. I have removed the deadline element as we do not need it currently and can add it later.
   I do not Want the create event form to have volunteer requirement element I want the template to have it. You can update the status of the template to show the required hours as it will be pertinent when they select the template.
  1.1 Add Volunteer Hour Requirements to Forms

  -- Migration: Add volunteer hours to EventForm model
  ALTER TABLE event_forms ADD COLUMN required_volunteer_hours DECIMAL(5,2) DEFAULT 0;

  1.2 Create Ship Volunteer Requirements Tracking

  -- New table to track volunteer hour requirements per ship
  CREATE TABLE ship_volunteer_requirements (
    id VARCHAR(36) PRIMARY KEY,
    ship_id VARCHAR(36) NOT NULL,
    form_submission_id VARCHAR(36) NOT NULL,
    required_hours DECIMAL(5,2) NOT NULL,
    status ENUM('pending', 'in_progress', 'completed', 'overdue') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (ship_id) REFERENCES ships(id) ON DELETE CASCADE,
    FOREIGN KEY (form_submission_id) REFERENCES form_submissions(id) ON DELETE CASCADE,
    UNIQUE KEY unique_ship_form (ship_id, form_submission_id)
  );

  Phase 2: Form Builder UI Updates

  2.1 Land Steward Form Creation Interface

  - Add volunteer hours configuration section to form builder
  - Include fields for:
    - Required volunteer hours (number input)

  2.2 Form Builder Component Extension

  Update web/packages/ui/src/form-builder/FormBuilder.tsx:
  - Add volunteer hours settings panel
  - Include validation for hour requirements
  - Save volunteer hour data with form structure

  Phase 3: Form Approval Integration

  3.1 Update Form Submission Review

  In web/apps/main-site/src/app/api/land-steward/submissions/[id]/review/route.ts:
  - When form is approved, create ShipVolunteerRequirement record
  
  3.2 Automatic Requirement Creation

  // On form approval
  if (submission.status === 'approved' && form.requiredVolunteerHours > 0) {
    await prisma.shipVolunteerRequirement.create({
      data: {
        shipId: submission.shipId,
        formSubmissionId: submission.id,
        requiredHours: form.requiredVolunteerHours,
        status: 'pending'
      }
    });
  }

  Phase 4: Captain Dashboard Updates

  4.1 Volunteer Hours Summary Widget

  Add to web/apps/main-site/src/app/captain/dashboard/page.tsx:
  - New "Volunteer Hours Required" section
  - Display active volunteer hour requirements for the captains ship 
  - Only visible when requirements exist

  4.2 Dashboard API Extensions

  Create new endpoint /api/captain/volunteer-requirements:
  - Fetch active volunteer hour requirements for captains ship

  Phase 5: API Endpoints

  5.1 New Endpoints

  GET  /api/captain/volunteer-requirements     # Get active requirements
  
  6.2 Enhanced Existing Endpoints

  - Update form creation APIs to handle volunteer hours
  - Modify form approval to create requirements
  - Extend captain dashboard data to include requirements

  
