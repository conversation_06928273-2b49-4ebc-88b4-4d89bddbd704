import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);

  // Parse query parameters
  const page = parseInt(searchParams.get("page") || "1");
  const limit = parseInt(searchParams.get("limit") || "10");
  const status = searchParams.get("status") || undefined;
  const featured =
    searchParams.get("featured") === "true"
      ? true
      : searchParams.get("featured") === "false"
      ? false
      : undefined;
  const category = searchParams.get("category") || undefined;
  const search = searchParams.get("search") || undefined;
  const sortBy = searchParams.get("sortBy") || "createdAt";
  const order = searchParams.get("order") || "desc";

  const skip = (page - 1) * limit;

  try {
    console.log("GET /api/news/articles - Query parameters:", {
      page,
      limit,
      status,
      featured,
      category,
      search,
      sortBy,
      order,
    });

    // Build the where clause based on provided filters
    const where: any = {};

    if (status) {
      where.status = status;
    }

    if (featured !== undefined) {
      where.featured = featured;
    }

    if (category) {
      where.category = {
        slug: category, // Changed from id to slug to match the public API behavior
      };
    }

    if (search) {
      where.OR = [
        { title: { contains: search } },
        { excerpt: { contains: search } },
        { content: { contains: search } },
      ];
    }

    // Get total count of matching articles
    const totalCount = await prisma.newsArticle.count({ where });

    // Get the articles with pagination, sorting and include author/category
    const articles = await prisma.newsArticle.findMany({
      where,
      skip,
      take: limit,
      orderBy: {
        [sortBy]: order as "asc" | "desc",
      },
      include: {
        author: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
    });

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalCount / limit);
    const hasNext = page < totalPages;
    const hasPrev = page > 1;

    return NextResponse.json({
      data: articles,
      meta: {
        currentPage: page,
        totalPages,
        totalCount,
        hasNext,
        hasPrev,
      },
    });
  } catch (error) {
    console.error("Error fetching articles:", error);
    return NextResponse.json(
      { error: "Failed to fetch articles" },
      { status: 500 },
    );
  }
}

export async function POST(request: Request) {
  try {
    const {
      title,
      content,
      excerpt,
      categoryId,
      image,
      status = "draft",
      featured = false,
    } = await request.json();
    // Validate required fields
    if (!title || !content || !categoryId) {
      return NextResponse.json(
        {
          error:
            "Missing required fields: title, content, and categoryId are required",
        },
        { status: 400 },
      );
    } // Get the current authenticated user
    const currentUser = await getCurrentUser(request);

    if (!currentUser) {
      return NextResponse.json(
        {
          error: "Authentication required. Please log in to create articles.",
        },
        { status: 401 },
      );
    }

    // Check if user has proper permissions (admin or editor)
    if (!currentUser.isAdmin && !currentUser.isEditor) {
      return NextResponse.json(
        {
          error:
            "Unauthorized. Only administrators and editors can create news articles.",
        },
        { status: 403 },
      );
    }

    // Generate slug from title
    const baseSlug = title
      .toLowerCase()
      .replace(/[^\w\s-]/g, "")
      .replace(/\s+/g, "-")
      .replace(/-+/g, "-");

    // Check if slug already exists and make it unique if needed
    const slugExists = await prisma.newsArticle.findFirst({
      where: {
        slug: baseSlug,
      },
    });

    const slug = slugExists
      ? `${baseSlug}-${Date.now().toString().slice(-6)}`
      : baseSlug;
    // Create the new article
    const newArticle = await prisma.newsArticle.create({
      data: {
        title,
        content,
        excerpt: excerpt || title, // Use title as excerpt if none provided
        image,
        authorId: currentUser.id,
        categoryId,
        slug,
        status,
        featured,
        publishedAt: status === "published" ? new Date() : null,
      },
    });

    return NextResponse.json(newArticle, { status: 201 });
  } catch (error) {
    console.error("Error creating article:", error);
    return NextResponse.json(
      { error: "Failed to create article" },
      { status: 500 },
    );
  }
}
