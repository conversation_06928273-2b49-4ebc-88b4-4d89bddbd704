"use client";

import { useState } from "react";
import { useProducts } from "@/hooks/useProducts";
import { useProductCategories } from "@/hooks/useProductCategories";
import { ProductCard } from "@/components/shop/ProductCard";
import { CategoryFilter } from "@/components/shop/CategoryFilter";
import { ProductSearch } from "@/components/shop/ProductSearch";
import { RedemptionCodeInput } from "@/components/shop/RedemptionCodeInput";
import { Spinner, Card } from "@bank-of-styx/ui";

export default function ShopPage() {
  const [selectedCategory, setSelectedCategory] = useState<string>("");
  const { data: productsData, isLoading: isLoadingProducts } = useProducts({
    categoryId: selectedCategory || undefined,
    isActive: true,
  });
  const { data: categoriesData, isLoading: isLoadingCategories } =
    useProductCategories(true);

  const handleCategoryChange = (categoryId: string) => {
    setSelectedCategory(categoryId);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
        <h1 className="text-3xl font-bold mb-4 md:mb-0">Shop</h1>
        <ProductSearch />
      </div>

      {/* Redemption Code Section */}
      <div className="mb-8">
        <RedemptionCodeInput />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Category sidebar */}
        <div className="lg:col-span-1">
          <Card className="sticky top-24">
            <h2 className="text-xl font-semibold mb-4">Categories</h2>
            {isLoadingCategories ? (
              <div className="flex justify-center p-4">
                <Spinner />
              </div>
            ) : (
              <CategoryFilter
                categories={categoriesData?.categories || []}
                selectedCategory={selectedCategory}
                onChange={handleCategoryChange}
              />
            )}
          </Card>
        </div>

        {/* Product grid */}
        <div className="lg:col-span-3">
          {isLoadingProducts ? (
            <div className="flex justify-center p-8">
              <Spinner size="lg" />
            </div>
          ) : !productsData || productsData.length === 0 ? (
            <div className="text-center p-8 bg-secondary-light rounded-lg">
              <p className="text-lg">No products found in this category.</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {productsData.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
