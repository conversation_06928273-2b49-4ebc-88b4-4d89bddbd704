export interface FormField {
  id: string;
  type: "text" | "textarea" | "select" | "checkbox" | "multi_select" | "file_upload" | "user_select" | "multi_user_select" | "volunteer_hours";
  label: string;
  description?: string;
  required?: boolean;
  options?: string[];
  validation?: {
    minLength?: number;
    maxLength?: number;
    pattern?: string;
    customMessage?: string;
  };
  // For volunteer hours field type
  volunteerHours?: number;
}

export interface FormTemplate {
  id?: string;
  name: string;
  description?: string;
  fields: FormField[];
}

export interface FormSubmissionData {
  [fieldId: string]: any;
}

export interface FormBuilderProps {
  /**
   * Initial form template to edit
   */
  initialTemplate?: FormTemplate;
  /**
   * Callback when form template is saved
   */
  onSave: (template: FormTemplate) => void;
  /**
   * Callback when form builder is closed
   */
  onCancel: () => void;
  /**
   * Whether the form builder is in edit mode
   */
  isEditing?: boolean;
}

export interface FormRendererProps {
  /**
   * Form template to render
   */
  form: FormTemplate;
  /**
   * Initial submission data (for editing existing submissions)
   */
  initialData?: FormSubmissionData;
  /**
   * Callback when form is submitted
   */
  onSubmit: (data: FormSubmissionData) => void;
  /**
   * Callback when form submission is cancelled
   */
  onCancel?: () => void;
  /**
   * Whether the form is in read-only mode
   */
  readOnly?: boolean;
  /**
   * Whether to show submission buttons
   */
  showActions?: boolean;
  /**
   * Custom submit button text
   */
  submitText?: string;
  /**
   * Whether the form is currently submitting
   */
  isSubmitting?: boolean;
}