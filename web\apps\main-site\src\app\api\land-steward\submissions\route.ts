import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth";
import prisma from "@/lib/prisma";

export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    
    if (!user || !user.isLandSteward) {
      return NextResponse.json(
        { error: "Unauthorized. Land Steward access required." },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const formId = searchParams.get("formId");
    const status = searchParams.get("status");
    const shipId = searchParams.get("shipId");

    const where: any = {};
    
    if (formId) {
      const formIdNum = parseInt(formId);
      if (!isNaN(formIdNum)) {
        where.formId = formIdNum;
      }
    }

    if (status && ["draft", "submitted", "reviewed", "approved", "rejected"].includes(status)) {
      where.status = status;
    }

    if (shipId) {
      const shipIdNum = parseInt(shipId);
      if (!isNaN(shipIdNum)) {
        where.shipId = shipIdNum;
      }
    }

    const submissions = await prisma.formSubmission.findMany({
      where,
      include: {
        form: {
          select: {
            id: true,
            name: true,
            event: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        ship: {
          select: {
            id: true,
            name: true,
            captain: {
              select: {
                id: true,
                username: true,
                displayName: true,
              },
            },
          },
        },
        submittedBy: {
          select: {
            id: true,
            username: true,
            displayName: true,
          },
        },
        reviewedBy: {
          select: {
            id: true,
            username: true,
            displayName: true,
          },
        },
      },
      orderBy: {
        submittedAt: "desc",
      },
    });

    return NextResponse.json(submissions);
  } catch (error) {
    console.error("Error fetching form submissions:", error);
    return NextResponse.json(
      { error: "Failed to fetch form submissions" },
      { status: 500 }
    );
  }
}