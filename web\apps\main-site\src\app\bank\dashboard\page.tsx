"use client";

import React, { Suspense } from "react";
import {
  DashboardLayout,
  QuickActions,
  RecentTransactions,
  AccountSummary,
} from "../../../components/bank";

// Simple loading spinner component
const LoadingSpinner = ({ size = "md" }: { size?: "sm" | "md" | "lg" }) => {
  const sizeClasses = {
    sm: "h-6 w-6",
    md: "h-8 w-8",
    lg: "h-12 w-12",
  };

  return (
    <div
      className={`animate-spin rounded-full ${sizeClasses[size]} border-t-2 border-b-2 border-primary`}
    ></div>
  );
};

export default function DashboardPage() {
  // Preload components to avoid multiple fetches
  const QuickActionsComponent = (
    <Suspense
      fallback={
        <div className="h-40 flex items-center justify-center">
          <LoadingSpinner size="md" />
        </div>
      }
    >
      <QuickActions />
    </Suspense>
  );

  const AccountSummaryComponent = (
    <Suspense
      fallback={
        <div className="h-40 flex items-center justify-center">
          <LoadingSpinner size="md" />
        </div>
      }
    >
      <AccountSummary />
    </Suspense>
  );

  const TransactionsComponent = (
    <Suspense
      fallback={
        <div className="h-60 flex items-center justify-center">
          <LoadingSpinner size="md" />
        </div>
      }
    >
      <RecentTransactions
        useApi={true}
        limit={4}
        viewAllLink="/bank/dashboard/transactions"
      />
    </Suspense>
  );

  return (
    <DashboardLayout>
      {/* Large screens (896px+): Quick Actions and Account Summary side by side */}
      <div className="hidden min-[896px]:grid min-[896px]:grid-cols-2 gap-6 mb-6">
        {QuickActionsComponent}
        {AccountSummaryComponent}
      </div>

      {/* Medium screens (768px-896px): Quick Actions full width, Account Summary below */}
      <div className="hidden md:block min-[896px]:hidden mb-6">
        <div className="mb-6">{QuickActionsComponent}</div>
        {AccountSummaryComponent}
      </div>

      {/* Mobile screens (below 768px): Stacked layout */}
      <div className="md:hidden mb-6">
        <div className="mb-6">{QuickActionsComponent}</div>
        {AccountSummaryComponent}
      </div>

      {TransactionsComponent}
    </DashboardLayout>
  );
}
