import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth";
import prisma from "@/lib/prisma";

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const submissionId = params.id;
    const body = await request.json();
    const { submissionData, status = "draft" } = body;

    // Get the user's ship
    const shipMember = await prisma.shipMember.findFirst({
      where: {
        userId: user.id,
        status: "active",
      },
      include: {
        ship: {
          select: {
            id: true,
            name: true,
            captainId: true,
          },
        },
      },
    });

    if (!shipMember) {
      return NextResponse.json(
        { error: "You must be a member of a ship to update submissions" },
        { status: 403 }
      );
    }

    // Check if user is the captain
    const isCaptain = shipMember.ship.captainId === user.id;
    
    if (!isCaptain) {
      return NextResponse.json(
        { error: "Only ship captains can update form submissions" },
        { status: 403 }
      );
    }

    // Get the submission and verify ownership
    const submission = await prisma.formSubmission.findUnique({
      where: { id: submissionId },
      include: {
        form: true,
      },
    });

    if (!submission) {
      return NextResponse.json(
        { error: "Submission not found" },
        { status: 404 }
      );
    }

    if (submission.shipId !== shipMember.ship.id) {
      return NextResponse.json(
        { error: "You can only update your own ship's submissions" },
        { status: 403 }
      );
    }

    // Check if submission can be updated
    if (submission.status === "approved" || submission.status === "rejected") {
      return NextResponse.json(
        { error: "Cannot update approved or rejected submissions" },
        { status: 400 }
      );
    }

    // Check if form is still available for updates
    const now = new Date();
    if (submission.form.status !== "active") {
      return NextResponse.json(
        { error: "Form is no longer active" },
        { status: 400 }
      );
    }

    if (submission.form.submissionDeadline && submission.form.submissionDeadline < now) {
      return NextResponse.json(
        { error: "Form submission deadline has passed" },
        { status: 400 }
      );
    }

    // Update the submission
    const updatedSubmission = await prisma.formSubmission.update({
      where: { id: submissionId },
      data: {
        submissionData,
        status,
        submittedAt: status === "submitted" ? new Date() : submission.submittedAt,
      },
      include: {
        form: {
          select: {
            id: true,
            name: true,
            event: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        ship: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    return NextResponse.json(updatedSubmission);
  } catch (error) {
    console.error("Error updating form submission:", error);
    return NextResponse.json(
      { error: "Failed to update form submission" },
      { status: 500 }
    );
  }
}