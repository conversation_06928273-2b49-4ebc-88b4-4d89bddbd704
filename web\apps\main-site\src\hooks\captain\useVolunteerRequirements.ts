import { useQuery } from "@tanstack/react-query";
import { getVolunteerRequirements, type VolunteerRequirement } from "@/services/captainService";

export const useVolunteerRequirements = () => {
  const {
    data: requirements = [],
    isLoading,
    error,
    refetch
  } = useQuery<VolunteerRequirement[]>({
    queryKey: ["volunteerRequirements"],
    queryFn: getVolunteerRequirements,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });

  return {
    requirements,
    isLoading,
    error,
    refetch
  };
};