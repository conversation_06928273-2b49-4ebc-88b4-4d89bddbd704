"use client";

import React, { useState, useEffect } from "react";
import { Modal } from "@bank-of-styx/ui";
import { EventStatsResponse } from "@/types/eventStats";
import { EventStatsService } from "@/services/eventStatsService";
import { useColorTheme } from "@/contexts/ColorThemeContext";

interface EventStatsModalProps {
  isOpen: boolean;
  onClose: () => void;
  eventId: string;
  eventName: string;
}

export const EventStatsModal: React.FC<EventStatsModalProps> = ({
  isOpen,
  onClose,
  eventId,
  eventName,
}) => {
  const { isDarkMode } = useColorTheme();
  const [stats, setStats] = useState<EventStatsResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch stats when modal opens
  useEffect(() => {
    if (isOpen && eventId) {
      fetchStats();
    }
  }, [isOpen, eventId]);

  const fetchStats = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await EventStatsService.fetchEventStats(eventId);
      setStats(data);
    } catch (err) {
      setError("Failed to load event statistics. Please try again.");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const renderOverviewCards = () => {
    if (!stats) return null;

    const cards = [
      {
        title: "Total Revenue",
        value: EventStatsService.formatCurrency(stats.overview.totalRevenue),
        icon: "💰",
        color: "text-green-600",
      },
      {
        title: "Units Sold",
        value: stats.overview.totalUnitsSold.toLocaleString(),
        icon: "🎫",
        color: "text-blue-600",
      },
      {
        title: "Unique Customers",
        value: stats.overview.uniqueCustomers.toLocaleString(),
        icon: "👥",
        color: "text-purple-600",
      },
      {
        title: "Refunds",
        value: EventStatsService.formatCurrency(stats.overview.totalRefunds),
        icon: "↩️",
        color: "text-red-600",
      },
    ];

    return (
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {cards.map((card, index) => (
          <div
            key={index}
            className="p-4 rounded-lg"
            style={{
              backgroundColor: "var(--color-bg-surface)",
              border: "1px solid var(--color-border-subtle)",
            }}
          >
            <div className="flex items-center justify-between">
              <div>
                <p
                  className="text-sm font-medium"
                  style={{ color: "var(--color-text-secondary)" }}
                >
                  {card.title}
                </p>
                <p
                  className={`text-2xl font-bold ${card.color}`}
                  style={{ color: card.color }}
                >
                  {card.value}
                </p>
              </div>
              <span className="text-2xl">{card.icon}</span>
            </div>
          </div>
        ))}
      </div>
    );
  };

  const renderProductBreakdown = () => {
    if (!stats || stats.products.length === 0) {
      return (
        <div className="mb-6">
          <h3
            className="text-lg font-semibold mb-4"
            style={{ color: "var(--color-text-primary)" }}
          >
            Product Breakdown
          </h3>
          <p
            className="text-center py-8"
            style={{ color: "var(--color-text-secondary)" }}
          >
            No products associated with this event.
          </p>
        </div>
      );
    }

    return (
      <div className="mb-6">
        <h3
          className="text-lg font-semibold mb-4"
          style={{ color: "var(--color-text-primary)" }}
        >
          Product Breakdown
        </h3>
        <div
          className="rounded-lg overflow-hidden"
          style={{
            backgroundColor: "var(--color-bg-surface)",
            border: "1px solid var(--color-border-subtle)",
          }}
        >
          <table className="min-w-full divide-y divide-gray-600">
            <thead style={{ backgroundColor: "var(--color-bg-header)" }}>
              <tr>
                <th
                  className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider"
                  style={{ color: "var(--color-text-secondary)" }}
                >
                  Product
                </th>
                <th
                  className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider"
                  style={{ color: "var(--color-text-secondary)" }}
                >
                  Price
                </th>
                <th
                  className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider"
                  style={{ color: "var(--color-text-secondary)" }}
                >
                  Sold
                </th>
                <th
                  className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider"
                  style={{ color: "var(--color-text-secondary)" }}
                >
                  Revenue
                </th>
                <th
                  className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider"
                  style={{ color: "var(--color-text-secondary)" }}
                >
                  Utilization
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-600">
              {stats.products.map((product) => {
                const utilization = EventStatsService.calculateTicketUtilization(
                  product.soldTickets,
                  product.totalTickets
                );
                return (
                  <tr key={product.id}>
                    <td
                      className="px-4 py-4 whitespace-nowrap"
                      style={{ color: "var(--color-text-primary)" }}
                    >
                      <div className="font-medium">{product.name}</div>
                      <div
                        className="text-sm"
                        style={{ color: "var(--color-text-secondary)" }}
                      >
                        {product.totalTickets} total tickets
                      </div>
                    </td>
                    <td
                      className="px-4 py-4 whitespace-nowrap"
                      style={{ color: "var(--color-text-primary)" }}
                    >
                      {EventStatsService.formatCurrency(product.price)}
                    </td>
                    <td
                      className="px-4 py-4 whitespace-nowrap"
                      style={{ color: "var(--color-text-primary)" }}
                    >
                      <div>{product.unitsSold}</div>
                      {product.heldTickets > 0 && (
                        <div
                          className="text-sm"
                          style={{ color: "var(--color-warning)" }}
                        >
                          {product.heldTickets} held
                        </div>
                      )}
                    </td>
                    <td
                      className="px-4 py-4 whitespace-nowrap font-medium"
                      style={{ color: "var(--color-text-primary)" }}
                    >
                      {EventStatsService.formatCurrency(product.revenue)}
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap">
                      <div
                        className={`font-medium ${EventStatsService.getUtilizationColor(utilization)}`}
                      >
                        {utilization.toFixed(1)}%
                      </div>
                      <div
                        className="w-full bg-gray-700 rounded-full h-2 mt-1"
                      >
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{ width: `${Math.min(utilization, 100)}%` }}
                        ></div>
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  const renderTopCustomers = () => {
    if (!stats || stats.topCustomers.length === 0) return null;

    return (
      <div className="mb-6">
        <h3
          className="text-lg font-semibold mb-4"
          style={{ color: "var(--color-text-primary)" }}
        >
          Top Customers
        </h3>
        <div
          className="rounded-lg overflow-hidden"
          style={{
            backgroundColor: "var(--color-bg-surface)",
            border: "1px solid var(--color-border-subtle)",
          }}
        >
          <table className="min-w-full divide-y divide-gray-600">
            <thead style={{ backgroundColor: "var(--color-bg-header)" }}>
              <tr>
                <th
                  className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider"
                  style={{ color: "var(--color-text-secondary)" }}
                >
                  Customer
                </th>
                <th
                  className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider"
                  style={{ color: "var(--color-text-secondary)" }}
                >
                  Items
                </th>
                <th
                  className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider"
                  style={{ color: "var(--color-text-secondary)" }}
                >
                  Orders
                </th>
                <th
                  className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider"
                  style={{ color: "var(--color-text-secondary)" }}
                >
                  Total Spent
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-600">
              {stats.topCustomers.slice(0, 10).map((customer, index) => (
                <tr key={customer.user.id}>
                  <td
                    className="px-4 py-4 whitespace-nowrap"
                    style={{ color: "var(--color-text-primary)" }}
                  >
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-8 w-8">
                        <div
                          className="h-8 w-8 rounded-full flex items-center justify-center text-xs font-medium"
                          style={{
                            backgroundColor: "var(--color-primary)",
                            color: "var(--color-text-on-primary)",
                          }}
                        >
                          {(customer.user.displayName || customer.user.username || customer.user.email).charAt(0).toUpperCase()}
                        </div>
                      </div>
                      <div className="ml-3">
                        <div className="font-medium">
                          {EventStatsService.getCustomerDisplayName(customer.user)}
                        </div>
                        <div
                          className="text-sm"
                          style={{ color: "var(--color-text-secondary)" }}
                        >
                          {customer.user.email}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td
                    className="px-4 py-4 whitespace-nowrap"
                    style={{ color: "var(--color-text-primary)" }}
                  >
                    {customer.itemsPurchased}
                  </td>
                  <td
                    className="px-4 py-4 whitespace-nowrap"
                    style={{ color: "var(--color-text-primary)" }}
                  >
                    {customer.orderCount}
                  </td>
                  <td
                    className="px-4 py-4 whitespace-nowrap font-medium"
                    style={{ color: "var(--color-text-primary)" }}
                  >
                    {EventStatsService.formatCurrency(customer.totalSpent)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  const renderRecentTransactions = () => {
    if (!stats || stats.recentTransactions.length === 0) return null;

    return (
      <div className="mb-6">
        <h3
          className="text-lg font-semibold mb-4"
          style={{ color: "var(--color-text-primary)" }}
        >
          Recent Transactions
        </h3>
        <div
          className="rounded-lg overflow-hidden"
          style={{
            backgroundColor: "var(--color-bg-surface)",
            border: "1px solid var(--color-border-subtle)",
          }}
        >
          <table className="min-w-full divide-y divide-gray-600">
            <thead style={{ backgroundColor: "var(--color-bg-header)" }}>
              <tr>
                <th
                  className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider"
                  style={{ color: "var(--color-text-secondary)" }}
                >
                  Order
                </th>
                <th
                  className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider"
                  style={{ color: "var(--color-text-secondary)" }}
                >
                  Customer
                </th>
                <th
                  className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider"
                  style={{ color: "var(--color-text-secondary)" }}
                >
                  Product
                </th>
                <th
                  className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider"
                  style={{ color: "var(--color-text-secondary)" }}
                >
                  Amount
                </th>
                <th
                  className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider"
                  style={{ color: "var(--color-text-secondary)" }}
                >
                  Date
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-600">
              {stats.recentTransactions.slice(0, 10).map((transaction) => (
                <tr key={transaction.id}>
                  <td
                    className="px-4 py-4 whitespace-nowrap font-medium"
                    style={{ color: "var(--color-text-primary)" }}
                  >
                    #{transaction.orderNumber}
                  </td>
                  <td
                    className="px-4 py-4 whitespace-nowrap"
                    style={{ color: "var(--color-text-primary)" }}
                  >
                    {EventStatsService.getCustomerDisplayName(transaction.customer)}
                  </td>
                  <td
                    className="px-4 py-4 whitespace-nowrap"
                    style={{ color: "var(--color-text-primary)" }}
                  >
                    <div>{transaction.productName}</div>
                    <div
                      className="text-sm"
                      style={{ color: "var(--color-text-secondary)" }}
                    >
                      Qty: {transaction.quantity} × {EventStatsService.formatCurrency(transaction.unitPrice)}
                    </div>
                  </td>
                  <td
                    className="px-4 py-4 whitespace-nowrap font-medium"
                    style={{ color: "var(--color-text-primary)" }}
                  >
                    {EventStatsService.formatCurrency(transaction.total)}
                  </td>
                  <td
                    className="px-4 py-4 whitespace-nowrap text-sm"
                    style={{ color: "var(--color-text-secondary)" }}
                  >
                    {EventStatsService.formatDateTime(transaction.date)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  const modalContent = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div
              className="animate-spin rounded-full h-12 w-12 border-b-2 mx-auto mb-4"
              style={{ borderColor: "var(--color-primary)" }}
            ></div>
            <p style={{ color: "var(--color-text-secondary)" }}>
              Loading statistics...
            </p>
          </div>
        </div>
      );
    }

    if (error) {
      return (
        <div className="text-center py-12">
          <div
            className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4"
            style={{
              backgroundColor: "var(--color-error-light)",
              borderColor: "var(--color-error)",
              color: "var(--color-error-dark)",
            }}
          >
            {error}
          </div>
          <button
            onClick={fetchStats}
            className="px-4 py-2 rounded transition-colors"
            style={{
              backgroundColor: "var(--color-primary)",
              color: "var(--color-text-on-primary)",
            }}
          >
            Retry
          </button>
        </div>
      );
    }

    if (!stats) return null;

    return (
      <div className="max-h-[80vh] overflow-y-auto">
        {renderOverviewCards()}
        {renderProductBreakdown()}
        {renderTopCustomers()}
        {renderRecentTransactions()}
      </div>
    );
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={`Event Statistics: ${eventName}`}
      size="full"
      closeOnClickOutside={false}
    >
      {modalContent()}
    </Modal>
  );
};