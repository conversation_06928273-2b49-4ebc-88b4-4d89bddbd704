import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth";
import prisma from "@/lib/prisma";

export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    
    if (!user || !user.isLandSteward) {
      return NextResponse.json(
        { error: "Unauthorized. Land Steward access required." },
        { status: 403 }
      );
    }

    const templates = await prisma.formTemplate.findMany({
      where: {
        isReusable: true,
      },
      include: {
        createdBy: {
          select: {
            id: true,
            username: true,
            displayName: true,
          },
        },
        _count: {
          select: {
            eventForms: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return NextResponse.json(templates);
  } catch (error) {
    console.error("Error fetching form templates:", error);
    return NextResponse.json(
      { error: "Failed to fetch form templates" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    
    if (!user || !user.isLandSteward) {
      return NextResponse.json(
        { error: "Unauthorized. Land Steward access required." },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { name, description, formStructure, isReusable = true } = body;

    // Validate required fields
    if (!name || !formStructure) {
      return NextResponse.json(
        { error: "Name and form structure are required" },
        { status: 400 }
      );
    }

    // Validate form structure
    if (!Array.isArray(formStructure) || formStructure.length === 0) {
      return NextResponse.json(
        { error: "Form structure must be a non-empty array of fields" },
        { status: 400 }
      );
    }

    // Validate each field in the structure
    for (const field of formStructure) {
      if (!field.id || !field.type || !field.label) {
        return NextResponse.json(
          { error: "Each field must have id, type, and label" },
          { status: 400 }
        );
      }

      const validTypes = [
        "text", "textarea", "select", "checkbox", "multi_select", 
        "file_upload", "user_select", "multi_user_select", "volunteer_hours"
      ];
      
      if (!validTypes.includes(field.type)) {
        return NextResponse.json(
          { error: `Invalid field type: ${field.type}` },
          { status: 400 }
        );
      }
    }

    const template = await prisma.formTemplate.create({
      data: {
        name,
        description,
        formStructure,
        isReusable,
        createdBy: {
          connect: { id: user.id },
        },
      },
      include: {
        createdBy: {
          select: {
            id: true,
            username: true,
            displayName: true,
          },
        },
      },
    });

    return NextResponse.json(template, { status: 201 });
  } catch (error) {
    console.error("Error creating form template:", error);
    return NextResponse.json(
      { error: "Failed to create form template" },
      { status: 500 }
    );
  }
}