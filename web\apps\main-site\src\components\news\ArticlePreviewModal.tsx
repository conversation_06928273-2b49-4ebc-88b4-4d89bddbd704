"use client";

import React from "react";

interface ArticlePreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  content: string;
  imageUrl: string;
  category?: string;
}

export const ArticlePreviewModal: React.FC<ArticlePreviewModalProps> = ({
  isOpen,
  onClose,
  title,
  content,
  imageUrl,
  category,
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div
          className="fixed inset-0 transition-opacity"
          aria-hidden="true"
          onClick={onClose}
        >
          <div className="absolute inset-0 bg-black bg-opacity-75"></div>
        </div>

        {/* Modal panel */}
        <div className="inline-block align-bottom bg-secondary-light rounded-lg text-left shadow-xl transform transition-all sm:my-8 sm:align-middle w-full max-w-full sm:max-w-4xl">
          <div className="flex justify-between items-center p-2 sm:p-3 border-b border-gray-600">
            <h3 className="text-lg sm:text-xl font-bold text-white">
              Article Preview
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white"
              aria-label="Close"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          <div className="max-h-[70vh] overflow-y-auto p-2 sm:p-3">
            <div className="bg-secondary-dark rounded-lg p-3 mb-4 border border-gray-600">
              {imageUrl && (
                <>
                  <div
                    className="relative w-full mb-4 overflow-hidden rounded-lg"
                    style={{
                      height:
                        "15.6rem" /* Match the 30% larger height in NewsArticleCard */,
                      maxHeight:
                        "15.6rem" /* Ensure maximum height constraint */,
                    }}
                  >
                    <img
                      src={imageUrl}
                      alt={title}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        // Fallback if image fails to load
                        const target = e.target as HTMLImageElement;
                        target.onerror = null; // Prevent infinite error loop
                        target.src = "/images/placeholder-news.jpg"; // Fallback image
                      }}
                    />
                    {category && (
                      <span className="absolute top-4 left-4 bg-primary text-white px-3 py-1 rounded-full text-sm font-medium">
                        {category}
                      </span>
                    )}
                  </div>
                  <div className="mb-4 p-2 bg-secondary border border-gray-600 rounded-md">
                    <p className="text-sm text-gray-400">
                      <span className="font-medium text-primary">
                        Image Preview:
                      </span>{" "}
                      This is how your image will appear in article cards. If
                      the image doesn't look right, you can return to the editor
                      and upload a different image with a better aspect ratio.
                    </p>
                  </div>
                </>
              )}

              <div className="p-2 sm:p-3">
                <h1 className="text-2xl sm:text-3xl font-bold mb-4 text-white">
                  {title}
                </h1>
                <div
                  className="ql-content prose prose-invert max-w-none overflow-x-hidden"
                  dangerouslySetInnerHTML={{ __html: content }}
                />

                <style>{`
                  .ql-content img {
                    max-width: 100%;
                    height: auto;
                  }
                  .ql-content pre {
                    white-space: pre-wrap;
                    word-wrap: break-word;
                    overflow-x: hidden;
                  }
                  .ql-content * {
                    max-width: 100%;
                  }
                `}</style>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row sm:justify-end gap-2 mt-4">
              <button
                onClick={onClose}
                className="px-4 py-2 bg-primary hover:bg-primary-dark text-white rounded-md w-full sm:w-auto text-center"
              >
                Close Preview
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
