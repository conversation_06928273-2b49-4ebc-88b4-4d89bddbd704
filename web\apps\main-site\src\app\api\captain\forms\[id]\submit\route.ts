import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth";
import prisma from "@/lib/prisma";

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const formId = params.id;

    const body = await request.json();
    const { submissionData, status = "submitted" } = body;

    // Validate submission data
    if (!submissionData || typeof submissionData !== "object") {
      return NextResponse.json(
        { error: "Submission data is required" },
        { status: 400 }
      );
    }

    // Get the user's ship
    const shipMember = await prisma.shipMember.findFirst({
      where: {
        userId: user.id,
        status: "active",
      },
      include: {
        ship: {
          select: {
            id: true,
            name: true,
            captainId: true,
          },
        },
      },
    });

    if (!shipMember) {
      return NextResponse.json(
        { error: "You must be a member of a ship to submit forms" },
        { status: 403 }
      );
    }

    // Check if user is the captain
    const isCaptain = shipMember.ship.captainId === user.id;
    
    if (!isCaptain) {
      return NextResponse.json(
        { error: "Only ship captains can submit forms" },
        { status: 403 }
      );
    }

    // Get the form and validate it's available
    const form = await prisma.eventForm.findUnique({
      where: { id: formId },
    });

    if (!form) {
      return NextResponse.json(
        { error: "Form not found" },
        { status: 404 }
      );
    }

    // Check if form is available for submission
    const now = new Date();
    if (form.status !== "active") {
      return NextResponse.json(
        { error: "Form is not active for submissions" },
        { status: 400 }
      );
    }

    if (form.submissionDeadline && form.submissionDeadline < now) {
      return NextResponse.json(
        { error: "Form submission deadline has passed" },
        { status: 400 }
      );
    }

    // Validate submission data against form structure
    const formFields = form.formStructure as any[];
    for (const field of formFields) {
      if (field.required) {
        const value = submissionData[field.id];
        if (!value || (typeof value === "string" && !value.trim())) {
          return NextResponse.json(
            { error: `${field.label} is required` },
            { status: 400 }
          );
        }
      }
    }

    // Check if ship already has a submission
    const existingSubmission = await prisma.formSubmission.findFirst({
      where: {
        formId,
        shipId: shipMember.ship.id,
      },
    });

    let submission;

    if (existingSubmission) {
      // Update existing submission
      submission = await prisma.formSubmission.update({
        where: { id: existingSubmission.id },
        data: {
          submissionData,
          status,
          submittedAt: status === "submitted" ? new Date() : existingSubmission.submittedAt,
        },
        include: {
          form: {
            select: {
              id: true,
              name: true,
            },
          },
          ship: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });
    } else {
      // Create new submission
      submission = await prisma.formSubmission.create({
        data: {
          formId,
          shipId: shipMember.ship.id,
          submittedById: user.id,
          submissionData,
          status,
          submittedAt: status === "submitted" ? new Date() : undefined,
        },
        include: {
          form: {
            select: {
              id: true,
              name: true,
            },
          },
          ship: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });
    }

    return NextResponse.json(submission, { 
      status: existingSubmission ? 200 : 201 
    });
  } catch (error) {
    console.error("Error submitting form:", error);
    return NextResponse.json(
      { error: "Failed to submit form" },
      { status: 500 }
    );
  }
}