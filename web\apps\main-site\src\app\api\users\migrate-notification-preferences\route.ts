import { NextRequest, NextResponse } from "next/server";
import prisma from "../../../../lib/prisma";
import { getCurrentUser } from "../../../../lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
/**
 * This endpoint is used to migrate user notification preferences from the old format to the new format.
 * It's a one-time operation that should be called when a user first accesses the app after the update.
 */
export async function POST(req: NextRequest) {
  try {
    // Get current user
    const currentUser = await getCurrentUser(req);
    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    // Get user from database with preferences
    const user = await prisma.user.findUnique({
      where: { id: currentUser.id },
      select: {
        id: true,
        notifyTransfers: true,
        notifyDeposits: true,
        notifyWithdrawals: true,
        notifyNewsEvents: true,
        notifyAuctions: true,
        notifyChat: true,
        notifyAdmin: true,
      },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Return the migrated preferences
    return NextResponse.json({
      success: true,
      preferences: {
        notifyTransfers: user.notifyTransfers,
        notifyDeposits: user.notifyDeposits,
        notifyWithdrawals: user.notifyWithdrawals,
        notifyNewsEvents: user.notifyNewsEvents,
        notifyAuctions: user.notifyAuctions,
        notifyChat: user.notifyChat,
        notifyAdmin: user.notifyAdmin,
      },
    });
  } catch (error) {
    console.error("Error migrating notification preferences:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
