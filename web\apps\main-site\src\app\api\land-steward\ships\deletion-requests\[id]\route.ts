import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth";

export const dynamic = 'force-dynamic';

export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const currentUser = await getCurrentUser(request);

    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Check if user is a land steward or admin
    if (!currentUser.isLandSteward && !currentUser.isAdmin) {
      return NextResponse.json(
        { error: "Land Steward or Admin permissions required" },
        { status: 403 }
      );
    }

    const { id } = params;
    const { action, message } = await request.json();

    if (!action || !['approve', 'reject'].includes(action)) {
      return NextResponse.json(
        { error: "Action must be 'approve' or 'reject'" },
        { status: 400 }
      );
    }

    // Find the deletion request
    const deletionRequest = await prisma.shipDeletionRequest.findUnique({
      where: { id },
      include: {
        ship: {
          select: {
            id: true,
            name: true,
            status: true,
          },
        },
      },
    });

    if (!deletionRequest) {
      return NextResponse.json(
        { error: "Deletion request not found" },
        { status: 404 }
      );
    }

    if (deletionRequest.status !== 'pending') {
      return NextResponse.json(
        { error: "Deletion request has already been reviewed" },
        { status: 400 }
      );
    }

    await prisma.$transaction(async (tx) => {
      // Update the deletion request
      await tx.shipDeletionRequest.update({
        where: { id },
        data: {
          status: action === 'approve' ? 'approved' : 'rejected',
          message: message || null,
          reviewedById: currentUser.id,
          reviewedAt: new Date(),
        },
      });

      if (action === 'approve') {
        // If approved, perform cascading delete of the ship
        const shipId = deletionRequest.ship.id;

        // Delete ship members
        await tx.shipMember.deleteMany({
          where: { shipId },
        });

        // Delete ship roles
        await tx.shipRole.deleteMany({
          where: { shipId },
        });

        // Delete join requests
        await tx.shipJoinRequest.deleteMany({
          where: { shipId },
        });

        // Delete form submissions
        await tx.formSubmission.deleteMany({
          where: { shipId },
        });

        // Delete all deletion requests for this ship
        await tx.shipDeletionRequest.deleteMany({
          where: { shipId },
        });

        // Delete the ship itself
        await tx.ship.delete({
          where: { id: shipId },
        });
      } else {
        // If rejected, set ship status back to active
        await tx.ship.update({
          where: { id: deletionRequest.ship.id },
          data: { status: 'active' },
        });
      }
    });

    return NextResponse.json({
      message: `Deletion request ${action}d successfully`,
      action,
      shipDeleted: action === 'approve',
    });
  } catch (error) {
    console.error("Error reviewing deletion request:", error);
    return NextResponse.json(
      { error: "Failed to review deletion request" },
      { status: 500 }
    );
  }
}