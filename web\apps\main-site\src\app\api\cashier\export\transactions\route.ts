import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { generateCSVOnServer } from "@/utils/csv-export";
import { getCurrentUser, userHasRole } from "@/lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
/**
 * API route for exporting transaction data as CSV for cashiers
 *
 * This endpoint allows cashiers to export transaction data in CSV format.
 * It supports filtering by date range, transaction type, and status.
 */
export async function GET(request: Request) {
  try {
    // Check if user is authenticated and has cashier role
    const currentUser = await getCurrentUser(request);
    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    const isCashier = await userHasRole(request, "banker");
    if (!isCashier) {
      return NextResponse.json(
        { error: "Cashier privileges required" },
        { status: 403 },
      );
    }

    // Get query parameters for filtering
    const url = new URL(request.url);
    const startDate = url.searchParams.get("startDate");
    const endDate = url.searchParams.get("endDate");
    const type = url.searchParams.get("type");
    const status = url.searchParams.get("status");

    // Build the query
    const where: any = {};

    // Date range filter
    if (startDate || endDate) {
      where.createdAt = {};

      if (startDate) {
        where.createdAt.gte = new Date(startDate);
      }

      if (endDate) {
        // Set end date to end of day
        const endDateTime = new Date(endDate);
        endDateTime.setHours(23, 59, 59, 999);
        where.createdAt.lte = endDateTime;
      }
    }

    // Transaction type filter
    if (type) {
      where.type = type;
    }

    // Status filter
    if (status) {
      where.status = status;
    }

    // Fetch transactions from database
    const transactions = await prisma.transaction.findMany({
      where,
      include: {
        sender: {
          select: {
            username: true,
            displayName: true,
          },
        },
        recipient: {
          select: {
            username: true,
            displayName: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Transform data for CSV export
    const formattedTransactions = transactions.map((transaction) => ({
      id: transaction.id,
      amount: transaction.amount.toString(),
      type: transaction.type,
      status: transaction.status,
      description: transaction.description || "",
      note: transaction.note || "",
      user:
        transaction.sender?.displayName || transaction.sender?.username || "",
      recipient:
        transaction.recipient?.displayName ||
        transaction.recipient?.username ||
        "",
      createdAt: transaction.createdAt.toISOString(),
      updatedAt: transaction.updatedAt.toISOString(),
    }));

    // Define headers with proper typing
    const headers: {
      key: keyof (typeof formattedTransactions)[0];
      label: string;
    }[] = [
      { key: "id", label: "Transaction ID" },
      { key: "amount", label: "Amount" },
      { key: "type", label: "Type" },
      { key: "status", label: "Status" },
      { key: "description", label: "Description" },
      { key: "note", label: "Note" },
      { key: "user", label: "User" },
      { key: "recipient", label: "Recipient" },
      { key: "createdAt", label: "Created At" },
      { key: "updatedAt", label: "Last Updated" },
    ];

    // Generate CSV content
    const csvContent = generateCSVOnServer(formattedTransactions, headers);

    // Set headers for CSV download
    const filename = `transactions-export-${
      new Date().toISOString().split("T")[0]
    }.csv`;

    return new Response(csvContent, {
      status: 200,
      headers: {
        "Content-Type": "text/csv;charset=utf-8;",
        "Content-Disposition": `attachment; filename="${filename}"`,
      },
    });
  } catch (error) {
    console.error("Error exporting transactions:", error);
    return NextResponse.json(
      { error: "Failed to export transactions" },
      { status: 500 },
    );
  }
}
