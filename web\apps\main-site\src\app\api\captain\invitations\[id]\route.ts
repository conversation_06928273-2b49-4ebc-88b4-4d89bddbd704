import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import prisma from '@/lib/prisma';

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const captainId = user.id;
    const invitationId = params.id;

    // Verify captain owns a ship and the invitation belongs to them
    const ship = await prisma.ship.findFirst({
      where: {
        captainId,
        status: 'active'
      }
    });

    if (!ship) {
      return NextResponse.json({ error: 'Ship not found or you are not the captain' }, { status: 404 });
    }

    // Find the invitation and verify it belongs to this captain's ship
    const invitation = await prisma.shipJoinRequest.findFirst({
      where: {
        id: invitationId,
        shipId: ship.id,
        type: 'invite',
        status: 'pending'
      }
    });

    if (!invitation) {
      return NextResponse.json({ error: 'Invitation not found' }, { status: 404 });
    }

    // Cancel the invitation by updating its status
    await prisma.shipJoinRequest.update({
      where: { id: invitationId },
      data: { status: 'cancelled' }
    });

    return NextResponse.json({
      success: true,
      message: 'Invitation cancelled successfully'
    });

  } catch (error) {
    console.error('Error cancelling invitation:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}