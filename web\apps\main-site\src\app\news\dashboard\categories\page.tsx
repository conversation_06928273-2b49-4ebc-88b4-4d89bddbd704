"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "../../../../contexts/AuthContext";
import { NewsDashboardLayout } from "../../../../components/news";
import {
  useCategories,
  useCreateCategory,
  useUpdateCategory,
  useDeleteCategory,
} from "../../../../hooks/useNews";
import { Category } from "../../../../services/newsService";
import { useQuery } from "@tanstack/react-query";

export default function CategoriesPage() {
  const { user, isLoading: authLoading } = useAuth();
  const router = useRouter();
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [newCategory, setNewCategory] = useState("");
  const [newDescription, setNewDescription] = useState("");
  const [error, setError] = useState("");
  const [editingCategory, setEditingCategory] = useState<string | null>(null);
  const [editingCategoryId, setEditingCategoryId] = useState<string | null>(
    null,
  );
  const [viewMode, setViewMode] = useState<"card" | "table">("card");

  // Fetch news analytics data for article stats
  const { data: analyticsData } = useQuery({
    queryKey: ["newsAnalytics"],
    queryFn: async () => {
      const response = await fetch("/api/news/analytics");
      if (!response.ok) {
        throw new Error("Failed to fetch analytics");
      }
      return response.json();
    },
  });

  // Fetch categories with React Query
  const {
    data: categories,
    isLoading: categoriesLoading,
    isError: categoriesError,
    refetch: refetchCategories,
  } = useCategories();

  // Mutations
  const createCategoryMutation = useCreateCategory();
  const updateCategoryMutation = useUpdateCategory(editingCategoryId || "");
  const deleteCategoryMutation = useDeleteCategory();

  // Check authorization after auth is loaded
  useEffect(() => {
    // Only proceed with checks if auth loading is complete
    if (!authLoading) {
      if (!user || !user.roles?.editor) {
        router.push("/");
      } else {
        // User is authorized
        setIsAuthorized(true);
      }
    }
  }, [user, router, authLoading]);

  // Show loading state or nothing while checking authorization
  if (authLoading || !isAuthorized) {
    return (
      <div className="min-h-screen bg-secondary-dark flex items-center justify-center">
        <div className="bg-secondary-light p-6 rounded-lg shadow-md border border-gray-600">
          <p className="text-white text-lg">Loading...</p>
        </div>
      </div>
    );
  }

  // Handle category action (add or update)
  const handleCategoryAction = async () => {
    if (!newCategory.trim()) {
      setError("Category name cannot be empty");
      return;
    }

    // If we're editing a category
    if (editingCategory && editingCategoryId) {
      // Check if name already exists (excluding the current category)
      const nameExists = categories?.some(
        (cat: Category) =>
          cat.name.toLowerCase() === newCategory.toLowerCase() &&
          cat.id !== editingCategoryId,
      );

      if (nameExists) {
        setError("A category with this name already exists");
        return;
      }

      try {
        // Update the category using mutation
        await updateCategoryMutation.mutateAsync({
          name: newCategory,
          description: newDescription,
        });

        // Reset form
        setNewCategory("");
        setNewDescription("");
        setEditingCategory(null);
        setEditingCategoryId(null);
        setError("");
      } catch (error) {
        console.error("Error updating category:", error);
        setError("Failed to update category. Please try again.");
      }
    } else {
      // We're creating a new category
      // Check if category already exists
      if (
        categories?.some(
          (cat: Category) =>
            cat.name.toLowerCase() === newCategory.toLowerCase(),
        )
      ) {
        setError("A category with this name already exists");
        return;
      }

      try {
        // Create the new category using mutation
        await createCategoryMutation.mutateAsync({
          name: newCategory,
          description: newDescription,
        });

        // Reset form
        setNewCategory("");
        setNewDescription("");
        setError("");
      } catch (error) {
        console.error("Error creating category:", error);
        setError("Failed to create category. Please try again.");
      }
    }
  };

  // Handle edit button click
  const handleEditCategory = (
    categoryId: string,
    categoryName: string,
    description?: string,
  ) => {
    setNewCategory(categoryName);
    setNewDescription(description || "");
    setEditingCategory(categoryName);
    setEditingCategoryId(categoryId);
    setError("");
  };

  // Handle cancel edit
  const handleCancelEdit = () => {
    setNewCategory("");
    setNewDescription("");
    setEditingCategory(null);
    setEditingCategoryId(null);
    setError("");
  };

  // Handle category deletion
  const handleDeleteCategory = async (id: string, articleCount: number) => {
    if (articleCount > 0) {
      alert(
        "Cannot delete a category with articles. Move the articles to another category first.",
      );
      return;
    }

    if (
      window.confirm(
        "Are you sure you want to delete this category? This action cannot be undone.",
      )
    ) {
      try {
        await deleteCategoryMutation.mutateAsync(id);
      } catch (error) {
        console.error("Error deleting category:", error);
        alert("Failed to delete category. Please try again.");
      }
    }
  };

  // Process categories data to build statistics
  const dashboardData = categories
    ? {
        stats: {
          // Calculate total number of categories
          totalCategories: categories.length,

          // Calculate total number of articles across all categories
          totalArticles: categories.reduce(
            (sum, category) => sum + (category.articleCount || 0),
            0,
          ),

          // Find categories with most articles
          topCategories: [...categories]
            .sort((a, b) => (b.articleCount || 0) - (a.articleCount || 0))
            .slice(0, 5),

          // These are needed for the sidebar stats display - use analytics data if available
          publishedArticles: analyticsData?.counts?.published || 0,
          draftArticles: analyticsData?.counts?.draft || 0,
          totalViews:
            analyticsData?.topViewed?.reduce(
              (sum: number, article: any) => sum + (article.views || 0),
              0,
            ) || 0,
        },
      }
    : null;

  // Show loading state while fetching categories
  if (categoriesLoading) {
    return (
      <NewsDashboardLayout dashboardData={dashboardData}>
        <div className="space-y-6">
          <div className="bg-secondary-light rounded-lg shadow-md pb-0 p-2 sm:p-3 border border-gray-600">
            <div className="text-center py-8">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
              <p className="mt-2 text-gray-400">Loading categories...</p>
            </div>
          </div>
        </div>
      </NewsDashboardLayout>
    );
  }

  // Show error state if categories fetching failed
  if (categoriesError) {
    return (
      <NewsDashboardLayout dashboardData={dashboardData}>
        <div className="space-y-6">
          <div className="bg-secondary-light rounded-lg shadow-md pb-0 p-2 sm:p-3 border border-gray-600">
            <div className="bg-accent bg-opacity-20 text-accent border border-accent rounded-md p-4 mb-6">
              <p>Error loading categories. Please try again.</p>
              <button
                onClick={() => refetchCategories()}
                className="mt-2 text-sm underline"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      </NewsDashboardLayout>
    );
  }

  return (
    <NewsDashboardLayout dashboardData={dashboardData}>
      <div className="space-y-6">
        <div className="bg-secondary-light rounded-lg shadow-md pb-0 p-2 sm:p-3 border border-gray-600">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-2">
            <div className="flex items-center justify-between w-full sm:w-auto">
              <h2 className="text-xl sm:text-2xl font-bold text-white">
                Categories
              </h2>

              {/* View toggle buttons - Only on tablet and desktop */}
              <div className="sm:ml-4 flex space-x-2">
                <button
                  onClick={() => setViewMode("card")}
                  className={`hidden sm:flex items-center px-2 py-1 rounded ${
                    viewMode === "card"
                      ? "bg-primary text-white"
                      : "bg-secondary text-gray-400 hover:bg-secondary-dark"
                  }`}
                  aria-label="Card view"
                >
                  <svg
                    className="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zm10 0a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zm10 0a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"
                    />
                  </svg>
                </button>
                <button
                  onClick={() => setViewMode("table")}
                  className={`hidden sm:flex items-center px-2 py-1 rounded ${
                    viewMode === "table"
                      ? "bg-primary text-white"
                      : "bg-secondary text-gray-400 hover:bg-secondary-dark"
                  }`}
                  aria-label="Table view"
                >
                  <svg
                    className="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                    />
                  </svg>
                </button>
              </div>
            </div>
          </div>

          {/* Stats Cards - Mobile Only */}
          <div className="md:hidden grid grid-cols-2 gap-3 mb-4">
            <div className="bg-secondary-dark rounded-lg p-3 border border-gray-600">
              <h3 className="text-sm text-gray-400 mb-1">Total Categories</h3>
              <p className="text-xl font-bold text-white">
                {dashboardData?.stats.totalCategories || 0}
              </p>
            </div>
            <div className="bg-secondary-dark rounded-lg p-3 border border-gray-600">
              <h3 className="text-sm text-gray-400 mb-1">Total Articles</h3>
              <p className="text-xl font-bold text-white">
                {dashboardData?.stats.totalArticles || 0}
              </p>
            </div>
          </div>

          {/* Top Categories - Mobile Only */}
          {dashboardData?.stats.topCategories &&
            dashboardData.stats.topCategories.length > 0 && (
              <div className="md:hidden bg-secondary-dark rounded-lg p-3 mb-4 border border-gray-600">
                <h3 className="text-sm font-medium text-white mb-2">
                  Top Categories
                </h3>
                <div className="space-y-2">
                  {dashboardData.stats.topCategories.map(
                    (category: any, index: number) => (
                      <div
                        key={index}
                        className="flex justify-between items-center"
                      >
                        <span className="text-gray-400 text-sm truncate w-3/4">
                          {category.name}
                        </span>
                        <span className="text-white text-sm font-medium">
                          {category.articleCount || 0} articles
                        </span>
                      </div>
                    ),
                  )}
                </div>
              </div>
            )}

          {/* Category Form */}
          <div className="bg-secondary-dark rounded-lg p-3 mb-4 border border-gray-600">
            <h3 className="text-lg font-medium text-white mb-3">
              {editingCategory ? "Edit Category" : "Add New Category"}
            </h3>
            <div className="space-y-3">
              <input
                type="text"
                placeholder={
                  editingCategory ? "Edit category name" : "New category name"
                }
                className={`w-full px-4 py-2 bg-secondary border ${
                  error ? "border-accent" : "border-gray-600"
                } rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary`}
                value={newCategory}
                onChange={(e) => {
                  setNewCategory(e.target.value);
                  setError("");
                }}
              />
              <input
                type="text"
                placeholder="Description (optional)"
                className="w-full px-4 py-2 bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary"
                value={newDescription}
                onChange={(e) => setNewDescription(e.target.value)}
              />
              <div className="flex flex-col sm:flex-row gap-2">
                <button
                  className={`px-4 py-2 flex items-center justify-center ${
                    createCategoryMutation.isPending ||
                    updateCategoryMutation.isPending
                      ? "bg-gray-600 cursor-not-allowed"
                      : "bg-primary hover:bg-primary-dark"
                  } text-white rounded-md w-full sm:w-auto`}
                  onClick={handleCategoryAction}
                  disabled={
                    createCategoryMutation.isPending ||
                    updateCategoryMutation.isPending
                  }
                >
                  {(createCategoryMutation.isPending ||
                    updateCategoryMutation.isPending) && (
                    <svg
                      className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                  )}
                  {editingCategory ? "Update Category" : "Add Category"}
                </button>
                {editingCategory && (
                  <button
                    className="px-4 py-2 bg-secondary text-white rounded-md hover:bg-secondary-dark w-full sm:w-auto"
                    onClick={handleCancelEdit}
                    disabled={
                      createCategoryMutation.isPending ||
                      updateCategoryMutation.isPending
                    }
                  >
                    Cancel
                  </button>
                )}
              </div>
            </div>
            {error && <p className="mt-2 text-sm text-accent">{error}</p>}
            {editingCategory && (
              <p className="mt-2 text-sm text-primary">
                Editing: {editingCategory}
              </p>
            )}
          </div>

          {categories?.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-400">
                No categories found. Create a category to get started.
              </p>
            </div>
          ) : (
            <>
              {/* Card View - For mobile and optionally desktop */}
              <div
                className={`${
                  viewMode === "card" || !viewMode ? "" : "sm:hidden"
                } space-y-4`}
              >
                {/* For tablet and larger screens, show grid layout when in card view */}
                <div
                  className={`${
                    viewMode === "card"
                      ? "sm:grid sm:grid-cols-2 lg:grid-cols-3 sm:gap-6"
                      : ""
                  }`}
                >
                  {categories?.map((category: Category) => (
                    <div
                      key={category.id}
                      className={`bg-secondary-dark rounded-lg p-4 border border-gray-600 ${
                        viewMode === "card" ? "mb-4 sm:mb-0 flex flex-col" : ""
                      }`}
                    >
                      <div className="flex flex-col mb-3">
                        <h4 className="text-white font-medium text-sm sm:text-base">
                          {category.name}
                        </h4>
                        <p className="text-gray-400 text-xs sm:text-sm mt-1">
                          {category.description || "No description"}
                        </p>
                      </div>

                      <div className="flex justify-between items-center mb-3">
                        <div className="flex items-center space-x-2">
                          <span className="text-gray-400 text-xs">
                            Articles:
                          </span>
                          <span className="text-white text-xs font-medium">
                            {category.articleCount}
                          </span>
                        </div>
                      </div>

                      <div className="flex space-x-2 pt-2 border-t border-gray-600 mt-auto">
                        <button
                          className="flex-1 bg-secondary text-center text-sm py-1 rounded-md text-white hover:bg-secondary-dark"
                          onClick={() =>
                            handleEditCategory(
                              category.id,
                              category.name,
                              category.description || "",
                            )
                          }
                          disabled={deleteCategoryMutation.isPending}
                        >
                          Edit
                        </button>
                        <button
                          className={`flex-1 text-center text-sm py-1 rounded-md ${
                            category.articleCount > 0
                              ? "bg-gray-700 text-gray-400 cursor-not-allowed"
                              : "bg-accent bg-opacity-20 text-white hover:bg-accent hover:bg-opacity-30"
                          }`}
                          onClick={() =>
                            handleDeleteCategory(
                              category.id,
                              category.articleCount,
                            )
                          }
                          disabled={
                            category.articleCount > 0 ||
                            deleteCategoryMutation.isPending
                          }
                        >
                          Delete
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Table View - Now optionally hidden on all screens based on viewMode */}
              <div
                className={`${
                  viewMode === "table" ? "block" : "hidden sm:hidden"
                } overflow-x-auto`}
              >
                <table className="min-w-full divide-y divide-gray-600">
                  <thead className="bg-secondary">
                    <tr>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"
                      >
                        Category
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"
                      >
                        Description
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"
                      >
                        Articles
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"
                      >
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-secondary-light divide-y divide-gray-600">
                    {categories?.map((category: Category) => (
                      <tr key={category.id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-white">
                            {category.name}
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm text-gray-400">
                            {category.description || "-"}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-400">
                            {category.articleCount}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-3">
                            <button
                              className="text-primary hover:text-primary-light"
                              onClick={() =>
                                handleEditCategory(
                                  category.id,
                                  category.name,
                                  category.description || "",
                                )
                              }
                              disabled={deleteCategoryMutation.isPending}
                            >
                              Edit
                            </button>
                            <button
                              className={`${
                                category.articleCount > 0
                                  ? "text-gray-500 cursor-not-allowed"
                                  : "text-accent hover:text-accent-light"
                              }`}
                              onClick={() =>
                                handleDeleteCategory(
                                  category.id,
                                  category.articleCount,
                                )
                              }
                              disabled={
                                category.articleCount > 0 ||
                                deleteCategoryMutation.isPending
                              }
                            >
                              Delete
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </>
          )}
        </div>
      </div>
    </NewsDashboardLayout>
  );
}
