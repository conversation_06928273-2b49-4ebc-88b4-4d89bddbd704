import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// GET /api/volunteer/public/shifts/[id] - Get a specific shift
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const { id: shiftId } = params;

    // Check if user is authenticated
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 },
      );
    }

    // Get the shift
    const shift = await prisma.volunteerShift.findUnique({
      where: { id: shiftId },
      select: {
        id: true,
        title: true,
        description: true,
        startTime: true,
        endTime: true,
        location: true,
        maxVolunteers: true,
        categoryId: true,
        eventId: true,
        // Include category for name and pay rate
        category: {
          select: {
            name: true,
            payRate: true,
          },
        },
        // Include event for name and status
        event: {
          select: {
            name: true,
            status: true,
          },
        },
        // Include assignments to calculate vacancies
        assignments: {
          select: {
            id: true,
            status: true,
          },
          where: {
            status: {
              notIn: ["cancelled", "no_show"],
            },
          },
        },
      },
    });

    if (!shift) {
      return NextResponse.json({ error: "Shift not found" }, { status: 404 });
    }

    // Check if event is published
    if (shift.event.status !== "published") {
      return NextResponse.json(
        { error: "Shift not available" },
        { status: 403 },
      );
    }

    // Calculate vacancies
    const { assignments, ...shiftData } = shift;
    const vacancies = shift.maxVolunteers - assignments.length;

    return NextResponse.json({
      shift: {
        ...shiftData,
        vacancies,
      },
    });
  } catch (error) {
    console.error("Error fetching shift:", error);
    return NextResponse.json(
      { error: "Failed to fetch shift" },
      { status: 500 },
    );
  }
}
