import React from "react";

interface RedeemPayCodeConfirmationModalProps {
  redeemDetails: {
    amount: string;
    createdBy: string;
    code: string;
  };
  userData: {
    balance?: number;
  } | null;
  onConfirm: () => void;
  onCancel: () => void;
}

export const RedeemPayCodeConfirmationModal: React.FC<
  RedeemPayCodeConfirmationModalProps
> = ({ redeemDetails, userData, onConfirm, onCancel }) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-secondary-light rounded-lg p-6 max-w-md w-full">
        <h3 className="text-xl font-bold text-white mb-4">
          Confirm Pay-Code Redemption
        </h3>
        <p className="mb-4 text-white">
          You are about to pay{" "}
          <span className="font-bold">NS {redeemDetails.amount}</span> to{" "}
          <span className="font-bold">{redeemDetails.createdBy}</span>.
        </p>
        <p className="mb-4 text-white">
          Your current balance:{" "}
          <span className="font-bold">NS {userData?.balance || 0}</span>
        </p>
        <p className="mb-4 text-white">
          New balance after payment:{" "}
          <span className="font-bold">
            NS{" "}
            {Math.round(
              (userData?.balance || 0) - parseFloat(redeemDetails.amount),
            )}
          </span>
        </p>
        <div className="flex justify-end space-x-4">
          <button
            onClick={onCancel}
            className="px-4 py-2 border border-gray-600 rounded-md text-white hover:bg-secondary"
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark"
          >
            Confirm
          </button>
        </div>
      </div>
    </div>
  );
};
