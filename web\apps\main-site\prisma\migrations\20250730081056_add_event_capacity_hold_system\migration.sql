-- CreateTable
CREATE TABLE `event_capacity_holds` (
    `id` VARCHAR(191) NOT NULL,
    `eventId` VARCHAR(191) NOT NULL,
    `userId` VARCHAR(191) NOT NULL,
    `cartItemId` VARCHAR(191) NOT NULL,
    `quantity` INTEGER NOT NULL,
    `expiresAt` DATETIME(3) NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `event_capacity_holds_cartItemId_key`(`cartItemId`),
    INDEX `event_capacity_holds_eventId_idx`(`eventId`),
    INDEX `event_capacity_holds_userId_idx`(`userId`),
    INDEX `event_capacity_holds_expiresAt_idx`(`expiresAt`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `event_capacity_holds` ADD CONSTRAINT `event_capacity_holds_eventId_fkey` FOREIGN KEY (`eventId`) REFERENCES `events`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `event_capacity_holds` ADD CONSTRAINT `event_capacity_holds_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `event_capacity_holds` ADD CONSTRAINT `event_capacity_holds_cartItemId_fkey` FOREIGN KEY (`cartItemId`) REFERENCES `cart_items`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
