import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser, userHasRole } from "@/lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// GET /api/admin/events/[id] - Get a specific event
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const currentUser = await getCurrentUser(req);

    // Check if user is authenticated and is an admin
    if (!currentUser || !(await userHasRole(req, "admin"))) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = params;

    const event = await prisma.event.findUnique({
      where: { id },
      include: {
        category: true,
        createdBy: {
          select: {
            id: true,
            username: true,
            displayName: true,
          },
        },
      },
    });

    if (!event) {
      return NextResponse.json({ error: "Event not found" }, { status: 404 });
    }

    return NextResponse.json(event);
  } catch (error) {
    console.error("Error fetching event:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}

// PUT /api/admin/events/[id] - Update an event
export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const currentUser = await getCurrentUser(req);

    // Check if user is authenticated and is an admin
    if (!currentUser || !(await userHasRole(req, "admin"))) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = params;
    const {
      name,
      description,
      shortDescription,
      startDate,
      endDate,
      location,
      address,
      virtualLink,
      isVirtual,
      image,
      status,
      capacity,
      categoryId,
    } = await req.json();

    // Validate required fields
    if (!name || !description || !startDate || !endDate || !categoryId) {
      return NextResponse.json(
        {
          error:
            "Missing required fields: name, description, startDate, endDate, and categoryId are required",
        },
        { status: 400 },
      );
    }

    // Validate dates
    const startDateTime = new Date(startDate);
    const endDateTime = new Date(endDate);

    if (isNaN(startDateTime.getTime()) || isNaN(endDateTime.getTime())) {
      return NextResponse.json(
        { error: "Invalid date format for startDate or endDate" },
        { status: 400 },
      );
    }

    if (endDateTime < startDateTime) {
      return NextResponse.json(
        { error: "endDate must be after startDate" },
        { status: 400 },
      );
    }

    // Check if event exists
    const existingEvent = await prisma.event.findUnique({
      where: { id },
    });

    if (!existingEvent) {
      return NextResponse.json({ error: "Event not found" }, { status: 404 });
    }

    // Check if category exists
    const category = await prisma.eventCategory.findUnique({
      where: { id: categoryId },
    });

    if (!category) {
      return NextResponse.json(
        { error: "Category not found" },
        { status: 400 },
      );
    }

    // Update event
    const updatedEvent = await prisma.event.update({
      where: { id },
      data: {
        name,
        description,
        shortDescription,
        startDate: startDateTime,
        endDate: endDateTime,
        location,
        address,
        virtualLink,
        isVirtual: isVirtual || false,
        image,
        status,
        capacity: capacity ? parseInt(capacity) : null,
        categoryId,
      },
    });

    return NextResponse.json(updatedEvent);
  } catch (error) {
    console.error("Error updating event:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}

// DELETE /api/admin/events/[id] - Delete/cancel an event
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const currentUser = await getCurrentUser(req);

    // Check if user is authenticated and is an admin
    if (!currentUser || !(await userHasRole(req, "admin"))) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = params;

    // Check if event exists
    const existingEvent = await prisma.event.findUnique({
      where: { id },
    });

    if (!existingEvent) {
      return NextResponse.json({ error: "Event not found" }, { status: 404 });
    }

    // For published events, we'll set status to 'cancelled' instead of deleting
    if (existingEvent.status === "published") {
      const cancelledEvent = await prisma.event.update({
        where: { id },
        data: {
          status: "cancelled",
        },
      });

      return NextResponse.json({
        success: true,
        message: "Event cancelled successfully",
        event: cancelledEvent,
      });
    }

    // For draft or other events, we'll delete them
    await prisma.event.delete({
      where: { id },
    });

    return NextResponse.json({
      success: true,
      message: "Event deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting event:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}
