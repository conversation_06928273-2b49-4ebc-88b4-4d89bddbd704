import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth";

export const dynamic = 'force-dynamic';

export async function GET(request: Request) {
  try {
    const currentUser = await getCurrentUser(request);

    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Check if user is a land steward or admin
    if (!currentUser.isLandSteward && !currentUser.isAdmin) {
      return NextResponse.json(
        { error: "Land Steward or Admin permissions required" },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status') || 'pending';

    const deletionRequests = await prisma.shipDeletionRequest.findMany({
      where: status !== 'all' ? { status } : {},
      include: {
        ship: {
          select: {
            id: true,
            name: true,
            status: true,
            _count: {
              select: {
                members: {
                  where: { status: 'active' },
                },
              },
            },
          },
        },
        requestedBy: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
        reviewedBy: {
          select: {
            id: true,
            username: true,
            displayName: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    const formattedRequests = deletionRequests.map(request => ({
      id: request.id,
      reason: request.reason,
      status: request.status,
      message: request.message,
      createdAt: request.createdAt.toISOString(),
      reviewedAt: request.reviewedAt?.toISOString(),
      ship: {
        id: request.ship.id,
        name: request.ship.name,
        status: request.ship.status,
        memberCount: request.ship._count.members,
      },
      requestedBy: request.requestedBy,
      reviewedBy: request.reviewedBy,
    }));

    return NextResponse.json(formattedRequests);
  } catch (error) {
    console.error("Error fetching deletion requests:", error);
    return NextResponse.json(
      { error: "Failed to fetch deletion requests" },
      { status: 500 }
    );
  }
}