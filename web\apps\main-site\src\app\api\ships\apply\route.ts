import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth";

export const dynamic = 'force-dynamic';

export async function POST(request: Request) {
  try {
    const currentUser = await getCurrentUser(request);

    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    const { shipName, description, tags, logoPath } = await request.json();

    // Validate required fields
    if (!shipName || !description) {
      return NextResponse.json(
        { error: "Ship name and description are required" },
        { status: 400 }
      );
    }

    // Check if user is already a captain
    const existingCaptainship = await prisma.ship.findFirst({
      where: {
        captainId: currentUser.id,
        status: { in: ['active', 'pending_deletion'] },
      },
    });

    if (existingCaptainship) {
      return NextResponse.json(
        { error: "You are already a captain of a ship" },
        { status: 400 }
      );
    }

    // Check if user is already a member of a ship
    const existingMembership = await prisma.shipMember.findFirst({
      where: {
        userId: currentUser.id,
        status: 'active',
      },
    });

    if (existingMembership) {
      return NextResponse.json(
        { error: "You are already a member of a ship" },
        { status: 400 }
      );
    }

    // Check if ship name is already taken
    const existingShip = await prisma.ship.findFirst({
      where: {
        name: shipName,
        status: { not: 'deleted' },
      },
    });

    if (existingShip) {
      return NextResponse.json(
        { error: "Ship name is already taken" },
        { status: 400 }
      );
    }

    // Check for existing pending application
    const existingApplication = await prisma.captainApplication.findFirst({
      where: {
        userId: currentUser.id,
        status: 'pending',
      },
    });

    if (existingApplication) {
      return NextResponse.json(
        { error: "You already have a pending captain application" },
        { status: 400 }
      );
    }

    // Check if user was previously rejected (for marking)
    const previousRejection = await prisma.captainApplication.findFirst({
      where: {
        userId: currentUser.id,
        status: 'rejected',
      },
    });

    // Create captain application
    const application = await prisma.captainApplication.create({
      data: {
        userId: currentUser.id,
        shipName,
        description,
        tags: tags || [],
        logoPath,
        status: 'pending',
        previouslyRejected: !!previousRejection,
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      message: "Captain application submitted successfully",
      application: {
        id: application.id,
        shipName: application.shipName,
        status: application.status,
        appliedAt: application.appliedAt.toISOString(),
        previouslyRejected: application.previouslyRejected,
      },
    });
  } catch (error) {
    console.error("Error creating captain application:", error);
    return NextResponse.json(
      { error: "Failed to submit captain application" },
      { status: 500 }
    );
  }
}