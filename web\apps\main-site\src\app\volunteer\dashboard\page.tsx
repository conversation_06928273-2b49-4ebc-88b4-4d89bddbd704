"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import {
  VolunteerDashboardLayout,
  DashboardMain,
} from "@/components/volunteer";

export default function VolunteerDashboardPage() {
  const { user, isLoading } = useAuth();
  const [isAuthorized, setIsAuthorized] = useState(false);
  const router = useRouter();

  // Check if user is authorized to access this page
  useEffect(() => {
    if (!isLoading) {
      if (!user || !user.roles?.volunteerCoordinator) {
        router.push("/");
      } else {
        setIsAuthorized(true);
      }
    }
  }, [user, isLoading, router]);

  // Show loading state while checking authorization
  if (isLoading || !isAuthorized) {
    return (
      <div className="min-h-screen bg-secondary-dark flex items-center justify-center">
        <div className="bg-secondary-light p-6 rounded-lg shadow-md border border-gray-600">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary mx-auto"></div>
          <p className="text-white text-lg mt-4">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <VolunteerDashboardLayout>
      <DashboardMain />
    </VolunteerDashboardLayout>
  );
}
