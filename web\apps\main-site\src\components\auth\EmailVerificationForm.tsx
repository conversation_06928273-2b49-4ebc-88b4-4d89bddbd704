"use client";

import React, { useState } from "react";
import { Input, Button } from "@bank-of-styx/ui";
import toast from "react-hot-toast";
import { useAuth } from "../../contexts/AuthContext";
import fetchClient from "@/lib/fetchClient";

interface EmailVerificationFormProps {
  onVerificationSuccess: (password: string) => void;
}

export const EmailVerificationForm = ({
  onVerificationSuccess,
}: EmailVerificationFormProps) => {
  const { user } = useAuth();
  const [verificationCode, setVerificationCode] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isSendingCode, setIsSendingCode] = useState(false);
  const [codeSent, setCodeSent] = useState(false);
  const [error, setError] = useState("");

  const handleSendCode = async () => {
    setError("");
    setIsSendingCode(true);

    try {
      await fetchClient.post("/api/auth/verify-email", {});

      setCodeSent(true);
      toast.success("Verification code sent to your email");
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred");
      toast.error(
        err instanceof Error ? err.message : "Failed to send verification code",
      );
    } finally {
      setIsSendingCode(false);
    }
  };

  const handleVerifyCode = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setIsLoading(true);

    try {
      if (!verificationCode) {
        throw new Error("Verification code is required");
      }

      const response = await fetchClient.put("/api/auth/verify-email", {
        code: verificationCode,
      });

      // Type check the response data
      if (
        typeof response === "object" &&
        response !== null &&
        "password" in response
      ) {
        onVerificationSuccess(response.password as string);
      } else {
        throw new Error("Invalid response format from server");
      }
      toast.success("Email verified successfully");
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred");
      toast.error(err instanceof Error ? err.message : "Failed to verify code");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      <div className="mb-4">
        <h3 className="text-lg font-medium text-white mb-2">
          Verify Your Email
        </h3>
        <p className="text-sm text-gray-300">
          To secure your account, please verify your email address. We'll send a
          verification code to {user?.email}.
        </p>
      </div>

      {error && (
        <div className="p-3 text-sm rounded-lg bg-accent bg-opacity-20 text-accent border border-accent">
          {error}
        </div>
      )}

      {!codeSent ? (
        <div className="flex justify-center">
          <Button
            type="button"
            variant="primary"
            onClick={handleSendCode}
            loading={isSendingCode}
          >
            Send Verification Code
          </Button>
        </div>
      ) : (
        <form onSubmit={handleVerifyCode} className="space-y-4">
          <Input
            type="text"
            label="Verification Code"
            value={verificationCode}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              setVerificationCode(e.target.value)
            }
            placeholder="Enter the code sent to your email"
            fullWidth
            helperText="Check your email for a 6-character code"
          />

          <div className="flex justify-between items-center">
            <Button
              type="button"
              variant="outline"
              onClick={() => handleSendCode()}
              disabled={isLoading || isSendingCode}
            >
              Resend Code
            </Button>
            <Button type="submit" variant="primary" loading={isLoading}>
              Verify
            </Button>
          </div>
        </form>
      )}
    </div>
  );
};
