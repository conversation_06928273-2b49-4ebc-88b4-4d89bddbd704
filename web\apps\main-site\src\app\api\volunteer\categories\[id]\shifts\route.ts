import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser, userHasRole } from "@/lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// GET /api/volunteer/categories/[id]/shifts - Get shifts for a specific category
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const { id: categoryId } = params;

    // Check if user is authenticated and has volunteer coordinator role
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 },
      );
    }

    const hasCoordinatorRole = await userHasRole(req, "volunteerCoordinator");
    if (!hasCoordinatorRole) {
      return NextResponse.json(
        { error: "Unauthorized - Coordinator role required" },
        { status: 403 },
      );
    }

    // Check if category exists
    const category = await prisma.volunteerCategory.findUnique({
      where: { id: categoryId },
    });

    if (!category) {
      return NextResponse.json(
        { error: "Category not found" },
        { status: 404 },
      );
    }

    // Get shifts for the category
    const shifts = await prisma.volunteerShift.findMany({
      where: {
        categoryId: categoryId,
      },
      include: {
        _count: {
          select: {
            assignments: true,
          },
        },
        assignments: {
          select: {
            status: true,
          },
        },
      },
      orderBy: {
        startTime: "asc",
      },
    });

    // Transform shifts to include statistics
    const shiftsWithStats = shifts.map((shift) => {
      const totalAssignments = shift._count.assignments;
      const completedAssignments = shift.assignments.filter(
        (assignment) => assignment.status === "completed",
      ).length;

      return {
        id: shift.id,
        title: shift.title,
        description: shift.description,
        startTime: shift.startTime,
        endTime: shift.endTime,
        location: shift.location,
        maxVolunteers: shift.maxVolunteers,
        eventId: shift.eventId,
        categoryId: shift.categoryId,
        isAutomated: shift.isAutomated,
        createdAt: shift.createdAt,
        updatedAt: shift.updatedAt,
        stats: {
          totalAssignments,
          completedAssignments,
        },
      };
    });

    return NextResponse.json(shiftsWithStats);
  } catch (error) {
    console.error("Error fetching shifts:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch shifts",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    );
  }
}
