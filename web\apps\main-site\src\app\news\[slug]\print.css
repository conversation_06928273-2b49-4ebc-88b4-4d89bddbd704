/* Print styles for news articles */
@media print {
  /* Hide navigation, footer, and other non-essential elements */
  header, footer, nav, .sidebar, button, .share-buttons {
    display: none !important;
  }

  /* Reset background colors and text colors for better printing */
  body, article, div, p, h1, h2, h3, h4, h5, h6 {
    background-color: white !important;
    color: black !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  /* Ensure the article content is visible and properly formatted */
  .container {
    width: 100% !important;
    max-width: 100% !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  article {
    border: none !important;
    box-shadow: none !important;
    padding: 0 !important;
  }

  /* Add page breaks where appropriate */
  h1, h2 {
    page-break-after: avoid;
  }

  p {
    page-break-inside: avoid;
  }

  /* Format the article header */
  .article-header {
    margin-bottom: 1cm !important;
  }

  /* Format the article content */
  .prose {
    font-size: 12pt !important;
    line-height: 1.5 !important;
  }

  /* Add the URL at the bottom of the printed page */
  article::after {
    content: "Source: " attr(data-url);
    display: block;
    text-align: center;
    font-size: 10pt;
    margin-top: 2cm;
  }
}
