import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth";

export const dynamic = 'force-dynamic';

export async function POST(request: Request, { params }: { params: { id: string } }) {
  try {
    const currentUser = await getCurrentUser(request);

    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    if (!currentUser.isAdmin && !currentUser.isLandSteward) {
      return NextResponse.json(
        { error: "Land Steward or Admin access required" },
        { status: 403 }
      );
    }

    const applicationId = params.id;
    const { slogan } = await request.json();

    // Get the application
    const application = await prisma.captainApplication.findUnique({
      where: { id: applicationId },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
      },
    });

    if (!application) {
      return NextResponse.json(
        { error: "Application not found" },
        { status: 404 }
      );
    }

    if (application.status !== 'pending') {
      return NextResponse.json(
        { error: "Application has already been reviewed" },
        { status: 400 }
      );
    }

    // Check if ship name is still available
    const existingShip = await prisma.ship.findFirst({
      where: {
        name: application.shipName,
        status: { not: 'deleted' },
      },
    });

    if (existingShip) {
      return NextResponse.json(
        { error: "Ship name is no longer available" },
        { status: 400 }
      );
    }

    // Use transaction to create ship and update application
    const result = await prisma.$transaction(async (tx) => {
      // Create the ship
      const ship = await tx.ship.create({
        data: {
          name: application.shipName,
          description: application.description,
          slogan: slogan || null,
          logo: application.logoPath,
          tags: application.tags as any,
          captainId: application.userId,
          status: 'active',
        },
        include: {
          captain: {
            select: {
              id: true,
              username: true,
              displayName: true,
              avatar: true,
            },
          },
        },
      });

      // Add captain as first member
      await tx.shipMember.create({
        data: {
          shipId: ship.id,
          userId: application.userId,
          role: 'Captain',
          status: 'active',
          joinedAt: new Date(),
        },
      });

      // Update application status
      await tx.captainApplication.update({
        where: { id: applicationId },
        data: {
          status: 'approved',
          reviewedAt: new Date(),
          reviewedById: currentUser.id,
        },
      });

      return ship;
    });

    return NextResponse.json({
      success: true,
      message: "Application approved and ship created successfully",
      ship: {
        id: result.id,
        name: result.name,
        description: result.description,
        slogan: result.slogan,
        logo: result.logo,
        tags: result.tags as string[],
        captain: result.captain,
        memberCount: 1,
        createdAt: result.createdAt.toISOString(),
      },
    });
  } catch (error) {
    console.error("Error approving captain application:", error);
    return NextResponse.json(
      { error: "Failed to approve application" },
      { status: 500 }
    );
  }
}