import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser, userHasRole } from "@/lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
interface Params {
  params: {
    id: string;
  };
}

export async function PATCH(request: Request, { params }: Params) {
  const { id } = params;

  try {
    // Check if user is authenticated and has admin role
    const currentUser = await getCurrentUser(request);
    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    const isAdmin = await userHasRole(request, "admin");
    if (!isAdmin) {
      return NextResponse.json(
        { error: "Admin privileges required" },
        { status: 403 },
      );
    }

    // Get request body
    const { email } = await request.json();

    // Validate email
    if (!email || !email.includes("@")) {
      return NextResponse.json(
        { error: "Valid email is required" },
        { status: 400 },
      );
    }

    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { id },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Check if email is already taken by another user
    const existingUser = await prisma.user.findFirst({
      where: {
        email,
        NOT: {
          id,
        },
      },
    });

    if (existingUser) {
      return NextResponse.json(
        { error: "Email is already in use by another user" },
        { status: 409 },
      );
    }

    // Update user email
    const updatedUser = await prisma.user.update({
      where: { id },
      data: {
        email,
        isEmailVerified: false, // Reset verification status
      },
      select: {
        id: true,
        username: true,
        displayName: true,
        avatar: true,
        email: true,
        isEmailVerified: true,
        isAdmin: true,
        isEditor: true,
        isBanker: true,
        isChatModerator: true,
        isVolunteerCoordinator: true,
        isLeadManager: true,
        createdAt: true,
      },
    });

    // Transform user to match the AdminUser interface
    const transformedUser = {
      id: updatedUser.id,
      username: updatedUser.username,
      displayName: updatedUser.displayName,
      avatar: updatedUser.avatar,
      email: updatedUser.email,
      isEmailVerified: updatedUser.isEmailVerified,
      status: "active", // Default status since it's not in the database yet
      roles: {
        admin: updatedUser.isAdmin,
        editor: updatedUser.isEditor,
        banker: updatedUser.isBanker,
        chatModerator: updatedUser.isChatModerator,
        volunteerCoordinator: updatedUser.isVolunteerCoordinator,
        leadManager: updatedUser.isLeadManager,
      },
      createdAt: updatedUser.createdAt.toISOString(),
    };

    return NextResponse.json(transformedUser);
  } catch (error) {
    console.error("Error updating user email:", error);
    return NextResponse.json(
      { error: "Failed to update user email" },
      { status: 500 },
    );
  }
}
