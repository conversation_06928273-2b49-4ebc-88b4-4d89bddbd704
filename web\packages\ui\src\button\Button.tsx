import React from "react";

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /**
   * Button variant
   */
  variant?: "primary" | "secondary" | "accent" | "outline" | "ghost";
  /**
   * Button size
   */
  size?: "sm" | "md" | "lg";
  /**
   * Is button full width
   */
  fullWidth?: boolean;
  /**
   * Is button disabled
   */
  disabled?: boolean;
  /**
   * Is button in loading state
   */
  loading?: boolean;
  /**
   * Button children
   */
  children: React.ReactNode;
}

export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      variant = "primary",
      size = "md",
      fullWidth = false,
      disabled = false,
      loading = false,
      className = "",
      children,
      ...props
    },
    ref,
  ) => {
    // Base classes
    const baseClasses =
      "inline-flex items-center justify-center rounded font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2";

    // Size classes
    const sizeClasses = {
      sm: "px-3 py-1.5 text-sm",
      md: "px-4 py-2 text-base",
      lg: "px-6 py-3 text-lg",
    };

    // Variant classes
    const variantClasses = {
      primary:
        "bg-primary text-text-primary hover:bg-primary-dark focus:ring-primary",
      secondary:
        "bg-secondary text-text-primary hover:bg-secondary-dark focus:ring-secondary",
      accent:
        "bg-accent text-text-primary hover:bg-accent-dark focus:ring-accent",
      outline:
        "border border-gray-600 bg-transparent text-text-primary hover:bg-secondary-light focus:ring-gray-400",
      ghost:
        "bg-transparent text-text-primary hover:bg-secondary-light focus:ring-gray-400",
    };

    // Width classes
    const widthClasses = fullWidth ? "w-full" : "";

    // Disabled classes
    const disabledClasses =
      disabled || loading ? "opacity-50 cursor-not-allowed" : "";

    return (
      <button
        ref={ref}
        disabled={disabled || loading}
        className={`
          ${baseClasses}
          ${sizeClasses[size]}
          ${variantClasses[variant]}
          ${widthClasses}
          ${disabledClasses}
          ${className}
        `}
        {...props}
      >
        {loading && (
          <svg
            className="animate-spin -ml-1 mr-2 h-4 w-4 text-current"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
        )}
        {children}
      </button>
    );
  },
);

Button.displayName = "Button";
