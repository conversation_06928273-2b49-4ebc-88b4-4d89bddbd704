"use client";

import { useCart, useUpdateCartItem, useRemoveCartItem } from "@/hooks/useCart";
import { <PERSON><PERSON>, <PERSON>, Spinner } from "@bank-of-styx/ui";
import Link from "next/link";
import { useState } from "react";
import { toast } from "react-hot-toast";
import { useAuth } from "@/contexts/AuthContext";
import { CartItemCard } from "@/components/shop/CartItemCard";

export default function CartPage() {
  const { user, isAuthenticated, openAuthModal } = useAuth();
  const { data, isLoading, error } = useCart();
  const updateCartItem = useUpdateCartItem();
  const removeCartItem = useRemoveCartItem();
  const [processingItems, setProcessingItems] = useState<Set<string>>(
    new Set(),
  );

  const handleQuantityChange = async (itemId: string, quantity: number) => {
    if (quantity < 1) return;

    // Find the item to check if it's a code redemption
    const cartItem = data?.cart?.items.find(item => item.id === itemId);
    if (cartItem?.isCodeRedemption) {
      toast.error("Quantity cannot be changed for items added via redemption codes");
      return;
    }

    setProcessingItems((prev) => new Set(prev).add(itemId));

    try {
      await updateCartItem.mutateAsync({ itemId, quantity });
    } catch (error) {
      toast.error("Failed to update quantity");
    } finally {
      setProcessingItems((prev) => {
        const newSet = new Set(prev);
        newSet.delete(itemId);
        return newSet;
      });
    }
  };

  const handleRemoveItem = async (itemId: string) => {
    setProcessingItems((prev) => new Set(prev).add(itemId));

    try {
      await removeCartItem.mutateAsync(itemId);
      toast.success("Item removed from cart");
    } catch (error) {
      toast.error("Failed to remove item");
    } finally {
      setProcessingItems((prev) => {
        const newSet = new Set(prev);
        newSet.delete(itemId);
        return newSet;
      });
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="p-8 text-center">
          <h2 className="text-2xl font-bold mb-4">Sign In Required</h2>
          <p className="mb-6">Please sign in to view your shopping cart.</p>
          <Button variant="primary" onClick={openAuthModal}>
            Sign In
          </Button>
        </Card>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8 flex justify-center">
        <Spinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="p-8 text-center">
          <h2 className="text-2xl font-bold mb-4">Error</h2>
          <p className="mb-6">
            Failed to load your cart. Please try again later.
          </p>
          <Button variant="primary" onClick={() => window.location.reload()}>
            Retry
          </Button>
        </Card>
      </div>
    );
  }

  const cart = data?.cart;
  const isEmpty = !cart?.items.length;

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">Your Cart</h1>

      {isEmpty ? (
        <Card className="p-8 text-center">
          <h2 className="text-2xl font-bold mb-4">Your cart is empty</h2>
          <p className="mb-6">Add some products to your cart to get started.</p>
          <Link href="/shop">
            <Button variant="primary">Continue Shopping</Button>
          </Link>
        </Card>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Cart Items */}
          <div className="lg:col-span-2">
            <Card>
              <div className="divide-y divide-border-subtle">
                {cart?.items.map((item) => (
                  <CartItemCard
                    key={item.id}
                    item={item}
                    onQuantityChange={handleQuantityChange}
                    onRemove={handleRemoveItem}
                    isProcessing={processingItems.has(item.id)}
                  />
                ))}
              </div>
            </Card>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <Card title="Order Summary" className="sticky top-24">
              <div className="p-4">
                <div className="flex justify-between mb-2">
                  <span>Subtotal</span>
                  <span>${cart?.subtotal.toFixed(2)}</span>
                </div>
                <div className="border-t border-border-subtle my-4"></div>
                <div className="flex justify-between font-bold text-lg mb-6">
                  <span>Total</span>
                  <span>${cart?.subtotal.toFixed(2)}</span>
                </div>
                <Link href="/shop/checkout">
                  <Button variant="primary" fullWidth>
                    Proceed to Checkout
                  </Button>
                </Link>
                <div className="mt-4">
                  <Link href="/shop">
                    <Button variant="primary" fullWidth>
                      Continue Shopping
                    </Button>
                  </Link>
                </div>
              </div>
            </Card>
          </div>
        </div>
      )}
    </div>
  );
}
