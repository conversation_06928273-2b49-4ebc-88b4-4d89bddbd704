const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function updateJoinRequestTypes() {
  try {
    console.log('Checking join request types...');
    
    // Show current status of all records
    const allRecords = await prisma.shipJoinRequest.findMany({
      select: {
        id: true,
        type: true,
        status: true,
        createdAt: true,
        userId: true,
        requestedById: true
      },
      where: {
        status: 'pending'
      }
    });
    
    console.log(`\nFound ${allRecords.length} pending join request records:`);
    allRecords.forEach(record => {
      const isInvite = record.type === 'invite';
      const isRequest = record.type === 'request';
      const typeDesc = isInvite ? 'INVITE (captain->user)' : isRequest ? 'REQUEST (user->captain)' : `UNKNOWN TYPE: ${record.type}`;
      console.log(`ID: ${record.id.substring(0, 8)}..., Type: ${typeDesc}, Created: ${record.createdAt.toLocaleDateString()}`);
      console.log(`  User: ${record.userId.substring(0, 8)}..., RequestedBy: ${record.requestedById.substring(0, 8)}...`);
    });
    
    // Update any records that might have empty or invalid type to 'request' (backwards compatibility)
    const result = await prisma.shipJoinRequest.updateMany({
      where: {
        AND: [
          { status: 'pending' },
          { 
            NOT: {
              OR: [
                { type: 'invite' },
                { type: 'request' }
              ]
            }
          }
        ]
      },
      data: {
        type: 'request'
      }
    });

    if (result.count > 0) {
      console.log(`\nUpdated ${result.count} records to type 'request' for backwards compatibility`);
    } else {
      console.log(`\nNo records needed updating - all have valid types.`);
    }
    
  } catch (error) {
    console.error('Error checking join request types:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updateJoinRequestTypes();