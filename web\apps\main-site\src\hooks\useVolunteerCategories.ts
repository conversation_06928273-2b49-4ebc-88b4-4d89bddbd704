"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import fetchClient from "@/lib/fetchClient";

// Types
export interface VolunteerCategoryLead {
  id: string;
  displayName: string;
  email: string;
  avatar: string | null;
}

export interface VolunteerCategoryStats {
  totalShifts: number;
  totalSignups: number;
  completedShifts: number;
}

export interface VolunteerCategory {
  id: string;
  name: string;
  description: string | null;
  payRate: number | null;
  eventId: string;
  createdAt: string;
  updatedAt: string;
  leadManagerId: string | null;
  leadManager: VolunteerCategoryLead | null;
  stats: VolunteerCategoryStats;
}

export interface CreateCategoryData {
  name: string;
  description?: string;
  payRate?: number;
  leadManagerId?: string;
}

export interface UpdateCategoryData {
  name: string;
  description?: string;
  payRate?: number;
  leadManagerId?: string;
}

// Using the centralized fetch client instead of local helper functions

// Query keys
export const volunteerQueryKeys = {
  categories: "volunteerCategories",
  categoryByEvent: (eventId: string) => ["volunteerCategories", eventId],
  category: (id: string) => ["volunteerCategory", id],
};

/**
 * Hook to fetch categories for a specific event
 */
export function useVolunteerCategoriesByEvent(eventId: string | null) {
  return useQuery<VolunteerCategory[]>({
    queryKey: eventId ? volunteerQueryKeys.categoryByEvent(eventId) : [],
    queryFn: async () => {
      if (!eventId) return [];
      return fetchClient.get(`/api/volunteer/events/${eventId}/categories`);
    },
    enabled: !!eventId, // Only run query if eventId is provided
  });
}

/**
 * Hook to fetch a specific category
 */
export function useVolunteerCategory(id: string | null) {
  return useQuery<VolunteerCategory | null>({
    queryKey: id ? volunteerQueryKeys.category(id) : [],
    queryFn: async () => {
      if (!id) return null;
      return fetchClient.get(`/api/volunteer/categories/${id}`);
    },
    enabled: !!id, // Only run query if id is provided
  });
}

/**
 * Hook to create a new category
 */
export function useCreateVolunteerCategory(eventId: string | null) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateCategoryData) => {
      if (!eventId) throw new Error("Event ID is required");

      return fetchClient.post(
        `/api/volunteer/events/${eventId}/categories`,
        data,
      );
    },
    onSuccess: () => {
      // Invalidate categories query to refetch the updated list
      if (eventId) {
        queryClient.invalidateQueries({
          queryKey: volunteerQueryKeys.categoryByEvent(eventId),
        });
      }
    },
  });
}

/**
 * Hook to update an existing category
 */
export function useUpdateVolunteerCategory(
  id: string | null,
  eventId: string | null,
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: UpdateCategoryData) => {
      if (!id) throw new Error("Category ID is required");

      return fetchClient.put(`/api/volunteer/categories/${id}`, data);
    },
    onSuccess: () => {
      // Invalidate specific category query
      if (id) {
        queryClient.invalidateQueries({
          queryKey: volunteerQueryKeys.category(id),
        });
      }

      // Invalidate categories by event query
      if (eventId) {
        queryClient.invalidateQueries({
          queryKey: volunteerQueryKeys.categoryByEvent(eventId),
        });
      }
    },
  });
}

/**
 * Hook to delete a category
 */
export function useDeleteVolunteerCategory(eventId: string | null) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      return fetchClient.delete(`/api/volunteer/categories/${id}`);
    },
    onSuccess: () => {
      // Invalidate categories by event query
      if (eventId) {
        queryClient.invalidateQueries({
          queryKey: volunteerQueryKeys.categoryByEvent(eventId),
        });
      }
    },
  });
}
