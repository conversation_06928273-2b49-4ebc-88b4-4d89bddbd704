# Notification Components

Real-time notification system components for displaying alerts, updates, and system messages.

## Components

- **NotificationPanel.tsx** - Main notification panel displaying list of notifications
- **NotificationItem.tsx** - Individual notification item with content and actions
- **NotificationIcon.tsx** - Notification icon with badge for unread count
- **index.ts** - Component exports

These components work together to provide users with real-time notifications for banking transactions, system updates, volunteer assignments, and other important platform activities using Server-Sent Events (SSE).
