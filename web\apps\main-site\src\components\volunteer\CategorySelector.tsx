"use client";

import React, { useState, useEffect } from "react";
import { useVolunteerCategoriesByEvent } from "@/hooks/useVolunteerCategories";

interface CategorySelectorProps {
  eventId: string | null;
  onCategorySelect: (categoryId: string) => void;
  className?: string;
  initialCategoryId?: string;
  disabled?: boolean;
}

export const CategorySelector: React.FC<CategorySelectorProps> = ({
  eventId,
  onCategorySelect,
  className = "",
  initialCategoryId = "",
  disabled = false,
}) => {
  const [selectedCategoryId, setSelectedCategoryId] =
    useState<string>(initialCategoryId);

  // Fetch categories for the selected event
  const {
    data: categories = [],
    isLoading,
    error,
  } = useVolunteerCategoriesByEvent(eventId);

  // Update selectedCategoryId when initialCategoryId changes
  useEffect(() => {
    if (initialCategoryId && initialCategoryId !== selectedCategoryId) {
      setSelectedCategoryId(initialCategoryId);
    }
  }, [initialCategoryId, selectedCategoryId]);

  // Update selectedCategoryId when eventId changes or categories are loaded
  useEffect(() => {
    if (eventId && categories.length > 0) {
      // If there's an initialCategoryId and it exists in the categories, use it
      if (
        initialCategoryId &&
        categories.some((cat) => cat.id === initialCategoryId)
      ) {
        setSelectedCategoryId(initialCategoryId);
      }
      // Otherwise, if the current selectedCategoryId doesn't exist in the new categories list,
      // set it to the first category
      else if (!categories.some((cat) => cat.id === selectedCategoryId)) {
        setSelectedCategoryId(categories[0].id);
        onCategorySelect(categories[0].id);
      }
    }
  }, [
    eventId,
    categories,
    initialCategoryId,
    selectedCategoryId,
    onCategorySelect,
  ]);

  // Handle category selection
  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const categoryId = e.target.value;
    setSelectedCategoryId(categoryId);
    onCategorySelect(categoryId);
  };

  return (
    <div className={className}>
      <label htmlFor="category-selector" className="block text-white mb-2">
        Select Category
      </label>

      {isLoading ? (
        <div className="flex items-center justify-center h-12">
          <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-primary"></div>
        </div>
      ) : error ? (
        <div className="text-accent p-2 rounded-md bg-secondary border border-accent">
          Error loading categories
        </div>
      ) : categories.length === 0 ? (
        <div className="text-gray-400 p-2">
          No categories available for this event. Please create a category
          first.
        </div>
      ) : (
        <div className="relative">
          <select
            id="category-selector"
            className="w-full px-4 py-2 bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary appearance-none"
            value={selectedCategoryId}
            onChange={handleCategoryChange}
            disabled={disabled}
          >
            {categories.map((category) => (
              <option key={category.id} value={category.id}>
                {category.name}
              </option>
            ))}
          </select>
          <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
            <svg
              className="w-5 h-5 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </div>
        </div>
      )}
    </div>
  );
};
