"use client";

import { useOrders } from "@/hooks/useOrders";
import { <PERSON>, Spinner, But<PERSON> } from "@bank-of-styx/ui";
import Link from "next/link";
import { useAuth } from "@/contexts/AuthContext";

export default function OrderHistoryPage() {
  const { user, isAuthenticated, openAuthModal } = useAuth();
  const { data, isLoading, error } = useOrders();

  if (!isAuthenticated) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="p-8 text-center">
          <h2 className="text-2xl font-bold mb-4">Sign In Required</h2>
          <p className="mb-6">Please sign in to view your order history.</p>
          <Button variant="primary" onClick={openAuthModal}>
            Sign In
          </Button>
        </Card>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8 flex justify-center">
        <Spinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="p-8 text-center">
          <h2 className="text-2xl font-bold mb-4">Error</h2>
          <p className="mb-6">
            Failed to load your orders. Please try again later.
          </p>
          <Button variant="primary" onClick={() => window.location.reload()}>
            Retry
          </Button>
        </Card>
      </div>
    );
  }

  const orders = data?.orders || [];
  const hasOrders = orders.length > 0;

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">Your Orders</h1>

      {!hasOrders ? (
        <Card className="p-8 text-center">
          <h2 className="text-2xl font-bold mb-4">No orders yet</h2>
          <p className="mb-6">You haven't placed any orders yet.</p>
          <Link href="/shop">
            <Button variant="primary">Shop Now</Button>
          </Link>
        </Card>
      ) : (
        <div className="space-y-6">
          {orders.map((order) => (
            <Card key={order.id} className="overflow-hidden">
              <div className="bg-secondary p-4 flex justify-between items-center">
                <div>
                  <p className="font-semibold">Order #{order.orderNumber}</p>
                  <p className="text-sm text-text-muted">
                    {new Date(order.createdAt).toLocaleDateString()} at{" "}
                    {new Date(order.createdAt).toLocaleTimeString()}
                  </p>
                </div>
                <div>
                  <span
                    className={`px-3 py-1 rounded-full text-sm font-medium ${
                      order.status === "paid"
                        ? "bg-success bg-opacity-20 text-success"
                        : order.status === "pending"
                        ? "bg-warning bg-opacity-20 text-warning"
                        : order.status === "cancelled"
                        ? "bg-accent bg-opacity-20 text-accent"
                        : "bg-info bg-opacity-20 text-info"
                    }`}
                  >
                    {order.status.charAt(0).toUpperCase() +
                      order.status.slice(1)}
                  </span>
                </div>
              </div>
              <div className="p-4">
                <div className="mb-4">
                  <p className="font-semibold">Items:</p>
                  <ul className="mt-2 space-y-2">
                    {order.items.map((item) => (
                      <li key={item.id} className="flex justify-between">
                        <span>
                          {item.quantity} x {item.name}
                        </span>
                        <span>${item.price.toFixed(2)}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                <div className="border-t border-border-subtle pt-4 flex justify-between">
                  <span className="font-semibold">Total:</span>
                  <span className="font-semibold">
                    ${order.total.toFixed(2)}
                  </span>
                </div>
              </div>
              <div className="bg-secondary-light p-4 text-right">
                <Link href={`/shop/orders/${order.id}`}>
                  <Button variant="outline" size="sm">
                    View Details
                  </Button>
                </Link>
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
