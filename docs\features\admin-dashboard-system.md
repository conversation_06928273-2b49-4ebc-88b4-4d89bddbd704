# Admin Dashboard System

## Overview
The Admin Dashboard System serves as the central administrative control hub for the Bank of Styx platform, providing comprehensive oversight and management capabilities across all systems. It features user management, role assignment, support ticket handling, event administration, featured content management, and system-wide analytics with real-time updates and bulk operations.

## Directory Structure
```
admin-dashboard-system/
├── src/app/admin/                     # Admin frontend interfaces
│   ├── dashboard/                     # Main admin dashboard
│   │   ├── page.tsx                   # Central dashboard with system statistics
│   │   ├── users/                     # User management
│   │   │   └── page.tsx               # Comprehensive user management interface
│   │   ├── featured/                  # Featured content management
│   │   │   └── page.tsx               # Home page hero and news featuring
│   │   └── tickets/                   # Support ticket management
│   │       └── page.tsx               # Complete support ticket interface
│   ├── events/                        # Event administration
│   │   └── page.tsx                   # Event management and oversight
│   └── event-categories/              # Event category management
│       └── page.tsx                   # Category administration
├── src/app/api/admin/                 # Admin API endpoints
│   ├── dashboard/                     # Dashboard statistics
│   ├── users/                         # User management APIs
│   ├── featured/                      # Featured content APIs
│   ├── events/                        # Event administration APIs
│   └── export/                        # Data export APIs
├── src/components/admin/              # Admin UI components
│   ├── AdminDashboardLayout.tsx       # Master admin layout wrapper
│   ├── UserProfileModal.tsx           # Comprehensive user profile management
│   ├── UserActionButtons.tsx          # User operation action buttons
│   ├── UserProfileCard.tsx            # User information display card
│   ├── UserStatusBadge.tsx            # User status indicator badges
│   ├── UserRoleBadges.tsx             # Role display badge system
│   ├── AddUserModal.tsx               # User creation modal interface
│   ├── CSVExportButton.tsx            # Reusable CSV export functionality
│   └── EventStatsModal.tsx            # Event statistics display modal
├── src/services/                      # Admin business logic
│   ├── adminService.ts                # Centralized admin API service
│   ├── eventStatsService.ts           # Event analytics service
│   └── csv-export.ts                  # CSV generation utility
├── src/hooks/                         # Admin React hooks
│   ├── useAdminDashboard.ts           # Dashboard data management
│   ├── useUserManagement.ts           # User operations hooks
│   └── useAdminEvents.ts              # Event management hooks
└── src/types/                         # Admin TypeScript definitions
    ├── admin.ts                       # Admin-specific types
    ├── user-management.ts             # User management interfaces
    └── support-ticket.ts              # Support ticket types
```

## Key Files & Components

### Frontend Components
- **`AdminDashboardLayout.tsx`** - Master layout with responsive navigation, desktop sidebar, mobile grid, and consistent theming
- **`UserProfileModal.tsx`** - Comprehensive user management with tabbed interface (Profile, Roles, Security, Activity) and real-time updates
- **`UserActionButtons.tsx`** - Context-aware action button set with compact and full view modes for user operations
- **`UserStatusBadge.tsx`** - Visual status indicators for user account states (active, pending, suspended, frozen, inactive)
- **`UserRoleBadges.tsx`** - Multi-role display system with color-coded badges for all platform roles
- **`AddUserModal.tsx`** - User creation interface with role assignment and account provisioning
- **`CSVExportButton.tsx`** - Reusable CSV export functionality with generic type support and toast notifications
- **`EventStatsModal.tsx`** - Event performance analytics with attendance tracking and revenue reporting

### API Endpoints
- **`GET /api/admin/dashboard/stats`** - System-wide statistics including user counts, merchant status, and recent activity summaries
- **`GET /api/admin/users`** - User listing with pagination, search, role filtering, and status filtering capabilities
- **`GET /api/admin/users/[id]`** - Individual user details with complete profile and activity information
- **`PATCH /api/admin/users/[id]/roles`** - User role assignment and management with real-time updates
- **`PUT /api/admin/users/[id]/status`** - User account status management (active, suspended, frozen, inactive)
- **`PUT /api/admin/users/[id]/email`** - Administrative email updates with verification workflow
- **`POST /api/admin/users/[id]/reset-password`** - Administrative password reset with email notification
- **`POST /api/admin/users/create`** - User account creation with role assignment and provisioning
- **`GET /api/admin/featured`** - Featured content management for news articles and hero banners
- **`PUT /api/admin/featured/[id]`** - Toggle featured status with real-time content promotion
- **`GET /api/admin/events`** - Administrative event listing with filtering, categorization, and status management
- **`GET /api/admin/export/users`** - CSV export with filtering preservation and custom header configuration

### Database Models
- **`User`** - Extended with comprehensive role flags, status management, and administrative relationship tracking
- **`SupportTicket`** - Complete support ticket system with assignment, priority, category, and resolution tracking
- **`TicketNote`** - Internal and external ticket notes with admin-only visibility options
- **`Event`** - Administrative event management with status tracking and category relationships

### Services
- **`adminService.ts`** - Centralized API service with type-safe interfaces, pagination support, and error handling
- **`csv-export.ts`** - Generic CSV generation utility with proper escaping, formatting, and large dataset handling
- **`eventStatsService.ts`** - Event analytics service with currency formatting, percentage calculations, and utilization metrics

### Hooks
- **`useAdminDashboard()`** - Dashboard data management with TanStack Query integration and real-time statistics
- **`useUserManagement()`** - User operations with optimistic updates, error handling, and cache invalidation
- **`useAdminEvents()`** - Event management hooks with filtering, pagination, and bulk operations

## Common Tasks

### Task 1: How to Manage Users as Administrator
1. Administrator navigates to `/admin/dashboard/users` (requires `isAdmin` role)
2. Uses search and filter controls to find specific users or groups
3. Views users in responsive grid or table format with pagination
4. Clicks on user card or "View" button to open `UserProfileModal`
5. Reviews user profile across tabbed interface (Profile, Roles, Security, Activity)
6. Updates user roles using toggle switches with immediate API persistence
7. Changes account status (active, suspended, frozen) via dropdown selection
8. Resets passwords or updates email addresses as needed
9. Views user activity history and administrative notes
10. Exports filtered user data to CSV for reporting and analysis

### Task 2: How to Handle Support Tickets
1. Administrator accesses `/admin/dashboard/tickets` for support oversight
2. Uses advanced filtering (status, priority, category, assigned tickets)
3. Reviews ticket cards with priority indicators and customer information
4. Assigns tickets to specific administrators or claims them personally
5. Updates ticket status from open → in_progress → resolved → closed
6. Adds internal notes for administrative coordination
7. Escalates priority levels (low, medium, high, urgent) as needed
8. Documents resolution details and closes tickets with complete notes
9. Exports ticket data to CSV for performance analysis and reporting
10. Monitors ticket resolution times and customer satisfaction metrics

### Task 3: How to Manage Featured Content
1. Administrator navigates to `/admin/dashboard/featured` for content management
2. Reviews current featured content across tabbed interface (News, Hero Banner)
3. Views list of eligible content with current featured status indicators
4. Toggles featured status using switches with real-time API updates
5. Promotes news articles to featured status for homepage visibility
6. Manages hero banner content for main landing page
7. Reviews featured content performance and engagement metrics
8. Coordinates content promotion schedule with editorial calendar
9. Removes featured status when content rotation is needed
10. Monitors featured content impact on user engagement and traffic

### Task 4: How to Administer Events
1. Administrator accesses `/admin/events` for complete event management
2. Views event listing with status filters (draft, published, cancelled, completed)
3. Creates new events with category assignment and scheduling
4. Edits existing events including capacity, pricing, and volunteer requirements
5. Views event statistics via `EventStatsModal` for performance analysis
6. Manages event categories via `/admin/event-categories` interface
7. Monitors registration numbers and capacity utilization
8. Updates event status through lifecycle management
9. Coordinates with volunteer and ship systems for event staffing
10. Exports event data and analytics for reporting and planning

## API Integration

### Authentication Requirements
- **All admin endpoints**: Valid JWT token + `isAdmin` role required for system administration
- **User management**: Admin role required for user CRUD operations and role assignments
- **Content management**: Admin or Editor role for featured content management
- **Support tickets**: Admin role for ticket assignment, resolution, and internal notes
- **Event administration**: Admin role for event creation, editing, and category management
- **Data export**: Admin role required for CSV generation and sensitive data export
- **Token format**: `Bearer <jwt-token>` in Authorization header with role validation

### Request/Response Examples
```typescript
// User Management Request
interface UpdateUserRolesRequest {
  roles: {
    isAdmin?: boolean;
    isEditor?: boolean;
    isBanker?: boolean;
    isChatModerator?: boolean;
    isVolunteerCoordinator?: boolean;
    isLeadManager?: boolean;
    isSalesManager?: boolean;
    isLandSteward?: boolean;
  };
}

// User Management Response
interface UserManagementResponse {
  id: string;
  username: string;
  displayName: string;
  email: string;
  status: 'active' | 'pending' | 'inactive' | 'suspended' | 'frozen';
  balance: number;
  isEmailVerified: boolean;
  roles: {
    isAdmin: boolean;
    isEditor: boolean;
    isBanker: boolean;
    isChatModerator: boolean;
    isVolunteerCoordinator: boolean;
    isLeadManager: boolean;
    isSalesManager: boolean;
    isLandSteward: boolean;
  };
  profile: {
    avatar: string;
    discordConnected: boolean;
    discordId?: string;
    notificationPreferences: {
      notifyTransfers: boolean;
      notifyDeposits: boolean;
      notifyWithdrawals: boolean;
      notifyNewsEvents: boolean;
    };
  };
  statistics: {
    transactionCount: number;
    volunteerHours: number;
    lastActive: string;
    registrationDate: string;
  };
}

// Dashboard Statistics Response
interface AdminDashboardStatsResponse {
  quickStats: {
    totalUsers: number;
    activeUsers: number;
    pendingApplications: number;
    activeMerchants: number;
    openTickets: number;
    urgentTickets: number;
  };
  recentActivity: Array<{
    id: string;
    type: 'user_created' | 'role_assigned' | 'content_featured' | 'ticket_resolved';
    description: string;
    timestamp: string;
    actor: {
      id: string;
      username: string;
      displayName: string;
    };
  }>;
  systemHealth: {
    activeConnections: number;
    serverUptime: string;
    lastBackup: string;
  };
}

// Support Ticket Management Request
interface UpdateTicketRequest {
  status?: 'open' | 'in_progress' | 'resolved' | 'closed';
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  assignedToId?: string;
  resolution?: string;
  internalNote?: string;
}

// CSV Export Request
interface CSVExportRequest {
  filters?: {
    status?: string[];
    roles?: string[];
    search?: string;
    dateRange?: {
      start: string;
      end: string;
    };
  };
  columns?: string[];        // Custom column selection
  filename?: string;         // Custom filename
}

// Event Administration Response
interface AdminEventResponse {
  id: string;
  name: string;
  description: string;
  status: 'draft' | 'published' | 'cancelled' | 'completed';
  startDate: string;
  endDate: string;
  capacity: number;
  registrations: number;
  revenue: number;
  category: {
    id: string;
    name: string;
    description?: string;
  };
  statistics: {
    attendanceRate: number;
    utilizationPercentage: number;
    volunteerHours: number;
    ticketsSold: number;
  };
  createdAt: string;
  updatedAt: string;
}
```

## Database Schema

### Primary Models
```sql
-- User table (administrative fields)
ALTER TABLE User ADD COLUMN status VARCHAR(50) DEFAULT 'active';
-- Role flags already exist: isAdmin, isEditor, isBanker, etc.

-- Support Ticket table (administrative support system)
CREATE TABLE SupportTicket (
  id VARCHAR(191) PRIMARY KEY,
  subject VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  status ENUM('open', 'in_progress', 'resolved', 'closed') DEFAULT 'open',
  priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
  category ENUM('general', 'account', 'banking', 'technical', 'other') DEFAULT 'general',
  
  -- User relationships
  userId VARCHAR(191) NOT NULL,        -- Ticket creator
  assignedToId VARCHAR(191),           -- Assigned administrator
  resolvedById VARCHAR(191),           -- Resolving administrator
  
  -- Timestamps
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  assignedAt DATETIME,                 -- Assignment timestamp
  resolvedAt DATETIME,                 -- Resolution timestamp
  
  -- Resolution details
  resolution TEXT,                     -- Resolution description
  
  FOREIGN KEY (userId) REFERENCES User(id),
  FOREIGN KEY (assignedToId) REFERENCES User(id),
  FOREIGN KEY (resolvedById) REFERENCES User(id),
  INDEX idx_ticket_status (status),
  INDEX idx_ticket_priority (priority),
  INDEX idx_ticket_assigned (assignedToId),
  INDEX idx_ticket_created (createdAt)
);

-- Ticket Note table (internal and external notes)
CREATE TABLE TicketNote (
  id VARCHAR(191) PRIMARY KEY,
  ticketId VARCHAR(191) NOT NULL,
  authorId VARCHAR(191) NOT NULL,
  content TEXT NOT NULL,
  isInternal BOOLEAN DEFAULT false,    -- Admin-only internal notes
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (ticketId) REFERENCES SupportTicket(id) ON DELETE CASCADE,
  FOREIGN KEY (authorId) REFERENCES User(id),
  INDEX idx_note_ticket (ticketId),
  INDEX idx_note_author (authorId),
  INDEX idx_note_internal (isInternal)
);

-- Admin Activity Log table (audit trail)
CREATE TABLE AdminActivityLog (
  id VARCHAR(191) PRIMARY KEY,
  adminId VARCHAR(191) NOT NULL,
  action VARCHAR(255) NOT NULL,       -- Action performed
  targetType VARCHAR(100),            -- User, Event, Ticket, etc.
  targetId VARCHAR(191),              -- ID of affected entity
  details JSON,                       -- Additional action details
  ipAddress VARCHAR(45),              -- IP address for security
  userAgent TEXT,                     -- Browser information
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (adminId) REFERENCES User(id),
  INDEX idx_activity_admin (adminId),
  INDEX idx_activity_action (action),
  INDEX idx_activity_target (targetType, targetId),
  INDEX idx_activity_created (createdAt)
);
```

### Relationships
- **User ↔ SupportTicket**: One-to-many (users create tickets, admins are assigned/resolve tickets)
- **SupportTicket ↔ TicketNote**: One-to-many (tickets have multiple notes from different users/admins)
- **User ↔ AdminActivityLog**: One-to-many (admins have activity history)
- **Admin ↔ All Systems**: Administrative oversight relationships across all platform models

## Related Features
- **[Authentication System](./authentication-system.md)** - Role-based access control with comprehensive admin role management
- **[Banking System](./banking-system.md)** - Administrative oversight of transactions, pay codes, and financial operations
- **[Ship Management System](./ship-management-system.md)** - Admin control over ship applications, captain assignments, and Land Steward operations
- **[Volunteer System](./volunteer-system.md)** - Administrative event management, volunteer coordination, and payment oversight
- **[Shopping & Sales System](./shopping-sales-system.md)** - Product management oversight, order administration, and sales analytics
- **[News Content Management System](./news-content-management-system.md)** - Featured content management and editorial oversight

## User Roles & Permissions
- **Super Admins**: Complete system access, role assignment, user management, and system configuration
- **Admins**: User management, content oversight, support ticket resolution, event administration
- **Support Staff**: Ticket management, user assistance, limited administrative functions
- **Content Moderators**: Featured content management, news oversight, content promotion controls

## Recent Changes
- **v8.2.0** - Enhanced CSV export functionality with custom column selection and filtering preservation
- **v8.1.0** - Support ticket system integration with assignment workflows and priority management
- **v8.0.0** - Complete admin dashboard redesign with responsive components and real-time updates
- **v7.9.0** - User status management system with granular account states and workflow controls
- **v7.8.0** - Role-based access control enhancement with multiple administrator role types
- **v7.7.0** - Event administration integration with comprehensive event lifecycle management
- **v7.6.0** - Featured content management system with cross-platform promotion capabilities

## Troubleshooting

### Common Issues
1. **Role assignment not persisting**: Check JWT token validity and admin role verification. Ensure database transaction completion.
2. **CSV export timeouts**: Large datasets may require pagination or background processing. Check server memory limits.
3. **User status changes not reflecting**: Verify cache invalidation and real-time update mechanisms. Check TanStack Query configuration.
4. **Support ticket assignment failures**: Ensure assigned user has appropriate role. Check foreign key constraints.
5. **Featured content toggle not working**: Verify content eligibility and API endpoint accessibility. Check role permissions.
6. **Dashboard statistics not updating**: Check real-time query intervals and cache management. Verify statistics calculation accuracy.

### Debug Information
- **Log locations**: Administrative action logs in database, server operation logs in console
- **Environment variables**: Database connection settings, JWT configuration, export file paths
- **Database queries**: Use Prisma Studio to inspect user roles, ticket assignments, and activity logs
- **Debugging commands**:
  ```bash
  # Check admin authentication
  curl -H "Authorization: Bearer <admin-token>" http://localhost:3000/api/admin/dashboard/stats
  
  # Test user management endpoints
  curl -H "Authorization: Bearer <admin-token>" http://localhost:3000/api/admin/users?page=1&limit=10
  
  # Verify role assignment
  curl -X PATCH -H "Authorization: Bearer <admin-token>" -d '{"roles":{"isEditor":true}}' http://localhost:3000/api/admin/users/{id}/roles
  
  # Test CSV export
  curl -H "Authorization: Bearer <admin-token>" http://localhost:3000/api/admin/export/users
  ```

### Performance Considerations
- **User Management**: Pagination implemented for large user bases with efficient database queries
- **Real-time Updates**: TanStack Query provides optimized caching with automatic invalidation
- **CSV Export**: Server-side generation prevents browser memory issues with large datasets
- **Dashboard Statistics**: Cached statistics with configurable refresh intervals for performance
- **Search Operations**: Debounced search inputs and indexed database queries for responsiveness
- **Bulk Operations**: Optimized batch processing for role assignments and status updates

### Security Measures
- **Multi-layer Authentication**: JWT validation with role-specific access control on all admin endpoints
- **Activity Auditing**: Complete audit trail of all administrative actions with IP tracking
- **Session Management**: Secure admin session handling with timeout and refresh mechanisms
- **Data Protection**: Sensitive user data handling with proper encryption and access logging
- **Role Isolation**: Strict role-based access control preventing privilege escalation
- **Input Validation**: Comprehensive validation on all administrative inputs and operations

### Integration Patterns
- **Cross-system Administration**: Centralized control over all platform systems from unified dashboard
- **Real-time Synchronization**: Administrative changes propagate immediately across all integrated systems
- **Audit Integration**: Administrative actions logged across all affected systems for complete traceability
- **Permission Cascade**: Role changes automatically affect access across all integrated platform features
- **Data Consistency**: Transaction-based operations ensure consistency across administrative changes

---

**File Locations:**
- Pages: `/src/app/admin/`
- Components: `/src/components/admin/`
- API: `/src/app/api/admin/`
- Services: `/src/services/adminService.ts`, `/src/services/csv-export.ts`
- Hooks: `/src/hooks/useAdminDashboard.ts`, `/src/hooks/useUserManagement.ts`
- Types: `/src/types/admin.ts`, `/src/types/user-management.ts`, `/src/types/support-ticket.ts`