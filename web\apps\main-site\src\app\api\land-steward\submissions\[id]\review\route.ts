import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth";
import prisma from "@/lib/prisma";

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    
    if (!user || !user.isLandSteward) {
      return NextResponse.json(
        { error: "Unauthorized. Land Steward access required." },
        { status: 403 }
      );
    }

    const submissionId = params.id;

    const body = await request.json();
    const { status, reviewNotes } = body;

    // Validate status
    if (!status || !["reviewed", "approved", "rejected"].includes(status)) {
      return NextResponse.json(
        { error: "Status must be reviewed, approved, or rejected" },
        { status: 400 }
      );
    }

    // Check if submission exists
    const existingSubmission = await prisma.formSubmission.findUnique({
      where: { id: submissionId },
      include: {
        form: {
          select: {
            id: true,
            name: true,
            formStructure: true,
            requiredVolunteerHours: true,
          },
        },
        ship: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!existingSubmission) {
      return NextResponse.json(
        { error: "Submission not found" },
        { status: 404 }
      );
    }

    // Update submission with review
    const updatedSubmission = await prisma.formSubmission.update({
      where: { id: submissionId },
      data: {
        status,
        reviewNotes,
        reviewedById: user.id,
        reviewedAt: new Date(),
      },
      include: {
        form: {
          select: {
            id: true,
            name: true,
            event: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        ship: {
          select: {
            id: true,
            name: true,
            captain: {
              select: {
                id: true,
                username: true,
                displayName: true,
              },
            },
          },
        },
        submittedBy: {
          select: {
            id: true,
            username: true,
            displayName: true,
          },
        },
        reviewedBy: {
          select: {
            id: true,
            username: true,
            displayName: true,
          },
        },
      },
    });

    // Create volunteer requirements if form is approved and has volunteer hours
    if (status === 'approved') {
      // Check form structure for volunteer hours field
      const formStructure = existingSubmission.form.formStructure as any[];
      const volunteerHoursField = formStructure.find((field: any) => field.type === 'volunteer_hours');
      
      if (volunteerHoursField && volunteerHoursField.volunteerHours > 0) {
        try {
          // Check if requirement already exists
          const existingRequirement = await prisma.shipVolunteerRequirement.findUnique({
            where: {
              formSubmissionId: submissionId,
            },
          });

          if (!existingRequirement) {
            await prisma.shipVolunteerRequirement.create({
              data: {
                shipId: existingSubmission.ship.id,
                formSubmissionId: submissionId,
                requiredHours: volunteerHoursField.volunteerHours,
                status: 'pending',
              },
            });
          }
        } catch (error) {
          console.error("Error creating volunteer requirement:", error);
          // Don't fail the whole request if volunteer requirement creation fails
        }
      }
    }

    // TODO: Send notification to ship captain about review status
    // This would integrate with the existing notification system

    return NextResponse.json(updatedSubmission);
  } catch (error) {
    console.error("Error reviewing form submission:", error);
    return NextResponse.json(
      { error: "Failed to review form submission" },
      { status: 500 }
    );
  }
}