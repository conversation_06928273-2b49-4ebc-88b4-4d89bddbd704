"use client";

import React, { useState, useEffect } from "react";
import { Button } from "@bank-of-styx/ui";
import Link from "next/link";
import { useAuth } from "@/contexts/AuthContext";
import { useParams, useRouter } from "next/navigation";
import { InvitationBar } from "@/components/ships/InvitationBar";
import { checkShipInvitation, acceptShipInvitation, declineShipInvitation, type ShipInvitation } from "@/services/shipService";

interface ShipMember {
  id: string;
  role: string;
  joinedAt: string;
  user: {
    id: string;
    username: string;
    displayName: string;
    avatar: string;
  };
}

interface Ship {
  id: string;
  name: string;
  description: string;
  slogan: string | null;
  logo: string | null;
  tags: string[];
  captain: {
    id: string;
    username: string;
    displayName: string;
    avatar: string;
  };
  members: ShipMember[];
  memberCount: number;
  createdAt: string;
  canJoin: boolean;
  hasPendingRequest: boolean;
  userShipStatus: {
    isMember: boolean;
    currentShip?: {
      id: string;
      name: string;
    };
  } | null;
}

export default function ShipDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { user } = useAuth();
  const [ship, setShip] = useState<Ship | null>(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [invitation, setInvitation] = useState<ShipInvitation | null>(null);
  const [invitationLoading, setInvitationLoading] = useState(false);

  const fetchShip = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/ships/${params.id}`, {
        headers: user
          ? {
              Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
            }
          : {},
      });

      if (!response.ok) {
        if (response.status === 404) {
          setError("Ship not found");
        } else {
          throw new Error("Failed to fetch ship");
        }
        return;
      }

      const data: Ship = await response.json();
      setShip(data);
    } catch (error) {
      console.error("Error fetching ship:", error);
      setError("Failed to load ship details");
    } finally {
      setLoading(false);
    }
  };

  const fetchInvitation = async () => {
    if (!user || !params.id) return;

    try {
      setInvitationLoading(true);
      const invitationData = await checkShipInvitation(params.id as string);
      setInvitation(invitationData);
    } catch (error) {
      console.error("Error checking invitation:", error);
    } finally {
      setInvitationLoading(false);
    }
  };

  const handleAcceptInvitation = async () => {
    if (!params.id) return;

    try {
      await acceptShipInvitation(params.id as string);
      // Refresh ship data and invitation status
      await fetchShip();
      await fetchInvitation();
    } catch (error) {
      console.error("Error accepting invitation:", error);
      alert("Failed to accept invitation. Please try again.");
    }
  };

  const handleDeclineInvitation = async () => {
    if (!params.id) return;

    try {
      await declineShipInvitation(params.id as string);
      // Refresh invitation status
      await fetchInvitation();
    } catch (error) {
      console.error("Error declining invitation:", error);
      alert("Failed to decline invitation. Please try again.");
    }
  };

  const handleJoinRequest = async () => {
    if (!user || !ship) return;

    try {
      setActionLoading(true);
      const response = await fetch(`/api/ships/${ship.id}/join`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to send join request");
      }

      // Show success and refresh ship data
      alert(data.message);
      fetchShip();
    } catch (error) {
      console.error("Error sending join request:", error);
      alert(error instanceof Error ? error.message : "Failed to send join request");
    } finally {
      setActionLoading(false);
    }
  };

  useEffect(() => {
    if (params.id) {
      fetchShip();
      if (user) {
        fetchInvitation();
      }
    }
  }, [params.id, user]);

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-secondary rounded mb-4 w-1/3"></div>
            <div className="bg-secondary rounded-lg p-6">
              <div className="flex flex-col lg:flex-row gap-8">
                <div className="lg:w-1/3">
                  <div className="h-64 bg-secondary-dark rounded mb-4"></div>
                  <div className="h-12 bg-secondary-dark rounded"></div>
                </div>
                <div className="lg:w-2/3">
                  <div className="h-8 bg-secondary-dark rounded mb-4"></div>
                  <div className="h-4 bg-secondary-dark rounded mb-2"></div>
                  <div className="h-32 bg-secondary-dark rounded mb-4"></div>
                  <div className="h-24 bg-secondary-dark rounded"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !ship) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-12">
            <div className="w-24 h-24 bg-secondary rounded-full flex items-center justify-center mx-auto mb-4">
              <svg
                className="w-12 h-12 text-red-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.728-.833-2.498 0L4.316 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">
              {error || "Ship not found"}
            </h3>
            <p className="text-gray-400 mb-4">
              The ship you're looking for doesn't exist or has been removed.
            </p>
            <Link href="/ships">
              <Button variant="primary">Back to Ships</Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <nav className="mb-6">
          <Link href="/ships" className="text-primary hover:text-primary-light">
            ← Back to Ships
          </Link>
        </nav>

        {/* Invitation Bar */}
        {user && invitation?.hasInvitation && invitation.invitation && (
          <InvitationBar
            shipName={ship.name}
            roleName={(() => {
              // Parse role name from invitation message
              const message = invitation.invitation?.message || '';
              const roleMatch = message.match(/as\s+([^!]+)/i);
              return roleMatch ? roleMatch[1].trim() : 'Member';
            })()}
            onAccept={handleAcceptInvitation}
            onDecline={handleDeclineInvitation}
          />
        )}

        {/* Ship Header */}
        <div className="bg-secondary rounded-lg shadow-lg overflow-hidden mb-8">
          <div className="p-6">
            <div className="flex flex-col lg:flex-row gap-8">
              {/* Ship Logo and Basic Info */}
              <div className="lg:w-1/3">
                <div className="bg-secondary-dark rounded-lg p-6 mb-6 text-center">
                  {ship.logo ? (
                    <img
                      src={ship.logo}
                      alt={`${ship.name} logo`}
                      className="max-h-48 max-w-48 object-contain mx-auto mb-4"
                    />
                  ) : (
                    <div className="w-32 h-32 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
                      <svg
                        className="w-16 h-16 text-white"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                  )}

                  <h1 className="text-2xl font-bold text-white mb-2">{ship.name}</h1>
                  
                  {ship.slogan && (
                    <p className="text-primary italic mb-4">"{ship.slogan}"</p>
                  )}

                  <div className="bg-primary text-white rounded-full px-4 py-2 inline-block">
                    {ship.memberCount} member{ship.memberCount !== 1 ? 's' : ''}
                  </div>
                </div>

                {/* Join Button */}
                {user && (
                  <div className="space-y-3">
                    {ship.canJoin && (
                      <Button
                        variant="primary"
                        size="lg"
                        className="w-full"
                        onClick={handleJoinRequest}
                        disabled={actionLoading}
                      >
                        {actionLoading ? "Sending..." : "Request to Join"}
                      </Button>
                    )}

                    {ship.hasPendingRequest && (
                      <div className="bg-yellow-900 text-yellow-100 px-4 py-2 rounded text-center">
                        Join request pending approval
                      </div>
                    )}

                    {ship.userShipStatus?.isMember && (
                      <div className="bg-green-900 text-green-100 px-4 py-2 rounded text-center">
                        You are a member of this ship
                      </div>
                    )}

                    {ship.userShipStatus?.currentShip && !ship.userShipStatus.isMember && (
                      <div className="bg-blue-900 text-blue-100 px-4 py-2 rounded text-center text-sm">
                        You are already a member of {ship.userShipStatus.currentShip.name}
                      </div>
                    )}
                  </div>
                )}

                {!user && (
                  <div className="bg-secondary-dark px-4 py-3 rounded text-center">
                    <p className="text-gray-400 text-sm mb-2">
                      Sign in to join this ship
                    </p>
                    <Link href="/auth/login">
                      <Button variant="primary" size="sm">
                        Sign In
                      </Button>
                    </Link>
                  </div>
                )}
              </div>

              {/* Ship Details */}
              <div className="lg:w-2/3">
                {/* Captain Info */}
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-white mb-3">Captain</h3>
                  <div className="flex items-center">
                    <img
                      src={ship.captain.avatar || "/images/avatars/default.png"}
                      alt={ship.captain.displayName}
                      className="w-12 h-12 rounded-full mr-4"
                    />
                    <div>
                      <p className="font-medium text-white">{ship.captain.displayName}</p>
                      <p className="text-gray-400 text-sm">@{ship.captain.username}</p>
                    </div>
                  </div>
                </div>

                {/* Description */}
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-white mb-3">About</h3>
                  <div className="bg-secondary-dark rounded-lg p-4">
                    <p className="text-gray-300 whitespace-pre-wrap">{ship.description}</p>
                  </div>
                </div>

                {/* Tags */}
                {ship.tags && ship.tags.length > 0 && (
                  <div className="mb-6">
                    <h3 className="text-lg font-semibold text-white mb-3">Tags</h3>
                    <div className="flex flex-wrap gap-2">
                      {ship.tags.map((tag, index) => (
                        <span
                          key={index}
                          className="bg-primary text-white px-3 py-1 rounded-full text-sm"
                        >
                          #{tag}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* Members */}
                {ship.members && ship.members.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-3">
                      Members ({ship.members.length})
                    </h3>
                    <div className="bg-secondary-dark rounded-lg p-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {ship.members.map((member) => (
                          <div key={member.id} className="flex items-center">
                            <img
                              src={member.user.avatar || "/images/avatars/default.png"}
                              alt={member.user.displayName}
                              className="w-8 h-8 rounded-full mr-3 flex-shrink-0"
                            />
                            <div className="min-w-0 flex-1">
                              <p className="text-white text-sm font-medium truncate">
                                {member.user.displayName}
                              </p>
                              <p className="text-gray-400 text-xs">
                                {member.role} • Joined {new Date(member.joinedAt).toLocaleDateString()}
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Additional Info */}
        <div className="bg-secondary rounded-lg shadow-lg p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
            <div>
              <h4 className="text-gray-400 text-sm uppercase tracking-wide mb-1">
                Founded
              </h4>
              <p className="text-white font-semibold">
                {new Date(ship.createdAt).toLocaleDateString()}
              </p>
            </div>
            <div>
              <h4 className="text-gray-400 text-sm uppercase tracking-wide mb-1">
                Members
              </h4>
              <p className="text-white font-semibold">{ship.memberCount}</p>
            </div>
            <div>
              <h4 className="text-gray-400 text-sm uppercase tracking-wide mb-1">
                Captain
              </h4>
              <p className="text-white font-semibold">{ship.captain.displayName}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}