/**
 * Volunteer System Type Definitions
 * Enhanced with check-in functionality
 */

export type VolunteerAssignmentStatus = 
  | 'pending'
  | 'assigned' 
  | 'confirmed'
  | 'checked_in'
  | 'completed'
  | 'abandoned'
  | 'no-show'
  | 'cancelled';

export type PaymentStatus = 
  | 'pending'
  | 'processing'
  | 'paid'
  | 'cancelled';

export type CheckInWindowStatus = 
  | 'not_ready'
  | 'available'
  | 'expired'
  | 'checked_in';

export interface CheckInStatus {
  canCheckIn: boolean;
  isCheckedIn: boolean;
  timeUntilCheckIn?: number; // minutes until check-in opens
  timeUntilExpiry?: number;  // minutes until check-in closes
  windowStart: Date;         // 15 minutes before shift
  windowEnd: Date;           // shift start time
  status: CheckInWindowStatus;
}

export interface VolunteerEvent {
  id: string;
  name: string;
  description: string;
  shortDescription: string | null;
  startDate: string;
  endDate: string;
  location: string | null;
  image: string | null;
  status: 'draft' | 'published' | 'cancelled' | 'completed';
}

export interface VolunteerCategory {
  id: string;
  name: string;
  description: string | null;
  payRate: number;
  eventId: string;
  leadManagerId: string | null;
  leadManager?: {
    id: string;
    displayName: string;
    email: string;
  };
  stats?: {
    totalShifts: number;
    totalSignups: number;
    availableSlots: number;
  };
}

export interface VolunteerShift {
  id: string;
  title: string;
  description: string | null;
  startTime: string;
  endTime: string;
  location: string | null;
  maxVolunteers: number;
  vacancies: number;
  categoryId: string;
  eventId: string;
  category: {
    id: string;
    name: string;
    payRate: number;
  };
  event: {
    id: string;
    name: string;
  };
  assignments?: VolunteerAssignment[];
}

export interface VolunteerAssignment {
  id: string;
  userId: string;
  shiftId: string;
  status: VolunteerAssignmentStatus;
  notes: string | null;
  metadata: any; // JSON field for additional form data
  createdAt: string;
  updatedAt: string;
  user?: {
    id: string;
    displayName: string;
    email: string;
    avatar: string;
  };
  shift: {
    id: string;
    title: string;
    description: string | null;
    startTime: string;
    endTime: string;
    location: string | null;
    category: {
      id: string;
      name: string;
      payRate: number;
    };
    event: {
      id: string;
      name: string;
    };
  };
  hours?: VolunteerHours;
  notificationPreferences?: {
    emailNotification: boolean;
    websiteNotification: boolean;
  };
  // Computed check-in status (client-side)
  checkInStatus?: CheckInStatus;
}

export interface VolunteerHours {
  id: string;
  assignmentId: string;
  userId: string;
  hoursWorked: number;
  paymentAmount: number;
  paymentStatus: PaymentStatus;
  verifiedById: string | null;
  verifiedAt: string | null;
  transactionId: string | null;
  createdAt: string;
  updatedAt: string;
  verifiedBy?: {
    id: string;
    displayName: string;
    email: string;
  };
}

export interface VolunteerSignupFormData {
  firstName: string;
  lastName: string;
  email: string;
  pirateName?: string;
  stayingWith?: string;
  isDock?: boolean;
  isLandGrant?: boolean;
  landGrantCredit?: number;
  pronouns?: string;
  addToDeedsLottery?: boolean;
  emailNotification: boolean;
  websiteNotification: boolean;
}

// API Response types for check-in functionality
export interface CheckInStatusResponse {
  assignmentId: string;
  shiftId: string;
  shiftTitle: string;
  shiftStartTime: string;
  currentStatus: VolunteerAssignmentStatus;
  checkInStatus: CheckInStatus;
}

export interface CheckInResponse {
  message: string;
  assignment: {
    id: string;
    status: VolunteerAssignmentStatus;
    shift: {
      title: string;
      startTime: string;
      category: string;
      event: string;
    };
  };
}

// Dashboard stats interfaces
export interface VolunteerStats {
  totalEvents: number;
  totalShifts: number;
  totalAssignments: number;
  completedAssignments: number;
  pendingPayments: number;
  totalHoursWorked: number;
  totalPaymentAmount: number;
}

export interface LeadDashboardStats {
  todayShifts: number;
  checkedInVolunteers: number;
  completedShifts: number;
  pendingPayments: number;
  categoryName: string;
}

// Search and filter types
export interface VolunteerSearchFilters {
  eventId?: string;
  categoryId?: string;
  status?: VolunteerAssignmentStatus[];
  dateFrom?: string;
  dateTo?: string;
  paymentStatus?: PaymentStatus[];
}

export interface VolunteerLeadFilters {
  date?: string;
  status?: VolunteerAssignmentStatus[];
}

// Payment processing types
export interface PaymentProcessingRequest {
  paymentIds: string[];
  note?: string;
}

export interface PaymentProcessingResult {
  paymentId: string;
  status: 'success' | 'error';
  transactionId?: string;
  error?: string;
}

export interface PaymentProcessingResponse {
  message: string;
  results: PaymentProcessingResult[];
}