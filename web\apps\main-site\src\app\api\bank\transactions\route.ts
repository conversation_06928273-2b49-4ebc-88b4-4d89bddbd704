import { NextRequest, NextResponse } from "next/server";
import prisma from "../../../../lib/prisma";
import jwt from "jsonwebtoken";
import {


  createNotification,
  createAdminNotification,
} from "../../../../lib/notifications";
// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
const JWT_SECRET = process.env.JWT_SECRET || "your-development-secret-key";

// Helper function to verify token
function verifyToken(token: string) {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    return null;
  }
}

export const GET = async (req: NextRequest) => {
  try {
    // Get authorization header
    const authHeader = req.headers.get("authorization");

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Extract token
    const token = authHeader.split(" ")[1];

    // Verify token
    const decoded = verifyToken(token);

    if (!decoded || typeof decoded !== "object" || !decoded.id) {
      return NextResponse.json({ error: "Invalid token" }, { status: 401 });
    }

    const userId = decoded.id as string;

    // Get query parameters
    const url = new URL(req.url);
    const type = url.searchParams.get("type");
    const startDate = url.searchParams.get("startDate");
    const endDate = url.searchParams.get("endDate");
    const search = url.searchParams.get("search");

    // Build filter conditions
    const whereConditions: any = {
      OR: [{ senderId: userId }, { recipientId: userId }],
    };

    if (type) {
      whereConditions.type = type;
    }

    if (startDate && endDate) {
      whereConditions.createdAt = {
        gte: new Date(startDate),
        lte: new Date(endDate),
      };
    } else if (startDate) {
      whereConditions.createdAt = {
        gte: new Date(startDate),
      };
    } else if (endDate) {
      whereConditions.createdAt = {
        lte: new Date(endDate),
      };
    }

    if (search) {
      // Create a search condition that preserves the user filter
      const userFilter = whereConditions.OR;

      // Create search conditions
      const searchConditions = [
        { description: { contains: search } },
        { note: { contains: search } },
      ];

      // Combine user filter with search conditions
      whereConditions.AND = [{ OR: userFilter }, { OR: searchConditions }];

      // Remove the original OR condition since it's now in the AND array
      delete whereConditions.OR;
    }

    // Query the database for transactions
    const transactions = await prisma.transaction.findMany({
      where: whereConditions,
      include: {
        sender: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
        recipient: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
        processedBy: {
          select: {
            id: true,
            username: true,
            displayName: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Format dates as ISO strings for JSON serialization
    const formattedTransactions = transactions.map((transaction) => ({
      ...transaction,
      createdAt: transaction.createdAt.toISOString(),
      updatedAt: transaction.updatedAt.toISOString(),
      processedAt: transaction.processedAt
        ? transaction.processedAt.toISOString()
        : null,
    }));

    return NextResponse.json(formattedTransactions);
  } catch (error) {
    console.error("Error fetching transactions:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
};

export const POST = async (req: NextRequest) => {
  try {
    // Get authorization header
    const authHeader = req.headers.get("authorization");

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Extract token
    const token = authHeader.split(" ")[1];

    // Verify token
    const decoded = verifyToken(token);

    if (!decoded || typeof decoded !== "object" || !decoded.id) {
      return NextResponse.json({ error: "Invalid token" }, { status: 401 });
    }

    const userId = decoded.id as string;

    // Get request body
    const { type, amount, recipient, description, note } = await req.json();

    // Validate input
    if (!type || !amount) {
      return NextResponse.json(
        { error: "Type and amount are required" },
        { status: 400 },
      );
    }

    // Validate amount
    if (amount <= 0) {
      return NextResponse.json(
        { error: "Amount must be greater than 0" },
        { status: 400 },
      );
    }

    // Get the user
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Handle different transaction types
    if (type === "withdrawal") {
      // Check if user has enough balance
      if (user.balance < amount) {
        return NextResponse.json(
          { error: "Insufficient balance" },
          { status: 400 },
        );
      }

      // Create withdrawal transaction and update balance in a transaction
      const transaction = await prisma.$transaction(async (prisma) => {
        // Create the transaction as pending
        const newTransaction = await prisma.transaction.create({
          data: {
            amount: amount,
            type: type,
            status: "pending", // Withdrawals start as pending
            description: description || "Withdrawal request",
            note: note,
            senderId: userId,
            paymentMethod: "bank_transfer", // Default payment method
          },
          include: {
            sender: {
              select: {
                id: true,
                username: true,
                displayName: true,
                avatar: true,
              },
            },
          },
        });

        // Deduct the amount from the user's balance immediately (escrow)
        await prisma.user.update({
          where: { id: userId },
          data: { balance: { decrement: amount } },
        });

        return newTransaction;
      });

      // Create notification for the user
      await createNotification(userId, {
        category: "transaction",
        type: "withdrawal_request",
        title: "Withdrawal Request Submitted",
        message: `Your withdrawal request of NS ${amount.toFixed(
          0,
        )} has been submitted and is pending approval.`,
        link: "/bank/dashboard/withdraw",
        priority: "medium",
        transactionId: transaction.id,
      });

      // Create notifications for all bankers
      const bankers = await prisma.user.findMany({
        where: {
          isBanker: true,
          status: "active",
        },
        select: {
          id: true,
        },
      });

      for (const banker of bankers) {
        await createNotification(banker.id, {
          category: "transaction",
          type: "withdrawal_request",
          title: "New Withdrawal Request",
          message: `New withdrawal request of NS ${amount.toFixed(
            0,
          )} from ${transaction.sender?.username}`,
          link: "/bank/cashier/pending-transactions",
          priority: "high",
          transactionId: transaction.id,
        });
      }

      // Format dates for response
      const formattedTransaction = {
        ...transaction,
        createdAt: transaction.createdAt.toISOString(),
        updatedAt: transaction.updatedAt.toISOString(),
        processedAt: transaction.processedAt
          ? transaction.processedAt.toISOString()
          : null,
      };

      return NextResponse.json(formattedTransaction);
    } else if (type === "transfer") {
      // Validate recipient
      if (!recipient) {
        return NextResponse.json(
          { error: "Recipient is required for transfers" },
          { status: 400 },
        );
      }

      // Find recipient user (remove @ if present)
      const recipientUsername = recipient.startsWith("@")
        ? recipient.substring(1)
        : recipient;

      const recipientUser = await prisma.user.findUnique({
        where: { username: recipientUsername },
      });

      if (!recipientUser) {
        return NextResponse.json(
          { error: "Recipient not found" },
          { status: 404 },
        );
      }

      // Check if user has enough balance
      if (user.balance < amount) {
        return NextResponse.json(
          { error: "Insufficient balance" },
          { status: 400 },
        );
      }

      // Create transaction and update balances in a transaction
      const transaction = await prisma.$transaction(async (prisma) => {
        // Create the transaction
        const newTransaction = await prisma.transaction.create({
          data: {
            amount: amount,
            type: type,
            status: "completed",
            description:
              description || `Transfer to @${recipientUser.username}`,
            note: note,
            senderId: userId,
            recipientId: recipientUser.id,
          },
          include: {
            sender: {
              select: {
                id: true,
                username: true,
                displayName: true,
                avatar: true,
              },
            },
            recipient: {
              select: {
                id: true,
                username: true,
                displayName: true,
                avatar: true,
              },
            },
          },
        });

        // Update sender's balance
        await prisma.user.update({
          where: { id: userId },
          data: { balance: { decrement: amount } },
        });

        // Update recipient's balance
        await prisma.user.update({
          where: { id: recipientUser.id },
          data: { balance: { increment: amount } },
        });

        return newTransaction;
      });

      // Create notification for sender
      await createNotification(userId, {
        category: "transaction",
        type: "transfer_sent",
        title: "Transfer Sent",
        message: `You sent NS ${amount.toFixed(0)} to @${
          recipientUser.username
        }. View this transaction in your Bank Dashboard > Transactions section.`,
        // No link property - clicking will just mark as read
        priority: "medium",
        transactionId: transaction.id,
      });

      // Create notification for recipient
      await createNotification(recipientUser.id, {
        category: "transaction",
        type: "transfer_received",
        title: "Transfer Received",
        message: `You received NS ${amount.toFixed(0)} from @${
          user.username
        }. View this transaction in your Bank Dashboard > Transactions section.`,
        // No link property - clicking will just mark as read
        priority: "medium",
        transactionId: transaction.id,
      });

      // Format dates for response
      const formattedTransaction = {
        ...transaction,
        createdAt: transaction.createdAt.toISOString(),
        updatedAt: transaction.updatedAt.toISOString(),
        processedAt: transaction.processedAt
          ? transaction.processedAt.toISOString()
          : null,
      };

      return NextResponse.json(formattedTransaction);
    } else if (type === "donation") {
      // Validate recipient
      if (!recipient) {
        return NextResponse.json(
          { error: "Recipient is required for donations" },
          { status: 400 },
        );
      }

      // Check if user has enough balance
      if (user.balance < amount) {
        return NextResponse.json(
          { error: "Insufficient balance" },
          { status: 400 },
        );
      }

      // Create transaction and update balance in a transaction
      const transaction = await prisma.$transaction(async (prisma) => {
        // Create the transaction
        const newTransaction = await prisma.transaction.create({
          data: {
            amount: amount,
            type: type,
            status: "completed",
            description: description || `Donation to ${recipient}`,
            note: note,
            senderId: userId,
          },
        });

        // Update sender's balance
        await prisma.user.update({
          where: { id: userId },
          data: { balance: { decrement: amount } },
        });

        return newTransaction;
      });

      // Create notification for the user
      await createNotification(userId, {
        category: "transaction",
        type: "donation_made",
        title: "Donation Made",
        message: `You donated NS ${amount.toFixed(
          0,
        )} to the ${recipient}. View your donation in the Bank Dashboard > Donate section.`,
        // No link property - clicking will just mark as read
        priority: "medium",
        transactionId: transaction.id,
      });

      // Create notification for admins about the donation
      await createAdminNotification({
        type: "donation_received",
        title: "Donation Received",
        message: `${user.username} donated NS ${amount.toFixed(
          0,
        )} to the ${recipient}. View this donation in the Admin Dashboard > Transactions section.`,
        // No link property - clicking will just mark as read
        priority: "low",
        transactionId: transaction.id,
      });

      // Format dates for response
      const formattedTransaction = {
        ...transaction,
        createdAt: transaction.createdAt.toISOString(),
        updatedAt: transaction.updatedAt.toISOString(),
        processedAt: transaction.processedAt
          ? transaction.processedAt.toISOString()
          : null,
      };

      return NextResponse.json(formattedTransaction);
    } else {
      return NextResponse.json(
        { error: "Unsupported transaction type" },
        { status: 400 },
      );
    }
  } catch (error) {
    console.error("Error creating transaction:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
};
