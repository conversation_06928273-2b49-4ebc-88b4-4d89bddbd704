"use client";

import { useProduct } from "@/hooks/useProducts";
import { useAddToCart } from "@/hooks/useCart";
import { useState } from "react";
import { <PERSON><PERSON>, Spinner, Card } from "@bank-of-styx/ui";
import { toast } from "react-hot-toast";
import Image from "next/image";
import Link from "next/link";

export default function ProductDetailPage({
  params,
}: {
  params: { id: string };
}) {
  const { id } = params;
  const { data: productData, isLoading, error } = useProduct(id);
  const [quantity, setQuantity] = useState(1);
  const addToCart = useAddToCart();

  const handleAddToCart = async () => {
    try {
      await addToCart.mutateAsync({
        productId: id,
        quantity,
      });
      toast.success("Added to cart");
    } catch (error) {
      toast.error("Failed to add to cart");
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[50vh]">
        <Spinner size="lg" />
      </div>
    );
  }

  if (error || !productData) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="p-8 text-center">
          <h2 className="text-2xl font-bold mb-4">Product Not Found</h2>
          <p className="mb-6">
            The product you're looking for doesn't exist or has been removed.
          </p>
          <Link href="/shop">
            <Button variant="primary">Back to Shop</Button>
          </Link>
        </Card>
      </div>
    );
  }

  const product = productData.product;
  const isOutOfStock = product.inventory !== null && product.inventory <= 0;
  const isFreeProduct = product.isFree;

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-4">
        <Link href="/shop" className="text-primary hover:underline">
          ← Back to Shop
        </Link>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Product Image */}
        <div className="bg-secondary-light rounded-lg overflow-hidden">
          {product.image ? (
            <div className="relative aspect-square">
              <Image
                src={product.image}
                alt={product.name}
                fill
                className="object-cover"
              />
            </div>
          ) : (
            <div className="aspect-square flex items-center justify-center bg-secondary">
              <span className="text-text-muted">No image available</span>
            </div>
          )}
        </div>

        {/* Product Details */}
        <div>
          <h1 className="text-3xl font-bold mb-2">{product.name}</h1>
          {isFreeProduct ? (
            <div className="mb-4">
              <p className="text-2xl font-semibold text-success mb-2">FREE</p>
              <p className="text-sm text-warning bg-warning-light p-2 rounded">
                This is a complimentary item available only through redemption codes.
                It cannot be purchased directly.
              </p>
            </div>
          ) : (
            <p className="text-2xl font-semibold text-primary mb-4">
              ${product.price.toFixed(2)}
            </p>
          )}

          {product.shortDescription && (
            <p className="text-lg mb-4">{product.shortDescription}</p>
          )}

          <div className="mb-6">
            <p className="font-semibold mb-1">Category:</p>
            <p>{product.category.name}</p>
          </div>

          {product.eventId && product.event && (
            <div className="mb-6">
              <p className="font-semibold mb-1">Event:</p>
              <p>{product.event.name}</p>
              <p className="text-sm text-text-muted">
                {new Date(product.event.startDate).toLocaleDateString()} -
                {new Date(product.event.endDate).toLocaleDateString()}
              </p>
            </div>
          )}

          {product.inventory !== null && (
            <div className="mb-6">
              <p className="font-semibold mb-1">Availability:</p>
              <p className={isOutOfStock ? "text-accent" : "text-success"}>
                {isOutOfStock
                  ? "Out of Stock"
                  : `${product.inventory} in stock`}
              </p>
            </div>
          )}

          {/* Add to Cart */}
          {!isFreeProduct ? (
            <div className="flex items-center space-x-4 mt-8">
              <div className="w-24">
                <input
                  type="number"
                  min="1"
                  max={product.inventory || 99}
                  value={quantity}
                  onChange={(e) => setQuantity(parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-border-subtle rounded-md bg-bg-input"
                  disabled={isOutOfStock}
                />
              </div>
              <Button
                variant="primary"
                size="lg"
                onClick={handleAddToCart}
                disabled={isOutOfStock || addToCart.isPending}
                loading={addToCart.isPending}
              >
                Add to Cart
              </Button>
            </div>
          ) : (
            <div className="mt-8 p-4 bg-info-light border border-info rounded-lg">
              <p className="text-info-dark font-medium mb-2">How to get this item:</p>
              <p className="text-sm text-info-dark">
                This complimentary item can only be obtained using a redemption code. 
                If you have a code, enter it on the shop homepage.
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Product Description */}
      {product.description && (
        <Card className="mt-8 p-6">
          <h2 className="text-xl font-bold mb-4">Description</h2>
          <div className="prose max-w-none">
            {product.description.split("\n").map((paragraph, index) => (
              <p key={index} className="mb-4">
                {paragraph}
              </p>
            ))}
          </div>
        </Card>
      )}
    </div>
  );
}
