import { NextRequest, NextResponse } from "next/server";
import prisma from "../../../../../lib/prisma";
import { verifyToken } from "../../../../../lib/auth";
import { writeFile, mkdir } from "fs/promises";
import { existsSync } from "fs";
import path from "path";
import { v4 as uuidv4 } from "uuid";
import { createNotification } from "../../../../../lib/notifications";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// Maximum file size (20MB)
const MAX_FILE_SIZE = 20 * 1024 * 1024;

// Allowed file types
const ALLOWED_FILE_TYPES = [
  "image/jpeg",
  "image/jpg",
  "image/png",
  "application/pdf",
];

// Upload directory - moved outside of public folder
const UPLOAD_DIR = path.join(process.cwd(), "..", "..", "uploads", "deposits");

// Helper function to ensure upload directory exists
async function ensureUploadDir() {
  if (!existsSync(UPLOAD_DIR)) {
    await mkdir(UPLOAD_DIR, { recursive: true });
  }
}

export const GET = async (req: NextRequest) => {
  try {
    // Get authorization header
    const authHeader = req.headers.get("authorization");

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Extract token
    const token = authHeader.split(" ")[1];

    // Verify token
    const decoded = verifyToken(token);

    if (!decoded || !decoded.id) {
      return NextResponse.json({ error: "Invalid token" }, { status: 401 });
    }

    const userId = decoded.id as string;

    // Get query parameters
    const url = new URL(req.url);
    const limitParam = url.searchParams.get("limit");
    const includeAllParam = url.searchParams.get("includeAll");
    const limit = limitParam ? parseInt(limitParam, 10) : 5;
    const includeAll = includeAllParam === "true";

    // Create the where condition
    const whereCondition = {
      senderId: userId,
      type: "deposit",
    };

    // Create the query options with proper typing
    const queryOptions: {
      where: typeof whereCondition;
      orderBy: any[]; // Using any[] for orderBy to avoid complex type issues
      include: {
        sender: {
          select: {
            id: boolean;
            username: boolean;
            displayName: boolean;
            avatar: boolean;
          };
        };
        processedBy: {
          select: {
            id: boolean;
            username: boolean;
            displayName: boolean;
          };
        };
      };
      take?: number;
    } = {
      where: whereCondition,
      orderBy: [
        // First order by status to put pending at the top
        { status: "asc" },
        // Then by date descending
        { createdAt: "desc" },
      ],
      include: {
        sender: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
        processedBy: {
          select: {
            id: true,
            username: true,
            displayName: true,
          },
        },
      },
    };

    // Only apply limit if not including all
    if (!includeAll) {
      queryOptions.take = limit;
    }

    // Query the database for deposits
    const deposits = await prisma.transaction.findMany(queryOptions);

    // Format dates as ISO strings for JSON serialization
    const formattedDeposits = deposits.map((deposit) => ({
      ...deposit,
      createdAt: deposit.createdAt.toISOString(),
      updatedAt: deposit.updatedAt.toISOString(),
      processedAt: deposit.processedAt
        ? deposit.processedAt.toISOString()
        : null,
    }));

    return NextResponse.json(formattedDeposits);
  } catch (error) {
    console.error("Error fetching deposits:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
};

export const POST = async (req: NextRequest) => {
  try {
    // Get authorization header
    const authHeader = req.headers.get("authorization");

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Extract token
    const token = authHeader.split(" ")[1];

    // Verify token
    const decoded = verifyToken(token);

    if (!decoded || !decoded.id) {
      return NextResponse.json({ error: "Invalid token" }, { status: 401 });
    }

    const userId = decoded.id as string;

    // Parse the form data
    const formData = await req.formData();
    const amountStr = formData.get("amount");
    const note = formData.get("note") as string | null;
    const receiptId = formData.get("receiptId") as string | null;
    const receiptUrl = formData.get("receiptUrl") as string | null;
    const receipt = formData.get("receipt") as File | null; // Keep for backwards compatibility

    // Validate amount
    if (!amountStr || isNaN(Number(amountStr)) || Number(amountStr) <= 0) {
      return NextResponse.json(
        { error: "Valid amount is required" },
        { status: 400 },
      );
    }

    const amount = Number(amountStr);

    // Validate receipt - now accepts either receiptId, receiptUrl, or File upload
    if (!receiptId && !receiptUrl && !receipt) {
      return NextResponse.json(
        { error: "Receipt file is required" },
        { status: 400 },
      );
    }

    let receiptPath = "";

    // Handle different receipt input types
    if (receiptId) {
      // If receiptId is provided, look up the uploaded file
      try {
        const uploadedFile = await prisma.uploadedImage.findUnique({
          where: { id: receiptId },
        });

        if (!uploadedFile) {
          return NextResponse.json(
            { error: "Receipt file not found" },
            { status: 400 },
          );
        }

        receiptPath = uploadedFile.url;
      } catch (error) {
        console.error("Error finding uploaded file:", error);
        return NextResponse.json(
          { error: "Invalid receipt file reference" },
          { status: 400 },
        );
      }
    } else if (receiptUrl) {
      // If receiptUrl is provided, use it directly
      receiptPath = receiptUrl;
    } else if (receipt) {
      // Legacy file upload handling
      // Check file size
      if (receipt.size > MAX_FILE_SIZE) {
        return NextResponse.json(
          { error: "File size exceeds the 20MB limit" },
          { status: 400 },
        );
      }

      // Check file type
      if (!ALLOWED_FILE_TYPES.includes(receipt.type)) {
        return NextResponse.json(
          { error: "Invalid file type. Allowed types: JPEG, PNG, PDF" },
          { status: 400 },
        );
      }

      // Ensure upload directory exists
      await ensureUploadDir();

      // Generate a unique filename
      const uniqueId = uuidv4();
      const fileExtension = receipt.name.split(".").pop() || "jpg";
      const filename = `deposit_${uniqueId}_${Date.now()}.${fileExtension}`;
      const filepath = path.join(UPLOAD_DIR, filename);
      receiptPath = `/api/uploads/deposits/${filename}`;

      // Read file as ArrayBuffer and convert to Uint8Array
      const arrayBuffer = await receipt.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);

      // Write file to disk
      await writeFile(filepath, uint8Array);
    }

    // Create the deposit transaction
    const transaction = await prisma.transaction.create({
      data: {
        amount: amount,
        type: "deposit",
        status: "pending",
        description: `Deposit request of NS ${amount.toFixed(0)}`,
        note: note || undefined,
        senderId: userId,
        receiptImage: receiptPath,
        paymentMethod: "bank_transfer", // Default payment method
      },
      include: {
        sender: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
      },
    });

    // Check if the user is also a cashier
    const userDetails = await prisma.user.findUnique({
      where: { id: userId },
      select: { isBanker: true },
    });

    // Create notification for the user with instructions in the message instead of a link
    const locationMessage = userDetails?.isBanker
      ? "Please check the Cashier Dashboard > Pending Transactions to view and process this request."
      : "Please check Bank Dashboard > Deposits to view this request.";

    await createNotification(userId, {
      category: "transaction",
      type: "deposit_request",
      title: "Deposit Request Submitted",
      message: `Your deposit request of NS ${amount.toFixed(
        0,
      )} has been submitted and is pending approval. ${locationMessage}`,
      // No link property - clicking will just mark as read
      priority: "medium",
      transactionId: transaction.id,
    });

    // Create notifications for all bankers
    const bankers = await prisma.user.findMany({
      where: {
        isBanker: true,
        status: "active",
      },
      select: {
        id: true,
      },
    });

    for (const banker of bankers) {
      await createNotification(banker.id, {
        category: "transaction",
        type: "deposit_request",
        title: "New Deposit Request",
        message: `New deposit request of NS ${amount.toFixed(
          0,
        )} from ${transaction.sender
          ?.username}. Please check Cashier Dashboard > Pending Transactions to process this request.`,
        // No link property - clicking will just mark as read
        priority: "high",
        transactionId: transaction.id,
      });
    }

    // Format dates for response
    const formattedTransaction = {
      ...transaction,
      createdAt: transaction.createdAt.toISOString(),
      updatedAt: transaction.updatedAt.toISOString(),
      processedAt: transaction.processedAt
        ? transaction.processedAt.toISOString()
        : null,
    };

    return NextResponse.json(formattedTransaction);
  } catch (error) {
    console.error("Error creating deposit:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
};
