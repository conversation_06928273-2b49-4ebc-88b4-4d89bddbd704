import React from "react";
import { Transaction } from "../../services/bankService";

export interface MemberTransactionStatsProps {
  transactions: Transaction[];
  userId: string;
}

export const MemberTransactionStats: React.FC<MemberTransactionStatsProps> = ({
  transactions,
  userId,
}) => {
  // Calculate statistics
  const stats = React.useMemo(() => {
    const totalDeposits = transactions
      .filter((t) => t.type === "deposit" && t.status === "completed")
      .reduce((sum, t) => sum + t.amount, 0);

    const totalWithdrawals = transactions
      .filter((t) => t.type === "withdrawal" && t.status === "completed")
      .reduce((sum, t) => sum + t.amount, 0);

    const totalTransfersIn = transactions
      .filter(
        (t) =>
          t.type === "transfer" &&
          t.recipientId === userId &&
          t.status === "completed",
      )
      .reduce((sum, t) => sum + t.amount, 0);

    const totalTransfersOut = transactions
      .filter(
        (t) =>
          t.type === "transfer" &&
          t.senderId === userId &&
          t.status === "completed",
      )
      .reduce((sum, t) => sum + t.amount, 0);

    const totalDonations = transactions
      .filter(
        (t) =>
          t.type === "donation" &&
          t.senderId === userId &&
          t.status === "completed",
      )
      .reduce((sum, t) => sum + t.amount, 0);

    const pendingDeposits = transactions
      .filter((t) => t.type === "deposit" && t.status === "pending")
      .reduce((sum, t) => sum + t.amount, 0);

    const pendingWithdrawals = transactions
      .filter((t) => t.type === "withdrawal" && t.status === "pending")
      .reduce((sum, t) => sum + t.amount, 0);

    const depositCount = transactions.filter(
      (t) => t.type === "deposit",
    ).length;
    const withdrawalCount = transactions.filter(
      (t) => t.type === "withdrawal",
    ).length;
    const transferCount = transactions.filter(
      (t) => t.type === "transfer",
    ).length;
    const donationCount = transactions.filter(
      (t) => t.type === "donation",
    ).length;

    // Calculate net flow (money in - money out)
    const netFlow =
      totalDeposits +
      totalTransfersIn -
      totalWithdrawals -
      totalTransfersOut -
      totalDonations;

    return {
      totalDeposits,
      totalWithdrawals,
      totalTransfersIn,
      totalTransfersOut,
      totalDonations,
      pendingDeposits,
      pendingWithdrawals,
      depositCount,
      withdrawalCount,
      transferCount,
      donationCount,
      netFlow,
      totalTransactions: transactions.length,
    };
  }, [transactions, userId]);

  return (
    <div className="bg-secondary-light rounded-lg shadow-md p-6 border border-gray-600 mb-6">
      <h3 className="text-xl font-bold text-white mb-4">
        Transaction Statistics
      </h3>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div className="bg-secondary-dark p-4 rounded-lg">
          <p className="text-gray-400 text-sm">Total Transactions</p>
          <p className="text-white text-2xl font-bold">
            {stats.totalTransactions}
          </p>
        </div>
        <div className="bg-secondary-dark p-4 rounded-lg">
          <p className="text-gray-400 text-sm">Net Flow</p>
          <p
            className={`text-2xl font-bold ${
              stats.netFlow >= 0 ? "text-success" : "text-error"
            }`}
          >
            NS {stats.netFlow.toFixed(0)}
          </p>
        </div>
        <div className="bg-secondary-dark p-4 rounded-lg">
          <p className="text-gray-400 text-sm">Pending Deposits</p>
          <p className="text-warning text-2xl font-bold">
            NS {stats.pendingDeposits.toFixed(0)}
          </p>
        </div>
        <div className="bg-secondary-dark p-4 rounded-lg">
          <p className="text-gray-400 text-sm">Pending Withdrawals</p>
          <p className="text-warning text-2xl font-bold">
            NS {stats.pendingWithdrawals.toFixed(0)}
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-secondary-dark p-4 rounded-lg">
          <h4 className="text-lg font-semibold text-white mb-3">Money In</h4>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Deposits:</span>
              <span className="text-success">
                NS {stats.totalDeposits.toFixed(0)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Transfers In:</span>
              <span className="text-success">
                NS {stats.totalTransfersIn.toFixed(0)}
              </span>
            </div>
            <div className="h-px bg-gray-600 my-2"></div>
            <div className="flex justify-between items-center font-bold">
              <span className="text-white">Total In:</span>
              <span className="text-success">
                NS {(stats.totalDeposits + stats.totalTransfersIn).toFixed(0)}
              </span>
            </div>
          </div>
        </div>

        <div className="bg-secondary-dark p-4 rounded-lg">
          <h4 className="text-lg font-semibold text-white mb-3">Money Out</h4>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Withdrawals:</span>
              <span className="text-error">
                NS {stats.totalWithdrawals.toFixed(0)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Transfers Out:</span>
              <span className="text-error">
                NS {stats.totalTransfersOut.toFixed(0)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Donations:</span>
              <span className="text-error">
                NS {stats.totalDonations.toFixed(0)}
              </span>
            </div>
            <div className="h-px bg-gray-600 my-2"></div>
            <div className="flex justify-between items-center font-bold">
              <span className="text-white">Total Out:</span>
              <span className="text-error">
                NS{" "}
                {(
                  stats.totalWithdrawals +
                  stats.totalTransfersOut +
                  stats.totalDonations
                ).toFixed(0)}
              </span>
            </div>
          </div>
        </div>
      </div>

      <div className="mt-6 pt-6 border-t border-gray-600">
        <h4 className="text-lg font-semibold text-white mb-3">
          Transaction Counts
        </h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-secondary-dark p-3 rounded-lg text-center">
            <p className="text-gray-400 text-sm">Deposits</p>
            <p className="text-white text-xl font-bold">{stats.depositCount}</p>
          </div>
          <div className="bg-secondary-dark p-3 rounded-lg text-center">
            <p className="text-gray-400 text-sm">Withdrawals</p>
            <p className="text-white text-xl font-bold">
              {stats.withdrawalCount}
            </p>
          </div>
          <div className="bg-secondary-dark p-3 rounded-lg text-center">
            <p className="text-gray-400 text-sm">Transfers</p>
            <p className="text-white text-xl font-bold">
              {stats.transferCount}
            </p>
          </div>
          <div className="bg-secondary-dark p-3 rounded-lg text-center">
            <p className="text-gray-400 text-sm">Donations</p>
            <p className="text-white text-xl font-bold">
              {stats.donationCount}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
