/**
 * Heartbeat Service for Server-Sent Events (SSE)
 *
 * This module provides a service for sending periodic heartbeat messages to SSE connections
 * to keep them alive and prevent timeouts.
 */

import { connectionStore } from "./connectionStore";
import crypto from "crypto";

// Helper function for timestamped logging
function logWithTimestamp(message: string, ...args: any[]) {
  const timestamp = new Date().toISOString().replace("T", " ").replace("Z", "");
  console.log(`[${timestamp}] ${message}`, ...args);
}

// Helper function for timestamped error logging
function errorWithTimestamp(message: string, ...args: any[]) {
  const timestamp = new Date().toISOString().replace("T", " ").replace("Z", "");
  console.error(`[${timestamp}] ${message}`, ...args);
}

// Helper function to get a stack trace
function getStackTrace(): string {
  const obj = {};
  Error.captureStackTrace(obj, getStackTrace);
  return (obj as any).stack;
}

// Heartbeat interval in milliseconds (30 seconds)
// More frequent heartbeats help prevent connection timeouts
const HEARTBEAT_INTERVAL = 30000;

// Cleanup interval in milliseconds (5 minutes)
const CLEANUP_INTERVAL = 5 * 60 * 1000;

// Maximum inactive time before connection is considered stale (30 minutes)
const MAX_INACTIVE_MINUTES = 30;

// Use a global object to track the heartbeat service state
// This is more reliable than module-level variables in Next.js
// because it will be shared across all instances of this module
declare global {
  var __heartbeatService:
    | {
        heartbeatIntervalId: NodeJS.Timeout | null;
        cleanupIntervalId: NodeJS.Timeout | null;
        serviceId: string | null;
        isRunning: boolean;
        startTime: Date | null;
        // Add a counter to track initialization attempts in development mode
        initAttempts: number;
        // Add a flag to track if we're in a hot reload cycle
        isHotReload: boolean;
        // Add a timestamp to track when the module was last loaded
        lastLoaded: Date;
        // Add a flag to track if this is the first load of the module
        isFirstLoad: boolean;
        // Add a counter to track module loads
        moduleLoads: number;
      }
    | undefined;
}

// Initialize the global heartbeat service state if it doesn't exist
if (!global.__heartbeatService) {
  global.__heartbeatService = {
    heartbeatIntervalId: null,
    cleanupIntervalId: null,
    serviceId: null,
    isRunning: false,
    startTime: null,
    initAttempts: 0,
    isHotReload: false,
    lastLoaded: new Date(),
    isFirstLoad: true,
    moduleLoads: 1,
  };
} else {
  // Update module load tracking
  global.__heartbeatService.moduleLoads++;
  global.__heartbeatService.isFirstLoad = false;

  // Calculate time since last load
  const now = new Date();
  const timeSinceLastLoad =
    now.getTime() - global.__heartbeatService.lastLoaded.getTime();
  global.__heartbeatService.lastLoaded = now;

  // Log module load information
  logWithTimestamp(
    `[Heartbeat] Module loaded (load #${global.__heartbeatService.moduleLoads}, ${timeSinceLastLoad}ms since last load)`,
  );
}

// In development mode, we need to detect hot reloads
if (process.env.NODE_ENV === "development") {
  // If we already have a service ID but we're initializing again,
  // this is likely a hot reload
  if (global.__heartbeatService.serviceId) {
    global.__heartbeatService.isHotReload = true;
    logWithTimestamp(
      `[Heartbeat] Hot reload detected, existing service ID: ${global.__heartbeatService.serviceId}`,
    );
  }
}

// Local references for convenience
let heartbeatIntervalId: NodeJS.Timeout | null =
  global.__heartbeatService.heartbeatIntervalId;
let cleanupIntervalId: NodeJS.Timeout | null =
  global.__heartbeatService.cleanupIntervalId;
let heartbeatServiceId: string | null = global.__heartbeatService.serviceId;

/**
 * Start the heartbeat service
 *
 * This function implements a robust singleton pattern using a global variable
 * to ensure only one heartbeat service is running across all server instances.
 */
export function startHeartbeatService(): void {
  // Generate a stack trace to identify where the service is being started from
  const stackTrace = getStackTrace();

  // Check if the service is already running using the global state
  if (global.__heartbeatService?.isRunning) {
    logWithTimestamp(
      `[Heartbeat] Service already running with ID: ${global.__heartbeatService.serviceId}`,
    );
    logWithTimestamp(
      `[Heartbeat] Started at: ${global.__heartbeatService.startTime?.toISOString()}`,
    );
    logWithTimestamp(`[Heartbeat] Attempted to start from:\n${stackTrace}`);

    // In development mode, we need to be extra careful
    if (process.env.NODE_ENV === "development") {
      // If this is a hot reload, update the local references to match the global state
      if (global.__heartbeatService.isHotReload) {
        heartbeatIntervalId = global.__heartbeatService.heartbeatIntervalId;
        cleanupIntervalId = global.__heartbeatService.cleanupIntervalId;
        heartbeatServiceId = global.__heartbeatService.serviceId;
        logWithTimestamp(
          `[Heartbeat] Updated local references during hot reload`,
        );
      }
    }

    return;
  }

  // Use a mutex-like approach with double-checking to prevent race conditions
  if (global.__heartbeatService?.heartbeatIntervalId || heartbeatIntervalId) {
    logWithTimestamp(
      `[Heartbeat] Service has active intervals but is marked as not running. Stopping existing service first.`,
    );
    stopHeartbeatService();
  }

  // In development mode, check if we're in a hot reload cycle
  if (
    process.env.NODE_ENV === "development" &&
    global.__heartbeatService!.isHotReload
  ) {
    logWithTimestamp(
      `[Heartbeat] Hot reload detected, resetting hot reload flag`,
    );
    global.__heartbeatService!.isHotReload = false;
  }

  // Generate a unique ID for this heartbeat service instance
  const newServiceId = crypto.randomUUID().substring(0, 8);
  heartbeatServiceId = newServiceId;
  global.__heartbeatService!.serviceId = newServiceId;
  global.__heartbeatService!.startTime = new Date();

  logWithTimestamp(`[Heartbeat] Starting service with ID: ${newServiceId}`);

  // Send heartbeats at regular intervals
  const newHeartbeatInterval = setInterval(async () => {
    try {
      const sentCount = await connectionStore.sendHeartbeat(newServiceId);
      if (sentCount === 0) {
        // No active connections, no need to log
      }
    } catch (error) {
      errorWithTimestamp(
        `[Heartbeat] Error sending heartbeat (Service ID: ${newServiceId}):`,
        error,
      );
    }
  }, HEARTBEAT_INTERVAL);

  heartbeatIntervalId = newHeartbeatInterval;
  global.__heartbeatService!.heartbeatIntervalId = newHeartbeatInterval;

  // Clean up inactive connections periodically
  const newCleanupInterval = setInterval(async () => {
    try {
      await connectionStore.cleanupInactiveConnections(MAX_INACTIVE_MINUTES);
    } catch (error) {
      errorWithTimestamp(
        `[Heartbeat] Error cleaning up connections (Service ID: ${newServiceId}):`,
        error,
      );
    }
  }, CLEANUP_INTERVAL);

  cleanupIntervalId = newCleanupInterval;
  global.__heartbeatService!.cleanupIntervalId = newCleanupInterval;

  // Mark the service as running in the global state
  global.__heartbeatService!.isRunning = true;

  logWithTimestamp(`[Heartbeat] Service started with ID: ${newServiceId}`);
}

/**
 * Stop the heartbeat service
 *
 * This function ensures proper cleanup of the heartbeat service
 * and updates the global state to reflect that the service is stopped.
 */
export function stopHeartbeatService(): void {
  // Get the current service ID for logging
  const currentServiceId =
    global.__heartbeatService?.serviceId || heartbeatServiceId;

  // Clear the heartbeat interval
  if (global.__heartbeatService?.heartbeatIntervalId) {
    clearInterval(global.__heartbeatService.heartbeatIntervalId);
    logWithTimestamp(
      `[Heartbeat] Cleared global heartbeat interval for service ID: ${currentServiceId}`,
    );
    global.__heartbeatService.heartbeatIntervalId = null;
  }

  if (heartbeatIntervalId) {
    clearInterval(heartbeatIntervalId);
    logWithTimestamp(
      `[Heartbeat] Cleared local heartbeat interval for service ID: ${currentServiceId}`,
    );
    heartbeatIntervalId = null;
  }

  // Clear the cleanup interval
  if (global.__heartbeatService?.cleanupIntervalId) {
    clearInterval(global.__heartbeatService.cleanupIntervalId);
    logWithTimestamp(
      `[Heartbeat] Cleared global cleanup interval for service ID: ${currentServiceId}`,
    );
    global.__heartbeatService.cleanupIntervalId = null;
  }

  if (cleanupIntervalId) {
    clearInterval(cleanupIntervalId);
    logWithTimestamp(
      `[Heartbeat] Cleared local cleanup interval for service ID: ${currentServiceId}`,
    );
    cleanupIntervalId = null;
  }

  // Reset the service state
  if (global.__heartbeatService) {
    global.__heartbeatService.isRunning = false;
    global.__heartbeatService.serviceId = null;
    global.__heartbeatService.startTime = null;
  }

  heartbeatServiceId = null;

  logWithTimestamp(`[Heartbeat] Service stopped (ID: ${currentServiceId})`);
}

/**
 * Initialize the heartbeat service at server startup
 *
 * This function should be called once during server initialization
 * to ensure the heartbeat service is started properly.
 *
 * It includes special handling for development mode to prevent
 * multiple services from being started during hot reloads.
 */
export function initializeHeartbeatService(): void {
  // Increment the initialization attempts counter
  global.__heartbeatService!.initAttempts++;

  // In development mode, we need to be more careful about initialization
  if (process.env.NODE_ENV === "development") {
    // If we're in a hot reload and the service is already running, don't restart it
    if (
      global.__heartbeatService!.isHotReload &&
      global.__heartbeatService!.isRunning
    ) {
      logWithTimestamp(
        `[Heartbeat] Skipping initialization during hot reload (attempt #${
          global.__heartbeatService!.initAttempts
        })`,
      );
      logWithTimestamp(
        `[Heartbeat] Existing service ID: ${
          global.__heartbeatService!.serviceId
        }`,
      );
      return;
    }

    // If we've already attempted to initialize more than once, this might be a duplicate
    if (global.__heartbeatService!.initAttempts > 1) {
      logWithTimestamp(
        `[Heartbeat] Multiple initialization attempts detected (attempt #${
          global.__heartbeatService!.initAttempts
        })`,
      );

      // If the service is already running, don't restart it
      if (global.__heartbeatService!.isRunning) {
        logWithTimestamp(
          `[Heartbeat] Service already running with ID: ${
            global.__heartbeatService!.serviceId
          }`,
        );
        return;
      }

      // If the service was started less than 5 seconds ago, don't restart it
      // This helps prevent duplicate services during rapid module reloads
      if (global.__heartbeatService!.startTime) {
        const now = new Date();
        const timeSinceStart =
          now.getTime() - global.__heartbeatService!.startTime.getTime();
        if (timeSinceStart < 5000) {
          // 5 seconds
          logWithTimestamp(
            `[Heartbeat] Service was started recently (${Math.round(
              timeSinceStart,
            )}ms ago), skipping initialization`,
          );
          return;
        }
      }
    }
  }

  logWithTimestamp(
    `[Heartbeat] Initializing service at server startup (attempt #${
      global.__heartbeatService!.initAttempts
    })`,
  );
  startHeartbeatService();
}

// Create a lock mechanism to prevent multiple initializations
let isInitializing = false;

/**
 * Safe initialization function that uses a lock to prevent concurrent initializations
 */
export function safeInitializeHeartbeatService(): void {
  if (isInitializing) {
    logWithTimestamp(
      `[Heartbeat] Initialization already in progress, skipping duplicate call`,
    );
    return;
  }

  try {
    isInitializing = true;

    // Check if service is already running
    if (global.__heartbeatService!.isRunning) {
      logWithTimestamp(
        `[Heartbeat] Service already running with ID: ${
          global.__heartbeatService!.serviceId
        }, skipping initialization`,
      );
      return;
    }

    // Initialize the service
    initializeHeartbeatService();
  } finally {
    isInitializing = false;
  }
}

// In Next.js development mode, we need to be careful about auto-initialization
// because the module can be loaded multiple times during hot reloading
// We'll only auto-initialize in production, and for development we'll use a different approach
if (process.env.NODE_ENV === "production") {
  // Auto-initialize the heartbeat service when this module is imported in production
  safeInitializeHeartbeatService();
}
