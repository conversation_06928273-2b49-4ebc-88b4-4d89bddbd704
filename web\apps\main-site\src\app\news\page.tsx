"use client";

import React, { useState, useEffect } from "react";
import { SearchBar } from "@bank-of-styx/ui";
import { NewsArticleCard } from "../../components/news";
import { useAuth } from "../../contexts/AuthContext";
import { useUserState } from "../../contexts/UserStateContext";
import {
  useStateBasedNews,
  useStateBasedCategories,
} from "../../hooks/useStateBasedNews";
import { useRouter } from "next/navigation";
import {
  PublicArticle,
  PublicNewsResponse,
  PublicCategory,
} from "../../services/publicNewsService";

export default function NewsPage() {
  const router = useRouter();
  const { user, isAuthenticated, openAuthModal } = useAuth();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("Categories");
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  // Get user state
  const { userState } = useUserState();

  // Get categories from API with state-based caching
  const {
    data: categoriesData = [] as PublicCategory[],
    isLoading: isCategoriesLoading,
    error: categoriesError,
  } = useStateBasedCategories({ maxAgeMinutes: 1440 }); // Cache for 24 hours

  // Build API filters based on UI state
  const categorySlug =
    selectedCategory !== "Categories"
      ? (categoriesData as PublicCategory[]).find(
          (c) => c.name === selectedCategory,
        )?.slug
      : undefined;

  const apiFilters = {
    page: currentPage,
    limit: itemsPerPage,
    search: searchQuery || undefined,
    category: categorySlug,
    sortBy: "publishedAt" as const,
    order: sortOrder,
  };

  // Get articles data from API with state-based caching
  const {
    data: articlesResponse = {} as PublicNewsResponse,
    isLoading: isArticlesLoading,
    error: articlesError,
  } = useStateBasedNews({
    ...apiFilters,
    maxAgeMinutes: 30, // Cache for 30 minutes
  });

  // Add the "All Categories" option
  const categories =
    (categoriesData as PublicCategory[]).length > 0
      ? [
          "Categories",
          ...(categoriesData as PublicCategory[]).map((c) => c.name),
        ]
      : ["Categories"];

  // Check if user is subscribed to news
  useEffect(() => {
    if (user && user.preferences.notifications.newsAndEvents) {
      setIsSubscribed(true);
    } else {
      setIsSubscribed(false);
    }
  }, [user]);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setCurrentPage(1); // Reset to first page when search changes
  };

  const handleClearSearch = () => {
    setSearchQuery("");
  };

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    setCurrentPage(1); // Reset to first page when category changes
  };

  // Handle page change
  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  // Function to clear all filters
  const handleClearFilters = () => {
    setSearchQuery("");
    setSelectedCategory("Categories");
    setSortOrder("desc");
    setCurrentPage(1);
    handleClearSearch();
  };

  // Handle sort order change
  const handleSortOrderChange = (
    event: React.ChangeEvent<HTMLSelectElement>,
  ) => {
    setSortOrder(event.target.value as "asc" | "desc");
    setCurrentPage(1);
  };

  const handleSubscribe = () => {
    if (!isAuthenticated) {
      openAuthModal();
    } else if (user) {
      // In a real app, this would make an API call to update the user's preferences
      const updatedUser = {
        ...user,
        preferences: {
          ...user.preferences,
          notifications: {
            ...user.preferences.notifications,
            newsAndEvents: true,
          },
        },
      };

      setIsSubscribed(true);
      console.log("User subscribed to news:", updatedUser);
      alert("You have successfully subscribed to news updates!");
    }
  };

  // Check if filtering is active
  const isFilteringActive =
    searchQuery !== "" || selectedCategory !== "Categories";

  // Extract data from API response or use empty arrays as fallbacks
  const articlesData = (articlesResponse as PublicNewsResponse).data || [];
  const meta = (articlesResponse as PublicNewsResponse).meta || {
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    hasNext: false,
    hasPrev: false,
  };

  // Split into featured and regular articles
  let featuredArticle: PublicArticle | null = null;
  let regularArticles: PublicArticle[] = [];

  if (!isFilteringActive && articlesData.length > 0) {
    // Find a featured article
    const featuredArticles = articlesData.filter(
      (article: PublicArticle) => article.featured,
    );
    if (featuredArticles.length > 0) {
      // Use the first featured article
      featuredArticle = featuredArticles[0];
      // Filter out the featured article from regular articles
      regularArticles = articlesData.filter(
        (article: PublicArticle) => article.id !== featuredArticle!.id,
      );
    } else {
      // If no featured articles, just use the first article
      featuredArticle = articlesData[0];
      regularArticles = articlesData.slice(1);
    }
  } else {
    // When filtering is active, all articles are regular
    regularArticles = articlesData;
  }

  // Loading state
  if (isArticlesLoading || isCategoriesLoading) {
    return (
      <div className="container mx-auto px-2 py-8">
        <div className="animate-pulse">
          <div className="flex justify-between items-center mb-2 md:mb-8">
            <div className="h-8 bg-gray-600 rounded w-1/4"></div>
          </div>
          <div className="h-10 bg-gray-600 rounded mb-8"></div>
          <div className="h-10 bg-gray-600 rounded mb-8"></div>
          <div className="h-64 bg-gray-600 rounded mb-8"></div>
          <div className="space-y-6">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-600 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (articlesError || categoriesError) {
    return (
      <div className="container mx-auto px-2 py-2">
        <div className="bg-secondary-light p-6 rounded-lg text-center border border-red-600 mt-4 md:mt-8 max-w-4xl mx-auto">
          <h3 className="text-lg md:text-xl font-bold mb-2 text-white">
            Error loading articles
          </h3>
          <p className="text-gray-400 mb-4 text-sm md:text-base">
            There was a problem loading the news articles. Please try again
            later.
          </p>
          <button
            className="bg-primary hover:bg-primary-dark text-white px-4 py-2 rounded-md text-sm transition-colors"
            onClick={() => window.location.reload()}
          >
            Refresh Page
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto pt-2 px-2 py-4 md:py-6 lg:py-8 max-w-[100vw] overflow-x-hidden">
      {/* Main header with Subscribe button */}
      <div className="flex justify-between items-center mb-4 md:mb-8">
        <h1 className="text-2xl md:text-3xl font-bold">News & Updates</h1>
        {!isSubscribed && (
          <button
            onClick={handleSubscribe}
            className="bg-primary hover:bg-primary-dark text-white px-3 py-1 sm:px-4 sm:py-2 rounded-md text-sm transition-colors ml-auto"
          >
            Subscribe
          </button>
        )}
      </div>

      <div className="mb-4 md:mb-8 max-w-full">
        <SearchBar
          placeholder="Search news..."
          onSearch={handleSearch}
          onClear={handleClearSearch}
          showButton={true}
          initialValue={searchQuery}
          categories={categories}
          selectedCategory={selectedCategory}
          onCategoryChange={handleCategoryChange}
          className="text-sm md:text-base"
        />
      </div>

      {/* Filter controls - shown when filters are active */}
      {isFilteringActive && (
        <div className="mb-4 md:mb-6">
          <div className="flex justify-end items-center">
            <div className="flex items-center gap-3">
              {/* Sort options */}
              <div className="flex items-center">
                <span className="text-gray-400 mr-2 text-sm md:text-base">
                  Sort by:
                </span>
                <select
                  value={sortOrder}
                  onChange={handleSortOrderChange}
                  className="bg-secondary border border-gray-600 rounded-md px-2 py-1 text-sm md:text-base text-white focus:outline-none focus:ring-2 focus:ring-primary"
                >
                  <option value="desc">Newest</option>
                  <option value="asc">Oldest</option>
                </select>
              </div>

              {/* Clear Filters button */}
              <button
                onClick={handleClearFilters}
                className="bg-secondary hover:bg-secondary-light text-white px-3 py-1 sm:px-4 sm:py-2 rounded-md text-sm transition-colors border border-gray-600"
              >
                Clear Filters
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Featured Article - only shown when no filters are active */}
      {!isFilteringActive && featuredArticle && (
        <div className="mb-8 md:mb-12">
          <div className="flex justify-between items-center mb-4 md:mb-6">
            <h2 className="text-xl md:text-2xl font-bold">
              {featuredArticle.featured ? "Featured Article" : "Latest News"}
            </h2>
          </div>
          <div className="max-w-4xl mx-auto">
            <NewsArticleCard
              article={featuredArticle}
              onClick={() => router.push(`/news/${featuredArticle.slug}`)}
            />
          </div>
        </div>
      )}

      {/* Main content with news articles */}
      {regularArticles.length > 0 ? (
        <div className="flex flex-col gap-6 max-w-4xl mx-auto">
          {regularArticles.map((article: PublicArticle) => (
            <NewsArticleCard
              key={article.id}
              article={article}
              onClick={() => router.push(`/news/${article.slug}`)}
            />
          ))}
        </div>
      ) : (
        !featuredArticle && (
          <div className="bg-secondary-light p-4 md:p-8 rounded-lg text-center border border-gray-600 mt-4 md:mt-8 max-w-4xl mx-auto">
            <h3 className="text-lg md:text-xl font-bold mb-2 text-white">
              No articles found
            </h3>
            <p className="text-gray-400 mb-4 text-sm md:text-base">
              No articles match your current search criteria. Try adjusting your
              filters or search query.
            </p>
            <button
              className="text-primary hover:text-primary-light font-medium text-sm md:text-base"
              onClick={handleClearFilters}
            >
              Clear all filters
            </button>
          </div>
        )
      )}

      {/* Pagination and article count */}
      {meta.totalCount > 0 && (
        <div className="mt-8 flex flex-col sm:flex-row justify-center items-center">
          {/* Article count - only show when filters are active */}
          {isFilteringActive && (
            <p className="text-gray-400 text-sm md:text-base mb-4 sm:mb-0 sm:mr-6">
              {meta.totalCount > 0 ? (
                <>
                  Showing {(meta.currentPage - 1) * itemsPerPage + 1}
                  {meta.currentPage * itemsPerPage < meta.totalCount
                    ? ` - ${meta.currentPage * itemsPerPage}`
                    : ""}
                  {` out of ${meta.totalCount}`} article
                  {meta.totalCount !== 1 ? "s" : ""}
                </>
              ) : (
                "No articles found"
              )}
            </p>
          )}

          {/* Pagination controls */}
          {meta.totalPages > 1 && (
            <div className="flex items-center space-x-2">
              <button
                onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                disabled={!meta.hasPrev}
                className={`px-3 py-1 rounded-md ${
                  !meta.hasPrev
                    ? "bg-gray-700 text-gray-400 cursor-not-allowed"
                    : "bg-secondary hover:bg-secondary-light text-white"
                } border border-gray-600`}
                aria-label="Previous page"
              >
                &larr;
              </button>

              <span className="text-gray-400 text-sm">
                Page {meta.currentPage} of {meta.totalPages}
              </span>

              <button
                onClick={() =>
                  handlePageChange(Math.min(meta.totalPages, currentPage + 1))
                }
                disabled={!meta.hasNext}
                className={`px-3 py-1 rounded-md ${
                  !meta.hasNext
                    ? "bg-gray-700 text-gray-400 cursor-not-allowed"
                    : "bg-secondary hover:bg-secondary-light text-white"
                } border border-gray-600`}
                aria-label="Next page"
              >
                &rarr;
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
