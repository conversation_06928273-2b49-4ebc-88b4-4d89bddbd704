# Pay Code Components

Specialized React components for the pay code generation and redemption system.

## Components

- **CreatePayCode.tsx** - Pay code creation form and generation interface
- **RedeemPayCode.tsx** - Pay code redemption form and validation interface
- **ManagePayCode.tsx** - Pay code management and history interface
- **modals/** - Modal components for pay code operations

## Component Features

These components provide:

- Intuitive pay code creation with amount validation
- Secure pay code redemption with verification
- Comprehensive pay code management tools
- QR code generation and display
- Real-time validation and error handling
- Mobile-responsive design for scanning
- Integration with banking transaction system
- Security features and fraud prevention

The pay code components create a seamless user experience for digital payment generation and redemption within the Bank of Styx platform.
