/**
 * User Service
 *
 * This service handles all user-related API calls, including profile updates
 * and security changes.
 *
 * Note: Notification preferences are now handled by notificationService.ts
 */
import fetchClient from "@/lib/fetchClient";

// Types for API requests
interface ProfileUpdateData {
  displayName: string;
  email?: string;
  avatar?: string;
}

interface SecurityUpdateData {
  currentPassword: string;
  newPassword: string;
}

/**
 * Update user profile information
 */
export async function updateProfile(data: ProfileUpdateData): Promise<any> {
  try {
    return fetchClient.put("/api/users/profile", data);
  } catch (error) {
    console.error("Profile update error:", error);
    throw error;
  }
}

/**
 * Update user default view
 */
export async function updateDefaultView(
  defaultView:
    | "dashboard"
    | "transactions"
    | "transfer"
    | "pay-code"
    | "donate",
): Promise<any> {
  try {
    return fetchClient.put("/api/users/default-view", { defaultView });
  } catch (error) {
    console.error("Default view update error:", error);
    throw error;
  }
}

/**
 * Update user password
 */
export async function updatePassword(data: SecurityUpdateData): Promise<any> {
  try {
    return fetchClient.put("/api/users/security", data);
  } catch (error) {
    console.error("Password update error:", error);
    throw error;
  }
}
