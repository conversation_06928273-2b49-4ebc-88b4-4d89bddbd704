-- This migration adds case-insensitive collation to username and email fields
-- to ensure case-insensitive searches work properly

-- Modify the username column to use case-insensitive collation
ALTER TABLE `user` MODIFY `username` VARCHAR(255) COLLATE utf8mb4_unicode_ci;

-- Modify the email column to use case-insensitive collation
ALTER TABLE `user` MODIFY `email` VARCHAR(255) COLLATE utf8mb4_unicode_ci;
