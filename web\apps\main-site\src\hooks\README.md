# Custom React Hooks

Reusable custom hooks containing business logic and state management for the Bank of Styx platform.

## Hook Categories

This directory contains hooks for:

- Authentication and user management
- Banking operations and real-time balance updates
- Form handling and validation
- API data fetching and caching
- Real-time notifications and SSE connections
- Local storage and persistence
- UI state management and interactions

Custom hooks encapsulate complex logic and promote code reuse across components while maintaining clean separation of concerns.
