"use client";

import React from "react";
import { useVolunteerEvents } from "@/hooks/usePublicVolunteer";
import { VolunteerEventCard } from "./VolunteerEventCard";

interface VolunteerEventsListProps {
  onEventSelect: (eventId: string) => void;
}

export const VolunteerEventsList: React.FC<VolunteerEventsListProps> = ({
  onEventSelect,
}) => {
  // Use the TanStack Query pattern for data fetching
  const { data, isLoading, error } = useVolunteerEvents();
  const events = data?.events || [];

  // Render events in a grid layout similar to the events page
  return (
    <div>
      <h2 className="text-xl font-bold text-white mb-4">Select an Event</h2>

      {isLoading ? (
        // Use the standard loading state pattern
        <div className="flex items-center justify-center h-40">
          <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-primary"></div>
        </div>
      ) : error ? (
        // Use the standard error state pattern
        <div className="bg-accent bg-opacity-20 border border-accent rounded-md p-4 text-accent">
          <p>{error instanceof Error ? error.message : "An error occurred"}</p>
        </div>
      ) : events.length === 0 ? (
        // Use the standard empty state pattern
        <div className="text-gray-400 p-4 text-center">
          <p className="mb-2">
            No events with volunteer opportunities available.
          </p>
          <p>Check back later for upcoming volunteer opportunities.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {events.map((event) => (
            <VolunteerEventCard
              key={event.id}
              event={event}
              onClick={() => onEventSelect(event.id)}
            />
          ))}
        </div>
      )}
    </div>
  );
};
