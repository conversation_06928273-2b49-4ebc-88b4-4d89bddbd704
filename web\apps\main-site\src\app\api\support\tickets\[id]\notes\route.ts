import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser, userHasRole } from "@/lib/auth";
import { sendEmail } from "@/lib/email";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// GET endpoint to retrieve notes for a specific ticket
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const { id } = params;

    // Check if user is authenticated
    const currentUser = await getCurrentUser(request);
    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    // Check if user is admin or the ticket owner
    const isAdmin = await userHasRole(request, "admin");

    // Get the ticket to check ownership
    const ticket = await prisma.supportTicket.findUnique({
      where: { id },
      select: { userId: true },
    });

    if (!ticket) {
      return NextResponse.json({ error: "Ticket not found" }, { status: 404 });
    }

    // Check if user has permission to view this ticket's notes
    if (!isAdmin && ticket.userId !== currentUser.id) {
      return NextResponse.json(
        { error: "You don't have permission to view notes for this ticket" },
        { status: 403 },
      );
    }

    // Get notes with filtering based on user role
    const notes = await prisma.ticketNote.findMany({
      where: {
        ticketId: id,
        // Non-admins can only see public notes
        ...(isAdmin ? {} : { isInternal: false }),
      },
      include: {
        author: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
      },
      orderBy: {
        createdAt: "asc",
      },
    });

    return NextResponse.json(notes);
  } catch (error) {
    console.error("Error fetching notes:", error);
    return NextResponse.json(
      { error: "Failed to fetch notes" },
      { status: 500 },
    );
  }
}

// POST endpoint to add a note to a ticket
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const { id } = params;

    // Check if user is authenticated
    const currentUser = await getCurrentUser(request);
    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    // Get the ticket to check ownership
    const ticket = await prisma.supportTicket.findUnique({
      where: { id },
      select: {
        userId: true,
        email: true,
        subject: true,
        status: true,
      },
    });

    if (!ticket) {
      return NextResponse.json({ error: "Ticket not found" }, { status: 404 });
    }

    // Parse the request body
    const body = await request.json();
    const { content, isInternal = false } = body;

    // Basic validation
    if (!content) {
      return NextResponse.json(
        { error: "Note content is required" },
        { status: 400 },
      );
    }

    // Check if user has permission to add internal notes
    const isAdmin = await userHasRole(request, "admin");
    if (isInternal && !isAdmin) {
      return NextResponse.json(
        { error: "Only admins can add internal notes" },
        { status: 403 },
      );
    }

    // Check if user has permission to add notes to this ticket
    if (!isAdmin && ticket.userId !== currentUser.id) {
      return NextResponse.json(
        { error: "You don't have permission to add notes to this ticket" },
        { status: 403 },
      );
    }

    // Create the note
    const note = await prisma.ticketNote.create({
      data: {
        content,
        isInternal,
        ticketId: id,
        authorId: currentUser.id,
      },
      include: {
        author: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
      },
    });

    // If this is a public note from an admin, send an email to the user
    if (!isInternal && isAdmin && ticket.userId) {
      const emailText = `
        New response to your support ticket:
        
        Ticket ID: ${id}
        Subject: ${ticket.subject}
        
        Response:
        ${content}
        
        You can reply to this email or log in to your account to view the full conversation.
        
        Thank you for contacting Bank of Styx support.
      `;

      const emailHtml = `
        <h2>New Response to Your Support Ticket</h2>
        <p><strong>Ticket ID:</strong> ${id}</p>
        <p><strong>Subject:</strong> ${ticket.subject}</p>
        <hr>
        <h3>Response:</h3>
        <p>${content.replace(/\n/g, "<br>")}</p>
        <p>You can reply to this email or log in to your account to view the full conversation.</p>
        <p>Thank you for contacting Bank of Styx support.</p>
      `;

      await sendEmail({
        to: ticket.email,
        subject: `Re: Support Ticket: ${ticket.subject}`,
        text: emailText,
        html: emailHtml,
      });
    }

    // If this is a note from a user, notify admin
    if (!isAdmin) {
      const ADMIN_EMAIL = process.env.ADMIN_EMAIL || "<EMAIL>";

      const emailText = `
        New response from user on support ticket:
        
        Ticket ID: ${id}
        Subject: ${ticket.subject}
        Status: ${ticket.status}
        
        User Response:
        ${content}
        
        Please log in to the admin dashboard to respond.
      `;

      const emailHtml = `
        <h2>New User Response on Support Ticket</h2>
        <p><strong>Ticket ID:</strong> ${id}</p>
        <p><strong>Subject:</strong> ${ticket.subject}</p>
        <p><strong>Status:</strong> ${ticket.status}</p>
        <hr>
        <h3>User Response:</h3>
        <p>${content.replace(/\n/g, "<br>")}</p>
        <p>Please log in to the admin dashboard to respond.</p>
      `;

      await sendEmail({
        to: ADMIN_EMAIL,
        subject: `User Response on Ticket: ${ticket.subject}`,
        text: emailText,
        html: emailHtml,
      });
    }

    return NextResponse.json(note);
  } catch (error) {
    console.error("Error adding note:", error);
    return NextResponse.json({ error: "Failed to add note" }, { status: 500 });
  }
}
