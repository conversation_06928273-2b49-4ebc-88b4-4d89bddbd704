"use client";

import React, { useEffect, useState, useMemo, useCallback } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "../../../../../contexts/AuthContext";
import {
  NewsDashboardLayout,
  NewsEditor,
  NewsImageUploader,
  CategorySelector,
  ArticlePreviewModal,
} from "../../../../../components/news";
import { sanitizeHtml } from "../../../../../lib/sanitize";
import Link from "next/link";
import {
  useArticle,
  useUpdateArticle,
  useCategories,
} from "../../../../../hooks/useNews";

interface EditArticlePageProps {
  params: {
    id: string;
  };
}

export default function EditArticlePage({ params }: EditArticlePageProps) {
  const { id } = params;
  const { user, isLoading: authLoading } = useAuth();
  const router = useRouter();
  const [isAuthorized, setIsAuthorized] = useState(false);

  // Form state
  const [title, setTitle] = useState("");
  const [content, setContent] = useState("");
  const [excerpt, setExcerpt] = useState("");
  const [categoryId, setCategoryId] = useState("");
  const [featured, setFeatured] = useState(false);
  const [status, setStatus] = useState<"published" | "draft" | "paused">(
    "draft",
  );
  const [imageUrl, setImageUrl] = useState("");
  const [errors, setErrors] = useState<{
    title?: string;
    content?: string;
    excerpt?: string;
    categoryId?: string;
    image?: string;
  }>({});

  // Preview state
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>("");

  // Fetch article data with React Query
  const {
    data: article,
    isLoading: articleLoading,
    isError: articleError,
    refetch: refetchArticle,
  } = useArticle(id);

  // Fetch categories
  const { data: categories, isLoading: categoriesLoading } = useCategories();

  // Update article mutation
  const updateArticleMutation = useUpdateArticle(id);

  // Handle image upload - memoized to prevent unnecessary re-renders
  const handleImageChange = useCallback(
    (imageUrl: string) => {
      setImageUrl(imageUrl);
      // Clear image error when an image is selected
      if (imageUrl && errors.image) {
        setErrors((prev) => ({ ...prev, image: undefined }));
      }
    },
    [errors.image],
  );

  // Handle category change - memoized to prevent unnecessary re-renders
  const handleCategoryChange = useCallback(
    (category: string) => {
      setCategoryId(category);
      // Clear category error when a category is selected
      if (category && errors.categoryId) {
        setErrors((prev) => ({ ...prev, categoryId: undefined }));
      }

      // Update selected category name for preview
      if (category && categories) {
        const selectedCat = categories.find((cat) => cat.id === category);
        if (selectedCat) {
          setSelectedCategory(selectedCat.name);
        }
      }
    },
    [errors.categoryId, categories],
  );

  // Memoize categories to prevent unnecessary re-renders of CategorySelector
  const memoizedCategories = useMemo(() => categories || [], [categories]);

  // Check authorization after auth is loaded
  useEffect(() => {
    // Only proceed with checks if auth loading is complete
    if (!authLoading) {
      if (!user || !user.roles?.editor) {
        router.push("/");
      } else {
        // User is authorized
        setIsAuthorized(true);
      }
    }
  }, [user, router, authLoading]);

  // Populate form with article data when it's loaded
  useEffect(() => {
    if (article) {
      setTitle(article.title);
      setContent(article.content);
      setExcerpt(article.excerpt);
      setCategoryId(article.category.id);
      setSelectedCategory(article.category.name);
      setFeatured(article.featured);
      setStatus(article.status);
      setImageUrl(article.image);
    }
  }, [article]);

  // Show loading state or nothing while checking authorization
  if (authLoading || !isAuthorized) {
    return (
      <div className="min-h-screen bg-secondary-dark flex items-center justify-center">
        <div className="bg-secondary-light p-6 rounded-lg shadow-md border border-gray-600">
          <p className="text-white text-lg">Loading...</p>
        </div>
      </div>
    );
  }

  // Validate form
  const validateForm = () => {
    const newErrors: typeof errors = {};

    if (!title.trim()) {
      newErrors.title = "Title is required";
    }

    if (!content.trim()) {
      newErrors.content = "Content is required";
    }

    if (!excerpt.trim()) {
      newErrors.excerpt = "Excerpt is required";
    }

    if (!categoryId) {
      newErrors.categoryId = "Category is required";
    }

    if (!imageUrl) {
      newErrors.image = "Featured image is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (
    e: React.FormEvent,
    newStatus?: "published" | "draft" | "paused",
  ) => {
    e.preventDefault();

    // If a new status is specified, use it, otherwise keep the current status
    const submissionStatus = newStatus || status;

    if (validateForm()) {
      try {
        // Sanitize content before submission
        const sanitizedContent = sanitizeHtml(content);

        // Submit the form data using mutation
        await updateArticleMutation.mutateAsync({
          title,
          content: sanitizedContent,
          excerpt,
          categoryId,
          image: imageUrl,
          status: submissionStatus,
          featured,
        });

        // Navigate to articles list on success
        router.push("/news/dashboard/articles");
      } catch (error) {
        console.error("Error updating article:", error);
      }
    }
  };

  // Show a loading state while fetching article data
  if (articleLoading) {
    return (
      <NewsDashboardLayout>
        <div className="space-y-6">
          <div className="bg-secondary-light rounded-lg shadow-md p-6 border border-gray-600">
            <div className="flex items-center justify-center h-64">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
              <p className="ml-2 text-gray-400">Loading article...</p>
            </div>
          </div>
        </div>
      </NewsDashboardLayout>
    );
  }

  // Show an error state if article fetching failed
  if (articleError) {
    return (
      <NewsDashboardLayout>
        <div className="space-y-6">
          <div className="bg-secondary-light rounded-lg shadow-md p-6 border border-gray-600">
            <div className="bg-accent bg-opacity-20 text-accent border border-accent rounded-md p-4">
              <p>
                Error loading article. The article may not exist or you don't
                have permission to edit it.
              </p>
              <div className="flex mt-4">
                <button
                  onClick={() => refetchArticle()}
                  className="mr-4 text-sm underline"
                >
                  Try Again
                </button>
                <Link
                  href="/news/dashboard/articles"
                  className="text-sm underline"
                >
                  Return to Articles List
                </Link>
              </div>
            </div>
          </div>
        </div>
      </NewsDashboardLayout>
    );
  }

  return (
    <NewsDashboardLayout>
      <div className="space-y-4">
        <div className="bg-secondary-light rounded-lg shadow-md p-4 border border-gray-600">
          <div className="flex justify-between items-center mb-2">
            <h2 className="text-2xl font-bold text-white">Edit Article</h2>
          </div>

          {updateArticleMutation.isError && (
            <div className="bg-accent bg-opacity-20 text-accent border border-accent rounded-md p-4 mb-6">
              <p>Error updating article. Please try again.</p>
            </div>
          )}

          <form onSubmit={handleSubmit}>
            <div className="space-y-4">
              <div>
                <label
                  htmlFor="title"
                  className="block text-sm font-medium text-gray-400 mb-1"
                >
                  Title
                </label>
                <input
                  type="text"
                  id="title"
                  className={`w-full px-4 py-2 bg-secondary border ${
                    errors.title ? "border-accent" : "border-gray-600"
                  } rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary`}
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  disabled={updateArticleMutation.isPending}
                />
                {errors.title && (
                  <p className="mt-1 text-sm text-accent">{errors.title}</p>
                )}
              </div>

              <div>
                <label
                  htmlFor="excerpt"
                  className="block text-sm font-medium text-gray-400 mb-1"
                >
                  Excerpt
                </label>
                <input
                  type="text"
                  id="excerpt"
                  className={`w-full px-4 py-2 bg-secondary border ${
                    errors.excerpt ? "border-accent" : "border-gray-600"
                  } rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary`}
                  value={excerpt}
                  onChange={(e) => setExcerpt(e.target.value)}
                  disabled={updateArticleMutation.isPending}
                />
                {errors.excerpt && (
                  <p className="mt-1 text-sm text-accent">{errors.excerpt}</p>
                )}
              </div>

              <div>
                <label
                  htmlFor="category"
                  className="block text-sm font-medium text-gray-400 mb-1"
                >
                  Category
                </label>
                <CategorySelector
                  selectedCategory={categoryId}
                  onChange={handleCategoryChange}
                  error={!!errors.categoryId}
                  errorMessage={errors.categoryId}
                  disabled={
                    updateArticleMutation.isPending || categoriesLoading
                  }
                  categories={memoizedCategories}
                />
              </div>

              <div>
                <label
                  htmlFor="content"
                  className="block text-sm font-medium text-gray-400 mb-1"
                >
                  Content
                </label>
                <NewsEditor
                  value={content}
                  onChange={setContent}
                  error={!!errors.content}
                  errorMessage={errors.content}
                  disabled={updateArticleMutation.isPending}
                  articleId={id}
                />
              </div>

              <div>
                <label
                  htmlFor="image"
                  className="block text-sm font-medium text-gray-400 mb-1"
                >
                  Featured Image
                </label>
                <div className="flex items-center space-x-4">
                  <div className="flex-1">
                    <NewsImageUploader
                      imageUrl={imageUrl}
                      onChange={handleImageChange}
                      error={!!errors.image}
                      errorMessage={errors.image}
                      disabled={updateArticleMutation.isPending}
                      articleId={id}
                    />
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-6">
                <div className="flex items-center">
                  <input
                    id="featured"
                    type="checkbox"
                    className="h-4 w-4 text-primary focus:ring-primary border-gray-600 rounded"
                    checked={featured}
                    onChange={(e) => setFeatured(e.target.checked)}
                    disabled={updateArticleMutation.isPending}
                  />
                  <label
                    htmlFor="featured"
                    className="ml-2 text-sm text-gray-400"
                  >
                    Featured Article
                  </label>
                </div>

                {/* Status selector */}
                <div className="flex items-center space-x-1">
                  <label htmlFor="status" className="text-sm text-gray-400">
                    Status:
                  </label>
                  <select
                    id="status"
                    className="bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary px-2 py-1"
                    value={status}
                    onChange={(e) =>
                      setStatus(
                        e.target.value as "published" | "draft" | "paused",
                      )
                    }
                    disabled={updateArticleMutation.isPending}
                  >
                    <option value="draft">Draft</option>
                    <option value="published">Published</option>
                    <option value="paused">Paused</option>
                  </select>
                </div>
              </div>

              <div className="flex justify-end space-x-4 pt-4 border-t border-gray-600">
                <Link
                  href="/news/dashboard/articles"
                  className="px-4 py-2 bg-secondary text-white rounded-md hover:bg-secondary-dark"
                >
                  Cancel
                </Link>
                <button
                  type="submit"
                  className={`px-4 py-2 ${
                    updateArticleMutation.isPending
                      ? "bg-gray-600 cursor-not-allowed"
                      : "bg-primary hover:bg-primary-dark"
                  } text-white rounded-md flex items-center`}
                  disabled={updateArticleMutation.isPending}
                >
                  {updateArticleMutation.isPending && (
                    <svg
                      className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                  )}
                  Save Changes
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </NewsDashboardLayout>
  );
}
