import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth";

export const dynamic = 'force-dynamic';

interface Params {
  params: {
    id: string;
  };
}

export async function POST(request: Request, { params }: Params) {
  try {
    const { id: shipId } = params;
    const currentUser = await getCurrentUser(request);

    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { requestId, action, message } = body;

    if (!requestId || !action || !['accept', 'decline'].includes(action)) {
      return NextResponse.json(
        { error: "Request ID and valid action (accept/decline) are required" },
        { status: 400 }
      );
    }

    // Verify the user is the captain of this ship
    const ship = await prisma.ship.findUnique({
      where: { 
        id: shipId,
        captainId: currentUser.id,
        status: 'active',
      },
    });

    if (!ship) {
      return NextResponse.json(
        { error: "Ship not found or you are not the captain" },
        { status: 404 }
      );
    }

    // Get the join request
    const joinRequest = await prisma.shipJoinRequest.findUnique({
      where: { 
        id: requestId,
        shipId: shipId,
        status: 'pending',
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
      },
    });

    if (!joinRequest) {
      return NextResponse.json(
        { error: "Join request not found or already processed" },
        { status: 404 }
      );
    }

    // Check if user is already a member of any ship
    if (action === 'accept') {
      const existingMembership = await prisma.shipMember.findFirst({
        where: {
          userId: joinRequest.userId,
          status: 'active',
        },
      });

      if (existingMembership) {
        return NextResponse.json(
          { error: "User is already a member of another ship" },
          { status: 400 }
        );
      }
    }

    // Process the request
    await prisma.$transaction(async (tx) => {
      // Update the join request
      await tx.shipJoinRequest.update({
        where: { id: requestId },
        data: {
          status: action === 'accept' ? 'accepted' : 'declined',
          respondedAt: new Date(),
        },
      });

      // If accepted, create ship membership
      if (action === 'accept') {
        await tx.shipMember.create({
          data: {
            userId: joinRequest.userId,
            shipId: shipId,
            role: 'Member',
            status: 'active',
          },
        });
      }
    });

    const responseMessage = action === 'accept' 
      ? `${joinRequest.user.displayName} has been accepted to join ${ship.name}`
      : `${joinRequest.user.displayName}'s request to join ${ship.name} has been declined`;

    return NextResponse.json({
      success: true,
      message: responseMessage,
      action,
      user: joinRequest.user,
    });
  } catch (error) {
    console.error("Error responding to join request:", error);
    return NextResponse.json(
      { error: "Failed to process join request response" },
      { status: 500 }
    );
  }
}