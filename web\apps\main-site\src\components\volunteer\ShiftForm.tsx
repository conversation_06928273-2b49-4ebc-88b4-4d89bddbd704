"use client";

import React, { useState, useEffect } from "react";
import { Button } from "@bank-of-styx/ui";
import {
  VolunteerShift,
  useCreateVolunteerShift,
  useUpdateVolunteerShift,
} from "@/hooks/useVolunteerShifts";
import {
  generateIntervalShifts,
  validateIntervalShift,
} from "@/utils/shiftIntervalHelper";

interface ShiftFormProps {
  eventId: string;
  categoryId: string;
  shift?: VolunteerShift | null;
  onSuccess: () => void;
  onCancel: () => void;
}

export const ShiftForm: React.FC<ShiftFormProps> = ({
  eventId,
  categoryId,
  shift,
  onSuccess,
  onCancel,
}) => {
  const isEditing = !!shift;

  // Form state
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [date, setDate] = useState("");
  const [startTime, setStartTime] = useState("");
  const [endTime, setEndTime] = useState("");
  const [location, setLocation] = useState("");
  const [maxVolunteers, setMaxVolunteers] = useState("1");
  const [intervalHours, setIntervalHours] = useState<number | null>(null);
  const [intervalValidation, setIntervalValidation] = useState<{
    isValid: boolean;
    message?: string;
  } | null>(null);

  // Error state
  const [errors, setErrors] = useState<{
    title?: string;
    date?: string;
    startTime?: string;
    endTime?: string;
    maxVolunteers?: string;
    intervalHours?: string;
    general?: string;
  }>({});

  // Mutations
  const createMutation = useCreateVolunteerShift(eventId, categoryId);
  const updateMutation = useUpdateVolunteerShift(
    shift?.id || null,
    eventId,
    categoryId,
  );

  // Initialize form with shift data if editing
  useEffect(() => {
    if (shift) {
      setTitle(shift.title);
      setDescription(shift.description || "");

      // Parse date and times from shift
      const startDate = new Date(shift.startTime);
      const endDate = new Date(shift.endTime);

      setDate(startDate.toISOString().split("T")[0]);
      setStartTime(startDate.toTimeString().slice(0, 5));
      setEndTime(endDate.toTimeString().slice(0, 5));

      setLocation(shift.location || "");
      setMaxVolunteers(shift.maxVolunteers.toString());
    }
  }, [shift]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    const validationErrors: typeof errors = {};

    if (!title.trim()) {
      validationErrors.title = "Title is required";
    }

    if (!date) {
      validationErrors.date = "Date is required";
    }

    if (!startTime) {
      validationErrors.startTime = "Start time is required";
    }

    if (!endTime) {
      validationErrors.endTime = "End time is required";
    }

    if (startTime && endTime && startTime >= endTime) {
      validationErrors.endTime = "End time must be after start time";
    }

    if (!maxVolunteers || parseInt(maxVolunteers) < 1) {
      validationErrors.maxVolunteers = "Maximum volunteers must be at least 1";
    }

    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    // Clear errors
    setErrors({});

    try {
      // Create ISO date strings for start and end times
      const startDateTime = new Date(`${date}T${startTime}`);
      const endDateTime = new Date(`${date}T${endTime}`);

      const baseShiftData = {
        title,
        description: description || undefined,
        startTime: startDateTime.toISOString(),
        endTime: endDateTime.toISOString(),
        location: location || undefined,
        maxVolunteers: parseInt(maxVolunteers),
      };

      if (isEditing) {
        // When editing, we don't support interval creation
        await updateMutation.mutateAsync(baseShiftData);
      } else {
        if (intervalHours) {
          // Generate shifts based on the selected interval
          const shifts = generateIntervalShifts(baseShiftData, intervalHours);

          // Create all shifts in a batch
          await createMutation.mutateAsync(shifts);
        } else {
          // Create a single shift
          await createMutation.mutateAsync(baseShiftData);
        }
      }

      // Reset form and notify parent
      resetForm();
      onSuccess();
    } catch (error) {
      console.error("Error saving shift:", error);
      setErrors({
        general:
          error instanceof Error ? error.message : "Failed to save shift",
      });
    }
  };

  // Reset form
  const resetForm = () => {
    setTitle("");
    setDescription("");
    setDate("");
    setStartTime("");
    setEndTime("");
    setLocation("");
    setMaxVolunteers("1");
    setIntervalHours(null);
    setIntervalValidation(null);
    setErrors({});
  };

  // Validate interval selection when time inputs change
  useEffect(() => {
    if (intervalHours && date && startTime && endTime) {
      try {
        const startDateTime = new Date(`${date}T${startTime}`);
        const endDateTime = new Date(`${date}T${endTime}`);

        if (!isNaN(startDateTime.getTime()) && !isNaN(endDateTime.getTime())) {
          const validation = validateIntervalShift(
            startDateTime.toISOString(),
            endDateTime.toISOString(),
            intervalHours,
          );
          setIntervalValidation(validation);
        }
      } catch (error) {
        console.error("Error validating interval:", error);
      }
    } else {
      setIntervalValidation(null);
    }
  }, [date, startTime, endTime, intervalHours]);

  return (
    <div className="bg-secondary rounded-lg shadow-md p-6 border border-gray-600">
      <h2 className="text-xl font-bold text-white mb-4">
        {isEditing ? "Edit Shift" : "Create New Shift"}
      </h2>

      <form onSubmit={handleSubmit}>
        {/* Title */}
        <div className="mb-4">
          <label htmlFor="title" className="block text-white mb-2">
            Title <span className="text-accent">*</span>
          </label>
          <input
            id="title"
            type="text"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            className={`w-full px-3 py-2 bg-secondary-dark border ${
              errors.title ? "border-accent" : "border-gray-600"
            } rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary`}
          />
          {errors.title && (
            <p className="mt-1 text-accent text-sm">{errors.title}</p>
          )}
        </div>

        {/* Description */}
        <div className="mb-4">
          <label htmlFor="description" className="block text-white mb-2">
            Description
          </label>
          <textarea
            id="description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            rows={3}
            className="w-full px-3 py-2 bg-secondary-dark border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary"
          />
        </div>

        {/* Date */}
        <div className="mb-4">
          <label htmlFor="date" className="block text-white mb-2">
            Date <span className="text-accent">*</span>
          </label>
          <input
            id="date"
            type="date"
            value={date}
            onChange={(e) => setDate(e.target.value)}
            className={`w-full px-3 py-2 bg-secondary-dark border ${
              errors.date ? "border-accent" : "border-gray-600"
            } rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary`}
          />
          {errors.date && (
            <p className="mt-1 text-accent text-sm">{errors.date}</p>
          )}
        </div>

        {/* Time Range */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label htmlFor="startTime" className="block text-white mb-2">
              Start Time <span className="text-accent">*</span>
            </label>
            <input
              id="startTime"
              type="time"
              value={startTime}
              onChange={(e) => setStartTime(e.target.value)}
              className={`w-full px-3 py-2 bg-secondary-dark border ${
                errors.startTime ? "border-accent" : "border-gray-600"
              } rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary`}
            />
            {errors.startTime && (
              <p className="mt-1 text-accent text-sm">{errors.startTime}</p>
            )}
          </div>
          <div>
            <label htmlFor="endTime" className="block text-white mb-2">
              End Time <span className="text-accent">*</span>
            </label>
            <input
              id="endTime"
              type="time"
              value={endTime}
              onChange={(e) => setEndTime(e.target.value)}
              className={`w-full px-3 py-2 bg-secondary-dark border ${
                errors.endTime ? "border-accent" : "border-gray-600"
              } rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary`}
            />
            {errors.endTime && (
              <p className="mt-1 text-accent text-sm">{errors.endTime}</p>
            )}
          </div>
        </div>

        {/* Location */}
        <div className="mb-4">
          <label htmlFor="location" className="block text-white mb-2">
            Location
          </label>
          <input
            id="location"
            type="text"
            value={location}
            onChange={(e) => setLocation(e.target.value)}
            className="w-full px-3 py-2 bg-secondary-dark border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary"
          />
        </div>

        {/* Max Volunteers */}
        <div className="mb-4">
          <label htmlFor="maxVolunteers" className="block text-white mb-2">
            Maximum Volunteers <span className="text-accent">*</span>
          </label>
          <input
            id="maxVolunteers"
            type="number"
            min="1"
            value={maxVolunteers}
            onChange={(e) => setMaxVolunteers(e.target.value)}
            className={`w-full px-3 py-2 bg-secondary-dark border ${
              errors.maxVolunteers ? "border-accent" : "border-gray-600"
            } rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary`}
          />
          {errors.maxVolunteers && (
            <p className="mt-1 text-accent text-sm">{errors.maxVolunteers}</p>
          )}
        </div>

        {/* Interval Creation - Only show when creating a new shift */}
        {!isEditing && (
          <div className="mb-6">
            <label htmlFor="intervalHours" className="block text-white mb-2">
              Interval Creation
            </label>
            <select
              id="intervalHours"
              value={intervalHours?.toString() || ""}
              onChange={(e) => {
                const value = e.target.value;
                setIntervalHours(value ? parseInt(value) : null);
              }}
              className="w-full px-3 py-2 bg-secondary-dark border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary"
            >
              <option value="">None (Create single shift)</option>
              <option value="1">1 Hour Intervals</option>
              <option value="2">2 Hour Intervals</option>
              <option value="4">4 Hour Intervals</option>
              <option value="6">6 Hour Intervals</option>
            </select>

            {/* Show interval validation message if available */}
            {intervalValidation && intervalHours && (
              <p
                className={`mt-1 text-sm ${
                  intervalValidation.isValid ? "text-success" : "text-accent"
                }`}
              >
                {intervalValidation.message}
              </p>
            )}

            {/* Show explanation of interval creation */}
            <p className="mt-2 text-xs text-gray-400">
              Selecting an interval will automatically create multiple shifts of
              the specified duration between the start and end times.
            </p>
          </div>
        )}

        {/* General Error */}
        {errors.general && (
          <div className="mb-4 p-3 bg-accent bg-opacity-20 border border-accent rounded-md">
            <p className="text-accent">{errors.general}</p>
          </div>
        )}

        {/* Form Actions */}
        <div className="flex justify-end space-x-3">
          <Button
            type="button"
            variant="secondary"
            onClick={onCancel}
            disabled={createMutation.isPending || updateMutation.isPending}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="primary"
            disabled={createMutation.isPending || updateMutation.isPending}
          >
            {createMutation.isPending || updateMutation.isPending ? (
              <span className="flex items-center">
                <span className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></span>
                Saving...
              </span>
            ) : isEditing ? (
              "Update Shift"
            ) : intervalHours ? (
              `Create ${
                intervalValidation?.isValid &&
                intervalValidation.message?.includes("will create")
                  ? intervalValidation.message.replace("This will create ", "")
                  : "Multiple Shifts"
              }`
            ) : (
              "Create Shift"
            )}
          </Button>
        </div>
      </form>
    </div>
  );
};
