# Bank of Styx - API Documentation

## Overview
This document provides comprehensive documentation for all API endpoints in the Bank of Styx application. The API follows RESTful conventions and is organized by feature domains.

## 🏗️ **API Architecture**

### Base Structure
- **Base URL**: `/api/`
- **Authentication**: JWT Bearer tokens
- **Response Format**: JSON
- **HTTP Methods**: GET, POST, PUT, PATCH, DELETE
- **Status Codes**: Standard HTTP status codes

### Authentication
Most endpoints require authentication via JWT tokens:
```typescript
Headers: {
  'Authorization': 'Bearer <jwt_token>',
  'Content-Type': 'application/json'
}
```

## 📊 **API Endpoint Categories**

### 🔐 Authentication & User Management
```
/api/auth/
├── POST   /login              # User login
├── POST   /register           # User registration
├── GET    /me                 # Get current user
├── POST   /verify-email       # Email verification
├── POST   /set-password       # Set/update password
├── GET    /has-password       # Check if user has password
└── /discord/                  # Discord OAuth
    ├── GET    /callback       # OAuth callback
    └── POST   /disconnect     # Disconnect Discord
```

### 🏦 Banking System APIs
```
/api/bank/
├── GET    /account-summary    # Account overview
├── GET    /statistics         # Banking statistics
├── GET    /user               # User banking data
├── /transactions/             # Transaction management
│   ├── GET    /               # List transactions
│   ├── POST   /transfers      # Create transfer
│   ├── POST   /deposits       # Create deposit
│   ├── POST   /withdrawals    # Create withdrawal
│   ├── POST   /donations      # Create donation
│   ├── GET    /recent         # Recent transactions
│   └── GET    /pending-deposits # Pending deposits
├── /pay-codes/                # Pay code system
│   ├── GET    /               # List pay codes
│   ├── POST   /               # Create pay code
│   ├── GET    /active         # Active codes
│   ├── GET    /redeemed       # Redeemed codes
│   ├── POST   /redeem         # Redeem code
│   ├── POST   /validate       # Validate code
│   └── GET    /validate/[code] # Validate specific code
└── /cashier/                  # Cashier endpoints
    ├── GET    /pending-transactions # Pending transactions
    ├── GET    /transactions   # Transaction management
    ├── GET    /members        # Member management
    ├── GET    /ledger         # Financial ledger
    └── GET    /notifications  # Cashier notifications
```

### ⚓ Ship Management APIs
```
/api/ships/
├── GET    /                   # List ships
├── GET    /[id]              # Get ship details
├── POST   /apply             # Ship application
└── GET    /my-ship           # User's ship

/api/captain/                  # Captain-only endpoints
├── GET    /dashboard         # Captain dashboard data
├── GET    /ship              # Captain's ship details
├── POST   /ship/deletion-request # Request ship deletion
├── /members/                 # Crew management
│   ├── GET    /              # List crew members
│   ├── POST   /invite        # Invite new member
│   ├── PUT    /[userId]      # Update member
│   └── DELETE /[userId]      # Remove member
├── /roles/                   # Role management
│   ├── GET    /              # List roles
│   ├── POST   /              # Create role
│   ├── PUT    /[id]          # Update role
│   └── DELETE /[id]          # Delete role
├── /forms/                   # Form management
│   ├── GET    /available     # Available forms
│   ├── GET    /[id]          # Get form
│   ├── POST   /[id]/submit   # Submit form
│   └── /submissions/         # Form submissions
│       ├── GET    /          # List submissions
│       └── GET    /[id]      # Get submission
└── /volunteer-requirements/  # Volunteer requirements

/api/land-steward/            # Land Steward endpoints
├── /applications/            # Ship applications
│   ├── GET    /              # List applications
│   ├── GET    /[id]          # Get application
│   ├── POST   /[id]/approve  # Approve application
│   └── POST   /[id]/reject   # Reject application
├── /ships/                   # Ship management
│   ├── GET    /              # List all ships
│   ├── GET    /[id]          # Get ship details
│   ├── PUT    /[id]          # Update ship
│   ├── DELETE /[id]          # Delete ship
│   ├── PUT    /[id]/change-captain # Change captain
│   └── /deletion-requests/   # Deletion requests
│       ├── GET    /          # List requests
│       └── PUT    /[id]      # Review request
├── /forms/                   # Form management
│   ├── GET    /              # List forms
│   ├── POST   /              # Create form
│   └── /templates/           # Form templates
│       ├── GET    /          # List templates
│       ├── POST   /          # Create template
│       ├── PUT    /[id]      # Update template
│       └── DELETE /[id]      # Delete template
├── /submissions/             # Form submissions
│   ├── GET    /              # List submissions
│   ├── GET    /[id]          # Get submission
│   └── POST   /[id]/review   # Review submission
└── GET /volunteer-requirements # Volunteer requirements
```

### 🎯 Volunteer System APIs
```
/api/volunteer/
├── /public/                  # Public volunteer endpoints
│   ├── /events/              # Events
│   │   ├── GET    /          # List events
│   │   ├── GET    /[id]      # Get event
│   │   └── GET    /[id]/categories # Event categories
│   ├── /categories/          # Categories
│   │   ├── GET    /[id]      # Get category
│   │   └── GET    /[id]/shifts # Category shifts
│   ├── /shifts/              # Shifts
│   │   ├── GET    /[id]      # Get shift
│   │   ├── POST   /[id]/signup # Sign up for shift
│   │   ├── POST   /[id]/checkin # Check in to shift
│   │   └── GET    /[id]/checkin # Check-in status
│   └── /user/                # User volunteer data
│       ├── GET    /shifts    # User's shifts
│       └── DELETE /shifts/[id] # Cancel shift signup
├── /lead/                    # Lead manager endpoints
│   ├── GET    /dashboard     # Lead dashboard
│   ├── GET    /category      # Lead's category
│   ├── GET    /shifts        # Category shifts
│   ├── PATCH  /shifts/[id]/attendance # Update attendance
│   └── /payments/            # Payment management
│       └── POST   /initiate  # Initiate payments
├── /management/              # Volunteer coordinator endpoints
│   ├── GET    /dashboard     # Management dashboard
│   ├── GET    /shifts        # All shifts
│   └── /payments/            # Payment management
│       ├── GET    /          # Payment history
│       └── POST   /process   # Process payments
├── GET    /categories        # List categories
├── GET    /events            # List events
├── GET    /dashboard/stats   # Dashboard statistics
└── GET    /user-search       # Search volunteers
```

### 🛒 Shopping & Sales APIs
```
/api/shop/                    # Public shopping
├── POST   /redeem-code       # Redeem discount code

/api/products/                # Product catalog
├── GET    /                  # List products
├── GET    /[id]              # Get product
└── GET    /search            # Search products

/api/product-categories/      # Product categories
├── GET    /                  # List categories

/api/cart/                    # Shopping cart
├── GET    /                  # Get cart
├── POST   /items             # Add to cart
├── PUT    /items/[id]        # Update cart item
├── DELETE /items/[id]        # Remove from cart
├── POST   /clear             # Clear cart
└── POST   /refresh-holds     # Refresh holds

/api/checkout/                # Checkout process
├── POST   /create-payment-intent # Create payment
└── POST   /webhook           # Payment webhook

/api/orders/                  # Order management
├── GET    /                  # List orders
└── GET    /[id]              # Get order

/api/sales/                   # Sales management
├── /products/                # Product management
│   ├── GET    /              # List products
│   ├── POST   /              # Create product
│   ├── GET    /[id]          # Get product
│   ├── PUT    /[id]          # Update product
│   └── DELETE /[id]          # Delete product
├── /product-categories/      # Category management
│   ├── GET    /              # List categories
│   ├── POST   /              # Create category
│   ├── GET    /[id]          # Get category
│   ├── PUT    /[id]          # Update category
│   └── DELETE /[id]          # Delete category
└── /orders/                  # Order management
    ├── GET    /              # List orders
    ├── GET    /[id]          # Get order
    └── PUT    /[id]          # Update order
```

### 📰 News & Content APIs
```
/api/news/
├── GET    /analytics         # News analytics
├── /articles/                # Article management
│   ├── GET    /              # List articles
│   ├── POST   /              # Create article
│   ├── GET    /[id]          # Get article
│   ├── PUT    /[id]          # Update article
│   └── DELETE /[id]          # Delete article
├── /categories/              # Category management
│   ├── GET    /              # List categories
│   ├── POST   /              # Create category
│   ├── GET    /[id]          # Get category
│   ├── PUT    /[id]          # Update category
│   └── DELETE /[id]          # Delete category
└── /public/                  # Public news endpoints
    ├── GET    /              # List public articles
    └── GET    /[slug]        # Get article by slug
```

### 🏛️ Admin System APIs
```
/api/admin/
├── /dashboard/               # Admin dashboard
│   └── GET    /stats         # Dashboard statistics
├── /users/                   # User management
│   ├── GET    /              # List users
│   ├── POST   /create        # Create user
│   ├── GET    /[id]          # Get user
│   ├── PUT    /[id]          # Update user
│   └── DELETE /[id]          # Delete user
├── /events/                  # Event management
│   ├── GET    /              # List events
│   ├── POST   /              # Create event
│   ├── GET    /[id]          # Get event
│   ├── PUT    /[id]          # Update event
│   └── DELETE /[id]          # Delete event
├── /event-categories/        # Event category management
│   ├── GET    /              # List categories
│   ├── POST   /              # Create category
│   ├── GET    /[id]          # Get category
│   ├── PUT    /[id]          # Update category
│   └── DELETE /[id]          # Delete category
├── /featured/                # Featured content
│   ├── GET    /              # List featured
│   ├── POST   /              # Create featured
│   ├── GET    /[id]          # Get featured
│   ├── PUT    /[id]          # Update featured
│   └── DELETE /[id]          # Delete featured
└── /export/                  # Data export
    └── GET    /users         # Export users
```

### 👤 User Management APIs
```
/api/users/
├── GET    /[id]              # Get user profile
├── PUT    /[id]              # Update user profile
├── GET    /search            # Search users
├── GET    /preferences       # Get preferences
├── PUT    /preferences       # Update preferences
├── GET    /notification-preferences # Get notification prefs
├── PUT    /notification-preferences # Update notification prefs
├── GET    /default-view      # Get default view
├── PUT    /default-view      # Update default view
├── GET    /profile           # Get current user profile
├── PUT    /profile           # Update current user profile
├── GET    /security          # Get security settings
├── PUT    /security          # Update security settings
└── POST   /sync-discord-avatar # Sync Discord avatar
```

### 🔔 Notification APIs
```
/api/notifications/
├── GET    /                  # List notifications
├── POST   /create            # Create notification
├── POST   /broadcast         # Broadcast notification
├── GET    /sse               # Server-Sent Events stream
├── GET    /test              # Test notifications
├── GET    /[id]              # Get notification
├── PUT    /[id]              # Update notification (mark as read)
└── DELETE /[id]              # Delete notification
```

### 🎫 Support System APIs
```
/api/support/
└── /tickets/                 # Support tickets
    ├── GET    /              # List tickets
    ├── POST   /              # Create ticket
    ├── GET    /[id]          # Get ticket
    ├── PUT    /[id]          # Update ticket
    └── /[id]/notes/          # Ticket notes
        ├── GET    /          # List notes
        └── POST   /          # Add note
```

### 🔧 System Utilities
```
/api/uploads/                 # File uploads
├── POST   /avatar            # Upload avatar
├── POST   /ship-logo         # Upload ship logo
├── POST   /[type]            # Upload by type
└── /v2/                      # Upload v2 system
    └── POST   /              # Enhanced upload

/api/images/                  # Image management
└── GET    /[id]              # Get image

/api/contact/                 # Contact form
├── POST   /                  # Submit contact

/api/cron/                    # Scheduled tasks
└── POST   /release-expired-holds # Release expired holds

/api/setup/                   # System setup
├── GET    /check-categories  # Check categories
└── POST   /first-time-seed   # Initial seed

/api/test/                    # Testing endpoints
├── GET    /sse-performance   # SSE performance test
└── GET    /                  # General testing
```

## 🔒 **Authentication & Authorization**

### Authentication Methods
1. **JWT Tokens**: Primary authentication method
2. **Discord OAuth**: Social login integration
3. **Email/Password**: Traditional authentication

### Authorization Levels
- **Public**: No authentication required
- **User**: Requires valid user authentication
- **Manager**: Requires manager role (volunteer, sales, etc.)
- **Admin**: Requires admin privileges
- **Super Admin**: Requires highest level access

### Role-Based Permissions
```typescript
interface UserRoles {
  isAdmin: boolean;
  isEditor: boolean;
  isBanker: boolean;
  isChatModerator: boolean;
  isVolunteer: boolean;
  isVolunteerCoordinator: boolean;
  isLeadManager: boolean;
  isSalesManager: boolean;
  isLandSteward: boolean;
}
```

## 📝 **Response Formats**

### Standard Success Response
```typescript
{
  success: true,
  data: any,
  message?: string
}
```

### Standard Error Response
```typescript
{
  success: false,
  error: string,
  details?: any,
  statusCode: number
}
```

### Pagination Response
```typescript
{
  data: any[],
  pagination: {
    page: number,
    limit: number,
    total: number,
    pages: number
  }
}
```

## 🚀 **Rate Limiting**

### Default Limits
- **Authentication**: 5 requests per minute
- **General APIs**: 100 requests per minute
- **File Uploads**: 10 requests per minute
- **Public APIs**: 200 requests per minute

### Headers
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 99
X-RateLimit-Reset: **********
```

---

**Last Updated**: 2025-01-07  
**Total Endpoints**: 200+  
**API Version**: v1  
**Documentation Coverage**: Structure Complete, Details Pending