import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import prisma from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = user.id;

    // Get the user's ship where they are captain
    const ship = await prisma.ship.findFirst({
      where: {
        captainId: userId,
        status: 'active'
      },
      include: {
        roles: {
          include: {
            _count: {
              select: {
                members: {
                  where: {
                    status: 'active'
                  }
                }
              }
            }
          },
          orderBy: {
            createdAt: 'asc'
          }
        }
      }
    });

    if (!ship) {
      return NextResponse.json({ error: 'Ship not found or you are not the captain' }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      roles: ship.roles
    });

  } catch (error) {
    console.error('Error fetching ship roles:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const captainId = user.id;

    const { name, description } = await request.json();

    if (!name || name.trim().length === 0) {
      return NextResponse.json({ error: 'Role name is required' }, { status: 400 });
    }

    // Verify captain owns a ship
    const ship = await prisma.ship.findFirst({
      where: {
        captainId,
        status: 'active'
      }
    });

    if (!ship) {
      return NextResponse.json({ error: 'Ship not found or you are not the captain' }, { status: 404 });
    }

    // Check if role name already exists for this ship
    const existingRole = await prisma.shipRole.findFirst({
      where: {
        shipId: ship.id,
        name: name.trim()
      }
    });

    if (existingRole) {
      return NextResponse.json({ error: 'A role with this name already exists' }, { status: 400 });
    }

    // Create the role
    const role = await prisma.shipRole.create({
      data: {
        shipId: ship.id,
        name: name.trim(),
        description: description?.trim() || null
      },
      include: {
        _count: {
          select: {
            members: {
              where: {
                status: 'active'
              }
            }
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      role
    });

  } catch (error) {
    console.error('Error creating ship role:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}