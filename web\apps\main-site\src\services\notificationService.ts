/**
 * Notification Service
 * Provides functions for interacting with the Notification API endpoints
 */
import fetchClient from "@/lib/fetchClient";

// Types
export interface Notification {
  id: string;
  category: string;
  type: string;
  title: string;
  message: string;
  read: boolean;
  link?: string;
  icon?: string;
  priority: "low" | "medium" | "high";
  userId: string;
  createdAt: string;
  updatedAt: string;
  transactionId?: string;
  transaction?: any;
}

// Using the centralized fetch client for all API calls

// Get user notifications
export const getUserNotifications = async (params?: {
  limit?: number;
  unreadOnly?: boolean;
  category?: string;
}): Promise<Notification[]> => {
  const queryParams: Record<string, string> = {};

  if (params?.limit) queryParams.limit = params.limit.toString();
  if (params?.unreadOnly) queryParams.unreadOnly = "true";
  if (params?.category) queryParams.category = params.category;

  return fetchClient.get<Notification[]>("/api/notifications", {
    params: queryParams,
  });
};

// Mark notifications as read
export const markNotificationsAsRead = async (data: {
  ids?: string[];
  all?: boolean;
}): Promise<{ message: string }> => {
  return fetchClient.patch<{ message: string }>("/api/notifications", data);
};

// Update notification preferences
export const updateNotificationPreferences = async (preferences: {
  notifyTransfers?: boolean;
  notifyDeposits?: boolean;
  notifyWithdrawals?: boolean;
  notifyNewsEvents?: boolean;
  notifyAuctions?: boolean;
  notifyChat?: boolean;
  notifyAdmin?: boolean;
}): Promise<{ success: boolean; preferences: any }> => {
  return fetchClient.put<{ success: boolean; preferences: any }>(
    "/api/users/notification-preferences",
    { notifications: preferences },
  );
};

// Get notification preferences
export const getNotificationPreferences = async (): Promise<{
  notifyTransfers: boolean;
  notifyDeposits: boolean;
  notifyWithdrawals: boolean;
  notifyNewsEvents: boolean;
  notifyAuctions: boolean;
  notifyChat: boolean;
  notifyAdmin: boolean;
}> => {
  const response = await fetchClient.get<{ preferences: any }>(
    "/api/users/notification-preferences",
  );
  return response.preferences;
};

// Create a notification (admin/system use)
export const createNotification = async (data: {
  userId: string;
  category: string;
  type: string;
  title: string;
  message: string;
  link?: string;
  icon?: string;
  priority?: "low" | "medium" | "high";
  transactionId?: string;
}): Promise<{ notification: Notification; serverTimestamp: string }> => {
  return fetchClient.post<{
    notification: Notification;
    serverTimestamp: string;
  }>("/api/notifications/create", data);
};

// Delete a notification
export const deleteNotification = async (
  id: string,
): Promise<{ success: boolean }> => {
  return fetchClient.delete<{ success: boolean }>(`/api/notifications/${id}`);
};
