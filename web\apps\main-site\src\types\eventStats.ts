export interface EventStatsResponse {
  event: {
    id: string;
    name: string;
    startDate: string;
    endDate: string;
    status: string;
    category: {
      id: string;
      name: string;
      color: string | null;
    };
  };
  overview: {
    totalRevenue: number;
    totalUnitsSold: number;
    totalRefunds: number;
    uniqueCustomers: number;
    conversionRate: number;
  };
  products: EventProductStats[];
  salesTimeline: SalesTimelineItem[];
  topCustomers: CustomerStats[];
  recentTransactions: RecentTransaction[];
}

export interface EventProductStats {
  id: string;
  name: string;
  price: number;
  unitsSold: number;
  revenue: number;
  availableTickets: number;
  heldTickets: number;
  soldTickets: number;
  totalTickets: number;
}

export interface SalesTimelineItem {
  date: string;
  revenue: number;
  unitsSold: number;
}

export interface CustomerStats {
  user: {
    id: string;
    username: string;
    email: string;
    displayName: string | null;
  };
  totalSpent: number;
  itemsPurchased: number;
  orderCount: number;
}

export interface RecentTransaction {
  id: string;
  orderNumber: string;
  customer: {
    id: string;
    username: string;
    email: string;
    displayName: string | null;
  };
  productName: string;
  quantity: number;
  unitPrice: number;
  total: number;
  date: string;
  status: string;
}