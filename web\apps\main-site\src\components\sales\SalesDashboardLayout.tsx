"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import React from "react";

interface SalesDashboardLayoutProps {
  children: React.ReactNode;
}

export const SalesDashboardLayout: React.FC<SalesDashboardLayoutProps> = ({
  children,
}) => {
  const pathname = usePathname();

  const navItems = [
    { name: "Dashboard", path: "/sales/dashboard" },
    { name: "Products", path: "/sales/products" },
    { name: "Categories", path: "/sales/categories" },
    { name: "Orders", path: "/sales/orders" },
  ];

  // Function to check if a path is active
  const isActive = (path: string) => {
    if (path === "/sales/dashboard") {
      return pathname === "/sales/dashboard";
    }
    return pathname.startsWith(path);
  };

  return (
    <div className="flex flex-col md:flex-row min-h-screen bg-bg-page">
      {/* Sidebar */}
      <aside className="w-full md:w-64 bg-secondary-light p-4 md:p-6 border-r border-border-subtle">
        <h2 className="text-xl font-bold mb-6 text-text-primary">
          Sales Dashboard
        </h2>
        <nav>
          <ul className="space-y-2">
            {navItems.map((item) => (
              <li key={item.path}>
                <Link
                  href={item.path}
                  className={`
                    block px-3 py-2 rounded-md transition-colors
                    ${
                      isActive(item.path)
                        ? "bg-primary text-white font-medium"
                        : "hover:bg-secondary hover:text-text-primary"
                    }
                  `}
                >
                  {item.name}
                </Link>
              </li>
            ))}
          </ul>
        </nav>
      </aside>

      {/* Main content */}
      <main className="flex-1 p-4 md:p-6 overflow-auto">{children}</main>
    </div>
  );
};
