"use client";

import React from "react";
import { Card } from "@bank-of-styx/ui";

interface VolunteerRequirement {
  id: string;
  requiredHours: number;
  completedHours: number;
  status: "pending" | "in_progress" | "completed" | "overdue";
  createdAt: string;
  formSubmission: {
    form: {
      name: string;
      event: {
        id: string;
        name: string;
        startDate: string;
      };
    };
  };
}

interface VolunteerHoursStatusProps {
  requirements: VolunteerRequirement[];
  loading?: boolean;
}

export const VolunteerHoursStatus: React.FC<VolunteerHoursStatusProps> = ({
  requirements,
  loading = false,
}) => {
  // Don't show anything if no requirements
  if (!loading && (!requirements || requirements.length === 0)) {
    return null;
  }

  if (loading) {
    return (
      <Card className="p-4">
        <div className="flex items-center gap-2 mb-2">
          <span className="text-lg">⏰</span>
          <h3 className="font-semibold text-white">Volunteer Hours Required</h3>
        </div>
        <p className="text-sm text-gray-400">Loading requirements...</p>
      </Card>
    );
  }

  const totalRequired = requirements.reduce((sum, req) => sum + req.requiredHours, 0);
  const totalCompleted = requirements.reduce((sum, req) => sum + req.completedHours, 0);
  const progressPercentage = totalRequired > 0 ? (totalCompleted / totalRequired) * 100 : 0;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-400';
      case 'in_progress':
        return 'text-blue-400';
      case 'overdue':
        return 'text-red-400';
      default:
        return 'text-yellow-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return '✅';
      case 'in_progress':
        return '🔄';
      case 'overdue':
        return '⚠️';
      default:
        return '⏳';
    }
  };

  return (
    <Card className="p-4 border-blue-400/30 bg-blue-900/10">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <span className="text-lg">⏰</span>
          <h3 className="font-semibold text-white">Volunteer Hours Required</h3>
        </div>
        <div className="text-right">
          <div className="text-sm font-medium text-white">
            {totalCompleted} / {totalRequired} hours
          </div>
          <div className="text-xs text-gray-400">
            {Math.round(progressPercentage)}% complete
          </div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="mb-3">
        <div className="w-full bg-gray-700 rounded-full h-2">
          <div 
            className="bg-blue-500 h-2 rounded-full transition-all duration-300" 
            style={{ width: `${Math.min(progressPercentage, 100)}%` }}
          ></div>
        </div>
      </div>

      {/* Requirements List */}
      <div className="space-y-2">
        {requirements.slice(0, 3).map((requirement) => (
          <div key={requirement.id} className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-2 min-w-0 flex-1">
              <span className="text-xs">{getStatusIcon(requirement.status)}</span>
              <span className="text-gray-300 truncate">
                {requirement.formSubmission.form.event.name}
              </span>
            </div>
            <div className="flex items-center gap-2 text-xs">
              <span className={getStatusColor(requirement.status)}>
                {requirement.completedHours}/{requirement.requiredHours}h
              </span>
            </div>
          </div>
        ))}
        
        {requirements.length > 3 && (
          <div className="text-xs text-gray-400 text-center pt-1 border-t border-gray-600">
            +{requirements.length - 3} more requirements
          </div>
        )}
      </div>

      {totalCompleted < totalRequired && (
        <div className="mt-3 pt-3 border-t border-gray-600">
          <p className="text-xs text-blue-400">
            Complete volunteer work to fulfill these requirements. 
            Hours are tracked automatically when you volunteer for events.
          </p>
        </div>
      )}
    </Card>
  );
};

VolunteerHoursStatus.displayName = "VolunteerHoursStatus";