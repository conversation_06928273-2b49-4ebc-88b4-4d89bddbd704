// prisma-test.js
const { PrismaClient } = require('@prisma/client');

async function main() {
  console.log('Testing database connection...');
  
  try {
    const prisma = new PrismaClient();
    
    // Test connection by counting users
    const userCount = await prisma.user.count();
    console.log(`Connection successful! Found ${userCount} users in the database.`);
    
    // Try to create a test user if none exists
    if (userCount === 0) {
      console.log('Creating test user...');
      const testUser = await prisma.user.create({
        data: {
          username: 'testuser',
          displayName: 'Test User',
          email: '<EMAIL>',
          passwordHash: '$2a$10$JKkDhdhigZoh6sjO.CerM.Ho5Z5wI3F5Z0QcnO8amVLBYuKIw51Mu', // password is 'password123'
          isAdmin: false
        },
      });
      console.log('Test user created:', testUser.username);
    }
    
    // Disconnect from the database
    await prisma.$disconnect();
  } catch (error) {
    console.error('Database connection failed:', error);
    process.exit(1);
  }
}

main();
