import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser, userHasRole } from "@/lib/auth";
import { generateRedemptionCode } from "@/lib/redemptionCodes";
import { TicketStatus } from "@prisma/client";

export const dynamic = 'force-dynamic';

interface Params {
  params: {
    id: string;
  };
}

// POST /api/sales/products/[id]/redemption-codes - Generate a new redemption code
export async function POST(req: NextRequest, { params }: Params) {
  try {
    const { id: productId } = params;

    // Check if user is authenticated and has sales manager role
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 },
      );
    }

    const hasSalesRole = await userHasRole(req, "salesManager");
    if (!hasSalesRole) {
      return NextResponse.json(
        { error: "Unauthorized - Sales Manager role required" },
        { status: 403 },
      );
    }

    // Check if product exists and is free
    const product = await prisma.product.findUnique({
      where: { id: productId },
    });

    if (!product) {
      return NextResponse.json({ error: "Product not found" }, { status: 404 });
    }

    if (!product.isFree) {
      return NextResponse.json(
        { error: "Redemption codes can only be generated for free products" },
        { status: 400 },
      );
    }

    // Generate a unique code
    let code: string;
    let isUnique = false;
    let attempts = 0;

    while (!isUnique && attempts < 10) {
      code = generateRedemptionCode();
      const existing = await prisma.redemptionCode.findUnique({
        where: { code },
      });
      
      if (!existing) {
        isUnique = true;
      }
      attempts++;
    }

    if (!isUnique) {
      return NextResponse.json(
        { error: "Failed to generate unique code after multiple attempts" },
        { status: 500 },
      );
    }

    // Create the redemption code and corresponding HELD ticket
    const result = await prisma.$transaction(async (tx) => {
      // Create the redemption code
      const redemptionCode = await tx.redemptionCode.create({
        data: {
          code: code!,
          productId,
        },
      });

      // Create a HELD ticket to reserve inventory
      await tx.ticket.create({
        data: {
          productId,
          status: TicketStatus.HELD,
        },
      });

      return redemptionCode;
    });

    return NextResponse.json({ redemptionCode: result }, { status: 201 });
  } catch (error) {
    console.error("Error generating redemption code:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}