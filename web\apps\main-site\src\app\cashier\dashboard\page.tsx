"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useAuth } from "../../../contexts/AuthContext";
import { CashierDashboardLayout } from "../../../components/cashier";
import {
  useBankStatistics,
  usePendingTransactions,
  useCashierRecentTransactions,
} from "../../../hooks/useBank";

export default function CashierDashboardPage() {
  const { user, isLoading: authLoading } = useAuth();
  const router = useRouter();
  const [isAuthorized, setIsAuthorized] = useState(false);

  // Fetch data from API
  const { data: bankStatistics, isLoading: statsLoading } = useBankStatistics();
  const { data: pendingTransactionsData, isLoading: pendingLoading } =
    usePendingTransactions();
  const { data: recentTransactions, isLoading: transactionsLoading } =
    useCashierRecentTransactions(
      5, // limit
      "completed", // status
    );

  // Filter transactions by type for display
  const pendingDeposits =
    pendingTransactionsData?.filter((t) => t.type === "deposit") || [];
  const pendingWithdrawals =
    pendingTransactionsData?.filter((t) => t.type === "withdrawal") || [];
  const recentApprovedTransactions = recentTransactions?.slice(0, 5) || [];

  // Check authorization after auth is loaded
  useEffect(() => {
    // Only proceed with checks if auth loading is complete
    if (!authLoading) {
      if (!user || !user.roles?.banker) {
        router.push("/");
      } else {
        // User is authorized
        setIsAuthorized(true);
      }
    }
  }, [user, router, authLoading]);

  // Show loading state or nothing while checking authorization
  if (authLoading || !isAuthorized) {
    return (
      <div className="min-h-screen bg-secondary-dark flex items-center justify-center">
        <div className="bg-secondary-light p-6 rounded-lg shadow-md border border-gray-600">
          <p className="text-white text-lg">Loading...</p>
        </div>
      </div>
    );
  }

  // Helper function to format dates
  const formatDate = (dateString: string | Date) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  return (
    <CashierDashboardLayout>
      {/* Welcome Section */}
      <div className="bg-secondary-light rounded-lg shadow-md p-4 sm:p-6 border border-gray-600 mb-6">
        <div className="max-w-full">
          <h2 className="text-xl sm:text-2xl font-bold mb-2 sm:mb-4 text-white">
            Welcome to the Cashier Dashboard
          </h2>
          <p className="text-gray-400 mb-4 text-sm sm:text-base">
            Manage bank transactions, approve deposits and withdrawals, and
            monitor bank activity.
          </p>
        </div>

        {/* Stats Cards - Responsive Layout */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 mt-4">
          {/* Quick Actions Card */}
          <div className="bg-secondary-dark rounded-lg p-4 border border-gray-600">
            <h3 className="text-base sm:text-lg font-semibold mb-2 text-white">
              Quick Links
            </h3>
            <ul className="space-y-2">
              <li>
                <Link
                  href="/cashier/dashboard/deposits"
                  className="text-primary hover:text-primary-light text-sm sm:text-base"
                >
                  Review Pending Deposits (
                  {statsLoading
                    ? "..."
                    : bankStatistics?.pendingTransactions.deposits || 0}
                  )
                </Link>
              </li>
              <li>
                <Link
                  href="/cashier/dashboard/withdrawals"
                  className="text-primary hover:text-primary-light text-sm sm:text-base"
                >
                  Review Pending Withdrawals (
                  {statsLoading
                    ? "..."
                    : bankStatistics?.pendingTransactions.withdrawals || 0}
                  )
                </Link>
              </li>
              <li>
                <Link
                  href="/cashier/dashboard/transactions"
                  className="text-primary hover:text-primary-light text-sm sm:text-base"
                >
                  Search User Transactions
                </Link>
              </li>
              <li>
                <Link
                  href="/cashier/dashboard/members"
                  className="text-primary hover:text-primary-light text-sm sm:text-base"
                >
                  Member Lookup
                </Link>
              </li>
            </ul>
          </div>

          {/* Today's Activity Card */}
          <div className="bg-secondary-dark rounded-lg p-4 border border-gray-600">
            <h3 className="text-base sm:text-lg font-semibold mb-2 text-white">
              Today's Activity
            </h3>
            {statsLoading ? (
              <div className="text-center py-2">
                <p className="text-gray-400 text-sm">
                  Loading activity data...
                </p>
              </div>
            ) : !bankStatistics ||
              !bankStatistics.dailyActivity ||
              bankStatistics.dailyActivity.length === 0 ? (
              <div className="text-center py-2">
                <p className="text-gray-400 text-sm">
                  No activity data available
                </p>
              </div>
            ) : (
              <ul className="space-y-2">
                <li className="flex justify-between">
                  <span className="text-gray-400 text-sm sm:text-base">
                    Deposits:
                  </span>
                  <span className="text-success text-sm sm:text-base">
                    {bankStatistics.dailyActivity[0].deposits}
                  </span>
                </li>
                <li className="flex justify-between">
                  <span className="text-gray-400 text-sm sm:text-base">
                    Withdrawals:
                  </span>
                  <span className="text-error text-sm sm:text-base">
                    {bankStatistics.dailyActivity[0].withdrawals}
                  </span>
                </li>
                <li className="flex justify-between">
                  <span className="text-gray-400 text-sm sm:text-base">
                    Transfers:
                  </span>
                  <span className="text-white text-sm sm:text-base">
                    {bankStatistics.dailyActivity[0].transfers}
                  </span>
                </li>
              </ul>
            )}
          </div>

          {/* Bank Status Card */}
          <div className="bg-secondary-dark rounded-lg p-4 border border-gray-600 md:col-span-2 lg:col-span-1">
            <h3 className="text-base sm:text-lg font-semibold mb-2 text-white">
              Bank Status
            </h3>
            {statsLoading ? (
              <div className="text-center py-2">
                <p className="text-gray-400 text-sm">Loading bank status...</p>
              </div>
            ) : !bankStatistics ? (
              <div className="text-center py-2">
                <p className="text-gray-400 text-sm">
                  No bank status available
                </p>
              </div>
            ) : (
              <ul className="space-y-2">
                <li className="flex justify-between">
                  <span className="text-gray-400 text-sm sm:text-base">
                    Total Deposits:
                  </span>
                  <span className="text-white text-sm sm:text-base">
                    NS {bankStatistics.totalDeposits.amount}
                  </span>
                </li>
                <li className="flex justify-between">
                  <span className="text-gray-400 text-sm sm:text-base">
                    Total Withdrawals:
                  </span>
                  <span className="text-white text-sm sm:text-base">
                    NS {bankStatistics.totalWithdrawals.amount}
                  </span>
                </li>
                <li className="flex justify-between">
                  <span className="text-gray-400 text-sm sm:text-base">
                    Net Balance:
                  </span>
                  <span className="text-success text-sm sm:text-base">
                    NS{" "}
                    {bankStatistics.totalDeposits.amount -
                      bankStatistics.totalWithdrawals.amount}
                  </span>
                </li>
              </ul>
            )}
          </div>
        </div>
      </div>

      {/* Pending Transactions Section */}
      <div className="flex flex-col md:grid md:grid-cols-2 gap-4 md:gap-6 mb-6">
        {/* Pending Deposits Card */}
        <div className="bg-secondary-light rounded-lg shadow-md p-4 sm:p-6 border border-gray-600 order-1">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg sm:text-xl font-bold text-white">
              Pending Deposits
            </h2>
            <Link
              href="/cashier/dashboard/deposits"
              className="text-primary hover:text-primary-light text-sm"
            >
              View All
            </Link>
          </div>

          {pendingLoading ? (
            <div className="text-center py-4 sm:py-6">
              <p className="text-gray-400 text-sm">
                Loading pending deposits...
              </p>
            </div>
          ) : pendingDeposits.length > 0 ? (
            <div className="space-y-4">
              {pendingDeposits.slice(0, 2).map((transaction) => (
                <div
                  key={transaction.id}
                  className="bg-secondary p-3 sm:p-4 rounded-lg border border-gray-600"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-center">
                      <img
                        src={
                          transaction.sender?.avatar ||
                          "/images/avatars/default.png"
                        }
                        alt={transaction.sender?.displayName || "User"}
                        className="w-8 h-8 sm:w-10 sm:h-10 rounded-full mr-2 sm:mr-3"
                      />
                      <div>
                        <div className="font-medium text-white text-sm sm:text-base">
                          {transaction.sender?.displayName || "Unknown User"}
                        </div>
                        <div className="text-xs sm:text-sm text-gray-400">
                          @{transaction.sender?.username || "unknown"}
                        </div>
                      </div>
                    </div>
                    <div className="text-success font-bold text-sm sm:text-base">
                      NS {transaction.amount}
                    </div>
                  </div>
                  <div className="mt-2 text-xs sm:text-sm text-gray-400">
                    <div>ID: {transaction.id}</div>
                    <div>Date: {formatDate(transaction.createdAt)}</div>
                    <div>
                      Method: {transaction.paymentMethod || "Not specified"}
                    </div>
                  </div>
                  <div className="mt-3 flex space-x-2">
                    <Link
                      href={`/cashier/dashboard/deposits?id=${transaction.id}`}
                      className="px-3 py-1 bg-primary text-white text-xs sm:text-sm rounded hover:bg-primary-dark"
                    >
                      Review
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-4 sm:py-6 text-gray-400 text-sm">
              No pending deposits.
            </div>
          )}
        </div>

        {/* Pending Withdrawals Card */}
        <div className="bg-secondary-light rounded-lg shadow-md p-4 sm:p-6 border border-gray-600 order-2">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg sm:text-xl font-bold text-white">
              Pending Withdrawals
            </h2>
            <Link
              href="/cashier/dashboard/withdrawals"
              className="text-primary hover:text-primary-light text-sm"
            >
              View All
            </Link>
          </div>

          {pendingLoading ? (
            <div className="text-center py-4 sm:py-6">
              <p className="text-gray-400 text-sm">
                Loading pending withdrawals...
              </p>
            </div>
          ) : pendingWithdrawals.length > 0 ? (
            <div className="space-y-4">
              {pendingWithdrawals.slice(0, 2).map((transaction) => (
                <div
                  key={transaction.id}
                  className="bg-secondary p-3 sm:p-4 rounded-lg border border-gray-600"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-center">
                      <img
                        src={
                          transaction.sender?.avatar ||
                          "/images/avatars/default.png"
                        }
                        alt={transaction.sender?.displayName || "User"}
                        className="w-8 h-8 sm:w-10 sm:h-10 rounded-full mr-2 sm:mr-3"
                      />
                      <div>
                        <div className="font-medium text-white text-sm sm:text-base">
                          {transaction.sender?.displayName || "Unknown User"}
                        </div>
                        <div className="text-xs sm:text-sm text-gray-400">
                          @{transaction.sender?.username || "unknown"}
                        </div>
                      </div>
                    </div>
                    <div className="text-error font-bold text-sm sm:text-base">
                      NS {transaction.amount}
                    </div>
                  </div>
                  <div className="mt-2 text-xs sm:text-sm text-gray-400">
                    <div>ID: {transaction.id}</div>
                    <div>Date: {formatDate(transaction.createdAt)}</div>
                    <div>
                      Method: {transaction.paymentMethod || "Not specified"}
                    </div>
                  </div>
                  <div className="mt-3 flex space-x-2">
                    <Link
                      href={`/cashier/dashboard/withdrawals?id=${transaction.id}`}
                      className="px-3 py-1 bg-primary text-white text-xs sm:text-sm rounded hover:bg-primary-dark"
                    >
                      Review
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-4 sm:py-6 text-gray-400 text-sm">
              No pending withdrawals.
            </div>
          )}
        </div>
      </div>

      {/* Recent Activity Section */}
      <div className="bg-secondary-light rounded-lg shadow-md p-4 sm:p-6 border border-gray-600">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg sm:text-xl font-bold text-white">
            Recent Approved Transactions
          </h2>
          <Link
            href="/cashier/dashboard/transactions"
            className="text-primary hover:text-primary-light text-sm"
          >
            View All
          </Link>
        </div>

        {transactionsLoading ? (
          <div className="text-center py-4 sm:py-6">
            <p className="text-gray-400 text-sm">
              Loading recent transactions...
            </p>
          </div>
        ) : recentApprovedTransactions.length > 0 ? (
          <div className="grid grid-cols-1 gap-4">
            {recentApprovedTransactions.map((transaction) => (
              <div
                key={transaction.id}
                className="bg-secondary p-3 sm:p-2 rounded-lg border border-gray-600 flex flex-col"
              >
                <div className="flex items-start justify-between mb-2 sm:mb-3">
                  <div className="flex items-center">
                    <img
                      src={
                        transaction.sender?.avatar ||
                        "/images/avatars/default.png"
                      }
                      alt={transaction.sender?.displayName || "User"}
                      className="w-8 h-8 sm:w-10 sm:h-10 rounded-full mr-2 sm:mr-3"
                    />
                    <div>
                      <div className="font-medium text-white text-sm sm:text-base overflow-hidden whitespace-nowrap truncate max-w-3">
                        {transaction.sender?.displayName || "Unknown User"}
                      </div>
                      <div className="text-xs sm:text-sm text-gray-400">
                        @{transaction.sender?.username || "unknown"}
                      </div>
                    </div>
                  </div>
                  <div>
                    <span
                      className={`font-bold text-sm sm:text-base ${
                        transaction.type === "deposit"
                          ? "text-success"
                          : "text-error"
                      }`}
                    >
                      {transaction.type === "deposit" ? "+" : "-"}NS{" "}
                      {transaction.amount}
                    </span>
                  </div>
                </div>

                {/* Desktop view details */}
                <div className="hidden sm:grid grid-cols-2 gap-3 text-sm">
                  <div>
                    <div className="text-gray-400 mb-1">Transaction</div>
                    <div className="text-white">
                      {transaction.description ||
                        `${transaction.type} transaction`}
                    </div>
                  </div>
                  <div>
                    <div className="text-gray-400 mb-1">Date</div>
                    <div className="text-white">
                      {formatDate(transaction.createdAt)}
                    </div>
                  </div>
                </div>

                {/* Mobile view details */}
                <div className="sm:hidden text-xs text-gray-400 space-y-1">
                  <div className="flex justify-between">
                    <span>Transaction:</span>
                    <span className="text-white">
                      {transaction.description || `${transaction.type}`}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Date:</span>
                    <span>{formatDate(transaction.createdAt)}</span>
                  </div>
                </div>

                <div className="mt-3 flex justify-between items-center">
                  <div className="text-xs text-gray-400 truncate max-w-[60%]">
                    ID: {transaction.id}
                  </div>
                  <span className="px-2 py-0.5 sm:py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                    {transaction.status.charAt(0).toUpperCase() +
                      transaction.status.slice(1)}
                  </span>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-4 sm:py-6 text-gray-400 text-sm">
            No recent approved transactions.
          </div>
        )}
      </div>
    </CashierDashboardLayout>
  );
}
