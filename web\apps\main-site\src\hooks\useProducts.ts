"use client";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  getProducts,
  getProduct,
  getSalesProducts,
  getSalesProduct,
  createProduct,
  updateProduct,
  deleteProduct,
  Product,
} from "@/services/productService";
import fetchClient from "@/lib/fetchClient";

// Hook for fetching public products
export const useProducts = (filters?: {
  categoryId?: string;
  eventId?: string;
  isActive?: boolean;
}) => {
  return useQuery({
    queryKey: ["products", filters],
    queryFn: () => getProducts(filters),
  });
};

// Hook for fetching a single product
export const useProduct = (id: string) => {
  return useQuery({
    queryKey: ["product", id],
    queryFn: () => getProduct(id),
    enabled: !!id,
  });
};

/**
 * Hook to search products
 */
export function useSearchProducts(
  query: string,
  options?: { categoryId?: string; eventId?: string },
) {
  const params: Record<string, string> = { q: query };

  if (options?.categoryId) params.categoryId = options.categoryId;
  if (options?.eventId) params.eventId = options.eventId;

  return useQuery({
    queryKey: ["products", "search", query, options],
    queryFn: async () => {
      return fetchClient.get<{ products: Product[] }>("/api/products/search", {
        params,
      });
    },
    enabled: !!query,
  });
}

// Hook for fetching sales manager products
export const useSalesProducts = (filters?: {
  categoryId?: string;
  eventId?: string;
  isActive?: boolean;
}) => {
  return useQuery({
    queryKey: ["salesProducts", filters],
    queryFn: () => getSalesProducts(filters),
  });
};

// Hook for fetching a single sales manager product
export const useSalesProduct = (id: string) => {
  return useQuery({
    queryKey: ["salesProduct", id],
    queryFn: () => getSalesProduct(id),
    enabled: !!id,
  });
};

// Hook for creating a product
export const useCreateProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: {
      name: string;
      description?: string;
      shortDescription?: string;
      price: number;
      image?: string;
      isActive?: boolean;
      affectsCapacity?: boolean;
      inventory?: number | null;
      categoryId: string;
      eventId?: string | null;
      isFree?: boolean;
    }) => createProduct(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["salesProducts"] });
      queryClient.invalidateQueries({ queryKey: ["products"] });
    },
  });
};

// Hook for updating a product
export const useUpdateProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      id,
      data,
    }: {
      id: string;
      data: {
        name?: string;
        description?: string;
        shortDescription?: string;
        price?: number;
        image?: string;
        isActive?: boolean;
        affectsCapacity?: boolean;
        inventory?: number | null;
        categoryId?: string;
        eventId?: string | null;
        isFree?: boolean;
      };
    }) => updateProduct(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: ["salesProduct", variables.id],
      });
      queryClient.invalidateQueries({ queryKey: ["salesProducts"] });
      queryClient.invalidateQueries({
        queryKey: ["product", variables.id],
      });
      queryClient.invalidateQueries({ queryKey: ["products"] });
    },
  });
};

// Hook for deleting a product
export const useDeleteProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => deleteProduct(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["salesProducts"] });
      queryClient.invalidateQueries({ queryKey: ["products"] });
    },
  });
};
