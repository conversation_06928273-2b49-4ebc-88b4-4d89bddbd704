# Bank of Styx Website

A modern banking platform built with Next.js, React, and TypeScript, featuring real-time updates, secure authentication, and a comprehensive banking interface.

## Project Overview

The Bank of Styx website is a full-featured banking platform that provides users with account management, transactions, merchant services, auctions, and news. The application is designed to serve approximately 1,000 users with 30-40 concurrent users (with peaks of up to 100).

## Technology Stack

### Development Stack

- **Frontend Framework**: [Next.js](https://nextjs.org/) (v13.4.2) with App Router
- **UI Library**: React (v18.2.0)
- **Programming Language**: TypeScript (v5.8.3)
- **Styling**: [TailwindCSS](https://tailwindcss.com/) (v3.3.2)
- **State Management**:
  - Server State: [TanStack Query](https://tanstack.com/query) (v5.17.19)
- **Form Handling**: Native React forms
- **Data Visualization**: [Recharts](https://recharts.org/) (v2.15.3)
- **Rich Text Editing**: [React Quill](https://github.com/zenoamaro/react-quill) (v2.0.0)
- **Package Management**: [PNPM](https://pnpm.io/) (v8.6.0)
- **Database ORM**: [Prisma](https://www.prisma.io/) (v6.6.0)
- **Authentication**: JWT ([jsonwebtoken](https://github.com/auth0/node-jsonwebtoken)) and OAuth
- **Email**: [Nodemailer](https://nodemailer.com/) (v6.10.1)

### Production Stack

- **Node.js Runtime**: >= 18.0.0
- **Database**: MySQL 8.0
- **Deployment Options**:
  - Self-hosted Node.js server with PM2
  - Vercel
  - Docker
- **Web Server**: Nginx (recommended for production)
- **Monitoring**: PM2 (for self-hosted option)

## Architecture

The Bank of Styx website is built as a monorepo using PNPM workspaces, with a focus on modularity and reusability.

### Code Organization

```
/
├── web/                                                              # Core application code (monorepo)
│   ├── apps/                                                         # Applications
│   │   ├── api/                                                      # Standalone API modules
│   │   │   └── auth/                                                 # Authentication API endpoints
│   │   └── main-site/                                                # Main Next.js application
│   │       ├── prisma/                                               # Database schema and migrations
│   │       │   └── migrations/                                       # Database migration scripts including:
│   │       │       ├── init/                                         # Initial database setup
│   │       │       ├── news_models/                                  # News system models
│   │       │       ├── bank_models/                                  # Banking system models
│   │       │       ├── notification_model/                           # Notification system models
│   │       │       ├── support_ticket_system/                        # Support ticket system
│   │       │       ├── event_management/                             # Event management system
│   │       │       ├── volunteer_models/                             # Volunteer management system
│   │       │       ├── cart_and_order_models/                        # Shopping system models
│   │       │       └── ticket_hold_system/                           # Ticket hold system
│   │       ├── public/                                               # Static assets and uploads
│   │       │   ├── images/                                           # Static images and icons
│   │       │   │   ├── avatars/                                      # User avatar images
│   │       │   │   └── icons/                                        # Platform and social icons
│   │       │   └── uploads/                                          # User-uploaded content
│   │       │       ├── avatars/                                      # User avatar uploads
│   │       │       ├── deposits/                                     # Deposit verification uploads
│   │       │       └── news/                                         # News article images
│   │       ├── scripts/                                              # Utility scripts
│   │       └── src/                                                  # Source code
│   │           ├── app/                                              # Next.js App Router pages and API routes
│   │           │   ├── about/                                        # About pages
│   │           │   ├── admin/                                        # Admin dashboard and management
│   │           │   │   ├── dashboard/                                # Admin dashboard
│   │           │   │   ├── events/                                   # Event management
│   │           │   │   └── event-categories/                         # Event categories management
│   │           │   ├── api/                                          # API endpoints
│   │           │   │   ├── admin/                                    # Admin-specific APIs
│   │           │   │   ├── auth/                                     # Authentication APIs
│   │           │   │   ├── bank/                                     # Banking transaction APIs
│   │           │   │   ├── cart/                                     # Shopping cart APIs
│   │           │   │   ├── cashier/                                  # Cashier functionality APIs
│   │           │   │   ├── checkout/                                 # Checkout and payment processing
│   │           │   │   ├── cron/                                     # Scheduled tasks
│   │           │   │   ├── events/                                   # Events management APIs
│   │           │   │   ├── news/                                     # News content APIs
│   │           │   │   ├── notifications/                            # Notification APIs
│   │           │   │   ├── orders/                                   # Order management APIs
│   │           │   │   ├── products/                                 # Product management APIs
│   │           │   │   ├── sales/                                    # Sales management APIs
│   │           │   │   ├── support/                                  # Support ticket system APIs
│   │           │   │   ├── uploads/                                  # File upload APIs
│   │           │   │   ├── users/                                    # User management APIs
│   │           │   │   └── volunteer/                                # Volunteer management APIs
│   │           │   ├── auth/                                         # Authentication pages
│   │           │   │   └── discord/                                  # Discord OAuth pages
│   │           │   ├── bank/                                         # Banking pages and dashboard
│   │           │   │   └── dashboard/                                # User banking dashboard
│   │           │   ├── cashier/                                      # Cashier portal
│   │           │   │   └── dashboard/                                # Cashier dashboard
│   │           │   ├── events/                                       # Event pages
│   │           │   ├── help/                                         # Help and support pages
│   │           │   ├── news/                                         # News system
│   │           │   │   └── dashboard/                                # News management dashboard
│   │           │   ├── rules/                                        # Platform rules
│   │           │   ├── sales/                                        # Sales management portal
│   │           │   ├── settings/                                     # User settings
│   │           │   ├── shop/                                         # Shopping system
│   │           │   │   ├── cart/                                     # Shopping cart
│   │           │   │   ├── checkout/                                 # Checkout process
│   │           │   │   ├── orders/                                   # Order management
│   │           │   │   └── products/                                 # Product browsing
│   │           │   ├── test/                                         # Testing pages
│   │           │   └── volunteer/                                    # Volunteer management system
│   │           │       ├── dashboard/                                # Volunteer dashboard
│   │           │       └── lead/                                     # Volunteer lead management
│   │           ├── components/                                       # React components
│   │           │   ├── admin/                                        # Admin components
│   │           │   ├── auth/                                         # Authentication components
│   │           │   ├── bank/                                         # Banking components
│   │           │   ├── cashier/                                      # Cashier components
│   │           │   ├── common/                                       # Common utility components
│   │           │   ├── events/                                       # Event components
│   │           │   ├── layout/                                       # Layout components
│   │           │   ├── news/                                         # News components
│   │           │   ├── notifications/                                # Notification components
│   │           │   ├── sales/                                        # Sales components
│   │           │   ├── settings/                                     # Settings components
│   │           │   ├── shared/                                       # Shared components
│   │           │   ├── shop/                                         # Shop components
│   │           │   ├── ui/                                           # UI utility components
│   │           │   ├── user/                                         # User profile components
│   │           │   └── volunteer/                                    # Volunteer components
│   │           │       └── lead/                                     # Volunteer lead components
│   │           ├── contexts/                                         # React contexts
│   │           ├── hooks/                                            # Custom React hooks
│   │           ├── lib/                                              # Utility functions
│   │           ├── providers/                                        # React providers
│   │           ├── services/                                         # API service functions
│   │           ├── scripts/                                          # Client-side scripts
│   │           ├── tests/                                            # Testing utilities
│   │           ├── types/                                            # TypeScript type definitions
│   │           └── utils/                                            # Utility helpers
│   └── packages/                                                     # Shared packages
│       ├── config/                                                   # Shared configuration
│       ├── ui/                                                       # Shared UI components
│       │   └── src/                                                  # UI component source
│       │       ├── button/                                           # Button components
│       │       ├── card/                                             # Card components
│       │       ├── content-card/                                     # Content card components
│       │       ├── editor/                                           # Rich text editor components
│       │       ├── featured-content/                                 # Featured content components
│       │       ├── hero/                                             # Hero section components
│       │       ├── hooks/                                            # UI-specific hooks
│       │       ├── input/                                            # Form input components
│       │       ├── modal/                                            # Modal dialog components
│       │       ├── pagination/                                       # Pagination components
│       │       ├── scroll-to-top/                                    # Scroll utility components
│       │       ├── search-bar/                                       # Search components
│       │       ├── sidebar/                                          # Sidebar navigation components
│       │       └── spinner/                                          # Loading spinner components
├── Guides/                                                           # Documentation
├── Maintenance/                                                      # Maintenance scripts
│   ├── backup_database.ps1                                           # Database backup script
│   ├── build-and-test-production.ps1                                 # Production build testing
│   ├── Get-FileTree.ps1                                              # File tree generator
│   ├── backup-*/                                                     # Backup scripts for different platforms
│   ├── restore-*/                                                    # Restore scripts for different platforms
│   ├── backups/                                                      # Database backups
│   ├── file-tree/                                                    # File tree documentation
│   └── logs/                                                         # Backup and maintenance logs
├── Prompt/                                                           # Project prompts and documentation
│   ├── file-tree/                                                    # File tree documentation
│   └── website-file-tree.txt                                         # Complete file tree listing
├── prod-imp/                                                         # Product implementation documentation
│   ├── product-implementation-phase*-completed.md                    # Completed phases
│   ├── product-implementation-plan-phase*.md                         # Implementation plans
│   ├── product-system.md                                             # Product system documentation
│   └── stripe-integration-guide.md                                   # Payment integration guide
├── ticket-hold/                                                      # Ticket hold system documentation
└── Tasks/                                                            # Project tasks and to-dos
```

### Monorepo Structure

The project uses a monorepo architecture with PNPM workspaces to manage multiple packages:

- **Main Site**: The primary Next.js application (`web/apps/main-site`)
- **API**: Standalone API modules (`web/apps/api`)
- **UI Package**: Shared UI components (`web/packages/ui`)
- **Config Package**: Shared configuration files (`web/packages/config`)

This structure allows for code sharing between packages while maintaining separation of concerns.

## Authentication

The application implements a comprehensive authentication system with multiple options for users.

### Authentication Endpoints

The authentication system is implemented through several API endpoints:

- `/api/auth/login`: Email/password authentication
- `/api/auth/register`: New user registration
- `/api/auth/me`: Get current user information
- `/api/auth/verify-email`: Email verification
- `/api/auth/set-password`: Password reset functionality
- `/api/auth/discord/*`: Discord OAuth integration endpoints

### Authentication Methods

#### Email/Password Authentication
- Secure password hashing using bcrypt
- JWT-based session management
- Email verification
- Password reset functionality

#### Discord OAuth Integration
- Login with Discord account
- Account linking between Discord and Bank of Styx
- Profile synchronization
- Dedicated error handling

## Banking System

The application includes a comprehensive banking system with various features for users and cashiers.

### Banking Features

- **Account Management**: View account details, balance, and settings
- **Transactions**: Process deposits, withdrawals, transfers, and donations
- **Pay Codes**: Generate and redeem payment codes
- **Transaction History**: View detailed transaction history
- **Real-time Updates**: Instant notifications for account changes

### Cashier Portal

The application includes a dedicated cashier portal for staff members:

- **Transaction Processing**: Process deposits and withdrawals
- **Member Management**: View and manage user accounts
- **Ledger**: Track all financial transactions
- **Statistics**: View banking system statistics
- **Notifications**: Real-time notifications for pending transactions

### Pay Code System

The application includes a robust pay code system for digital payments:

- **Generation**: Create new pay codes with specific amounts
- **Validation**: Validate pay codes before redemption
- **Redemption**: Redeem pay codes to receive funds
- **History**: Track active and redeemed pay codes

## Real-time Updates

The application uses Server-Sent Events (SSE) to provide real-time updates to users.

### Server-Sent Events (SSE)

- One-way communication channel from server to client
- Efficient for broadcasting updates to multiple clients
- Integrated with TanStack Query for state management

### Real-time Features

- **Balance Updates**: Instant balance updates when transactions occur
- **Transaction Notifications**: Real-time transaction history updates
- **Notifications**: System notifications for various events
- **Cashier Dashboard**: Real-time updates for cashiers processing transactions

## Setup Instructions

### Prerequisites

- Node.js >= 18.0.0
- PNPM 8.6.0 or later
- MySQL 8.0 or later
- Git

### Development Environment Setup

1. **Clone the repository**:
   ```bash
   git clone https://github.com/your-organization/bank-of-styx.git
   cd bank-of-styx
   ```

2. **Install dependencies**:
   ```bash
   pnpm install
   ```

3. **Set up environment variables**:
   - Copy `.env.example` to `.env.local` in `web/apps/main-site`
   - Update the variables with your local configuration

4. **Set up the database**:
   ```bash
   cd web/apps/main-site
   pnpm prisma migrate dev
   pnpm prisma:seed
   ```

5. **Start the development server**:
   ```bash
   cd ../..
   pnpm dev
   ```

### Environment Variables

Key environment variables required for the application:

```
# Database Connection
DATABASE_URL="mysql://username:password@localhost:3306/bank_of_styx"

# Authentication
JWT_SECRET="your_secret_key"

# Discord OAuth (if using)
DISCORD_CLIENT_ID=your_discord_client_id
DISCORD_CLIENT_SECRET=your_discord_client_secret
DISCORD_REDIRECT_URI=http://localhost:3000/api/auth/discord/callback

# Email Configuration
EMAIL_HOST=smtp.your-email-provider.com
EMAIL_PORT=465
EMAIL_SECURE=true
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_email_password
ADMIN_EMAIL=<EMAIL>
```

## Additional Features

### Core Administrative Features

#### News System
- Article creation, editing, and deletion
- Category organization
- Featured content highlighting
- Rich text editing

#### Admin Dashboard
- User account management
- Content management
- Support ticket handling
- Site usage statistics

## Recent Enhancements

### Ticket Hold System

The application now features a robust ticket hold system to prevent overselling and improve user experience:

- **Automatic Generation**: System auto-generates tickets for products and slots for volunteer shifts
- **Hold Management**: 15-minute automatic expiration with user-triggered extensions
- **Status Tracking**: Complete lifecycle tracking (Available, Held, Sold, Cancelled)
- **Race Condition Prevention**: Transaction-based system prevents double-booking
- **Real-time Updates**: Countdown timers show users how long their holds last

### Event Management System

A comprehensive event management system has been implemented:

- **Event Creation**: Admins can create and manage events with rich details
- **Event Categories**: Events can be organized by categories
- **Featured Events**: Highlight important upcoming events
- **Calendar Integration**: View events in calendar format with filters

### Volunteer Management System

A complete volunteer management system is now available:

- **Volunteer Categories**: Create categories with specific roles and pay rates
- **Shift Creation**: Set up shifts with time slots and volunteer limits
- **Volunteer Sign-up**: Users can browse and sign up for volunteer opportunities
- **Hour Tracking**: Track volunteer hours and process payments
- **Admin Dashboard**: Comprehensive management interface for organizers

### Support Ticket System

An enhanced support ticket system has been implemented:

- **Ticket Categories**: Organize tickets by type (general, account, banking, etc.)
- **Priority Levels**: Assign low, medium, high, or urgent priority to tickets
- **Assignment**: Admins can assign tickets to specific staff members
- **Communication**: Public and internal notes for ticket management
- **Email Notifications**: Automatic notifications for ticket updates

### Shop Items and Shopping System

The application now includes a robust shopping system:

- **Product Management**: Create and manage products with inventory tracking
- **Shopping Cart**: Add items to cart with automatic hold management
- **Checkout Process**: Secure payment processing integration
- **Order History**: View past purchases and order status
- **Inventory Control**: Automatic inventory management with ticket-based system

## Database Schema

The application uses Prisma ORM with a MySQL database. The schema includes multiple migrations for features such as:

- User accounts and authentication
- Banking transactions and balances
- News articles and categories
- Notification system
- Support ticket system
- User verification and credentials

## Deployment

### Building and Running in Production

1. **Build the packages**:
   ```bash
   cd web
   pnpm --filter @bank-of-styx/ui build
   pnpm --filter main-site build
   ```

2. **Running the application**:
   ```bash
   cd web/apps/main-site
   NODE_ENV=production pnpm start
   ```
   
   For production use, PM2 is recommended:
   ```bash
   npm install -g pm2
   pm2 start npm --name "bank-of-styx" -- start
   ```


### Post-Deployment Verification

After deploying to production, verify the following:

1. Database connection
2. Authentication system
3. Real-time updates (SSE)
4. Banking functionality
5. News system
6. Ticket hold system
7. Event management system
8. Volunteer management system
9. Support ticket system
10. Shopping system
