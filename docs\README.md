# Bank of Styx - Documentation System

## Overview
This documentation system provides comprehensive information about the Bank of Styx website structure, features, and components. Each major feature area has its own documentation following a standardized template.

## Quick Navigation

### Core Systems
- [🏛️ Administration System](./features/admin-dashboard-system.md) - Admin dashboard, user management, system analytics
- [🏦 Banking System](./features/banking-system.md) - Financial transactions, pay codes, cashier tools
- [⚓ Ship Management System](./features/ship-management-system.md) - Ship registration, crew management, applications
- [📰 News & Content Management](./features/news-content-management-system.md) - Article publishing, categories, featured content
- [🎯 Volunteer System](./features/volunteer-system.md) - Event volunteers, scheduling, hour tracking
- [🛒 Shopping & Sales System](./features/shopping-sales-system.md) - Product sales, cart management, order processing
- [🔔 Real-Time Notification System](./features/real-time-notification-system.md) - Real-time notifications for users

### User Management
- [👤 Authentication System](./features/authentication-system.md) - Login, registration, Discord OAuth
- [⚙️ User Settings & Preferences](./features/user-settings-profile-management-system.md) - Profiles, notifications, preferences
- [🎫 Support Ticket System](./features/support-system.md) - Help desk, ticket management

### Developer Resources
- [🏗️ API Structure](./api/README.md) - Complete API endpoint documentation
- [🧩 Component Library](./components/ui-library.md) - Reusable UI components
- [🗂️ Directory Structure](./architecture/directory-structure.md) - Complete codebase organization
- [🔧 Development Guide](./development/setup-guide.md) - Setup, build, and deployment
- [📜 Database Schema](./database/schema-documentation.md) - Documentation of the database schema
- [🛠️ System Utilities](./technical/system-utilities.md) - Documentation for system utilities
- [📤 Upload System](./technical/upload-system.md) - Documentation for the upload system

### Documentation Guidelines
- [📝 Documentation Template](./TEMPLATE.md) - Template for creating new documentation
- [✅ Documentation Checklist](./DOCUMENTATION-CHECKLIST.md) - Checklist for ensuring documentation quality

## Documentation Standards

Each feature documentation includes:
- **Overview**: Purpose and functionality
- **Directory Structure**: File organization
- **Key Components**: Main files and their roles
- **API Endpoints**: Related backend endpoints
- **Database Models**: Associated data structures
- **Common Tasks**: How-to guides for common operations
- **Related Features**: Integration points with other systems

## Getting Started

1. **For New Developers**: Start with [Directory Structure](./architecture/directory-structure.md)
2. **For Feature Work**: Navigate to the specific [feature documentation](./features/)
3. **For API Integration**: Check [API Documentation](./api/)
4. **For UI Development**: Review [Component Library](./components/ui-library.md)

## Contributing to Documentation

When adding new features or modifying existing ones:
1. Update the relevant feature documentation
2. Add new API endpoints to API docs
3. Update the directory structure if needed
4. Follow the established template format

## Last Updated
Generated: 2025-01-07