import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser, userHasRole } from "@/lib/auth";

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// GET /api/volunteer/shifts/[id]/status - Get detailed shift status information
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const { id } = params;

    // Check if user is authenticated and has volunteer coordinator role
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 },
      );
    }

    const hasCoordinatorRole = await userHasRole(req, "volunteerCoordinator");
    if (!hasCoordinatorRole) {
      return NextResponse.json(
        { error: "Unauthorized - Coordinator role required" },
        { status: 403 },
      );
    }

    // Get the shift with detailed assignment information
    const shift = await prisma.volunteerShift.findUnique({
      where: { id },
      include: {
        assignments: {
          include: {
            user: {
              select: {
                id: true,
                displayName: true,
                username: true,
                email: true,
                discordId: true,
              },
            },
            hours: {
              select: {
                paymentStatus: true,
                paymentAmount: true,
                hoursWorked: true,
                verifiedAt: true,
                verifiedBy: {
                  select: {
                    displayName: true,
                  },
                },
              },
            },
          },
          orderBy: {
            createdAt: "asc",
          },
        },
        event: {
          select: {
            id: true,
            name: true,
          },
        },
        category: {
          select: {
            id: true,
            name: true,
            payRate: true,
          },
        },
      },
    });

    if (!shift) {
      return NextResponse.json({ error: "Shift not found" }, { status: 404 });
    }

    // Process assignments to include additional status information
    const processedAssignments = shift.assignments.map((assignment) => {
      const hours = assignment.hours; // Single hours record (one-to-one relationship)
      
      return {
        id: assignment.id,
        status: assignment.status,
        signupDate: assignment.createdAt,
        checkedInAt: assignment.checkedInAt,
        user: assignment.user,
        payment: hours ? {
          status: hours.paymentStatus,
          amount: hours.paymentAmount,
          hoursWorked: hours.hoursWorked,
          verifiedAt: hours.verifiedAt,
          verifiedBy: hours.verifiedBy?.displayName,
        } : null,
      };
    });

    // Calculate statistics
    const totalSlots = shift.maxVolunteers;
    const filledSlots = shift.assignments.length;
    const checkedInCount = shift.assignments.filter(a => a.checkedInAt !== null).length;
    const completedCount = shift.assignments.filter(a => a.status === "completed").length;
    const pendingPayments = shift.assignments.filter(a => 
      a.hours?.paymentStatus === "pending"
    ).length;

    const shiftStatus = {
      shift: {
        id: shift.id,
        title: shift.title,
        description: shift.description,
        startTime: shift.startTime,
        endTime: shift.endTime,
        location: shift.location,
        maxVolunteers: shift.maxVolunteers,
        event: shift.event,
        category: shift.category,
      },
      statistics: {
        totalSlots,
        filledSlots,
        availableSlots: totalSlots - filledSlots,
        checkedInCount,
        completedCount,
        pendingPayments,
      },
      assignments: processedAssignments,
    };

    return NextResponse.json(shiftStatus);
  } catch (error) {
    console.error("Error fetching shift status:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch shift status",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    );
  }
}