# User Settings & Profile Management System

## Overview
The User Settings & Profile Management System provides comprehensive user-facing customization, profile management, and account control capabilities. It features advanced theme customization with 8 preset themes, granular notification preferences, avatar management with Discord integration, security settings, and seamless account linking across multiple authentication methods.

## Directory Structure
```
user-settings-profile-management-system/
├── src/app/settings/                  # User settings frontend
│   ├── page.tsx                       # Main settings hub with profile, security, and preferences
│   ├── layout.tsx                     # Settings section layout wrapper
│   ├── notifications/                 # Dedicated notification preferences
│   │   └── page.tsx                   # Granular notification control interface
│   └── colors/                        # Advanced color theme customization
│       └── page.tsx                   # Theme presets and individual color customization
├── src/app/api/users/                 # User settings API endpoints
│   ├── profile/                       # Profile information management
│   ├── notification-preferences/      # Notification settings API
│   ├── default-view/                  # Dashboard view preferences
│   ├── security/                      # Password and security management
│   ├── sync-discord-avatar/           # Discord avatar synchronization
│   └── state/                         # Extended user state and analytics
├── src/components/user/               # User interface components
│   ├── UserMenu.tsx                   # Navigation dropdown with role-based access
│   ├── SimpleAvatarUpload.tsx         # Avatar upload with automatic cropping
│   └── SyncDiscordAvatarButton.tsx    # Discord avatar sync integration
├── src/components/settings/           # Settings-specific components
│   ├── ColorPicker.tsx                # Individual color customization interface
│   └── ThemePresets.tsx               # 8 predefined theme selection interface
├── src/contexts/                      # Settings state management
│   ├── AuthContext.tsx                # User authentication and profile state
│   └── ColorThemeContext.tsx          # Theme management and CSS variable updates
├── src/services/                      # Settings business logic
│   ├── userService.ts                 # Profile and settings API service
│   ├── notificationService.ts         # Notification preferences management
│   └── avatarService.ts               # Avatar upload and processing
├── src/hooks/                         # Settings React hooks
│   ├── useNotificationPreferences.ts  # Notification preference management with optimistic updates
│   ├── useUserProfile.ts              # User profile data management
│   └── useColorTheme.ts               # Theme customization hooks
└── src/types/                         # Settings TypeScript definitions
    ├── user-settings.ts               # User settings interfaces
    ├── notification-preferences.ts    # Notification preference types
    └── color-theme.ts                 # Theme customization types
```

## Key Files & Components

### Frontend Components
- **`UserMenu.tsx`** - Navigation dropdown with role-based dashboard access, theme toggle, and settings navigation
- **`SimpleAvatarUpload.tsx`** - Avatar upload with client-side cropping to square format, automatic resizing to 100x100, and JPEG compression
- **`SyncDiscordAvatarButton.tsx`** - Discord avatar synchronization with automatic download and local processing
- **`ColorPicker.tsx`** - Individual color customization with live preview, color validation, and CSS variable mapping
- **`ThemePresets.tsx`** - 8 predefined themes including Bank of Styx-specific Pirate Gold and Crimson Gold themes
- **`NotificationSettings.tsx`** - Granular notification preference controls with toggle interfaces and descriptions
- **`ProfileSettings.tsx`** - Profile information management with email verification workflow integration

### API Endpoints
- **`PUT /api/users/profile`** - Update user profile information including display name, email, and avatar with validation
- **`GET /api/users/notification-preferences`** - Retrieve current notification preference settings
- **`PUT /api/users/notification-preferences`** - Update notification preferences with immediate application
- **`PUT /api/users/default-view`** - Set default dashboard view preference with validation of allowed values
- **`PUT /api/users/security`** - Password change functionality with current password validation
- **`POST /api/users/sync-discord-avatar`** - Discord avatar synchronization with download and local processing
- **`GET /api/user/state`** - Extended user state and analytics data retrieval
- **`PATCH /api/user/state`** - Update custom user state and feature access settings

### Database Models
- **`User`** - Core user model with profile information, notification preferences, connected accounts, and role management
- **`UserState`** - Extended user settings with beta features, UI preferences, analytics data, and custom state storage
- **`UserCredential`** - Multi-method authentication storage supporting email, Discord, and future OAuth providers

### Services
- **`userService.ts`** - Centralized API service for profile updates, security settings, and preference management
- **`notificationService.ts`** - Notification preference management with TanStack Query integration and optimistic updates
- **`avatarService.ts`** - Avatar upload processing with image validation and server integration

### Hooks
- **`useNotificationPreferences()`** - Notification preference management with caching, optimistic updates, and toast feedback
- **`useUserProfile()`** - User profile data management with real-time updates and validation
- **`useColorTheme()`** - Theme customization with localStorage persistence and CSS variable management

## Common Tasks

### Task 1: How to Customize Profile and Account Settings
1. User navigates to `/settings` for main settings hub (authentication required)
2. Updates profile information including display name, email, and avatar
3. Uploads new avatar using `SimpleAvatarUpload` with automatic cropping and resizing
4. Alternatively syncs Discord avatar using `SyncDiscordAvatarButton` for automatic processing
5. Updates notification preferences using toggle switches with immediate API persistence
6. Selects default dashboard view from available options (dashboard, transactions, transfer, pay-code, donate)
7. Changes password through security section with current password validation
8. Links or unlinks Discord account through OAuth integration
9. Reviews and updates email verification status for enhanced security
10. All changes automatically reflected across the application with real-time updates

### Task 2: How to Manage Notification Preferences
1. User accesses `/settings/notifications` for detailed notification management
2. Reviews all available notification categories with descriptions:
   - Transaction notifications (transfers, deposits, withdrawals)
   - Content notifications (news & events, auctions, chat messages)
   - System notifications (administrative notifications)
3. Toggles individual notification preferences using switch interfaces
4. Changes immediately persisted to database via API calls
5. Toast notifications confirm successful preference updates
6. Notification system automatically applies new preferences to future notifications
7. Real-time updates ensure consistent notification behavior across sessions

### Task 3: How to Customize Color Themes
1. User navigates to `/settings/colors` for advanced theme customization
2. Chooses from 8 predefined theme presets including:
   - Discord Light and Dark themes
   - Bank of Styx themed Pirate Gold and Crimson Gold
   - Specialty themes like Midnight Blue, Emerald Dark, Seafoam, and Royal Purple
3. Alternatively customizes individual colors by category:
   - Primary colors (main actions and branding)
   - Secondary colors (backgrounds and surfaces)
   - Accent colors (destructive actions and highlights)
   - Status colors (success, warning, error, info states)
   - Text colors (various hierarchy levels)
   - Border and background colors
4. Uses `ColorPicker` component for precise color selection with live preview
5. Themes automatically saved to localStorage with CSS variable updates
6. Reset to defaults option available for reverting customizations
7. Changes immediately applied across entire application interface

## API Integration

### Authentication Requirements
- **All settings endpoints**: Valid JWT token required for user settings access
- **Profile updates**: User can only modify their own profile information
- **Security operations**: Current password validation required for password changes
- **Discord integration**: OAuth token validation for avatar sync and account linking
- **State management**: User-specific state access with proper authorization
- **Token format**: `Bearer <jwt-token>` in Authorization header with user context validation

### Request/Response Examples
```typescript
// Profile Update Request
interface UpdateProfileRequest {
  displayName?: string;    // User's display name
  email?: string;          // Email address with uniqueness validation
  avatar?: string;         // Avatar URL or upload path
}

// User Profile Response
interface UserProfileResponse {
  id: string;
  username: string;
  displayName: string;
  email: string;
  avatar: string;
  isEmailVerified: boolean;
  balance: number;
  status: string;
  defaultView: string;     // Dashboard view preference
  
  // Notification preferences
  notificationPreferences: {
    notifyTransfers: boolean;
    notifyDeposits: boolean;
    notifyWithdrawals: boolean;
    notifyNewsEvents: boolean;
    notifyAuctions: boolean;
    notifyChat: boolean;
    notifyAdmin: boolean;
  };
  
  // Connected accounts
  connectedAccounts: {
    discordConnected: boolean;
    discordId?: string;
    facebookConnected: boolean;
    facebookId?: string;
  };
  
  // Role information
  roles: {
    isAdmin: boolean;
    isEditor: boolean;
    isBanker: boolean;
    isChatModerator: boolean;
    isVolunteerCoordinator: boolean;
    isLeadManager: boolean;
    isSalesManager: boolean;
    isLandSteward: boolean;
  };
}

// Notification Preferences Update Request
interface UpdateNotificationPreferencesRequest {
  notifyTransfers?: boolean;
  notifyDeposits?: boolean;
  notifyWithdrawals?: boolean;
  notifyNewsEvents?: boolean;
  notifyAuctions?: boolean;
  notifyChat?: boolean;
  notifyAdmin?: boolean;
}

// Security Settings Request
interface UpdateSecurityRequest {
  currentPassword: string;  // Required for validation
  newPassword: string;      // New password with minimum requirements
}

// User State Response
interface UserStateResponse {
  id: string;
  userId: string;
  betaFeaturesEnabled: boolean;
  darkModeEnabled: boolean;
  compactViewEnabled: boolean;
  customState: Record<string, any>;    // Flexible custom settings storage
  
  // Analytics data
  pageViews: number;
  articleReads: number;
  lastActive: string;
  
  // Feature access control
  hasAccessToAuctions: boolean;
  hasAccessToMerchants: boolean;
  hasAccessToChat: boolean;
  
  createdAt: string;
  updatedAt: string;
}

// Color Theme Configuration
interface ColorThemeConfig {
  name: string;
  colors: {
    primary: {
      main: string;
      hover: string;
      active: string;
    };
    secondary: {
      main: string;
      hover: string;
      active: string;
    };
    accent: {
      destructive: string;
      warning: string;
      success: string;
      info: string;
    };
    text: {
      primary: string;
      secondary: string;
      muted: string;
      inverse: string;
    };
    border: {
      default: string;
      muted: string;
      emphasis: string;
    };
    background: {
      page: string;
      card: string;
      input: string;
      hover: string;
    };
  };
}
```

## Database Schema

### Primary Models
```sql
-- User table (core settings storage)
-- Profile and preference fields already documented in authentication system
-- Key settings-related fields:
ALTER TABLE User ADD COLUMN defaultView VARCHAR(50) DEFAULT 'dashboard';

-- Notification preference fields (already exist):
-- notifyTransfers, notifyDeposits, notifyWithdrawals BOOLEAN DEFAULT true
-- notifyNewsEvents, notifyAuctions, notifyChat, notifyAdmin BOOLEAN DEFAULT false

-- Connected account fields (already exist):
-- discordConnected BOOLEAN DEFAULT false, discordId VARCHAR(255)
-- facebookConnected BOOLEAN DEFAULT false, facebookId VARCHAR(255)

-- UserState table (extended settings and analytics)
CREATE TABLE UserState (
  id VARCHAR(191) PRIMARY KEY,
  userId VARCHAR(191) UNIQUE NOT NULL,
  
  -- UI and feature preferences
  betaFeaturesEnabled BOOLEAN DEFAULT false,
  darkModeEnabled BOOLEAN DEFAULT true,
  compactViewEnabled BOOLEAN DEFAULT false,
  customState JSON,                    -- Flexible custom settings storage
  
  -- Analytics and activity tracking
  pageViews INT DEFAULT 0,
  articleReads INT DEFAULT 0,
  lastActive DATETIME,
  
  -- Feature access control
  hasAccessToAuctions BOOLEAN DEFAULT false,
  hasAccessToMerchants BOOLEAN DEFAULT false,
  hasAccessToChat BOOLEAN DEFAULT false,
  
  -- Synchronization tracking
  lastNewsSync DATETIME,
  lastCategorySync DATETIME,
  
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (userId) REFERENCES User(id) ON DELETE CASCADE,
  INDEX idx_userstate_user (userId),
  INDEX idx_userstate_active (lastActive)
);

-- UserCredential table (multi-method authentication)
-- Already documented in authentication system
-- Supports email, discord, facebook authentication methods
-- Stores OAuth tokens for connected accounts

-- Color theme preferences (client-side storage)
-- Themes stored in localStorage as 'bankOfStyxColorTheme'
-- No server-side storage required for theme preferences
```

### Relationships
- **User ↔ UserState**: One-to-one relationship for extended settings and analytics
- **User ↔ UserCredential**: One-to-many relationship for multiple authentication methods
- **User ↔ Authentication System**: Integration with JWT authentication and role management
- **UserState ↔ Analytics**: Integration with user activity tracking and feature access control

## Related Features
- **[Authentication System](./authentication-system.md)** - User authentication, role management, and account security integration
- **[Notification System](./notification-system.md)** - Notification preference application and delivery management
- **[Upload System](./upload-system.md)** - Avatar upload processing and image management
- **[Banking System](./banking-system.md)** - Notification preferences for transaction alerts and financial updates
- **[News Content Management System](./news-content-management-system.md)** - Content notification preferences and reading analytics

## User Roles & Permissions
- **All Authenticated Users**: Complete access to personal settings, profile management, and customization options
- **Users with Connected Accounts**: Additional options for Discord avatar sync and account linking management
- **Beta Users**: Access to beta features toggle and advanced customization options via UserState configuration
- **Role-based Navigation**: UserMenu component provides role-appropriate dashboard access for admins, captains, coordinators, etc.

## Recent Changes
- **v9.2.0** - Enhanced color theme system with 8 predefined themes including Bank of Styx-specific Pirate Gold and Crimson Gold themes
- **v9.1.0** - Advanced notification preference management with granular controls and immediate application
- **v9.0.0** - Complete settings redesign with tabbed interface and improved user experience
- **v8.9.0** - Discord avatar synchronization with automatic download and local processing
- **v8.8.0** - UserState model implementation for extended analytics and feature access control
- **v8.7.0** - Avatar upload system with client-side cropping and automatic resizing
- **v8.6.0** - Default dashboard view preferences with validation and application

## Troubleshooting

### Common Issues
1. **Avatar upload failures**: Check file size limits (10MB), supported formats (JPEG, PNG, GIF, WEBP), and upload endpoint accessibility
2. **Theme changes not applying**: Verify localStorage access, CSS variable updates, and context provider configuration
3. **Notification preferences not saving**: Check API endpoint connectivity, authentication token validity, and database transaction completion
4. **Discord avatar sync failing**: Verify Discord OAuth tokens, API connectivity, and image processing pipeline functionality
5. **Password changes rejected**: Ensure current password validation, minimum password requirements, and proper bcrypt comparison
6. **Profile updates not persisting**: Check email uniqueness validation, database constraints, and API error responses

### Debug Information
- **Log locations**: User setting changes logged in server console, client-side theme changes in browser storage
- **Environment variables**: Upload service configuration, Discord OAuth credentials, database connection settings
- **Database queries**: Use Prisma Studio to inspect User and UserState records for preference consistency
- **Client storage**: Check browser localStorage for theme preferences and temporary settings
- **Debugging commands**:
  ```bash
  # Check user profile and settings
  curl -H "Authorization: Bearer <token>" http://localhost:3000/api/users/profile
  
  # Verify notification preferences
  curl -H "Authorization: Bearer <token>" http://localhost:3000/api/users/notification-preferences
  
  # Test user state management
  curl -H "Authorization: Bearer <token>" http://localhost:3000/api/user/state
  
  # Check Discord avatar sync
  curl -X POST -H "Authorization: Bearer <token>" http://localhost:3000/api/users/sync-discord-avatar
  ```

### Performance Considerations
- **Theme Application**: CSS variables updated dynamically for immediate theme changes without page reload
- **Avatar Processing**: Client-side image cropping and resizing reduces server load and improves upload performance
- **Preference Caching**: TanStack Query provides optimized caching for notification preferences and user settings
- **Optimistic Updates**: Immediate UI updates with background API calls for responsive user experience
- **LocalStorage Usage**: Theme preferences stored locally for instant application and reduced server requests

### Security Measures
- **Profile Validation**: Email uniqueness validation and display name sanitization prevent data conflicts
- **Password Security**: Current password validation required for password changes with bcrypt hashing
- **OAuth Security**: Discord token validation and secure avatar download with proper error handling
- **Input Sanitization**: All user inputs validated and sanitized before database storage
- **Access Control**: Users can only modify their own settings with proper JWT token validation
- **Avatar Security**: File type validation and size limits prevent malicious uploads

### Integration Patterns
- **Authentication Integration**: Seamless integration with JWT authentication system and user context management
- **Notification Integration**: Preference changes immediately applied to notification delivery system
- **Theme Integration**: Color theme changes propagate across all UI components via CSS variable system
- **Upload Integration**: Avatar uploads utilize existing upload infrastructure with proper validation and processing
- **State Management**: Settings changes synchronized across multiple browser tabs and sessions

---

**File Locations:**
- Pages: `/src/app/settings/`
- Components: `/src/components/user/`, `/src/components/settings/`
- API: `/src/app/api/users/`, `/src/app/api/user/`
- Contexts: `/src/contexts/AuthContext.tsx`, `/src/contexts/ColorThemeContext.tsx`
- Services: `/src/services/userService.ts`, `/src/services/notificationService.ts`
- Hooks: `/src/hooks/useNotificationPreferences.ts`, `/src/hooks/useUserProfile.ts`
- Types: `/src/types/user-settings.ts`, `/src/types/color-theme.ts`