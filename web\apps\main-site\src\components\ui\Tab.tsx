import React from "react";

interface TabProps {
  active: boolean;
  onClick: () => void;
  children: React.ReactNode;
}

export const Tab: React.FC<TabProps> = ({ active, onClick, children }) => {
  return (
    <button
      onClick={onClick}
      className={`px-2 py-2 font-medium border-b-2 transition-colors ${
        active
          ? "text-primary border-primary"
          : "text-gray-400 border-transparent hover:text-white hover:border-gray-600"
      }`}
    >
      {children}
    </button>
  );
};
