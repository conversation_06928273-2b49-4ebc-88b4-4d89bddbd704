"use client";

import React, { useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { Button } from "@bank-of-styx/ui";

interface Ship {
  id: string;
  name: string;
  logo?: string;
}

interface CaptainDashboardLayoutProps {
  children: React.ReactNode;
  ship?: Ship;
}

export const CaptainDashboardLayout: React.FC<CaptainDashboardLayoutProps> = ({
  children,
  ship,
}) => {
  const pathname = usePathname();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const navItems = [
    { name: "Overview", path: "/captain/dashboard", icon: "📊" },
    { name: "Members", path: "/captain/dashboard/members", icon: "👥" },
    { name: "Roles", path: "/captain/dashboard/roles", icon: "⚡" },
    { name: "Invite", path: "/captain/dashboard/invite", icon: "✉️" },
    { name: "Forms", path: "/captain/dashboard/forms", icon: "📝" },
    { name: "Setting<PERSON>", path: "/captain/dashboard/settings", icon: "⚙️" },
  ];

  const isActive = (path: string) => {
    if (path === "/captain/dashboard") {
      return pathname === "/captain/dashboard";
    }
    return pathname.startsWith(path);
  };

  return (
    <div className="min-h-screen bg-secondary-dark">
      <div className="container mx-auto px-2 sm:px-4 py-2">
        {/* Header */}
        <div className="flex items-center justify-between mb-4 p-3 bg-secondary-light rounded-lg border border-gray-600">
          <div className="flex items-center">
            <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-full mr-3 bg-primary flex items-center justify-center text-white font-bold text-lg sm:text-xl overflow-hidden">
              {ship?.logo ? (
                <img 
                  src={ship.logo} 
                  alt={`${ship.name} logo`} 
                  className="w-full h-full object-cover"
                />
              ) : (
                ship?.name?.charAt(0) || "C"
              )}
            </div>
            <div className="min-w-0 flex-1">
              <h1 className="text-lg sm:text-2xl font-bold text-white truncate">
                {ship?.name || "Captain Dashboard"}
              </h1>
              <p className="text-xs sm:text-sm text-gray-400 hidden sm:block">
                Manage your ship and crew
              </p>
            </div>
          </div>
          
          {/* Mobile menu button */}
          <Button
            variant="ghost"
            size="sm"
            className="md:hidden"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            <span className="text-lg">☰</span>
          </Button>
        </div>

        <div className="flex flex-col md:flex-row gap-4">
          {/* Mobile Navigation Menu */}
          {isMobileMenuOpen && (
            <div className="md:hidden bg-secondary-light rounded-lg border border-gray-600 p-2 mb-4">
              <div className="grid grid-cols-2 gap-2">
                {navItems.map((item) => (
                  <Link
                    key={item.path}
                    href={item.path}
                    onClick={() => setIsMobileMenuOpen(false)}
                    className={`
                      flex items-center gap-2 p-2 rounded text-sm font-medium transition-colors border border-gray-600
                      ${
                        isActive(item.path)
                          ? "bg-primary text-white border-primary"
                          : "text-gray-300 hover:text-white hover:bg-gray-700 hover:border-gray-500"
                      }
                    `}
                  >
                    <span className="text-base">{item.icon}</span>
                    <span className="truncate">{item.name}</span>
                  </Link>
                ))}
              </div>
            </div>
          )}

          {/* Desktop Sidebar Navigation */}
          <div className="hidden md:block w-64 bg-secondary-light rounded-lg border border-gray-600 p-4 h-fit">
            <nav className="space-y-2">
              {navItems.map((item) => (
                <Link
                  key={item.path}
                  href={item.path}
                  className={`
                    flex items-center gap-3 p-3 rounded-lg text-sm font-medium transition-colors border border-gray-600
                    ${
                      isActive(item.path)
                        ? "bg-primary text-white border-primary"
                        : "text-gray-300 hover:text-white hover:bg-gray-700 hover:border-gray-500"
                    }
                  `}
                >
                  <span className="text-lg">{item.icon}</span>
                  <span>{item.name}</span>
                </Link>
              ))}
            </nav>
          </div>

          {/* Main Content */}
          <div className="flex-1 bg-secondary-light rounded-lg border border-gray-600 p-3 sm:p-3">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
};

CaptainDashboardLayout.displayName = "CaptainDashboardLayout";