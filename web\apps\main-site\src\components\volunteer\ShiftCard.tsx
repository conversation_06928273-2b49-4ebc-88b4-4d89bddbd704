"use client";

import React, { useState } from "react";
import { Button } from "@bank-of-styx/ui";
import { VolunteerShift } from "@/hooks/useVolunteerShifts";
import { ShiftStatusModal } from "./ShiftStatusModal";

interface ShiftCardProps {
  shift: VolunteerShift;
  onEdit: (shift: VolunteerShift) => void;
  onDelete: (shift: VolunteerShift) => void;
}

export const ShiftCard: React.FC<ShiftCardProps> = ({
  shift,
  onEdit,
  onDelete,
}) => {
  const [showStatusModal, setShowStatusModal] = useState(false);
  // Format date and time
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      weekday: "short",
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    });
  };

  // Calculate duration in hours
  const calculateDuration = () => {
    const start = new Date(shift.startTime);
    const end = new Date(shift.endTime);
    const durationMs = end.getTime() - start.getTime();
    const durationHours = durationMs / (1000 * 60 * 60);
    return durationHours.toFixed(1);
  };

  return (
    <div className="bg-secondary-light rounded-lg shadow-md overflow-hidden border border-gray-600 flex flex-col h-full">
      {/* Card Header */}
      <div className="p-4 border-b border-gray-600 bg-secondary">
        <h3 className="text-lg font-bold text-white truncate">{shift.title}</h3>
        <p className="text-sm text-gray-400">{formatDate(shift.startTime)}</p>
      </div>

      {/* Card Body */}
      <div className="p-4 flex-grow">
        {/* Time Information */}
        <div className="mb-4">
          <div className="flex justify-between mb-1">
            <span className="text-sm font-medium text-gray-400">Start:</span>
            <span className="text-sm text-white">
              {formatTime(shift.startTime)}
            </span>
          </div>
          <div className="flex justify-between mb-1">
            <span className="text-sm font-medium text-gray-400">End:</span>
            <span className="text-sm text-white">
              {formatTime(shift.endTime)}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-sm font-medium text-gray-400">Duration:</span>
            <span className="text-sm text-white">
              {calculateDuration()} hours
            </span>
          </div>
        </div>

        {/* Description */}
        {shift.description && (
          <div className="mb-4">
            <p className="text-gray-300 line-clamp-3">{shift.description}</p>
          </div>
        )}

        {/* Location */}
        {shift.location && (
          <div className="mb-4">
            <span className="text-sm font-medium text-gray-400">Location:</span>
            <p className="text-gray-300">{shift.location}</p>
          </div>
        )}

        {/* Volunteer Slots */}
        <div className="mb-4">
          <span className="text-sm font-medium text-gray-400">
            Volunteer Slots:
          </span>
          <div className="flex items-center mt-1">
            <div className="w-full bg-secondary-dark rounded-full h-2.5">
              <div
                className="bg-primary h-2.5 rounded-full"
                style={{
                  width: `${
                    (shift.stats?.totalAssignments ?? 0) && shift.maxVolunteers
                      ? Math.min(
                          100,
                          ((shift.stats?.totalAssignments ?? 0) /
                            shift.maxVolunteers) *
                            100,
                        )
                      : 0
                  }%`,
                }}
              ></div>
            </div>
            <span className="ml-2 text-sm text-white">
              {shift.stats?.totalAssignments ?? 0}/{shift.maxVolunteers}
            </span>
          </div>
        </div>

        {/* Completion Rate */}
        {(shift.stats?.totalAssignments ?? 0) > 0 && (
          <div className="mb-4">
            <span className="text-sm font-medium text-gray-400">
              Completion Rate:
            </span>
            <div className="flex items-center mt-1">
              <div className="w-full bg-secondary-dark rounded-full h-2.5">
                <div
                  className="bg-success h-2.5 rounded-full"
                  style={{
                    width: `${
                      shift.stats?.totalAssignments
                        ? Math.min(
                            100,
                            ((shift.stats?.completedAssignments ?? 0) /
                              shift.stats.totalAssignments) *
                              100,
                          )
                        : 0
                    }%`,
                  }}
                ></div>
              </div>
              <span className="ml-2 text-sm text-white">
                {shift.stats?.completedAssignments ?? 0}/
                {shift.stats?.totalAssignments ?? 0}
              </span>
            </div>
          </div>
        )}
      </div>

      {/* Card Footer */}
      <div className="p-4 border-t border-gray-600 bg-secondary">
        <div className="flex justify-between gap-2">
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={() => onEdit(shift)}>
              Edit
            </Button>
            <Button variant="outline" size="sm" onClick={() => onDelete(shift)} className="border-red-600 text-red-600 hover:bg-red-600 hover:text-white">
              Delete
            </Button>
          </div>
          <Button 
            variant="primary" 
            size="sm" 
            onClick={() => setShowStatusModal(true)}
          >
            Status
          </Button>
        </div>
      </div>

      {/* Status Modal */}
      <ShiftStatusModal
        isOpen={showStatusModal}
        onClose={() => setShowStatusModal(false)}
        shiftId={shift.id}
        shiftTitle={shift.title}
      />
    </div>
  );
};
