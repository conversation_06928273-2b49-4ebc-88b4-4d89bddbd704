import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import prisma from '@/lib/prisma';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const captainId = user.id;
    const requestId = params.id;
    const { action, roleId, roleName } = await request.json();

    if (!action || !['accept', 'decline'].includes(action)) {
      return NextResponse.json({ error: 'Invalid action. Must be accept or decline' }, { status: 400 });
    }

    // Verify captain owns a ship
    const ship = await prisma.ship.findFirst({
      where: {
        captainId,
        status: 'active'
      }
    });

    if (!ship) {
      return NextResponse.json({ error: 'Ship not found or you are not the captain' }, { status: 404 });
    }

    // Find the join request and verify it belongs to this captain's ship
    const joinRequest = await prisma.shipJoinRequest.findFirst({
      where: {
        id: requestId,
        shipId: ship.id,
        type: 'request',
        status: 'pending'
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true
          }
        }
      }
    });

    if (!joinRequest) {
      return NextResponse.json({ error: 'Join request not found' }, { status: 404 });
    }

    if (action === 'accept') {
      // Use transaction to ensure both operations succeed
      await prisma.$transaction(async (tx) => {
        // Update request status
        await tx.shipJoinRequest.update({
          where: { id: requestId },
          data: { 
            status: 'accepted',
            respondedAt: new Date()
          }
        });

        // Add user as ship member
        await tx.shipMember.create({
          data: {
            userId: joinRequest.userId,
            shipId: ship.id,
            role: roleName || 'Member',
            roleId: roleId || null,
            status: 'active',
            joinedAt: new Date()
          }
        });
      });

      return NextResponse.json({
        success: true,
        message: 'Join request accepted successfully',
        action: 'accepted'
      });

    } else { // decline
      await prisma.shipJoinRequest.update({
        where: { id: requestId },
        data: { 
          status: 'declined',
          respondedAt: new Date()
        }
      });

      return NextResponse.json({
        success: true,
        message: 'Join request declined',
        action: 'declined'
      });
    }

  } catch (error) {
    console.error('Error processing join request:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}