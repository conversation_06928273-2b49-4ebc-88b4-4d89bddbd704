# Real-time SSE Implementation

## Overview
The application uses Server-Sent Events (SSE) for real-time communication with a sophisticated connection management system.

## Key Components

### SSE Endpoint
- **Route**: `/api/notifications/sse`
- **Authentication**: JWT token via query parameter (primary) or Authorization header
- **Connection**: Persistent HTTP connection using TransformStream

### Connection Store (`connectionStore.ts`)
- **Singleton Pattern**: Global connection management
- **One Connection Per User**: Replaces old user connections with new ones
- **Connection Tracking**: Maintains `userConnectionMap` for active connections
- **Statistics**: Tracks messages sent, bytes transferred, errors

### Heartbeat Service (`heartbeatService.ts`)
- **Interval**: 30-second heartbeats to keep connections alive
- **Cleanup**: 5-minute intervals for inactive connection cleanup
- **Duplicate Prevention**: Detects and prevents multiple service instances
- **Global State**: Uses `global.__heartbeatService` for singleton behavior

## Unique Patterns

### Authentication for SSE
Since EventSource doesn't support custom headers, auth token is passed via query parameter:

```typescript
// Primary method for SSE
const url = new URL(req.url);
token = url.searchParams.get("auth_token");

// Fallback for other requests
const authHeader = req.headers.get("authorization");
if (authHeader && authHeader.startsWith("Bearer ")) {
  token = authHeader.split(" ")[1];
}
```

### Connection Replacement Strategy
When a user opens a new connection, the old one is gracefully replaced:

```typescript
if (existingConnectionId) {
  // Send transition message to old connection
  const transitionMessage = {
    type: "connection_replaced",
    message: "Your connection has been replaced by a new session",
    timestamp: new Date().toISOString(),
  };
  
  // Close old connection and replace with new one
  await existingConnection.writer.close();
  this.connections.delete(existingConnectionId);
}
```

### User Activity Tracking
Connections are monitored for user activity via database `userState` table:

```typescript
async isUserInactive(userId: string, maxInactiveMinutes: number = 60): Promise<boolean> {
  const userState = await prisma.userState.findUnique({
    where: { userId },
    select: { lastActive: true },
  });
  
  // Check if user has been inactive beyond threshold
  const diffMinutes = (now.getTime() - lastActive.getTime()) / (1000 * 60);
  return diffMinutes > maxInactiveMinutes;
}
```

### Batch Heartbeat Processing
Heartbeats are sent in batches with duplicate prevention:

```typescript
async sendHeartbeat(serviceId: string | null = null): Promise<number> {
  // Generate unique batch ID
  const batchId = crypto.randomUUID().substring(0, 8);
  
  // Prevent duplicate services
  if (this.lastHeartbeatServiceId !== serviceId && 
      now - this.lastHeartbeatTime < 15000) {
    // Detected duplicate service, disable this instance
    return 0;
  }
  
  // Send to all active user connections
  for (const [userId, connectionId] of this.userConnectionMap.entries()) {
    // Skip inactive users and process heartbeat
  }
}
```

### Hot Reload Handling
Development mode includes sophisticated hot reload detection:

```typescript
// Track module loads and detect hot reloads
global.__heartbeatService = {
  moduleLoads: 1,
  isHotReload: false,
  lastLoaded: new Date(),
  // ... other properties
};

// Prevent duplicate services during hot reloads
if (process.env.NODE_ENV === "development" && 
    global.__heartbeatService.isHotReload && 
    global.__heartbeatService.isRunning) {
  // Skip initialization during hot reload
  return;
}
```

## Message Types

### Connection Events
- `connected` - Initial connection established
- `connection_replaced` - Old connection being replaced
- `heartbeat` - Keep-alive message with batch ID and service ID

### Application Events
- Bank transactions, deposits, withdrawals
- News article updates
- Volunteer shift notifications
- Shopping cart updates

## Performance Features

### Connection Pooling
- One connection per user (not per tab/window)
- Automatic cleanup of stale connections
- Efficient broadcasting to active users only

### Error Handling
- Graceful connection failures
- Automatic reconnection support
- Statistics tracking for monitoring

### Scalability
- Connection statistics and monitoring
- Batch processing for efficiency
- User activity-based connection management

## Important Files
- `src/app/api/notifications/sse/route.ts` - SSE endpoint
- `src/lib/connectionStore.ts` - Connection management
- `src/lib/heartbeatService.ts` - Heartbeat and cleanup service
- `src/hooks/useSSE.ts` - Client-side SSE hook