"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import { format } from "date-fns";
import { toast } from "react-hot-toast";
import {
  useUserUpcomingShifts,
  useCancelVolunteerShift,
  useVolunteerCheckInStatus,
  useVolunteerCheckIn,
} from "@/hooks/usePublicVolunteer";
import { getCheckInStatus, formatTimeRemaining } from "@/services/volunteerCheckinService";

interface UserVolunteerQuickActionsProps {
  userId: string;
}

export const UserVolunteerQuickActions: React.FC<
  UserVolunteerQuickActionsProps
> = ({ userId }) => {
  // Use the existing hook pattern for fetching user shifts
  const { data, isLoading, isError, error, refetch } =
    useUserUpcomingShifts(userId);
  const cancelShiftMutation = useCancelVolunteerShift();
  const checkInMutation = useVolunteerCheckIn();
  const [timeRemaining, setTimeRemaining] = useState<string>("");
  const [checkInStatus, setCheckInStatus] = useState<any>(null);

  const upcomingShifts = data?.assignments || [];

  // Format date for display
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "EEE, MMM d, yyyy h:mm a");
  };

  // Calculate time remaining until next shift and check-in status
  useEffect(() => {
    if (upcomingShifts.length === 0) return;

    const nextAssignment = upcomingShifts[0];
    const nextShiftDate = new Date(nextAssignment.shift.startTime);

    const updateCountdown = () => {
      const now = new Date();
      const diff = nextShiftDate.getTime() - now.getTime();

      if (diff <= 0) {
        setTimeRemaining("Starting now!");
      } else {
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));
        const hours = Math.floor(
          (diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60),
        );
        const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

        setTimeRemaining(`${days}d ${hours}h ${minutes}m`);
      }

      // Update check-in status
      const currentCheckInStatus = getCheckInStatus(nextShiftDate, nextAssignment.status, now);
      setCheckInStatus(currentCheckInStatus);
    };

    updateCountdown();
    const interval = setInterval(updateCountdown, 60000); // Update every minute

    return () => clearInterval(interval);
  }, [upcomingShifts]);

  // Handle shift cancellation
  const handleCancelShift = async (shiftId: string) => {
    if (confirm("Are you sure you want to cancel this shift?")) {
      try {
        await cancelShiftMutation.mutateAsync(shiftId);
        toast.success("Shift cancelled successfully");
        refetch();
      } catch (error) {
        const errorMessage =
          error instanceof Error
            ? error.message
            : "Failed to cancel shift. Please try again.";
        toast.error(errorMessage);
      }
    }
  };

  // Handle check-in
  const handleCheckIn = async (shiftId: string) => {
    try {
      await checkInMutation.mutateAsync(shiftId);
      toast.success("Successfully checked in!");
      refetch();
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to check in. Please try again.";
      toast.error(errorMessage);
    }
  };

  // Handle retry when there's an error
  const handleRetry = () => {
    refetch();
    toast.success("Retrying to fetch your shifts...");
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="bg-secondary-light rounded-lg shadow-md p-4 mb-6 border border-gray-600 animate-pulse">
        <div className="h-6 bg-secondary rounded w-1/3 mb-4"></div>
        <div className="h-4 bg-secondary rounded w-1/2 mb-2"></div>
        <div className="h-4 bg-secondary rounded w-1/4"></div>
      </div>
    );
  }

  // Show error state
  if (isError) {
    return (
      <div className="bg-secondary-light rounded-lg shadow-md p-4 mb-6 border border-red-600">
        <h2 className="text-lg font-bold text-white mb-3">
          Unable to Load Volunteer Shifts
        </h2>
        <p className="text-sm text-gray-300 mb-3">
          There was an error loading your upcoming volunteer shifts. This might
          be due to a temporary server issue.
        </p>
        <button
          onClick={handleRetry}
          className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark transition-colors"
        >
          Retry
        </button>
      </div>
    );
  }

  // Don't show anything if no shifts
  if (upcomingShifts.length === 0) {
    return null; // Don't show quick actions if no upcoming shifts
  }

  const nextAssignment = upcomingShifts[0];

  return (
    <div className="bg-secondary-light rounded-lg shadow-md p-4 mb-6 border border-gray-600">
      <h2 className="text-lg font-bold text-white mb-3">
        Your Next Volunteer Shift
      </h2>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
        {/* Shift Details */}
        <div className="bg-secondary p-4 rounded-md">
          <h3 className="font-medium text-white mb-1">
            {nextAssignment.shift.title}
          </h3>
          <p className="text-sm text-gray-400">
            {nextAssignment.shift.category.name}
          </p>
          <p className="text-sm text-gray-400">
            {formatDate(nextAssignment.shift.startTime)}
          </p>
        </div>

        {/* Countdown */}
        <div className="bg-secondary p-4 rounded-md">
          <h3 className="font-medium text-white mb-1">Time Remaining</h3>
          <p className="text-xl font-bold text-primary">{timeRemaining}</p>
        </div>

        {/* Quick Links */}
        <div className="bg-secondary p-4 rounded-md">
          <h3 className="font-medium text-white mb-2">Actions</h3>
          <div className="flex flex-col space-y-2">
            <Link
              href="/volunteer"
              className="text-sm text-primary hover:text-primary-dark transition-colors"
            >
              View All My Shifts
            </Link>
            
            {/* Check-in button - shows when check-in window is open */}
            {checkInStatus?.canCheckIn && (
              <button
                className="text-sm bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded-md font-medium transition-colors disabled:opacity-50"
                onClick={() => handleCheckIn(nextAssignment.shift.id)}
                disabled={checkInMutation.isPending}
              >
                {checkInMutation.isPending ? "Checking In..." : "Check In Now"}
              </button>
            )}
            
            {/* Check-in status messages */}
            {checkInStatus?.isCheckedIn && (
              <div className="text-sm text-green-400 font-medium">
                ✓ Checked In
              </div>
            )}
            
            {checkInStatus?.timeUntilCheckIn && (
              <div className="text-sm text-gray-400">
                Check-in available in {formatTimeRemaining(checkInStatus.timeUntilCheckIn)}
              </div>
            )}
            
            {checkInStatus?.status === 'expired' && !checkInStatus.isCheckedIn && (
              <div className="text-sm text-orange-400">
                Check-in window closed
              </div>
            )}
            
            <button
              className="text-sm text-accent hover:text-accent-dark transition-colors text-left"
              onClick={() => handleCancelShift(nextAssignment.shift.id)}
              disabled={cancelShiftMutation.isPending || checkInStatus?.isCheckedIn}
            >
              {cancelShiftMutation.isPending
                ? "Cancelling..."
                : "Cancel This Shift"}
            </button>
          </div>
        </div>
      </div>

      {/* Notification Preferences */}
      <div className="flex items-center justify-between border-t border-gray-600 pt-3">
        <span className="text-sm text-gray-400">Notification Preferences:</span>
        <div className="flex items-center space-x-4">
          <label className="flex items-center text-sm text-white">
            <input
              type="checkbox"
              className="mr-2 h-4 w-4 rounded border-gray-600 text-primary focus:ring-primary"
              checked={nextAssignment.emailNotification}
              onChange={() => {
                /* Toggle email notification - would need API endpoint */
              }}
              disabled
            />
            Email
          </label>
          <label className="flex items-center text-sm text-white">
            <input
              type="checkbox"
              className="mr-2 h-4 w-4 rounded border-gray-600 text-primary focus:ring-primary"
              checked={nextAssignment.websiteNotification}
              onChange={() => {
                /* Toggle website notification - would need API endpoint */
              }}
              disabled
            />
            Website
          </label>
        </div>
      </div>
    </div>
  );
};
