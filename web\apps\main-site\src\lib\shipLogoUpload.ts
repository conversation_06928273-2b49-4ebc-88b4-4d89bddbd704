import { uploadConfig } from "./uploadConfig";

export interface ShipLogoUploadResponse {
  success: boolean;
  logoUrl?: string;
  error?: string;
}

export const validateShipLogo = (file: File): string | null => {
  const config = uploadConfig["ship-logo"];
  
  // Check file size
  if (file.size > config.maxSize) {
    const maxSizeMB = config.maxSize / (1024 * 1024);
    return `Logo file must be less than ${maxSizeMB}MB`;
  }
  
  // Check file type
  if (!config.allowedTypes.includes(file.type)) {
    return `Logo must be a ${config.allowedTypes.map(type => type.split('/')[1].toUpperCase()).join(', ')} image`;
  }
  
  return null;
};

export const uploadShipLogo = async (file: File): Promise<ShipLogoUploadResponse> => {
  try {
    // Validate file first
    const validationError = validateShipLogo(file);
    if (validationError) {
      return {
        success: false,
        error: validationError,
      };
    }

    const formData = new FormData();
    formData.append("file", file);
    formData.append("type", "ship-logo");

    const response = await fetch("/api/uploads/v2", {
      method: "POST",
      headers: {
        Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
      },
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to upload logo");
    }

    const uploadData = await response.json();
    
    return {
      success: true,
      logoUrl: uploadData.originalUrl || uploadData.file?.url || uploadData.url,
    };
  } catch (error) {
    console.error("Ship logo upload error:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to upload logo",
    };
  }
};