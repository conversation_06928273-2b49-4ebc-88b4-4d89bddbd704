import React, { useState } from "react";
import { Transaction } from "../../services/bankService";
import { CSVExportButton } from "../admin/CSVExportButton";
import { TransactionHistoryCard } from "./TransactionHistoryCard";

export interface MemberTransactionHistoryProps {
  transactions: Transaction[];
  userId: string;
}

export const MemberTransactionHistory: React.FC<
  MemberTransactionHistoryProps
> = ({ transactions, userId }) => {
  const [filter, setFilter] = useState<string>("all");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [viewMode, setViewMode] = useState<"card" | "table">("card");

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + " " + date.toLocaleTimeString();
  };

  // Get transaction type label
  const getTypeLabel = (type: string) => {
    switch (type) {
      case "deposit":
        return "Deposit";
      case "withdrawal":
        return "Withdrawal";
      case "transfer":
        return "Transfer";
      case "donation":
        return "Donation";
      default:
        return type.charAt(0).toUpperCase() + type.slice(1);
    }
  };

  // Get status label with appropriate color class
  const getStatusLabel = (status: string) => {
    switch (status) {
      case "completed":
        return <span className="text-success">Completed</span>;
      case "pending":
        return <span className="text-warning">Pending</span>;
      case "rejected":
        return <span className="text-error">Rejected</span>;
      case "cancelled":
        return <span className="text-gray-400">Cancelled</span>;
      default:
        return <span>{status}</span>;
    }
  };

  // Get amount display with appropriate color
  const getAmountDisplay = (transaction: Transaction) => {
    const isIncoming =
      transaction.type === "deposit" ||
      (transaction.type === "transfer" && transaction.recipientId === userId);

    const amountClass = isIncoming ? "text-success" : "text-error";
    const prefix = isIncoming ? "+" : "-";

    return (
      <span className={amountClass}>
        {prefix} NS {transaction.amount.toFixed(0)}
      </span>
    );
  };

  // Filter transactions based on selected filters
  const filteredTransactions = transactions.filter((transaction) => {
    // Filter by type
    if (filter !== "all" && transaction.type !== filter) {
      return false;
    }

    // Filter by status
    if (statusFilter !== "all" && transaction.status !== statusFilter) {
      return false;
    }

    return true;
  });

  // Format transactions for CSV export
  const formattedTransactions = filteredTransactions.map((transaction) => {
    // Determine if this is an incoming or outgoing transaction
    const isIncoming =
      transaction.type === "deposit" ||
      (transaction.type === "transfer" && transaction.recipientId === userId);

    // Format amount with sign
    const formattedAmount = isIncoming
      ? transaction.amount
      : -transaction.amount;

    // Determine sender/recipient info
    let senderRecipient = "";
    if (transaction.type === "transfer") {
      if (transaction.senderId === userId) {
        senderRecipient = `To: ${transaction.recipient?.username || "Unknown"}`;
      } else {
        senderRecipient = `From: ${transaction.sender?.username || "Unknown"}`;
      }
    }

    return {
      ...transaction,
      amount: formattedAmount,
      type: getTypeLabel(transaction.type),
      status: getStatusLabel(transaction.status),
      senderRecipient,
    };
  });

  // Define headers for CSV export with proper typing
  const transactionHeaders: {
    key: keyof (typeof formattedTransactions)[0];
    label: string;
  }[] = [
    { key: "id", label: "Transaction ID" },
    { key: "type", label: "Type" },
    { key: "amount", label: "Amount" },
    { key: "status", label: "Status" },
    { key: "description", label: "Description" },
    { key: "note", label: "Note" },
    { key: "createdAt", label: "Date & Time" },
    { key: "senderRecipient", label: "Sender/Recipient" },
  ];

  return (
    <div className="bg-secondary-light rounded-lg shadow-md p-6 border border-gray-600">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
        <h3 className="text-xl font-bold text-white">Transaction History</h3>
        <div className="flex items-center gap-4">
          {/* View toggle buttons */}
          <div className="flex space-x-2">
            <button
              onClick={() => setViewMode("card")}
              className={`flex items-center px-3 py-1 rounded-md ${
                viewMode === "card"
                  ? "bg-primary text-white"
                  : "bg-secondary text-gray-400 hover:bg-secondary-dark"
              }`}
              aria-label="Card view"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"
                />
              </svg>
              <span className="ml-1 hidden sm:inline">Cards</span>
            </button>
            <button
              onClick={() => setViewMode("table")}
              className={`flex items-center px-3 py-1 rounded-md ${
                viewMode === "table"
                  ? "bg-primary text-white"
                  : "bg-secondary text-gray-400 hover:bg-secondary-dark"
              }`}
              aria-label="Table view"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                />
              </svg>
              <span className="ml-1 hidden sm:inline">Table</span>
            </button>
          </div>
          <CSVExportButton
            data={formattedTransactions}
            filename={`member-transactions-${
              new Date().toISOString().split("T")[0]
            }`}
            headers={transactionHeaders}
            buttonText="Export to CSV"
            variant="outline"
          />
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div>
          <label
            htmlFor="type-filter"
            className="block text-sm font-medium text-gray-400 mb-1"
          >
            Transaction Type
          </label>
          <select
            id="type-filter"
            className="bg-secondary border border-gray-600 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-primary"
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
          >
            <option value="all">All Types</option>
            <option value="deposit">Deposits</option>
            <option value="withdrawal">Withdrawals</option>
            <option value="transfer">Transfers</option>
            <option value="donation">Donations</option>
          </select>
        </div>

        <div>
          <label
            htmlFor="status-filter"
            className="block text-sm font-medium text-gray-400 mb-1"
          >
            Status
          </label>
          <select
            id="status-filter"
            className="bg-secondary border border-gray-600 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-primary"
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
          >
            <option value="all">All Statuses</option>
            <option value="completed">Completed</option>
            <option value="pending">Pending</option>
            <option value="rejected">Rejected</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>
      </div>

      {/* Transactions Display */}
      {filteredTransactions.length === 0 ? (
        <div className="bg-secondary-dark p-4 rounded-lg text-center text-gray-400">
          No transactions found matching the selected filters.
        </div>
      ) : viewMode === "card" ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredTransactions.map((transaction) => (
            <TransactionHistoryCard
              key={transaction.id}
              transaction={transaction}
              userId={userId}
            />
          ))}
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-secondary-dark">
                <th className="p-3 text-left text-white">Date</th>
                <th className="p-3 text-left text-white">Type</th>
                <th className="p-3 text-left text-white">Amount</th>
                <th className="p-3 text-left text-white">Status</th>
                <th className="p-3 text-left text-white">Details</th>
              </tr>
            </thead>
            <tbody>
              {filteredTransactions.map((transaction) => (
                <tr
                  key={transaction.id}
                  className="border-t border-gray-600 hover:bg-secondary-dark"
                >
                  <td className="p-3 text-white">
                    {formatDate(transaction.createdAt)}
                  </td>
                  <td className="p-3 text-white">
                    {getTypeLabel(transaction.type)}
                  </td>
                  <td className="p-3">{getAmountDisplay(transaction)}</td>
                  <td className="p-3">{getStatusLabel(transaction.status)}</td>
                  <td className="p-3 text-white">
                    {transaction.type === "transfer" && (
                      <>
                        {transaction.senderId === userId ? (
                          <span>
                            To: {transaction.recipient?.username || "Unknown"}
                          </span>
                        ) : (
                          <span>
                            From: {transaction.sender?.username || "Unknown"}
                          </span>
                        )}
                      </>
                    )}
                    {transaction.description && (
                      <div className="text-gray-400 text-sm mt-1">
                        {transaction.description}
                      </div>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};
