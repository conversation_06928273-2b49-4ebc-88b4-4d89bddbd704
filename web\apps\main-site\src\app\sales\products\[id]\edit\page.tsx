"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { SalesDashboardLayout } from "@/components/sales/SalesDashboardLayout";
import { ProductForm } from "@/components/sales/ProductForm";
import { useSalesProduct } from "@/hooks/useProducts";
import { Spinner } from "@bank-of-styx/ui";

export default function EditProductPage({
  params,
}: {
  params: { id: string };
}) {
  const { id } = params;
  const { user, isLoading: authLoading } = useAuth();
  const [isAuthorized, setIsAuthorized] = useState(false);
  const router = useRouter();

  // Fetch product data
  const {
    data: productData,
    isLoading: productLoading,
    error,
  } = useSalesProduct(id);

  // Check if user is authorized to access this page
  useEffect(() => {
    if (!authLoading) {
      if (!user || !user.roles?.salesManager) {
        router.push("/");
      } else {
        setIsAuthorized(true);
      }
    }
  }, [user, authLoading, router]);

  if (authLoading || productLoading) {
    return (
      <SalesDashboardLayout>
        <div className="flex justify-center items-center min-h-[50vh]">
          <Spinner size="lg" />
        </div>
      </SalesDashboardLayout>
    );
  }

  if (!isAuthorized) {
    return null; // Don't render anything while redirecting
  }

  if (error || !productData) {
    return (
      <SalesDashboardLayout>
        <div className="p-8 text-center">
          <h2 className="text-xl font-bold mb-4">Product Not Found</h2>
          <p className="mb-4">
            The product you're trying to edit doesn't exist or has been removed.
          </p>
          <button
            onClick={() => router.push("/sales/products")}
            className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark"
          >
            Back to Products
          </button>
        </div>
      </SalesDashboardLayout>
    );
  }

  return (
    <SalesDashboardLayout>
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Edit Product</h1>
      </div>

      <ProductForm initialData={productData} isEditing={true} />
    </SalesDashboardLayout>
  );
}
