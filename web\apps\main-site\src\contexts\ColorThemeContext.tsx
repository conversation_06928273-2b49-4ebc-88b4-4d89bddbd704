"use client";

import React, { createContext, useContext, useEffect, useState } from "react";

// Pirate Gold Dark theme preset (default dark theme)
const darkTheme = {
  "--color-primary": "#FFD700",
  "--color-primary-dark": "#DAA520",
  "--color-primary-light": "#FFDF5E",
  "--color-secondary": "#1C1C1C",
  "--color-secondary-dark": "#0F0F0F",
  "--color-secondary-light": "#2A2A2A",
  "--color-accent": "#C41E3A",
  "--color-accent-dark": "#A01A2F",
  "--color-accent-light": "#E63E58",
  "--color-success": "#2E8B57",
  "--color-warning": "#FF8C00",
  "--color-error": "#B22222",
  "--color-info": "#FFD700",
  "--color-text-primary": "#FFFFFF",
  "--color-text-secondary": "#D4AF37",
  "--color-text-muted": "#B8860B",
  "--color-text-disabled": "rgba(184, 134, 11, 0.75)",
  "--color-border-dark": "#3A3A3A",
  "--color-border-primary": "#FFD700",
  "--color-border-accent": "#C41E3A",
  "--color-border-subtle": "#1C1C1C",
  "--color-bg-page": "#0F0F0F",
  "--color-bg-card": "#2A2A2A",
  "--color-bg-input": "#1C1C1C",
  "--color-bg-hover": "#3A3A3A",
};

// Discord Light theme preset (default light theme)
const lightTheme = {
  "--color-primary": "#4752C4", // Darker blue for better contrast on light backgrounds
  "--color-primary-dark": "#3A43B1", // Even darker for hover states
  "--color-primary-light": "#5865F2", // Original primary color
  "--color-secondary": "#F2F3F5",
  "--color-secondary-dark": "#E3E5E8",
  "--color-secondary-light": "#FFFFFF",
  "--color-accent": "#D03A3D", // Darker red for better contrast
  "--color-accent-dark": "#B22D30",
  "--color-accent-light": "#ED4245",
  "--color-success": "#2E8B57", // Darker green for better contrast
  "--color-warning": "#E67E22", // Adjusted orange
  "--color-error": "#D32F2F", // Darker red for errors
  "--color-info": "#4752C4", // Matches primary
  "--color-text-primary": "#1A1C20", // Darker text for better contrast
  "--color-text-secondary": "#4F5660",
  "--color-text-muted": "#747F8D",
  "--color-text-disabled": "rgba(116, 127, 141, 0.75)",
  "--color-border-dark": "#C7CCD1",
  "--color-border-primary": "#4752C4", // Matches primary
  "--color-border-accent": "#D03A3D", // Matches accent
  "--color-border-subtle": "#E3E5E8",
  "--color-bg-page": "#F5F7FA", // Slightly lighter background
  "--color-bg-card": "#FFFFFF",
  "--color-bg-input": "#F2F3F5",
  "--color-bg-hover": "#E9ECEF", // Slightly darker for better contrast
};

interface ColorThemeContextType {
  isDarkMode: boolean;
  toggleTheme: () => void;
  applyTheme: (theme: Record<string, string>) => void;
}

const ColorThemeContext = createContext<ColorThemeContextType | undefined>(
  undefined,
);

export function ColorThemeProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  // State to track if we're on the client side
  const [mounted, setMounted] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(true); // Default to dark mode

  // Apply a theme
  const applyTheme = (themeValues: Record<string, string>) => {
    // Apply saved color values to CSS variables
    Object.entries(themeValues).forEach(([variable, value]) => {
      document.documentElement.style.setProperty(variable, value as string);
    });

    // Save to localStorage
    localStorage.setItem("bankOfStyxColorTheme", JSON.stringify(themeValues));

    // Update isDarkMode state based on the background color
    const bgColor = themeValues["--color-bg-page"];
    setIsDarkMode(
      bgColor.toLowerCase() !== lightTheme["--color-bg-page"].toLowerCase(),
    );
  };

  // Toggle between light and dark mode
  const toggleTheme = () => {
    const newMode = !isDarkMode;
    setIsDarkMode(newMode);

    // Apply the appropriate theme
    if (newMode) {
      applyTheme(darkTheme);
    } else {
      applyTheme(lightTheme);
    }
  };

  // Load saved color theme from localStorage on client side
  useEffect(() => {
    setMounted(true);

    try {
      const savedTheme = localStorage.getItem("bankOfStyxColorTheme");
      if (savedTheme) {
        const themeValues = JSON.parse(savedTheme);

        // Apply saved color values to CSS variables
        Object.entries(themeValues).forEach(([variable, value]) => {
          document.documentElement.style.setProperty(variable, value as string);
        });

        // Determine if it's dark mode based on the background color
        const bgColor = themeValues["--color-bg-page"];
        // If the background color is closer to dark than light
        setIsDarkMode(
          bgColor.toLowerCase() !== lightTheme["--color-bg-page"].toLowerCase(),
        );
      }
    } catch (error) {
      console.error("Error loading saved color theme:", error);
    }
  }, []);

  return (
    <ColorThemeContext.Provider value={{ isDarkMode, toggleTheme, applyTheme }}>
      {children}
    </ColorThemeContext.Provider>
  );
}

export function useColorTheme() {
  const context = useContext(ColorThemeContext);
  if (context === undefined) {
    throw new Error("useColorTheme must be used within a ColorThemeProvider");
  }
  return context;
}
