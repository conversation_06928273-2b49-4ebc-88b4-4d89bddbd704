"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, FormBuilder } from "@bank-of-styx/ui";
import { useAuth } from "@/contexts/AuthContext";
import { getEvents } from "@/services/eventService";
import Link from "next/link";

interface Event {
  id: string;
  name: string;
  startDate: string;
  endDate: string;
}

interface FormTemplate {
  id: string;
  name: string;
  description?: string;
  formStructure: any[];
  isReusable: boolean;
  createdBy: {
    username: string;
    displayName: string;
  };
  createdAt: string;
  _count: {
    eventForms: number;
  };
}

interface EventForm {
  id: string;
  name: string;
  description?: string;
  formStructure: any[];
  status: "draft" | "active" | "closed";
  submissionDeadline?: string;
  event: {
    id: string;
    name: string;
  };
  template?: {
    id: string;
    name: string;
  };
  createdBy: {
    username: string;
    displayName: string;
  };
  createdAt: string;
  _count: {
    submissions: number;
  };
}

interface FormSubmission {
  id: string;
  submissionData: any;
  status: "draft" | "submitted" | "reviewed" | "approved" | "rejected";
  reviewNotes?: string;
  submittedAt: string;
  reviewedAt?: string;
  form: {
    id: string;
    name: string;
    event: {
      id: string;
      name: string;
    };
  };
  ship: {
    id: string;
    name: string;
    captain: {
      id: string;
      username: string;
      displayName: string;
    };
  };
  submittedBy: {
    id: string;
    username: string;
    displayName: string;
  };
  reviewedBy?: {
    id: string;
    username: string;
    displayName: string;
  };
}

export default function LandStewardDashboardTabs() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<'applications' | 'forms' | 'submissions' | 'ships' | 'volunteer-requirements'>('submissions');
  
  // Forms state
  const [templates, setTemplates] = useState<FormTemplate[]>([]);
  const [eventForms, setEventForms] = useState<EventForm[]>([]);
  const [events, setEvents] = useState<Event[]>([]);
  const [formsLoading, setFormsLoading] = useState(false);
  const [formTab, setFormTab] = useState<'templates' | 'event-forms'>('templates');
  const [showTemplateBuilder, setShowTemplateBuilder] = useState(false);
  const [showEventFormBuilder, setShowEventFormBuilder] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<FormTemplate | null>(null);

  // Submissions state
  const [submissions, setSubmissions] = useState<FormSubmission[]>([]);
  const [submissionsLoading, setSubmissionsLoading] = useState(false);
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [selectedSubmission, setSelectedSubmission] = useState<FormSubmission | null>(null);
  const [showReviewModal, setShowReviewModal] = useState(false);
  const [reviewNotes, setReviewNotes] = useState("");

  // Ships state
  const [ships, setShips] = useState<any[]>([]);
  const [shipsLoading, setShipsLoading] = useState(false);
  const [selectedShip, setSelectedShip] = useState<any>(null);
  const [showShipModal, setShowShipModal] = useState(false);
  const [showChangeCaptainModal, setShowChangeCaptainModal] = useState(false);
  const [showExportModal, setShowExportModal] = useState(false);
  const [shipFilters, setShipFilters] = useState({
    status: 'all',
    search: '',
  });
  const [newCaptainId, setNewCaptainId] = useState('');
  const [exportFields, setExportFields] = useState<string[]>([]);

  // Load data when tabs change
  useEffect(() => {
    if (activeTab === 'forms') {
      loadTemplates();
      loadEventForms();
      loadEvents();
    } else if (activeTab === 'submissions') {
      loadSubmissions();
    } else if (activeTab === 'ships') {
      loadShips();
    }
  }, [activeTab, filterStatus, shipFilters]);

  const loadTemplates = async () => {
    try {
      setFormsLoading(true);
      const response = await fetch("/api/land-steward/forms/templates", {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
        },
      });
      if (response.ok) {
        const data = await response.json();
        setTemplates(Array.isArray(data) ? data : []);
      } else {
        setTemplates([]);
      }
    } catch (error) {
      console.error("Error loading templates:", error);
      setTemplates([]);
    } finally {
      setFormsLoading(false);
    }
  };

  const loadEventForms = async () => {
    try {
      setFormsLoading(true);
      const response = await fetch("/api/land-steward/forms", {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
        },
      });
      if (response.ok) {
        const data = await response.json();
        setEventForms(Array.isArray(data) ? data : []);
      } else {
        setEventForms([]);
      }
    } catch (error) {
      console.error("Error loading event forms:", error);
      setEventForms([]);
    } finally {
      setFormsLoading(false);
    }
  };

  const loadEvents = async () => {
    try {
      const data = await getEvents({ upcoming: true, limit: 100 });
      setEvents(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error("Error loading events:", error);
      setEvents([]);
    }
  };

  const loadSubmissions = async () => {
    try {
      setSubmissionsLoading(true);
      const params = new URLSearchParams();
      if (filterStatus !== "all") {
        params.append("status", filterStatus);
      }
      
      const response = await fetch(`/api/land-steward/submissions?${params.toString()}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
        },
      });
      if (response.ok) {
        const data = await response.json();
        setSubmissions(Array.isArray(data) ? data : []);
      } else {
        setSubmissions([]);
      }
    } catch (error) {
      console.error("Error loading submissions:", error);
      setSubmissions([]);
    } finally {
      setSubmissionsLoading(false);
    }
  };

  const handleSaveTemplate = async (template: any) => {
    try {
      const url = selectedTemplate 
        ? `/api/land-steward/forms/templates/${selectedTemplate.id}`
        : "/api/land-steward/forms/templates";
      
      const method = selectedTemplate ? "PUT" : "POST";
      
      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
        },
        body: JSON.stringify({
          name: template.name,
          description: template.description,
          formStructure: template.fields,
          isReusable: true,
        }),
      });

      if (response.ok) {
        await loadTemplates();
        setShowTemplateBuilder(false);
        setSelectedTemplate(null);
      } else {
        const error = await response.json();
        alert(`Error saving template: ${error.error}`);
      }
    } catch (error) {
      console.error("Error saving template:", error);
      alert("Error saving template");
    }
  };

  const handleDeleteTemplate = async (templateId: string) => {
    if (!confirm("Are you sure you want to delete this template?")) {
      return;
    }

    try {
      const response = await fetch(`/api/land-steward/forms/templates/${templateId}`, {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
        },
      });

      if (response.ok) {
        await loadTemplates();
      } else {
        const error = await response.json();
        alert(`Error deleting template: ${error.error}`);
      }
    } catch (error) {
      console.error("Error deleting template:", error);
      alert("Error deleting template");
    }
  };

  const handleCreateEventForm = async (formData: any) => {
    try {
      const response = await fetch("/api/land-steward/forms", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        await loadEventForms();
        setShowEventFormBuilder(false);
      } else {
        const error = await response.json();
        alert(`Error creating event form: ${error.error}`);
      }
    } catch (error) {
      console.error("Error creating event form:", error);
      alert("Error creating event form");
    }
  };

  const handleUpdateEventFormStatus = async (formId: string, status: string) => {
    try {
      const response = await fetch(`/api/land-steward/forms/${formId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
        },
        body: JSON.stringify({ status }),
      });

      if (response.ok) {
        await loadEventForms();
      } else {
        const error = await response.json();
        alert(`Error updating form status: ${error.error}`);
      }
    } catch (error) {
      console.error("Error updating form status:", error);
      alert("Error updating form status");
    }
  };

  const handleReviewSubmission = async (status: "approved" | "rejected") => {
    if (!selectedSubmission) return;

    try {
      const response = await fetch(`/api/land-steward/submissions/${selectedSubmission.id}/review`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
        },
        body: JSON.stringify({
          status,
          reviewNotes: reviewNotes.trim() || undefined,
        }),
      });

      if (response.ok) {
        await loadSubmissions();
        setShowReviewModal(false);
        setSelectedSubmission(null);
        setReviewNotes("");
      } else {
        const error = await response.json();
        alert(`Error reviewing submission: ${error.error}`);
      }
    } catch (error) {
      console.error("Error reviewing submission:", error);
      alert("Error reviewing submission");
    }
  };

  const loadShips = async () => {
    try {
      setShipsLoading(true);
      const params = new URLSearchParams();
      if (shipFilters.status !== 'all') {
        params.append('status', shipFilters.status);
      }
      if (shipFilters.search) {
        params.append('search', shipFilters.search);
      }
      
      const response = await fetch(`/api/land-steward/ships?${params.toString()}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
        },
      });
      if (response.ok) {
        const data = await response.json();
        setShips(data.ships || []);
      } else {
        setShips([]);
      }
    } catch (error) {
      console.error("Error loading ships:", error);
      setShips([]);
    } finally {
      setShipsLoading(false);
    }
  };

  const loadShipDetails = async (shipId: string) => {
    try {
      const response = await fetch(`/api/land-steward/ships/${shipId}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
        },
      });
      if (response.ok) {
        const data = await response.json();
        setSelectedShip(data);
        setShowShipModal(true);
      } else {
        alert("Failed to load ship details");
      }
    } catch (error) {
      console.error("Error loading ship details:", error);
      alert("Error loading ship details");
    }
  };

  const handleChangeCaptain = async () => {
    if (!selectedShip || !newCaptainId) return;

    try {
      const response = await fetch(`/api/land-steward/ships/${selectedShip.id}/change-captain`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
        },
        body: JSON.stringify({ newCaptainId }),
      });

      if (response.ok) {
        setShowChangeCaptainModal(false);
        setNewCaptainId('');
        loadShipDetails(selectedShip.id); // Refresh ship details
        loadShips(); // Refresh ships list
        alert("Captain changed successfully!");
      } else {
        const error = await response.json();
        alert(`Error changing captain: ${error.error}`);
      }
    } catch (error) {
      console.error("Error changing captain:", error);
      alert("Error changing captain");
    }
  };

  const handleDeleteShip = async (shipId: string, shipName: string) => {
    if (!confirm(`Are you sure you want to permanently delete the ship "${shipName}" and all its data? This action cannot be undone.`)) {
      return;
    }

    try {
      const response = await fetch(`/api/land-steward/ships/${shipId}`, {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
        },
      });

      if (response.ok) {
        loadShips(); // Refresh ships list
        if (selectedShip?.id === shipId) {
          setShowShipModal(false);
          setSelectedShip(null);
        }
        alert("Ship deleted successfully!");
      } else {
        const error = await response.json();
        alert(`Error deleting ship: ${error.error}`);
      }
    } catch (error) {
      console.error("Error deleting ship:", error);
      alert("Error deleting ship");
    }
  };

  const handleReviewDeletionRequest = async (requestId: string, action: 'approve' | 'reject', message?: string) => {
    try {
      const response = await fetch(`/api/land-steward/ships/deletion-requests/${requestId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
        },
        body: JSON.stringify({ action, message }),
      });

      if (response.ok) {
        const data = await response.json();
        loadShips(); // Refresh ships list
        if (selectedShip) {
          loadShipDetails(selectedShip.id); // Refresh ship details
        }
        alert(data.message);
      } else {
        const error = await response.json();
        alert(`Error reviewing deletion request: ${error.error}`);
      }
    } catch (error) {
      console.error("Error reviewing deletion request:", error);
      alert("Error reviewing deletion request");
    }
  };

  const handleExportShips = async () => {
    if (exportFields.length === 0) {
      alert("Please select at least one field to export");
      return;
    }

    try {
      const response = await fetch("/api/land-steward/ships/export", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
        },
        body: JSON.stringify({
          fields: exportFields,
          filters: shipFilters,
        }),
      });

      if (response.ok) {
        const csvContent = await response.text();
        const blob = new Blob([csvContent], { type: "text/csv" });
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = `ships-export-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
        setShowExportModal(false);
        setExportFields([]);
        alert("Ships data exported successfully!");
      } else {
        const error = await response.json();
        alert(`Error exporting ships: ${error.error}`);
      }
    } catch (error) {
      console.error("Error exporting ships:", error);
      alert("Error exporting ships");
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "submitted":
        return "bg-blue-900 text-blue-300";
      case "reviewed":
        return "bg-yellow-900 text-yellow-300";
      case "approved":
        return "bg-green-900 text-green-300";
      case "rejected":
        return "bg-red-900 text-red-300";
      case "draft":
        return "bg-gray-900 text-gray-300";
      default:
        return "bg-gray-900 text-gray-300";
    }
  };

  // Check permissions after all hooks
  if (!user || (!user.roles?.admin && !user.roles?.landSteward)) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-12">
            <h1 className="text-3xl font-bold text-white mb-4">Access Denied</h1>
            <p className="text-gray-400 mb-6">
              You need Land Steward or Admin permissions to access this page.
            </p>
            <Link href="/ships">
              <Button variant="primary">Back to Ships</Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-3xl font-bold text-white mb-2">
                Land Steward Dashboard
              </h1>
              <p className="text-gray-400">
                Manage captain applications, forms, and submissions.
              </p>
            </div>
            <Link href="/ships">
              <Button variant="outline">Back to Ships</Button>
            </Link>
          </div>
        </div>

        {/* Main Tab Navigation */}
        <div className="mb-8">
          <div className="border-b border-gray-600">
            <nav className="-mb-px flex space-x-8">
              {[
                { id: 'submissions', label: 'Form Submissions', icon: '📤' },               
                { id: 'forms', label: 'Form Management', icon: '📝' },
                { id: 'applications', label: 'Ship Applications', icon: '📋' },
                { id: 'ships', label: 'Ships Management', icon: '🚢' },
                { id: 'volunteer-requirements', label: 'Volunteer Requirements', icon: '⏰' }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-primary text-primary'
                      : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                  }`}
                >
                  <span className="mr-2">{tab.icon}</span>
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Applications Tab Content */}
        {activeTab === 'applications' && (
          <div>
            <Card className="p-6 text-center">
              <h3 className="text-xl font-bold mb-4">Captain Applications</h3>
              <p className="text-gray-400 mb-6">
                The captain application management system is available as a dedicated page for better performance and full functionality.
              </p>
              <Link href="/land-steward/applications">
                <Button variant="primary" size="lg">
                  Open Application Management
                </Button>
              </Link>
            </Card>
          </div>
        )}

        {/* Forms Tab Content */}
        {activeTab === 'forms' && (
          <div>
            {/* Forms Sub-tabs */}
            <div className="mb-6">
              <div className="flex space-x-1 bg-secondary rounded-lg p-1">
                {[
                  { key: 'templates', label: 'Templates', count: templates.length },
                  { key: 'event-forms', label: 'Event Forms', count: eventForms.length },
                ].map((tab) => (
                  <button
                    key={tab.key}
                    onClick={() => setFormTab(tab.key as any)}
                    className={`flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                      formTab === tab.key
                        ? 'bg-primary text-white'
                        : 'text-gray-400 hover:text-white hover:bg-secondary-dark'
                    }`}
                  >
                    {tab.label} ({tab.count})
                  </button>
                ))}
              </div>
            </div>

            {/* Templates */}
            {formTab === 'templates' && (
              <div>
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-xl font-bold">Form Templates</h2>
                  <Button
                    variant="primary"
                    onClick={() => {
                      setSelectedTemplate(null);
                      setShowTemplateBuilder(true);
                    }}
                  >
                    Create Template
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {templates.map((template) => (
                    <Card key={template.id} className="p-4">
                      <div className="mb-4">
                        <h3 className="font-bold text-lg mb-2">{template.name}</h3>
                        {template.description && (
                          <p className="text-gray-400 text-sm mb-2">{template.description}</p>
                        )}
                        <div className="text-xs text-gray-400">
                          <p>Created by: {template.createdBy.displayName || template.createdBy.username}</p>
                          <p>Used in: {template._count.eventForms} forms</p>
                          <p>Fields: {template.formStructure.length}</p>
                          {(() => {
                            const volunteerHoursField = template.formStructure.find((field: any) => field.type === 'volunteer_hours');
                            return volunteerHoursField ? (
                              <p className="text-blue-400 font-medium">
                                ⏰ Requires {volunteerHoursField.volunteerHours || '?'} volunteer hours
                              </p>
                            ) : null;
                          })()}
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            setSelectedTemplate(template);
                            setShowTemplateBuilder(true);
                          }}
                        >
                          Edit
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleDeleteTemplate(template.id)}
                          disabled={template._count.eventForms > 0}
                        >
                          Delete
                        </Button>
                      </div>
                    </Card>
                  ))}
                </div>
              </div>
            )}

            {/* Event Forms */}
            {formTab === 'event-forms' && (
              <div>
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-xl font-bold">Event Forms</h2>
                  <Button
                    variant="primary"
                    onClick={() => setShowEventFormBuilder(true)}
                  >
                    Create Event Form
                  </Button>
                </div>

                <div className="space-y-4">
                  {eventForms.map((form) => (
                    <Card key={form.id} className="p-6">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h3 className="font-bold text-lg">{form.name}</h3>
                            <span className={`
                              px-2 py-1 text-xs rounded-full
                              ${form.status === "active" ? "bg-green-900 text-green-300" :
                                form.status === "draft" ? "bg-yellow-900 text-yellow-300" :
                                "bg-gray-900 text-gray-300"
                              }
                            `}>
                              {form.status}
                            </span>
                          </div>
                          {form.description && (
                            <p className="text-gray-400 mb-2">{form.description}</p>
                          )}
                          <div className="text-sm text-gray-400 space-y-1">
                            <p><strong>Event:</strong> {form.event.name}</p>
                            {form.template && (
                              <p><strong>Template:</strong> {form.template.name}</p>
                            )}
                            <p><strong>Fields:</strong> {form.formStructure.length}</p>
                            <p><strong>Submissions:</strong> {form._count.submissions}</p>
                            {form.submissionDeadline && (
                              <p><strong>Deadline:</strong> {new Date(form.submissionDeadline).toLocaleDateString()}</p>
                            )}
                          </div>
                        </div>
                        <div className="flex gap-2">
                          {form.status === "draft" && (
                            <Button
                              size="sm"
                              variant="primary"
                              onClick={() => handleUpdateEventFormStatus(form.id, "active")}
                            >
                              Activate
                            </Button>
                          )}
                          {form.status === "active" && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleUpdateEventFormStatus(form.id, "closed")}
                            >
                              Close
                            </Button>
                          )}
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Submissions Tab Content */}
        {activeTab === 'submissions' && (
          <div>
            {/* Filters */}
            <div className="mb-6">
              <div className="flex gap-4 items-center">
                <label className="text-sm font-medium">Filter by status:</label>
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="px-3 py-2 border border-gray-600 rounded bg-input text-text-primary"
                >
                  <option value="all">All Submissions</option>
                  <option value="submitted">Submitted</option>
                  <option value="reviewed">Reviewed</option>
                  <option value="approved">Approved</option>
                  <option value="rejected">Rejected</option>
                </select>
              </div>
            </div>

            {submissionsLoading ? (
              <div className="text-center py-8">
                <p className="text-gray-400">Loading submissions...</p>
              </div>
            ) : (
              <div className="space-y-4">
                {submissions.length > 0 ? (
                  submissions.map((submission) => (
                    <Card key={submission.id} className="p-6">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h3 className="font-bold text-lg">{submission.form.name}</h3>
                            <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(submission.status)}`}>
                              {submission.status}
                            </span>
                          </div>
                          <div className="text-sm text-gray-400 space-y-1">
                            <p><strong>Event:</strong> {submission.form.event.name}</p>
                            <p><strong>Ship:</strong> {submission.ship.name}</p>
                            <p><strong>Captain:</strong> {submission.ship.captain.displayName || submission.ship.captain.username}</p>
                            <p><strong>Submitted:</strong> {new Date(submission.submittedAt).toLocaleString()}</p>
                            {submission.reviewedAt && (
                              <p><strong>Reviewed:</strong> {new Date(submission.reviewedAt).toLocaleString()}</p>
                            )}
                            {submission.reviewedBy && (
                              <p><strong>Reviewed by:</strong> {submission.reviewedBy.displayName || submission.reviewedBy.username}</p>
                            )}
                            {submission.reviewNotes && (
                              <p><strong>Review Notes:</strong> {submission.reviewNotes}</p>
                            )}
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              setSelectedSubmission(submission);
                              setReviewNotes(submission.reviewNotes || "");
                              setShowReviewModal(true);
                            }}
                          >
                            {submission.status === "submitted" ? "Review" : "View Details"}
                          </Button>
                        </div>
                      </div>
                    </Card>
                  ))
                ) : (
                  <Card className="p-8 text-center">
                    <h3 className="text-xl font-bold mb-2">No Submissions Found</h3>
                    <p className="text-gray-400">
                      {filterStatus === "all" ? 
                        "There are no form submissions to review." :
                        `There are no ${filterStatus} submissions.`
                      }
                    </p>
                  </Card>
                )}
              </div>
            )}
          </div>
        )}

        {/* Volunteer Requirements Tab Content */}
        {activeTab === 'volunteer-requirements' && (
          <div>
            <Card className="p-6 text-center">
              <h3 className="text-xl font-bold mb-4">Ship Volunteer Requirements</h3>
              <p className="text-gray-400 mb-6">
                Monitor and track ship volunteer hour requirements and progress. This system helps ensure ships meet their volunteer commitments.
              </p>
              <Link href="/land-steward/volunteer-requirements">
                <Button variant="primary" size="lg">
                  Open Volunteer Requirements Tracker
                </Button>
              </Link>
            </Card>
          </div>
        )}

        {/* Ships Tab Content */}
        {activeTab === 'ships' && (
          <div>
            {/* Filters and Export */}
            <div className="mb-6 flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
              <div className="flex gap-4 items-center">
                <select
                  value={shipFilters.status}
                  onChange={(e) => setShipFilters(prev => ({ ...prev, status: e.target.value }))}
                  className="px-3 py-2 border border-gray-600 rounded bg-input text-text-primary"
                >
                  <option value="all">All Ships</option>
                  <option value="active">Active</option>
                  <option value="pending_deletion">Pending Deletion</option>
                  <option value="inactive">Inactive</option>
                </select>
                <input
                  type="text"
                  placeholder="Search ships, captains..."
                  value={shipFilters.search}
                  onChange={(e) => setShipFilters(prev => ({ ...prev, search: e.target.value }))}
                  className="px-3 py-2 border border-gray-600 rounded bg-input text-text-primary"
                />
              </div>
              <Button
                variant="outline"
                onClick={() => setShowExportModal(true)}
              >
                Export Ships
              </Button>
            </div>

            {shipsLoading ? (
              <div className="text-center py-8">
                <p className="text-gray-400">Loading ships...</p>
              </div>
            ) : (
              <div className="space-y-4">
                {ships.length > 0 ? (
                  ships.map((ship) => (
                    <Card key={ship.id} className="p-6">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h3 className="font-bold text-lg">{ship.name}</h3>
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              ship.status === "active" ? "bg-green-900 text-green-300" :
                              ship.status === "pending_deletion" ? "bg-yellow-900 text-yellow-300" :
                              ship.status === "inactive" ? "bg-gray-900 text-gray-300" :
                              "bg-red-900 text-red-300"
                            }`}>
                              {ship.status}
                            </span>
                            {ship.hasPendingDeletion && (
                              <span className="px-2 py-1 text-xs rounded-full bg-red-900 text-red-300">
                                Deletion Requested
                              </span>
                            )}
                          </div>
                          {ship.slogan && (
                            <p className="text-primary italic text-sm mb-2">"{ship.slogan}"</p>
                          )}
                          <p className="text-gray-400 mb-2">{ship.description}</p>
                          <div className="text-sm text-gray-400 space-y-1">
                            <p><strong>Captain:</strong> {ship.captain.displayName || ship.captain.username}</p>
                            <p><strong>Members:</strong> {ship.memberCount}</p>
                            <p><strong>Created:</strong> {new Date(ship.createdAt).toLocaleDateString()}</p>
                            {ship.tags && ship.tags.length > 0 && (
                              <p><strong>Tags:</strong> {ship.tags.join(', ')}</p>
                            )}
                            {ship.pendingDeletionRequest && (
                              <div className="mt-2 p-2 border border-yellow-600 rounded bg-yellow-900/10">
                                <p className="text-yellow-300 text-sm">
                                  <strong>Deletion Reason:</strong> {ship.pendingDeletionRequest.reason || 'No reason provided'}
                                </p>
                                <p className="text-yellow-300 text-sm">
                                  <strong>Requested:</strong> {new Date(ship.pendingDeletionRequest.createdAt).toLocaleDateString()}
                                </p>
                              </div>
                            )}
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => loadShipDetails(ship.id)}
                          >
                            View Details
                          </Button>
                          {ship.pendingDeletionRequest && (
                            <>
                              <Button
                                size="sm"
                                variant="primary"
                                onClick={() => handleReviewDeletionRequest(ship.pendingDeletionRequest.id, 'approve')}
                              >
                                Approve Deletion
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleReviewDeletionRequest(ship.pendingDeletionRequest.id, 'reject', 'Deletion request rejected')}
                              >
                                Reject Deletion
                              </Button>
                            </>
                          )}
                        </div>
                      </div>
                    </Card>
                  ))
                ) : (
                  <Card className="p-8 text-center">
                    <h3 className="text-xl font-bold mb-2">No Ships Found</h3>
                    <p className="text-gray-400">
                      {shipFilters.status === 'all' && !shipFilters.search ? 
                        "There are no ships in the system." :
                        "No ships match the current filters."
                      }
                    </p>
                  </Card>
                )}
              </div>
            )}
          </div>
        )}

        {/* Template Builder Modal */}
        {showTemplateBuilder && (
          <Modal
            isOpen={showTemplateBuilder}
            onClose={() => {
              setShowTemplateBuilder(false);
              setSelectedTemplate(null);
            }}
            title={selectedTemplate ? "Edit Template" : "Create Template"}
            size="full"
          >
            <div style={{ height: "80vh" }}>
              <FormBuilder
                initialTemplate={selectedTemplate ? {
                  id: selectedTemplate.id,
                  name: selectedTemplate.name,
                  description: selectedTemplate.description,
                  fields: selectedTemplate.formStructure,
                } : undefined}
                onSave={handleSaveTemplate}
                onCancel={() => {
                  setShowTemplateBuilder(false);
                  setSelectedTemplate(null);
                }}
                isEditing={!!selectedTemplate}
              />
            </div>
          </Modal>
        )}

        {/* Event Form Creation Modal */}
        {showEventFormBuilder && (
          <Modal
            isOpen={showEventFormBuilder}
            onClose={() => setShowEventFormBuilder(false)}
            title="Create Event Form"
            size="lg"
          >
            <EventFormCreator
              templates={templates}
              events={events}
              onSave={handleCreateEventForm}
              onCancel={() => setShowEventFormBuilder(false)}
            />
          </Modal>
        )}

        {/* Review Modal */}
        {showReviewModal && selectedSubmission && (
          <Modal
            isOpen={showReviewModal}
            onClose={() => {
              setShowReviewModal(false);
              setSelectedSubmission(null);
              setReviewNotes("");
            }}
            title={`Review Submission - ${selectedSubmission.form.name}`}
            size="lg"
          >
            <div className="space-y-4">
              {/* Submission Details */}
              <div className="border-b border-gray-600 pb-4">
                <h4 className="font-medium mb-2">Submission Details</h4>
                <div className="text-sm space-y-1">
                  <p><strong>Ship:</strong> {selectedSubmission.ship.name}</p>
                  <p><strong>Captain:</strong> {selectedSubmission.ship.captain.displayName}</p>
                  <p><strong>Submitted:</strong> {new Date(selectedSubmission.submittedAt).toLocaleString()}</p>
                </div>
              </div>

              {/* Submission Data */}
              <div className="border-b border-gray-600 pb-4">
                <h4 className="font-medium mb-2">Form Responses</h4>
                <div className="bg-secondary p-4 rounded max-h-60 overflow-y-auto">
                  <pre className="text-sm whitespace-pre-wrap">
                    {JSON.stringify(selectedSubmission.submissionData, null, 2)}
                  </pre>
                </div>
              </div>

              {/* Review Notes */}
              <div>
                <label className="block text-sm font-medium mb-2">Review Notes</label>
                <textarea
                  value={reviewNotes}
                  onChange={(e) => setReviewNotes(e.target.value)}
                  className="w-full p-3 border border-gray-600 rounded bg-input text-text-primary"
                  placeholder="Add notes about this submission..."
                  rows={4}
                />
              </div>

              {/* Action Buttons */}
              <div className="flex gap-4 pt-4">
                {selectedSubmission.status === "submitted" && (
                  <>
                    <Button
                      variant="primary"
                      onClick={() => handleReviewSubmission("approved")}
                    >
                      Approve
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => handleReviewSubmission("rejected")}
                    >
                      Reject
                    </Button>
                  </>
                )}
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowReviewModal(false);
                    setSelectedSubmission(null);
                    setReviewNotes("");
                  }}
                >
                  Close
                </Button>
              </div>
            </div>
          </Modal>
        )}

        {/* Ship Details Modal */}
        {showShipModal && selectedShip && (
          <Modal
            isOpen={showShipModal}
            onClose={() => {
              setShowShipModal(false);
              setSelectedShip(null);
            }}
            title={`Ship Details - ${selectedShip.name}`}
            size="xl"
          >
            <div className="space-y-6">
              {/* Ship Info */}
              <div className="border-b border-gray-600 pb-4">
                <div className="flex items-start gap-4">
                  {selectedShip.logo && (
                    <img
                      src={selectedShip.logo}
                      alt={`${selectedShip.name} logo`}
                      className="w-16 h-16 object-contain rounded"
                    />
                  )}
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-xl font-bold">{selectedShip.name}</h3>
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        selectedShip.status === "active" ? "bg-green-900 text-green-300" :
                        selectedShip.status === "pending_deletion" ? "bg-yellow-900 text-yellow-300" :
                        "bg-gray-900 text-gray-300"
                      }`}>
                        {selectedShip.status}
                      </span>
                    </div>
                    {selectedShip.slogan && (
                      <p className="text-primary italic mb-2">"{selectedShip.slogan}"</p>
                    )}
                    <p className="text-gray-300 mb-2">{selectedShip.description}</p>
                    <div className="text-sm text-gray-400">
                      <p><strong>Created:</strong> {new Date(selectedShip.createdAt).toLocaleDateString()}</p>
                      <p><strong>Updated:</strong> {new Date(selectedShip.updatedAt).toLocaleDateString()}</p>
                      {selectedShip.tags && selectedShip.tags.length > 0 && (
                        <p><strong>Tags:</strong> {selectedShip.tags.join(', ')}</p>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Captain Info & Actions */}
              <div className="border-b border-gray-600 pb-4">
                <div className="flex justify-between items-start">
                  <div>
                    <h4 className="font-medium mb-2">Captain</h4>
                    <div className="flex items-center gap-2">
                      <img
                        src={selectedShip.captain.avatar || "/images/avatars/default.png"}
                        alt={selectedShip.captain.displayName}
                        className="w-8 h-8 rounded-full"
                      />
                      <div>
                        <p className="text-white">{selectedShip.captain.displayName}</p>
                        <p className="text-gray-400 text-sm">@{selectedShip.captain.username}</p>
                      </div>
                    </div>
                  </div>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setShowChangeCaptainModal(true)}
                  >
                    Change Captain
                  </Button>
                </div>
              </div>

              {/* Members */}
              <div className="border-b border-gray-600 pb-4">
                <h4 className="font-medium mb-2">Members ({selectedShip.members.length})</h4>
                <div className="max-h-40 overflow-y-auto space-y-2">
                  {selectedShip.members.map((member: any) => (
                    <div key={member.id} className="flex items-center justify-between p-2 bg-secondary rounded">
                      <div className="flex items-center gap-2">
                        <img
                          src={member.user.avatar || "/images/avatars/default.png"}
                          alt={member.user.displayName}
                          className="w-6 h-6 rounded-full"
                        />
                        <span className="text-sm">{member.user.displayName}</span>
                      </div>
                      <span className="text-xs text-gray-400">
                        {member.customRole?.name || member.role}
                      </span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Deletion Requests */}
              {selectedShip.deletionRequests && selectedShip.deletionRequests.length > 0 && (
                <div className="border-b border-gray-600 pb-4">
                  <h4 className="font-medium mb-2">Deletion Requests</h4>
                  <div className="space-y-2">
                    {selectedShip.deletionRequests.map((request: any) => (
                      <div key={request.id} className="p-3 border border-gray-600 rounded">
                        <div className="flex items-center justify-between mb-2">
                          <span className={`px-2 py-1 text-xs rounded-full ${
                            request.status === 'pending' ? 'bg-yellow-900 text-yellow-300' :
                            request.status === 'approved' ? 'bg-green-900 text-green-300' :
                            'bg-red-900 text-red-300'
                          }`}>
                            {request.status}
                          </span>
                          <span className="text-xs text-gray-400">
                            {new Date(request.createdAt).toLocaleDateString()}
                          </span>
                        </div>
                        {request.reason && (
                          <p className="text-sm text-gray-300 mb-2">
                            <strong>Reason:</strong> {request.reason}
                          </p>
                        )}
                        {request.message && (
                          <p className="text-sm text-gray-300 mb-2">
                            <strong>Admin Response:</strong> {request.message}
                          </p>
                        )}
                        {request.status === 'pending' && (
                          <div className="flex gap-2 mt-2">
                            <Button
                              size="sm"
                              variant="primary"
                              onClick={() => handleReviewDeletionRequest(request.id, 'approve')}
                            >
                              Approve
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleReviewDeletionRequest(request.id, 'reject', 'Deletion request rejected')}
                            >
                              Reject
                            </Button>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Actions */}
              <div className="flex gap-4 pt-4">
                <Button
                  variant="outline"
                  onClick={() => handleDeleteShip(selectedShip.id, selectedShip.name)}
                  className="border-red-600 text-red-400 hover:bg-red-900/20"
                >
                  Delete Ship Permanently
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowShipModal(false);
                    setSelectedShip(null);
                  }}
                >
                  Close
                </Button>
              </div>
            </div>
          </Modal>
        )}

        {/* Change Captain Modal */}
        {showChangeCaptainModal && selectedShip && (
          <Modal
            isOpen={showChangeCaptainModal}
            onClose={() => {
              setShowChangeCaptainModal(false);
              setNewCaptainId('');
            }}
            title="Change Ship Captain"
            size="md"
          >
            <div className="space-y-4">
              <div className="border border-yellow-600 rounded-lg p-3 bg-yellow-900/10">
                <p className="text-yellow-300 text-sm">
                  Select a new captain from the ship's active members. The current captain will remain as a member.
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Select New Captain
                </label>
                <select
                  value={newCaptainId}
                  onChange={(e) => setNewCaptainId(e.target.value)}
                  className="w-full p-3 border border-gray-600 rounded bg-background text-white"
                >
                  <option value="">Choose a member...</option>
                  {selectedShip.members
                    .filter((member: any) => member.status === 'active' && member.user.id !== selectedShip.captain.id)
                    .map((member: any) => (
                      <option key={member.user.id} value={member.user.id}>
                        {member.user.displayName} (@{member.user.username})
                      </option>
                    ))}
                </select>
              </div>

              <div className="flex gap-4 pt-4">
                <Button
                  variant="primary"
                  onClick={handleChangeCaptain}
                  disabled={!newCaptainId}
                >
                  Change Captain
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowChangeCaptainModal(false);
                    setNewCaptainId('');
                  }}
                >
                  Cancel
                </Button>
              </div>
            </div>
          </Modal>
        )}

        {/* Export Ships Modal */}
        {showExportModal && (
          <Modal
            isOpen={showExportModal}
            onClose={() => {
              setShowExportModal(false);
              setExportFields([]);
            }}
            title="Export Ships Data"
            size="md"
          >
            <div className="space-y-4">
              <p className="text-gray-300 text-sm">
                Select the fields you want to include in the export. Current filters will be applied.
              </p>

              <div className="grid grid-cols-2 gap-2">
                {[
                  { key: 'id', label: 'Ship ID' },
                  { key: 'name', label: 'Ship Name' },
                  { key: 'description', label: 'Description' },
                  { key: 'slogan', label: 'Slogan' },
                  { key: 'status', label: 'Status' },
                  { key: 'captainName', label: 'Captain Name' },
                  { key: 'captainUsername', label: 'Captain Username' },
                  { key: 'captainEmail', label: 'Captain Email' },
                  { key: 'memberCount', label: 'Member Count' },
                  { key: 'tags', label: 'Tags' },
                  { key: 'createdAt', label: 'Created Date' },
                  { key: 'updatedAt', label: 'Updated Date' },
                  { key: 'hasPendingDeletion', label: 'Has Pending Deletion' },
                  { key: 'deletionReason', label: 'Deletion Reason' },
                ].map((field) => (
                  <label key={field.key} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={exportFields.includes(field.key)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setExportFields(prev => [...prev, field.key]);
                        } else {
                          setExportFields(prev => prev.filter(f => f !== field.key));
                        }
                      }}
                      className="rounded border-gray-600"
                    />
                    <span className="text-sm text-gray-300">{field.label}</span>
                  </label>
                ))}
              </div>

              <div className="flex gap-4 pt-4">
                <Button
                  variant="primary"
                  onClick={handleExportShips}
                  disabled={exportFields.length === 0}
                >
                  Export CSV
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowExportModal(false);
                    setExportFields([]);
                  }}
                >
                  Cancel
                </Button>
              </div>
            </div>
          </Modal>
        )}
      </div>
    </div>
  );
}

// Event Form Creator Component
const EventFormCreator: React.FC<{
  templates: FormTemplate[];
  events: Event[];
  onSave: (formData: any) => void;
  onCancel: () => void;
}> = ({ templates, events, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    eventId: "",
    templateId: "",
    name: "",
    description: "",
    submissionDeadline: "",
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.eventId || !formData.name) {
      alert("Event and name are required");
      return;
    }

    const submitData: any = {
      eventId: formData.eventId,
      name: formData.name,
      description: formData.description || undefined,
      submissionDeadline: formData.submissionDeadline || undefined,
    };

    if (formData.templateId) {
      submitData.templateId = formData.templateId;
    }

    onSave(submitData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label className="block text-sm font-medium mb-1">Event *</label>
        <select
          value={formData.eventId}
          onChange={(e) => setFormData(prev => ({ ...prev, eventId: e.target.value }))}
          className="w-full p-2 border border-gray-600 rounded bg-input text-text-primary"
          required
        >
          <option value="">Select an event...</option>
          {events.map((event) => (
            <option key={event.id} value={event.id}>
              {event.name}
            </option>
          ))}
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium mb-1">Template (Optional)</label>
        <select
          value={formData.templateId}
          onChange={(e) => setFormData(prev => ({ ...prev, templateId: e.target.value }))}
          className="w-full p-2 border border-gray-600 rounded bg-input text-text-primary"
        >
          <option value="">No template (create custom form)</option>
          {templates.map((template) => (
            <option key={template.id} value={template.id}>
              {template.name}
            </option>
          ))}
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium mb-1">Form Name *</label>
        <input
          type="text"
          value={formData.name}
          onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
          className="w-full p-2 border border-gray-600 rounded bg-input text-text-primary"
          placeholder="Enter form name"
          required
        />
      </div>

      <div>
        <label className="block text-sm font-medium mb-1">Description</label>
        <textarea
          value={formData.description}
          onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
          className="w-full p-2 border border-gray-600 rounded bg-input text-text-primary"
          placeholder="Optional form description"
          rows={3}
        />
      </div>

      <div>
        <label className="block text-sm font-medium mb-1">Submission Deadline</label>
        <input
          type="datetime-local"
          value={formData.submissionDeadline}
          onChange={(e) => setFormData(prev => ({ ...prev, submissionDeadline: e.target.value }))}
          className="w-full p-2 border border-gray-600 rounded bg-input text-text-primary"
        />
      </div>

      <div className="flex gap-4 pt-4">
        <Button type="submit" variant="primary">
          Create Form
        </Button>
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
      </div>
    </form>
  );
};