import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser, userHasRole } from "@/lib/auth";
import { TicketStatus } from "@prisma/client";

export const dynamic = 'force-dynamic';

interface Params {
  params: {
    id: string;
    codeId: string;
  };
}

// PUT /api/sales/products/[id]/redemption-codes/[codeId] - Update a redemption code
export async function PUT(req: NextRequest, { params }: Params) {
  try {
    const { id: productId, codeId } = params;

    // Check if user is authenticated and has sales manager role
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 },
      );
    }

    const hasSalesRole = await userHasRole(req, "salesManager");
    if (!hasSalesRole) {
      return NextResponse.json(
        { error: "Unauthorized - Sales Manager role required" },
        { status: 403 },
      );
    }

    // Get request body
    const { isActive } = await req.json();

    // Check if redemption code exists and belongs to the product
    const existingCode = await prisma.redemptionCode.findUnique({
      where: { id: codeId },
    });

    if (!existingCode) {
      return NextResponse.json(
        { error: "Redemption code not found" },
        { status: 404 },
      );
    }

    if (existingCode.productId !== productId) {
      return NextResponse.json(
        { error: "Redemption code does not belong to this product" },
        { status: 400 },
      );
    }

    // Update the redemption code
    const updatedCode = await prisma.redemptionCode.update({
      where: { id: codeId },
      data: {
        isActive: isActive !== undefined ? isActive : existingCode.isActive,
      },
    });

    return NextResponse.json({ redemptionCode: updatedCode });
  } catch (error) {
    console.error("Error updating redemption code:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}

// DELETE /api/sales/products/[id]/redemption-codes/[codeId] - Delete a redemption code
export async function DELETE(req: NextRequest, { params }: Params) {
  try {
    const { id: productId, codeId } = params;

    // Check if user is authenticated and has sales manager role
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 },
      );
    }

    const hasSalesRole = await userHasRole(req, "salesManager");
    if (!hasSalesRole) {
      return NextResponse.json(
        { error: "Unauthorized - Sales Manager role required" },
        { status: 403 },
      );
    }

    // Check if redemption code exists and belongs to the product
    const existingCode = await prisma.redemptionCode.findUnique({
      where: { id: codeId },
    });

    if (!existingCode) {
      return NextResponse.json(
        { error: "Redemption code not found" },
        { status: 404 },
      );
    }

    if (existingCode.productId !== productId) {
      return NextResponse.json(
        { error: "Redemption code does not belong to this product" },
        { status: 400 },
      );
    }

    // Delete the redemption code and corresponding HELD ticket
    await prisma.$transaction(async (tx) => {
      // Delete the redemption code
      await tx.redemptionCode.delete({
        where: { id: codeId },
      });

      // Find and delete a corresponding HELD ticket
      const heldTicket = await tx.ticket.findFirst({
        where: {
          productId,
          status: TicketStatus.HELD,
        },
      });

      if (heldTicket) {
        await tx.ticket.delete({
          where: { id: heldTicket.id },
        });
      }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting redemption code:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}