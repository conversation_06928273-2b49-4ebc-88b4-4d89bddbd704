"use client";

import React from "react";
import { CategoryCard } from "./CategoryCard";
import { VolunteerCategory } from "@/hooks/useVolunteerCategories";

interface CategoryListProps {
  categories: VolunteerCategory[];
  isLoading: boolean;
  error: Error | null;
  onEdit: (category: VolunteerCategory) => void;
  onDelete: (category: VolunteerCategory) => void;
}

export const CategoryList: React.FC<CategoryListProps> = ({
  categories,
  isLoading,
  error,
  onEdit,
  onDelete,
}) => {
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-40">
        <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-accent bg-opacity-20 border border-accent rounded-md p-4 text-accent">
        <p>Error loading categories: {error.message}</p>
      </div>
    );
  }

  if (categories.length === 0) {
    return (
      <div className="bg-secondary-light rounded-lg shadow-md p-6 border border-gray-600 text-center">
        <p className="text-gray-400 mb-4">
          No categories found for this event.
        </p>
        <p className="text-white">Create a new category to get started.</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {categories.map((category) => (
        <CategoryCard
          key={category.id}
          category={category}
          onEdit={onEdit}
          onDelete={onDelete}
        />
      ))}
    </div>
  );
};
