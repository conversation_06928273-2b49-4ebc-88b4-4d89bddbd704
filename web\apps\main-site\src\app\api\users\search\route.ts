import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser, userHasRole, verifyToken } from "@/lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
export async function GET(req: NextRequest) {
  try {
    // Get query parameters
    const { searchParams } = new URL(req.url);
    const query = searchParams.get("q");
    const limit = parseInt(searchParams.get("limit") || "10");
    const isVolunteerSearch = searchParams.get("volunteer") === "true";

    // Different authentication methods based on the type of search
    if (isVolunteerSearch) {
      // For volunteer coordinator searches, use session-based auth
      const user = await getCurrentUser(req);
      if (!user) {
        return NextResponse.json(
          { error: "Unauthorized - Authentication required" },
          { status: 401 },
        );
      }

      const hasCoordinatorRole = await userHasRole(req, "volunteerCoordinator");
      if (!hasCoordinatorRole) {
        return NextResponse.json(
          { error: "Unauthorized - Coordinator role required" },
          { status: 403 },
        );
      }
    } else {
      // For regular searches, use token-based auth
      const authHeader = req.headers.get("authorization");

      if (!authHeader || !authHeader.startsWith("Bearer ")) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
      }

      // Extract token
      const token = authHeader.split(" ")[1];

      // Verify token
      const decoded = verifyToken(token);

      if (!decoded || typeof decoded !== "object" || !decoded.id) {
        return NextResponse.json({ error: "Invalid token" }, { status: 401 });
      }
    }

    // Validate query
    if (!query || query.length < 2) {
      return NextResponse.json(
        { error: "Search query must be at least 2 characters" },
        { status: 400 },
      );
    }

    // Different search logic based on the type of search
    if (isVolunteerSearch) {
      // For volunteer coordinator searches, use case-insensitive search
      const lowercaseQuery = query.toLowerCase();

      // Fetch users with basic filtering
      const users = await prisma.user.findMany({
        where: {
          OR: [
            { displayName: { contains: lowercaseQuery } },
            { email: { contains: lowercaseQuery } },
          ],
        },
        select: {
          id: true,
          displayName: true,
          email: true,
          avatar: true,
        },
        take: limit * 2, // Fetch more to allow for manual filtering
      });

      // Apply manual case-insensitive filtering
      const filteredUsers = users
        .filter(
          (user) =>
            (user.displayName &&
              user.displayName.toLowerCase().includes(lowercaseQuery)) ||
            (user.email && user.email.toLowerCase().includes(lowercaseQuery)),
        )
        .slice(0, limit); // Apply the limit after filtering

      // Map the database fields to the expected response format
      const mappedUsers = filteredUsers.map((user) => ({
        id: user.id,
        name: user.displayName || "",
        email: user.email,
        image: user.avatar,
      }));

      return NextResponse.json({ users: mappedUsers });
    } else {
      // For regular searches, use the existing logic
      const lowercaseQuery = query.toLowerCase();
      const currentUser = await getCurrentUser(req);

      const users = await prisma.user.findMany({
        where: {
          OR: [
            { username: { contains: lowercaseQuery } },
            { displayName: { contains: lowercaseQuery } },
            { email: { contains: lowercaseQuery } },
          ],
          NOT: {
            id: currentUser?.id, // Exclude the current user
          },
        },
        select: {
          id: true,
          username: true,
          displayName: true,
          avatar: true,
          email: true, // Include email in the results
          // Check if user is already a member of any ship
          shipMemberships: {
            where: {
              status: 'active'
            },
            select: {
              ship: {
                select: {
                  id: true,
                  name: true
                }
              }
            }
          }
        },
        take: limit,
      });

      // Manual filtering for case-insensitive search if the database doesn't support it
      const filteredUsers = users.filter(
        (user) =>
          user.username.toLowerCase().includes(lowercaseQuery) ||
          (user.displayName &&
            user.displayName.toLowerCase().includes(lowercaseQuery)) ||
          (user.email && user.email.toLowerCase().includes(lowercaseQuery)),
      );

      // Transform the results to include ship membership info for ship invitation search
      const transformedUsers = filteredUsers.map(user => ({
        ...user,
        currentShip: user.shipMemberships && user.shipMemberships.length > 0 ? user.shipMemberships[0].ship : null,
        isAvailable: !user.shipMemberships || user.shipMemberships.length === 0
      }));

      return NextResponse.json(transformedUsers);
    }
  } catch (error) {
    console.error("Error searching users:", error);
    return NextResponse.json(
      { error: "An error occurred while searching users" },
      { status: 500 },
    );
  }
}
