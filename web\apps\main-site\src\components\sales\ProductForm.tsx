"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Button, Input, Textarea, Checkbox, Card } from "@bank-of-styx/ui";
import { Select } from "@/components/shared";
import { useCreateProduct, useUpdateProduct } from "@/hooks/useProducts";
import { useProductCategories } from "@/hooks/useProductCategories";
import { Product } from "@/services/productService";
import { toast } from "react-hot-toast";

// Add this import to fetch events
import { useQuery } from "@tanstack/react-query";
import { getEvents } from "@/services/eventService";

interface ProductFormProps {
  initialData?: Product;
  isEditing?: boolean;
  onSuccess?: () => void;
}

interface ProductFormData {
  name: string;
  description: string;
  shortDescription: string;
  price: string;
  image: string;
  isActive: boolean;
  affectsCapacity: boolean;
  inventory: string;
  categoryId: string;
  eventId: string;
  isFree: boolean;
}

export const ProductForm: React.FC<ProductFormProps> = ({
  initialData,
  isEditing = false,
  onSuccess,
}) => {
  const router = useRouter();
  const { data: categoriesData, isLoading: categoriesLoading } =
    useProductCategories();
  const createProduct = useCreateProduct();
  const updateProduct = useUpdateProduct();

  // Fetch events
  const { data: eventsData } = useQuery({
    queryKey: ["events"],
    queryFn: () => getEvents(),
  });

  // Form state
  const [formData, setFormData] = useState<ProductFormData>({
    name: "",
    description: "",
    shortDescription: "",
    price: "",
    image: "",
    isActive: true,
    affectsCapacity: true,
    inventory: "",
    categoryId: "",
    eventId: "",
    isFree: false,
  });

  // Form validation
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Initialize form with data if editing
  useEffect(() => {
    if (isEditing && initialData) {
      setFormData({
        name: initialData.name,
        description: initialData.description || "",
        shortDescription: initialData.shortDescription || "",
        price: initialData.price.toString(),
        image: initialData.image || "",
        isActive: initialData.isActive,
        affectsCapacity: initialData.affectsCapacity,
        inventory: initialData.inventory?.toString() || "",
        categoryId: initialData.categoryId,
        eventId: initialData.eventId || "",
        isFree: (initialData as any).isFree || false,
      });
    }
  }, [isEditing, initialData]);

  // Handle form input changes
  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >,
  ) => {
    const { name, value, type } = e.target;

    if (type === "checkbox") {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData((prev) => ({ ...prev, [name]: checked }));
    } else {
      setFormData((prev) => ({ ...prev, [name]: value }));
    }

    // Clear error when field is edited
    if (errors[name]) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = "Product name is required";
    }

    if (!formData.price.trim()) {
      newErrors.price = "Price is required";
    } else if (
      isNaN(parseFloat(formData.price)) ||
      parseFloat(formData.price) < 0
    ) {
      newErrors.price = "Price must be a valid number";
    }

    if (!formData.categoryId) {
      newErrors.categoryId = "Category is required";
    }

    if (formData.inventory) {
      try {
        const inventoryValue = parseInt(formData.inventory);
        if (isNaN(inventoryValue) || inventoryValue < 0) {
          newErrors.inventory = "Inventory must be a valid non-negative number";
        }
      } catch (error) {
        newErrors.inventory = "Inventory must be a valid number";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      if (isEditing && initialData) {
        await updateProduct.mutateAsync({
          id: initialData.id,
          data: {
            name: formData.name,
            description: formData.description,
            shortDescription: formData.shortDescription,
            price: parseFloat(formData.price),
            image: formData.image,
            isActive: formData.isActive,
            affectsCapacity: formData.affectsCapacity,
            inventory: formData.inventory ? parseInt(formData.inventory) : null,
            categoryId: formData.categoryId,
            eventId: formData.eventId || null,
            isFree: formData.isFree,
          },
        });
        toast.success("Product updated successfully");
      } else {
        await createProduct.mutateAsync({
          name: formData.name,
          description: formData.description,
          shortDescription: formData.shortDescription,
          price: parseFloat(formData.price),
          image: formData.image,
          isActive: formData.isActive,
          affectsCapacity: formData.affectsCapacity,
          inventory: formData.inventory ? parseInt(formData.inventory) : null,
          categoryId: formData.categoryId,
          eventId: formData.eventId || null,
          isFree: formData.isFree,
        });
        toast.success("Product created successfully");
      }

      if (onSuccess) {
        onSuccess();
      } else {
        router.push("/sales/products");
      }
    } catch (error) {
      console.error("Error saving product:", error);

      // Extract error message if available
      let errorMessage = "Failed to save product";
      if (error && typeof error === "object") {
        const err = error as any;
        if (err.response?.data?.error) {
          errorMessage = err.response.data.error;
        } else if (err.message) {
          errorMessage = err.message;
        }
      }

      toast.error(errorMessage);
    }
  };

  return (
    <Card
      title={isEditing ? "Edit Product" : "Create New Product"}
      className="mb-6"
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Input
              label="Product Name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              error={errors.name}
              helperText={errors.name}
              fullWidth
            />
          </div>
          <div>
            <Input
              label="Price ($)"
              name="price"
              type="number"
              step="0.01"
              min="0"
              value={formData.price}
              onChange={handleChange}
              error={errors.price}
              helperText={errors.price}
              fullWidth
            />
          </div>
        </div>

        {/* Category and Event */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Select
              label="Category"
              name="categoryId"
              value={formData.categoryId}
              onChange={handleChange}
              error={!!errors.categoryId}
              helperText={errors.categoryId}
              fullWidth
            >
              <option value="">
                {categoriesLoading
                  ? "Loading categories..."
                  : "Select a category"}
              </option>
              {!categoriesLoading &&
                categoriesData?.categories?.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
            </Select>
          </div>
          <div>
            <Select
              label="Event (Optional)"
              name="eventId"
              value={formData.eventId}
              onChange={handleChange}
              fullWidth
            >
              <option value="">None (General Product)</option>
              {eventsData?.map((event) => (
                <option key={event.id} value={event.id}>
                  {event.name}
                </option>
              ))}
            </Select>
          </div>
        </div>

        {/* Descriptions */}
        <div>
          <Input
            label="Short Description"
            name="shortDescription"
            value={formData.shortDescription}
            onChange={handleChange}
            fullWidth
          />
        </div>
        <div>
          <Textarea
            label="Full Description"
            name="description"
            value={formData.description}
            onChange={handleChange}
            rows={4}
            fullWidth
          />
        </div>

        {/* Image URL */}
        <div>
          <Input
            label="Image URL"
            name="image"
            value={formData.image}
            onChange={handleChange}
            fullWidth
          />
        </div>

        {/* Inventory */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Input
              label="Inventory (Leave empty for unlimited)"
              name="inventory"
              type="number"
              min="0"
              value={formData.inventory}
              onChange={handleChange}
              error={errors.inventory}
              helperText={errors.inventory}
              fullWidth
            />
          </div>
        </div>

        {/* Checkboxes */}
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex items-center">
            <Checkbox
              name="isActive"
              checked={formData.isActive}
              onChange={handleChange}
              label="Active (visible to customers)"
            />
          </div>
          <div className="flex items-center">
            <Checkbox
              name="affectsCapacity"
              checked={formData.affectsCapacity}
              onChange={handleChange}
              label="Affects Event Capacity"
            />
          </div>
          <div className="flex items-center">
            <Checkbox
              name="isFree"
              checked={formData.isFree}
              onChange={handleChange}
              label="Free Product (generates redemption codes)"
            />
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-2 mt-6">
          <Button
            variant="outline"
            onClick={() => router.push("/sales/products")}
            type="button"
          >
            Cancel
          </Button>
          <Button
            variant="primary"
            type="submit"
            loading={createProduct.isPending || updateProduct.isPending}
          >
            {isEditing ? "Update Product" : "Create Product"}
          </Button>
        </div>
      </form>
    </Card>
  );
};
