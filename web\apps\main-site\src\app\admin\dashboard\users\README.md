# User Management Dashboard

Administrative interface for managing user accounts and permissions across the platform.

## Features

- **page.tsx** - Main user management interface

## User Management Functions

This interface provides administrators with tools for:

- Viewing and searching user accounts
- Managing user roles and permissions
- Account activation and deactivation
- User profile editing and updates
- Bulk user operations
- User activity monitoring
- Account verification and approval
- Role assignment (admin, cashier, volunteer lead, etc.)

The user management dashboard is essential for platform administration, providing comprehensive control over user accounts and access permissions.
