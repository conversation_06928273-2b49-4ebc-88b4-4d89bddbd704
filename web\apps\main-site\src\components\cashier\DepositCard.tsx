import React from "react";
import { Transaction } from "../../services/bankService";

interface DepositCardProps {
  deposit: Transaction;
  onSelect: (deposit: Transaction) => void;
}

export const DepositCard: React.FC<DepositCardProps> = ({
  deposit,
  onSelect,
}) => {
  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return (
      date.toLocaleDateString() +
      " " +
      date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
    );
  };

  return (
    <div
      className="bg-secondary rounded-lg shadow-md p-4 border border-gray-600 hover:border-primary transition-colors cursor-pointer flex flex-col"
      onClick={() => onSelect(deposit)}
    >
      <div className="flex items-center mb-3">
        <div className="flex-shrink-0 h-12 w-12 bg-success/20 rounded-full flex items-center justify-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-6 w-6 text-success"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 6v6m0 0v6m0-6h6m-6 0H6"
            />
          </svg>
        </div>
        <div className="ml-4">
          <h3 className="text-lg font-medium text-white">Deposit Request</h3>
          <p className="text-sm text-gray-400">
            ID: {deposit.id.substring(0, 8)}...
          </p>
        </div>
      </div>

      <div className="mb-3">
        <div className="flex justify-between mb-1">
          <span className="text-gray-400">From:</span>
          <span className="text-white font-medium">
            {deposit.sender?.displayName || "Unknown"}
          </span>
        </div>
        <div className="flex justify-between mb-1">
          <span className="text-gray-400">Amount:</span>
          <span className="text-success font-bold">
            NS {deposit.amount.toFixed(0)}
          </span>
        </div>
        <div className="flex justify-between mb-1">
          <span className="text-gray-400">Date:</span>
          <span className="text-white">{formatDate(deposit.createdAt)}</span>
        </div>
      </div>

      {deposit.description && (
        <div className="mb-3 bg-secondary-dark p-2 rounded-md">
          <p className="text-sm text-gray-400">{deposit.description}</p>
        </div>
      )}

      <div className="mt-auto pt-3 border-t border-gray-600">
        <button
          onClick={(e) => {
            e.stopPropagation();
            onSelect(deposit);
          }}
          className="w-full px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
        >
          Review Deposit
        </button>
      </div>
    </div>
  );
};
