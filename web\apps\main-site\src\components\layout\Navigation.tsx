"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { useAuth } from "../../contexts/AuthContext";
import { useState, useEffect } from "react";

interface NavigationProps {
  isMobileNavOpen: boolean;
  closeMobileNav: () => void;
}

interface NavItem {
  name: string;
  path: string;
}

// These items are always in the navigation
const staticNavItems: NavItem[] = [
  { name: "Home", path: "/" },
  // Auctions, Merchants, Chat and Rules links removed for initial launch
  { name: "Events", path: "/events" },
  { name: "Volunteer", path: "/volunteer" },
  { name: "Ships", path: "/ships" },
  { name: "Shop", path: "/shop" },
  { name: "News", path: "/news" },
  { name: "Help", path: "/help" },
  { name: "About", path: "/about" },
];

// Items that require authentication
const authNavItems: NavItem[] = [{ name: "Settings", path: "/settings" }];

// Default navigation items for initial render (prevents hydration mismatch)
const defaultNavItems: NavItem[] = [
  { name: "Home", path: "/" },
  { name: "Bank", path: "/bank" },
  { name: "Events", path: "/events" },
  { name: "Volunteer", path: "/volunteer" },
  { name: "Ships", path: "/ships" },
  { name: "Shop", path: "/shop" },
  { name: "News", path: "/news" },
  { name: "Help", path: "/help" },
  { name: "About", path: "/about" },
];

export default function Navigation({
  isMobileNavOpen,
  closeMobileNav,
}: NavigationProps) {
  const pathname = usePathname();
  const { isAuthenticated, user } = useAuth();
  const [mounted, setMounted] = useState(false);
  const [navItems, setNavItems] = useState<NavItem[]>(defaultNavItems);

  // Function to check if a path is active
  const isActive = (path: string) => {
    if (path === "/") {
      return pathname === "/";
    }
    return pathname.startsWith(path);
  };

  // Update navigation items after component mounts to prevent hydration mismatch
  useEffect(() => {
    setMounted(true);

    // Only update nav items after initial render to prevent hydration mismatch
    const updatedNavItems = [
      ...staticNavItems.slice(0, 1), // Home is always first
      // If user is authenticated, bank link goes directly to dashboard
      { name: "Bank", path: isAuthenticated ? "/bank/dashboard" : "/bank" },
      ...staticNavItems.slice(1), // The rest of the static items
      // Add settings link only if authenticated
      ...(isAuthenticated ? authNavItems : []),
    ];

    setNavItems(updatedNavItems);
  }, [isAuthenticated]);

  return (
    <>
      {/* Overlay for mobile - only shown when mobile nav is open */}
      {isMobileNavOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
          onClick={closeMobileNav}
          aria-hidden="true"
        />
      )}

      <nav
        className={`
          bg-secondary flex-shrink-0 shadow-md
          md:w-40 md:sticky md:top-12 md:self-start md:block md:z-10
          ${
            isMobileNavOpen
              ? "block fixed z-50 h-full right-0 top-0 w-32"
              : "hidden"
          }
        `}
      >
        <div className="p-3 md:p-3 space-y-2 md:space-y-3">
          <div className="border-b border-gray-600 pb-2 flex justify-between items-center">
            <h2 className=" text-md md:text-xl pl-1 font-bold">Navigation</h2>

            {/* Close button - only visible on mobile */}
            <button
              className="md:hidden text-text-secondary hover:text-text-primary"
              onClick={closeMobileNav}
              aria-label="Close navigation"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6 md:h-6 md:w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          <ul className="space-y-2 md:space-y-4">
            {navItems.map((item) => (
              <li key={item.path}>
                <Link
                  href={item.path}
                  onClick={() => {
                    // Only close on mobile devices
                    if (window.innerWidth < 768) {
                      closeMobileNav();
                    }
                  }}
                  className={`
                  block px-2 md:px-3 py-2 md:py-2 text-sm md:text-base rounded-md transition-colors
                  ${
                    isActive(item.path)
                      ? "border-2 border-primary font-medium shadow-md"
                      : "border-2 border-gray-600 hover:bg-secondary-light hover:border-primary-light"
                  }
                `}
                >
                  {item.name}
                </Link>
              </li>
            ))}
          </ul>
        </div>
      </nav>
    </>
  );
}
