import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth";
import { TicketStatus } from "@prisma/client";
import { extendEventCapacityHold } from "@/lib/event-capacity-system";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// POST /api/cart/refresh-holds - Extend hold expiration
export async function POST(req: NextRequest) {
  try {
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    return await prisma.$transaction(async (tx) => {
      const newExpiration = new Date(Date.now() + 15 * 60 * 1000);
      let totalHoldsRefreshed = 0;
      let totalTicketsRefreshed = 0;
      let eventCapacityRefreshed = 0;

      // Find all individual ticket holds for this user
      const ticketHolds = await tx.ticketHold.findMany({
        where: {
          userId: user.id,
        },
        include: {
          tickets: true,
        },
      });

      // Find all event capacity holds for this user
      const eventCapacityHolds = await tx.eventCapacityHold.findMany({
        where: {
          userId: user.id,
          expiresAt: { gt: new Date() }, // Only active holds
        },
      });

      // Refresh individual ticket holds
      for (const hold of ticketHolds) {
        if (
          hold.tickets.some((ticket) => ticket.status === TicketStatus.HELD)
        ) {
          await tx.ticketHold.update({
            where: { id: hold.id },
            data: {
              expiresAt: newExpiration,
            },
          });

          totalTicketsRefreshed += hold.tickets.filter(
            (t) => t.status === TicketStatus.HELD,
          ).length;
          totalHoldsRefreshed++;
        }
      }

      // Refresh event capacity holds
      for (const hold of eventCapacityHolds) {
        await tx.eventCapacityHold.update({
          where: { id: hold.id },
          data: {
            expiresAt: newExpiration,
          },
        });
        
        eventCapacityRefreshed += hold.quantity;
        totalHoldsRefreshed++;
      }

      if (totalHoldsRefreshed === 0) {
        return NextResponse.json({
          refreshed: 0,
          message: "No active holds found",
        });
      }

      return NextResponse.json({
        refreshed: totalHoldsRefreshed,
        ticketsRefreshed: totalTicketsRefreshed,
        eventCapacityRefreshed,
        expiresAt: newExpiration,
      });
    });
  } catch (error) {
    console.error("Error refreshing holds:", error);
    return NextResponse.json(
      { error: "Failed to refresh holds" },
      { status: 500 },
    );
  }
}
