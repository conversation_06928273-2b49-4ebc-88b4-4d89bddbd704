/**
 * Utility functions for generating Lorem Ipsum text for sample content
 */

/**
 * Generate a Lorem Ipsum paragraph
 * @param sentences Number of sentences to generate (default: 5)
 * @returns A paragraph of Lorem Ipsum text
 */
export function generateLoremParagraph(sentences = 5): string {
  const loremWords = [
    "lorem",
    "ipsum",
    "dolor",
    "sit",
    "amet",
    "consectetur",
    "adipiscing",
    "elit",
    "sed",
    "do",
    "eiusmod",
    "tempor",
    "incididunt",
    "ut",
    "labore",
    "et",
    "dolore",
    "magna",
    "aliqua",
    "ut",
    "enim",
    "ad",
    "minim",
    "veniam",
    "quis",
    "nostrud",
    "exercitation",
    "ullamco",
    "laboris",
    "nisi",
    "ut",
    "aliquip",
    "ex",
    "ea",
    "commodo",
    "consequat",
    "duis",
    "aute",
    "irure",
    "dolor",
    "in",
    "reprehenderit",
    "in",
    "voluptate",
    "velit",
    "esse",
    "cillum",
    "dolore",
    "eu",
    "fugiat",
    "nulla",
    "pariatur",
    "excepteur",
    "sint",
    "occaecat",
    "cupidatat",
    "non",
    "proident",
    "sunt",
    "in",
    "culpa",
    "qui",
    "officia",
    "deserunt",
    "mollit",
    "anim",
    "id",
    "est",
    "laborum",
  ];

  let paragraph = "";

  for (let i = 0; i < sentences; i++) {
    // Generate a sentence with 5-15 words
    const sentenceLength = Math.floor(Math.random() * 10) + 5;
    let sentence = "";

    for (let j = 0; j < sentenceLength; j++) {
      const randomIndex = Math.floor(Math.random() * loremWords.length);
      let word = loremWords[randomIndex];

      // Capitalize first word of the sentence
      if (j === 0) {
        word = word.charAt(0).toUpperCase() + word.slice(1);
      }

      sentence += word + (j === sentenceLength - 1 ? ". " : " ");
    }

    paragraph += sentence;
  }

  return paragraph.trim();
}

/**
 * Generate a Lorem Ipsum HTML article with headings, paragraphs, and lists
 * @param paragraphs Number of paragraphs to generate (default: 4)
 * @returns HTML string with formatted Lorem Ipsum content
 */
export function generateLoremArticle(paragraphs = 4): string {
  let article = "";

  // Add a main heading
  article += `<h2>Lorem Ipsum Heading</h2>\n`;

  // Add first paragraph
  article += `<p>${generateLoremParagraph(4)}</p>\n`;

  // Add second paragraph
  article += `<p>${generateLoremParagraph(5)}</p>\n`;

  // Add a subheading
  article += `<h3>Subheading</h3>\n`;

  // Add a list
  article += `<ul>\n`;
  for (let i = 0; i < 4; i++) {
    article += `  <li>${generateLoremParagraph(1)}</li>\n`;
  }
  article += `</ul>\n`;

  // Add remaining paragraphs
  for (let i = 0; i < paragraphs - 2; i++) {
    article += `<p>${generateLoremParagraph(3)}</p>\n`;
  }

  return article;
}

/**
 * Generate a short Lorem Ipsum excerpt
 * @returns A short excerpt for article summaries
 */
export function generateLoremExcerpt(): string {
  return generateLoremParagraph(2);
}
