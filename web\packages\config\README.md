# Shared Configuration

Shared configuration files and settings used across Bank of Styx applications to maintain consistency and standardization.

## Configuration Files

- **eslint-preset.js** - ESLint configuration preset for code quality and style enforcement
- **tailwind.config.js** - TailwindCSS configuration for consistent styling across applications
- **tsconfig.base.json** - Base TypeScript configuration extended by individual applications
- **package.json** - Package configuration for the shared config

## Purpose

This package ensures:

- Consistent code formatting and linting rules
- Unified styling system across all applications
- Standardized TypeScript configuration
- Shared build and development settings

Applications extend these base configurations while maintaining the flexibility to override specific settings when needed.
