"use client";

import React from "react";
import Image from "next/image";
import { VolunteerCategory } from "@/hooks/useVolunteerCategories";

interface CategoryCardProps {
  category: VolunteerCategory;
  onEdit: (category: VolunteerCategory) => void;
  onDelete: (category: VolunteerCategory) => void;
}

export const CategoryCard: React.FC<CategoryCardProps> = ({
  category,
  onEdit,
  onDelete,
}) => {
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  return (
    <div className="bg-secondary-light rounded-lg shadow-md overflow-hidden border border-gray-600 flex flex-col h-full">
      {/* Card Header */}
      <div className="p-4 border-b border-gray-600 bg-secondary">
        <h3 className="text-lg font-bold text-white truncate">
          {category.name}
        </h3>
      </div>

      {/* Card Body */}
      <div className="p-4 flex-grow">
        {/* Description */}
        <div className="mb-4">
          <p className="text-gray-300 line-clamp-3">
            {category.description || "No description provided."}
          </p>
        </div>

        {/* Pay Rate */}
        {category.payRate !== null && (
          <div className="mb-4">
            <span className="text-sm font-medium text-gray-400">Pay Rate:</span>
            <span className="ml-2 text-white">
              £{category.payRate.toFixed(2)}/hr
            </span>
          </div>
        )}

        {/* Lead Manager */}
        <div className="mb-4">
          <span className="text-sm font-medium text-gray-400">
            Lead Manager:
          </span>
          {category.leadManager ? (
            <div className="mt-1 flex items-center">
              {category.leadManager.avatar ? (
                <div className="w-6 h-6 rounded-full overflow-hidden mr-2">
                  <Image
                    src={category.leadManager.avatar}
                    alt={category.leadManager.displayName}
                    width={24}
                    height={24}
                    className="object-cover"
                  />
                </div>
              ) : (
                <div className="w-6 h-6 rounded-full bg-primary flex items-center justify-center mr-2">
                  <span className="text-white text-xs">
                    {category.leadManager.displayName.charAt(0).toUpperCase()}
                  </span>
                </div>
              )}
              <span className="text-white">
                {category.leadManager.displayName}
              </span>
            </div>
          ) : (
            <span className="ml-2 text-gray-400">None assigned</span>
          )}
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-3 gap-2 mb-4">
          <div className="bg-secondary p-2 rounded-md text-center">
            <div className="text-lg font-bold text-white">
              {category.stats.totalShifts}
            </div>
            <div className="text-xs text-gray-400">Shifts</div>
          </div>
          <div className="bg-secondary p-2 rounded-md text-center">
            <div className="text-lg font-bold text-white">
              {category.stats.totalSignups}
            </div>
            <div className="text-xs text-gray-400">Signups</div>
          </div>
          <div className="bg-secondary p-2 rounded-md text-center">
            <div className="text-lg font-bold text-white">
              {category.stats.completedShifts}
            </div>
            <div className="text-xs text-gray-400">Completed</div>
          </div>
        </div>

        {/* Created/Updated Date */}
        <div className="text-xs text-gray-400">
          Created: {formatDate(category.createdAt)}
          <br />
          Updated: {formatDate(category.updatedAt)}
        </div>
      </div>

      {/* Card Actions */}
      <div className="p-3 border-t border-gray-600 bg-secondary flex justify-end space-x-2">
        <button
          onClick={() => onEdit(category)}
          className="px-3 py-1 bg-primary hover:bg-primary-dark text-white rounded-md transition-colors text-sm"
        >
          Edit
        </button>
        <button
          onClick={() => onDelete(category)}
          className="px-3 py-1 bg-accent hover:bg-accent-dark text-white rounded-md transition-colors text-sm"
        >
          Delete
        </button>
      </div>
    </div>
  );
};
