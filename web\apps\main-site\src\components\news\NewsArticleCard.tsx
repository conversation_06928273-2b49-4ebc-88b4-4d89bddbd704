import React from "react";
import { PublicArticle } from "../../services/publicNewsService";

interface NewsArticleCardProps {
  article: PublicArticle;
  onClick?: () => void;
  className?: string;
  showFullContent?: boolean;
}

export const NewsArticleCard: React.FC<NewsArticleCardProps> = ({
  article,
  onClick,
  className = "",
  showFullContent = false,
}) => {
  // Handle date format correctly for API data
  const articleDate = article.publishedAt
    ? new Date(article.publishedAt)
    : new Date();

  const formattedDate = articleDate.toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });

  // Get the content to display, ensuring it's a string
  const displayContent = showFullContent
    ? article.content || ""
    : article.excerpt || "";

  return (
    <div
      className={`bg-secondary-light rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300 border border-gray-600 max-w-full ${className} ${
        onClick ? "cursor-pointer" : ""
      }`}
      onClick={onClick}
    >
      {article.image && (
        <div className="relative h-80 w-full overflow-hidden">
          <img
            src={article.image}
            alt={article.title}
            className="w-full h-full object-contain"
            onError={(e) => {
              // Fallback if image fails to load
              const target = e.target as HTMLImageElement;
              target.onerror = null; // Prevent infinite error loop
              target.src = "/images/placeholder-news.jpg"; // Fallback image
            }}
          />
          <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-t from-black/60 to-transparent"></div>
          <span className="absolute top-4 right-4 bg-primary text-white text-xs font-bold px-3 py-1 rounded-full">
            {article.category?.name || "Uncategorized"}
          </span>
        </div>
      )}
      <div className="px-3 py-2">
        <h3 className="text-xl font-bold mb-2 text-white">{article.title}</h3>
        <div className="flex items-center mb-3 text-sm text-gray-400">
          <span className="mr-3">{formattedDate}</span>
          <span>By {article.author?.displayName || "Unknown"}</span>
        </div>

        {/* Render rich text content with dangerouslySetInnerHTML */}
        <div
          className="text-gray-400 mb-4 ql-content"
          dangerouslySetInnerHTML={{ __html: displayContent }}
        />

        {!showFullContent && (
          <div className="mt-4 mb-2">
            <span className="text-primary hover:text-primary-light text-sm font-medium">
              Read more →
            </span>
          </div>
        )}
      </div>
    </div>
  );
};
