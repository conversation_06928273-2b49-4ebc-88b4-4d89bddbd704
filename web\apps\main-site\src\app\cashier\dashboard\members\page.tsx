"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "../../../../contexts/AuthContext";
import {
  CashierDashboardLayout,
  MemberSearch,
} from "../../../../components/cashier";

export default function MembersPage() {
  const { user, isLoading: authLoading } = useAuth();
  const router = useRouter();
  const [isAuthorized, setIsAuthorized] = useState(false);

  // Check if user is authorized to access this page
  useEffect(() => {
    if (!authLoading && user) {
      if (user.roles?.banker || user.roles?.admin) {
        setIsAuthorized(true);
      } else {
        router.push("/bank/dashboard");
      }
    }
  }, [user, authLoading, router]);

  // Show loading state or nothing while checking authorization
  if (authLoading || !isAuthorized) {
    return (
      <div className="min-h-screen bg-secondary-dark flex items-center justify-center">
        <div className="bg-secondary-light p-6 rounded-lg shadow-md border border-gray-600">
          <p className="text-white text-lg">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <CashierDashboardLayout>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-white mb-2">Member Lookup</h1>
        <p className="text-gray-400">
          Search for members to view their account details and transaction
          history.
        </p>
      </div>

      <MemberSearch />
    </CashierDashboardLayout>
  );
}
