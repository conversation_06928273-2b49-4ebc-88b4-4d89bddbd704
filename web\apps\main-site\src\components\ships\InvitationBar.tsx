"use client";

import { useState } from 'react';
import { Button } from "@bank-of-styx/ui";

interface InvitationBarProps {
  shipName: string;
  roleName?: string;
  onAccept: () => Promise<void>;
  onDecline: () => Promise<void>;
}

export const InvitationBar: React.FC<InvitationBarProps> = ({
  shipName,
  roleName = "Member",
  onAccept,
  onDecline
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [actionType, setActionType] = useState<'accept' | 'decline' | null>(null);

  const handleAccept = async () => {
    setIsLoading(true);
    setActionType('accept');
    try {
      await onAccept();
    } finally {
      setIsLoading(false);
      setActionType(null);
    }
  };

  const handleDecline = async () => {
    setIsLoading(true);
    setActionType('decline');
    try {
      await onDecline();
    } finally {
      setIsLoading(false);
      setActionType(null);
    }
  };

  return (
    <div className="bg-gradient-to-r from-blue-600 to-purple-600 border border-blue-400 rounded-lg p-4 mb-6 shadow-lg">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 rounded-full bg-white/20 flex items-center justify-center flex-shrink-0">
            <span className="text-lg">✉️</span>
          </div>
          <div>
            <p className="text-white font-semibold text-sm sm:text-base">
              {shipName} has invited you to join as {roleName}
            </p>
            <p className="text-blue-100 text-xs sm:text-sm">
              Respond to this invitation to join the crew
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={handleAccept}
            disabled={isLoading}
            className="bg-green-600 border-green-500 text-white hover:bg-green-700 hover:border-green-600 disabled:opacity-50"
          >
            {isLoading && actionType === 'accept' ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                Accepting...
              </div>
            ) : (
              'Accept'
            )}
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleDecline}
            disabled={isLoading}
            className="bg-red-600 border-red-500 text-white hover:bg-red-700 hover:border-red-600 disabled:opacity-50"
          >
            {isLoading && actionType === 'decline' ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                Declining...
              </div>
            ) : (
              'Decline'
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default InvitationBar;