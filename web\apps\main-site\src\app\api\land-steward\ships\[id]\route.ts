import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth";

export const dynamic = 'force-dynamic';

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const currentUser = await getCurrentUser(request);

    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Check if user is a land steward or admin
    if (!currentUser.isLandSteward && !currentUser.isAdmin) {
      return NextResponse.json(
        { error: "Land Steward or Admin permissions required" },
        { status: 403 }
      );
    }

    const { id } = params;

    const ship = await prisma.ship.findUnique({
      where: { id },
      include: {
        captain: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
            email: true,
          },
        },
        members: {
          include: {
            user: {
              select: {
                id: true,
                username: true,
                displayName: true,
                avatar: true,
                email: true,
              },
            },
            customRole: {
              select: {
                id: true,
                name: true,
                description: true,
              },
            },
          },
          orderBy: { joinedAt: 'asc' },
        },
        roles: {
          include: {
            _count: {
              select: {
                members: true,
              },
            },
          },
          orderBy: { createdAt: 'asc' },
        },
        deletionRequests: {
          include: {
            requestedBy: {
              select: {
                id: true,
                username: true,
                displayName: true,
              },
            },
            reviewedBy: {
              select: {
                id: true,
                username: true,
                displayName: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
        },
        joinRequests: {
          where: { status: 'pending' },
          include: {
            user: {
              select: {
                id: true,
                username: true,
                displayName: true,
                avatar: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
        },
      },
    });

    if (!ship) {
      return NextResponse.json(
        { error: "Ship not found" },
        { status: 404 }
      );
    }

    const formattedShip = {
      id: ship.id,
      name: ship.name,
      description: ship.description,
      slogan: ship.slogan,
      logo: ship.logo,
      tags: ship.tags as string[] || [],
      status: ship.status,
      createdAt: ship.createdAt.toISOString(),
      updatedAt: ship.updatedAt.toISOString(),
      captain: ship.captain,
      members: ship.members.map(member => ({
        id: member.id,
        role: member.role,
        status: member.status,
        joinedAt: member.joinedAt.toISOString(),
        leftAt: member.leftAt?.toISOString(),
        user: member.user,
        customRole: member.customRole,
      })),
      roles: ship.roles.map(role => ({
        id: role.id,
        name: role.name,
        description: role.description,
        createdAt: role.createdAt.toISOString(),
        memberCount: role._count.members,
      })),
      deletionRequests: ship.deletionRequests.map(request => ({
        id: request.id,
        reason: request.reason,
        status: request.status,
        message: request.message,
        createdAt: request.createdAt.toISOString(),
        reviewedAt: request.reviewedAt?.toISOString(),
        requestedBy: request.requestedBy,
        reviewedBy: request.reviewedBy,
      })),
      joinRequests: ship.joinRequests.map(request => ({
        id: request.id,
        type: request.type,
        status: request.status,
        message: request.message,
        createdAt: request.createdAt.toISOString(),
        user: request.user,
      })),
    };

    return NextResponse.json(formattedShip);
  } catch (error) {
    console.error("Error fetching ship details:", error);
    return NextResponse.json(
      { error: "Failed to fetch ship details" },
      { status: 500 }
    );
  }
}

// DELETE ship (permanent deletion)
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const currentUser = await getCurrentUser(request);

    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Check if user is a land steward or admin
    if (!currentUser.isLandSteward && !currentUser.isAdmin) {
      return NextResponse.json(
        { error: "Land Steward or Admin permissions required" },
        { status: 403 }
      );
    }

    const { id } = params;

    // Check if ship exists
    const ship = await prisma.ship.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            members: true,
            roles: true,
            joinRequests: true,
            deletionRequests: true,
            formSubmissions: true,
          },
        },
      },
    });

    if (!ship) {
      return NextResponse.json(
        { error: "Ship not found" },
        { status: 404 }
      );
    }

    // Perform cascading delete
    await prisma.$transaction(async (tx) => {
      // Delete ship members
      await tx.shipMember.deleteMany({
        where: { shipId: id },
      });

      // Delete ship roles
      await tx.shipRole.deleteMany({
        where: { shipId: id },
      });

      // Delete join requests
      await tx.shipJoinRequest.deleteMany({
        where: { shipId: id },
      });

      // Delete deletion requests
      await tx.shipDeletionRequest.deleteMany({
        where: { shipId: id },
      });

      // Delete form submissions
      await tx.formSubmission.deleteMany({
        where: { shipId: id },
      });

      // Delete the ship itself
      await tx.ship.delete({
        where: { id },
      });
    });

    return NextResponse.json({
      message: "Ship deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting ship:", error);
    return NextResponse.json(
      { error: "Failed to delete ship" },
      { status: 500 }
    );
  }
}