# Authentication System

## Overview
The Authentication System provides comprehensive user authentication, authorization, and session management for the Bank of Styx application. It supports multiple authentication methods including email/password and Discord OAuth, features dual credential storage for security, email verification, password management, and role-based access control.

## Directory Structure
```
authentication/
├── src/app/auth/                    # Authentication pages
│   ├── discord/                     # Discord OAuth handling
│   │   ├── callback/               # OAuth callback handler
│   │   └── error/                  # OAuth error display
├── src/app/api/auth/               # Authentication API endpoints
│   ├── login/                      # Email/password login
│   ├── register/                   # User registration
│   ├── me/                         # Current user session
│   ├── set-password/               # Password management
│   ├── has-password/               # Password status check
│   ├── verify-email/               # Email verification
│   └── discord/                    # Discord OAuth endpoints
├── src/components/auth/            # Authentication UI components
│   ├── AuthModal.tsx              # Main login/register modal
│   ├── EmailVerificationForm.tsx  # Email verification interface
│   ├── GeneratedPasswordDisplay.tsx # Temporary password display
│   └── PasswordCreationModal.tsx  # Password creation interface
├── src/contexts/                   # Authentication state management
│   └── AuthContext.tsx            # Global authentication context
├── src/lib/                       # Authentication utilities
│   ├── auth.ts                    # JWT validation and user helpers
│   ├── discord.ts                 # Discord OAuth utilities
│   └── email.ts                   # Email service configuration
└── src/services/                  # Authentication business logic
    └── verificationService.ts     # Email verification system
```

## Key Files & Components

### Frontend Components
- **`AuthModal.tsx`** - Main authentication modal with login/register forms and form switching
- **`EmailVerificationForm.tsx`** - Email verification code input and validation interface
- **`GeneratedPasswordDisplay.tsx`** - Displays temporary passwords after email verification
- **`PasswordCreationModal.tsx`** - Modal for creating permanent passwords

### API Endpoints
- **`POST /api/auth/login`** - Email/password authentication with dual credential system support
- **`POST /api/auth/register`** - New user registration creating User and UserCredential records
- **`GET /api/auth/me`** - Current user session validation returning complete user profile
- **`POST /api/auth/set-password`** - Set/update user password with dual storage system
- **`GET /api/auth/has-password`** - Check if user has password set for Discord disconnect validation
- **`POST /api/auth/verify-email`** - Send verification code to user's email address
- **`PUT /api/auth/verify-email`** - Verify code and generate temporary password
- **`GET /api/auth/discord`** - Initiate Discord OAuth flow with state management
- **`GET /api/auth/discord/callback`** - Handle Discord OAuth callback and account linking
- **`POST /api/auth/discord/disconnect`** - Disconnect Discord account from user profile

### Database Models
- **`User`** - Primary user model with profile data, preferences, role flags, and legacy password storage
- **`UserCredential`** - New credential system supporting multiple authentication methods
- **`VerificationCode`** - Email verification code storage with expiration and attempt tracking
- **`TemporaryPassword`** - Temporary password system for email verification flow
- **`UserState`** - User preferences and feature flags (dark mode, beta features, etc.)

### Services
- **`verificationService.ts`** - Email verification code generation, validation, and temporary password management
- **`authService.ts`** - Frontend authentication service for API interactions and state management

### Hooks
- **`useAuth()`** - Access authentication context including user data, login/logout functions, and modal controls
- **`useRequireAuth()`** - Route protection hook redirecting unauthenticated users
- **`useRoleCheck()`** - Role-based access control validation

## Common Tasks

### Task 1: How to Authenticate a User
1. Import and use the AuthContext: `const { login, user, isAuthenticated } = useAuth()`
2. Call `login(email, password)` with user credentials
3. Handle the returned promise for success/error states
4. User data automatically available in context after successful login

### Task 2: How to Add Role-Based Protection
1. Use `getCurrentUser(request)` in API routes to get authenticated user
2. Check role flags: `user.isAdmin`, `user.isBanker`, etc.
3. Use `userHasRole(request, "admin")` helper for complex role checks
4. Return 401/403 status for unauthorized access

### Task 3: How to Implement Discord OAuth
1. User clicks connect button, call `connectDiscord()` from auth context
2. System redirects to `/api/auth/discord` with state parameters
3. Discord OAuth flow completes at `/api/auth/discord/callback`
4. Avatar downloaded and user profile updated automatically
5. Redirect to dashboard with linked account

## API Integration

### Authentication Requirements
- **Public endpoints**: `/api/auth/login`, `/api/auth/register`, `/api/auth/discord/*`
- **Protected endpoints**: All other endpoints require valid JWT token
- **Token format**: `Bearer <jwt-token>` in Authorization header or `token` cookie
- **Token expiration**: 7 days from issuance

### Request/Response Examples
```typescript
// Login Request
interface LoginRequest {
  email: string;
  password: string;
}

// Registration Request
interface RegisterRequest {
  username: string;
  email: string;
  password: string;
}

// Authentication Response
interface AuthResponse {
  user: {
    id: string;
    username: string;
    displayName: string;
    email: string;
    avatar: string;
    balance: number;
    isEmailVerified: boolean;
    // Role flags
    isAdmin: boolean;
    isBanker: boolean;
    isEditor: boolean;
    // ... other roles
    // Ship information
    captainOfShips?: Array<{
      id: string;
      name: string;
    }>;
  };
  token: string;
}

// Current User Response
interface CurrentUserResponse {
  id: string;
  username: string;
  displayName: string;
  email: string;
  avatar: string;
  balance: number;
  status: string;
  defaultView: string;
  // Notification preferences
  notifyTransfers: boolean;
  notifyDeposits: boolean;
  notifyWithdrawals: boolean;
  notifyNewsEvents: boolean;
  // Connected accounts
  discordConnected: boolean;
  discordId: string | null;
  // All role flags...
}
```

## Database Schema

### Primary Models
```sql
-- User table (primary authentication model)
CREATE TABLE User (
  id VARCHAR(191) PRIMARY KEY,
  username VARCHAR(255) UNIQUE NOT NULL,
  displayName VARCHAR(255) NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  passwordHash VARCHAR(255), -- Legacy field
  avatar VARCHAR(255) DEFAULT '/images/avatars/default.png',
  balance FLOAT DEFAULT 0,
  isEmailVerified BOOLEAN DEFAULT false,
  status VARCHAR(255) DEFAULT 'active',
  defaultView VARCHAR(255) DEFAULT 'dashboard',
  
  -- Notification preferences
  notifyTransfers BOOLEAN DEFAULT true,
  notifyDeposits BOOLEAN DEFAULT true,
  notifyWithdrawals BOOLEAN DEFAULT true,
  notifyNewsEvents BOOLEAN DEFAULT false,
  
  -- Connected accounts
  discordConnected BOOLEAN DEFAULT false,
  discordId VARCHAR(255),
  facebookConnected BOOLEAN DEFAULT false,
  facebookId VARCHAR(255),
  
  -- Role flags (comprehensive RBAC)
  isAdmin BOOLEAN DEFAULT false,
  isEditor BOOLEAN DEFAULT false,
  isBanker BOOLEAN DEFAULT false,
  isChatModerator BOOLEAN DEFAULT false,
  isVolunteer BOOLEAN DEFAULT false,
  isVolunteerCoordinator BOOLEAN DEFAULT false,
  isLeadManager BOOLEAN DEFAULT false,
  isSalesManager BOOLEAN DEFAULT false,
  isLandSteward BOOLEAN DEFAULT false,
  
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- UserCredential table (new multi-method credential system)
CREATE TABLE UserCredential (
  id VARCHAR(191) PRIMARY KEY,
  userId VARCHAR(191) NOT NULL,
  type VARCHAR(255) NOT NULL, -- 'email', 'discord', 'facebook'
  identifier VARCHAR(255) NOT NULL, -- email address, OAuth ID
  passwordHash VARCHAR(255), -- Only for email credentials
  accessToken TEXT, -- OAuth access tokens
  refreshToken TEXT, -- OAuth refresh tokens
  lastUsedAt DATETIME,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (userId) REFERENCES User(id) ON DELETE CASCADE
);

-- Email verification system
CREATE TABLE VerificationCode (
  id VARCHAR(191) PRIMARY KEY,
  userId VARCHAR(191) NOT NULL,
  type VARCHAR(255) NOT NULL, -- 'EMAIL_VERIFICATION', 'PASSWORD_RESET', 'ACCOUNT_SECURITY'
  code VARCHAR(6) NOT NULL, -- 6-character hex code
  expiresAt DATETIME NOT NULL,
  verified BOOLEAN DEFAULT false,
  attempts INT DEFAULT 0,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (userId) REFERENCES User(id) ON DELETE CASCADE
);

-- Temporary password system
CREATE TABLE TemporaryPassword (
  id VARCHAR(191) PRIMARY KEY,
  userId VARCHAR(191) UNIQUE NOT NULL,
  passwordHash VARCHAR(255) NOT NULL,
  expiresAt DATETIME NOT NULL,
  used BOOLEAN DEFAULT false,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (userId) REFERENCES User(id) ON DELETE CASCADE
);
```

### Relationships
- **User ↔ UserCredential**: One-to-many (user can have multiple authentication methods)
- **User ↔ VerificationCode**: One-to-many (multiple verification codes per user)
- **User ↔ TemporaryPassword**: One-to-one (single active temporary password)
- **User ↔ Ship**: Many-to-many via Captain relationship (users can captain multiple ships)

## Related Features
- **[Banking System](./banking-system.md)** - Role-based access for cashier operations and transaction management
- **[Ship Management System](./ship-management-system.md)** - Captain permissions and ship ownership validation
- **[Notification System](./notification-system.md)** - User preference management for notification delivery
- **[Real-time SSE](./real-time-sse-system.md)** - Authentication for Server-Sent Events connections

## User Roles & Permissions
- **Super Admin**: System configuration, role assignment, critical operations
- **Admin**: Full system access, user management, financial operations  
- **Banker/Cashier**: Banking operations, transaction processing, deposit/withdrawal management
- **Editor**: Content management, news articles, event creation
- **Land Steward**: Ship volunteer management, hour tracking, requirement oversight
- **Volunteer Coordinator**: Volunteer shift management, category administration
- **Lead Manager**: Advanced volunteer operations, coordinator supervision
- **Sales Manager**: Product management, order processing, inventory control
- **Chat Moderator**: Community moderation, chat management
- **Volunteer**: Basic volunteer operations, shift signup
- **User**: Standard user access, personal data management, basic system features

## Recent Changes
- **v3.1.0** - Enhanced dual credential system with UserCredential model for multiple auth methods
- **v3.0.0** - Implemented Discord OAuth integration with avatar synchronization
- **v2.5.0** - Added comprehensive email verification system with temporary passwords
- **v2.4.0** - Role-based access control expansion with ship captain permissions
- **v2.3.0** - Added notification preferences management within authentication system

## Troubleshooting

### Common Issues
1. **"Invalid token" errors**: Check JWT_SECRET environment variable and token expiration (7 days)
2. **Discord OAuth failures**: Verify DISCORD_CLIENT_ID and DISCORD_CLIENT_SECRET configuration
3. **Email verification not working**: Check email service configuration (SMTP settings)
4. **Password reset failures**: Ensure VerificationCode table has proper indexes and cleanup

### Debug Information
- **Log locations**: Server logs show authentication attempts and JWT validation failures
- **Environment variables**: `JWT_SECRET`, `DISCORD_CLIENT_ID`, `DISCORD_CLIENT_SECRET`, email SMTP settings
- **Debugging commands**: 
  ```bash
  # Check user authentication status
  curl -H "Authorization: Bearer <token>" http://localhost:3000/api/auth/me
  
  # Verify Discord OAuth configuration
  echo $DISCORD_CLIENT_ID
  ```

### Performance Considerations
- JWT token validation occurs on every protected endpoint request
- Discord avatar downloads happen asynchronously during OAuth flow
- Email verification codes expire after 24 hours with automatic cleanup
- User credential lookups optimized with database indexes on userId and type fields

---

**File Locations:**
- Pages: `/src/app/auth/`
- Components: `/src/components/auth/`
- API: `/src/app/api/auth/`
- Context: `/src/contexts/AuthContext.tsx`
- Utils: `/src/lib/auth.ts`, `/src/lib/discord.ts`
- Types: `/src/types/auth.ts`
- Services: `/src/services/verificationService.ts`