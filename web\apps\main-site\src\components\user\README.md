# User Components

User profile and account management components for personal settings and customization.

## Components

- **UserMenu.tsx** - User dropdown menu with profile options and account actions
- **AvatarUploadModal.tsx** - Modal component for uploading and managing user avatars
- **SimpleAvatarUpload.tsx** - Streamlined avatar upload component for quick updates
- **SyncDiscordAvatarButton.tsx** - Button component for syncing avatar from Discord account

These components handle user profile management, avatar customization, and account integration features, providing users with control over their personal settings and appearance on the platform.
