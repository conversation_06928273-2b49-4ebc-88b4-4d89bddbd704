import React from "react";
import { Transaction } from "../../services/bankService";

interface TransactionHistoryCardProps {
  transaction: Transaction;
  userId: string;
}

export const TransactionHistoryCard: React.FC<TransactionHistoryCardProps> = ({
  transaction,
  userId,
}) => {
  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return (
      date.toLocaleDateString() +
      " " +
      date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
    );
  };

  // Get transaction type label
  const getTypeLabel = (type: string) => {
    switch (type) {
      case "deposit":
        return "Deposit";
      case "withdrawal":
        return "Withdrawal";
      case "transfer":
        return "Transfer";
      case "donation":
        return "Donation";
      default:
        return type.charAt(0).toUpperCase() + type.slice(1);
    }
  };

  // Get status label with appropriate styling
  const getStatusLabel = (status: string) => {
    let statusClass = "";
    switch (status) {
      case "completed":
        statusClass = "bg-success/20 text-success";
        break;
      case "pending":
        statusClass = "bg-warning/20 text-warning";
        break;
      case "rejected":
        statusClass = "bg-error/20 text-error";
        break;
      case "cancelled":
        statusClass = "bg-gray-600/20 text-gray-400";
        break;
      default:
        statusClass = "bg-gray-600/20 text-gray-400";
    }
    return (
      <span
        className={`px-2 py-1 rounded-full text-xs font-medium ${statusClass}`}
      >
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  // Get amount display with appropriate color
  const getAmountDisplay = () => {
    const isIncoming =
      transaction.type === "deposit" ||
      (transaction.type === "transfer" && transaction.recipientId === userId);

    const amountClass = isIncoming ? "text-success" : "text-error";
    const prefix = isIncoming ? "+" : "-";

    return (
      <span className={`font-bold ${amountClass}`}>
        {prefix} NS {transaction.amount.toFixed(0)}
      </span>
    );
  };

  // Get icon based on transaction type
  const getTransactionIcon = (type: string) => {
    switch (type) {
      case "deposit":
        return (
          <div className="flex-shrink-0 h-12 w-12 bg-success/20 rounded-full flex items-center justify-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6 text-success"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 6v6m0 0v6m0-6h6m-6 0H6"
              />
            </svg>
          </div>
        );
      case "withdrawal":
        return (
          <div className="flex-shrink-0 h-12 w-12 bg-error/20 rounded-full flex items-center justify-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6 text-error"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 10l7-7m0 0l7 7m-7-7v18"
              />
            </svg>
          </div>
        );
      case "transfer":
        return (
          <div className="flex-shrink-0 h-12 w-12 bg-primary/20 rounded-full flex items-center justify-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6 text-primary"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"
              />
            </svg>
          </div>
        );
      case "donation":
        return (
          <div className="flex-shrink-0 h-12 w-12 bg-purple-500/20 rounded-full flex items-center justify-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6 text-purple-500"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
              />
            </svg>
          </div>
        );
      default:
        return (
          <div className="flex-shrink-0 h-12 w-12 bg-gray-600/20 rounded-full flex items-center justify-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
        );
    }
  };

  // Get transfer details
  const getTransferDetails = () => {
    if (transaction.type === "transfer") {
      if (transaction.senderId === userId) {
        return (
          <span className="text-gray-400 text-sm">
            To: {transaction.recipient?.username || "Unknown"}
          </span>
        );
      } else {
        return (
          <span className="text-gray-400 text-sm">
            From: {transaction.sender?.username || "Unknown"}
          </span>
        );
      }
    }
    return null;
  };

  return (
    <div className="bg-secondary rounded-lg shadow-md p-4 border border-gray-600 flex flex-col">
      <div className="flex items-center mb-3">
        {getTransactionIcon(transaction.type)}
        <div className="ml-4">
          <h3 className="text-lg font-medium text-white">
            {getTypeLabel(transaction.type)}
          </h3>
          <p className="text-sm text-gray-400">
            ID: {transaction.id.substring(0, 8)}...
          </p>
        </div>
      </div>

      <div className="mb-3">
        <div className="flex justify-between mb-1">
          <span className="text-gray-400">Amount:</span>
          {getAmountDisplay()}
        </div>
        <div className="flex justify-between mb-1">
          <span className="text-gray-400">Date:</span>
          <span className="text-white">
            {formatDate(transaction.createdAt)}
          </span>
        </div>
        <div className="flex justify-between mb-1">
          <span className="text-gray-400">Status:</span>
          {getStatusLabel(transaction.status)}
        </div>
        {getTransferDetails() && (
          <div className="mt-2">{getTransferDetails()}</div>
        )}
      </div>

      {transaction.description && (
        <div className="mt-auto pt-3 border-t border-gray-600">
          <p className="text-sm text-gray-400">{transaction.description}</p>
        </div>
      )}
    </div>
  );
};
