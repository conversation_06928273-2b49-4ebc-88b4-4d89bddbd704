/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,ts,jsx,tsx}",
    "../../packages/ui/src/**/*.{js,ts,jsx,tsx}"
  ],
  theme: {
    extend: {
      colors: {
        // Bank of Styx color scheme - Dark Mode Optimized
        primary: {
          DEFAULT: "var(--color-primary)", // Discord blue
          dark: "var(--color-primary-dark)",
          light: "var(--color-primary-light)"
        },
        secondary: {
          DEFAULT: "var(--color-secondary)", // Discord dark (nav, input backgrounds)
          dark: "var(--color-secondary-dark)", // Near black (page background)
          light: "var(--color-secondary-light)" // Lighter dark (card backgrounds)
        },
        accent: {
          DEFAULT: "var(--color-accent)", // Discord red
          dark: "var(--color-accent-dark)",
          light: "var(--color-accent-light)"
        },
        // Status colors
        success: "var(--color-success)", // Green for success states
        warning: "var(--color-warning)", // Orange for warnings
        error: "var(--color-error)", // Red for errors
        info: "var(--color-info)", // Blue for info (same as primary)
        // Text colors
        gray: {
          400: "var(--color-text-secondary)", // Secondary text
          500: "var(--color-text-muted)", // Muted text, placeholders
        },
        // Additional colors
        hover: "var(--color-bg-hover)", // Hover background
      },
      fontFamily: {
        sans: ["Inter", "sans-serif"],
        display: ["Poppins", "sans-serif"]
      },
      backgroundColor: {
        'card': "var(--color-bg-card)", // Card background (secondary-light)
        'input': "var(--color-bg-input)", // Input background (secondary)
      },
      textColor: {
        'disabled': "var(--color-text-disabled)", // Disabled text (gray-500 with opacity)
      },
      borderColor: {
        'subtle': "var(--color-border-subtle)", // Subtle border (secondary)
      },
    },
  },
  plugins: [],
};
