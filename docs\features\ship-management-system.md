# Ship Management System

## Overview
The Ship Management System is a comprehensive organizational framework that enables community members to form and manage groups called "ships." It provides three distinct interfaces: public ship browsing and joining, captain dashboard for ship leadership, and Land Steward administration for system oversight. The system integrates deeply with volunteer tracking, event management, and banking systems to provide a cohesive community experience.

## Directory Structure
```
ship-management/
├── src/app/ships/                     # Public ship interface
│   ├── page.tsx                       # Ship browsing and discovery
│   ├── [id]/                          # Individual ship details
│   │   ├── page.tsx                   # Ship profile and join options
│   │   └── join/                      # Ship join request process
├── src/app/captain/                   # Captain dashboard
│   ├── page.tsx                       # Captain landing page
│   └── dashboard/                     # Captain management interface
│       ├── page.tsx                   # Dashboard overview
│       ├── members/                   # Member management
│       │   ├── page.tsx               # Member list and roles
│       │   ├── invite/                # Member invitation system
│       │   └── [id]/                  # Individual member management
│       ├── settings/                  # Ship configuration
│       ├── forms/                     # Event form management
│       │   ├── page.tsx               # Form list and creation
│       │   ├── create/                # Form creation wizard
│       │   ├── [formId]/              # Form management
│       │   └── submissions/           # Form submission review
│       └── applications/              # Captain application management
├── src/app/land-steward/              # Land Steward administration
│   ├── page.tsx                       # Land Steward dashboard
│   ├── ships/                         # Ship oversight
│   ├── applications/                  # Captain application review
│   ├── forms/                         # Form template management
│   └── volunteer-requirements/        # Volunteer hour monitoring
├── src/app/api/ships/                 # Ship management APIs
├── src/app/api/captain/               # Captain operation APIs
├── src/app/api/land-steward/          # Land Steward administration APIs
├── src/components/ships/              # Ship UI components
│   ├── ShipCard.tsx                   # Ship display cards
│   ├── ShipList.tsx                   # Ship listing interface
│   ├── MemberCard.tsx                 # Member display components
│   ├── RoleManager.tsx                # Role assignment interface
│   ├── InviteSystem.tsx               # Member invitation components
│   ├── JoinRequestManager.tsx         # Join request processing
│   ├── ShipSearch.tsx                 # Ship search and filtering
│   ├── VolunteerProgress.tsx          # Volunteer hour tracking display
│   ├── FormSubmissionCard.tsx         # Form submission display
│   ├── ShipSettingsForm.tsx           # Ship configuration interface
│   └── ShipDeletionRequest.tsx        # Ship deletion management
├── src/services/shipService.ts        # Ship business logic
└── src/hooks/useShips.ts              # Ship state management hooks
```

## Key Files & Components

### Frontend Components
- **`ShipCard.tsx`** - Ship display card with member count, description, and action buttons for browsing
- **`ShipList.tsx`** - Paginated ship listing with filtering and search capabilities
- **`MemberCard.tsx`** - Individual member display with role information and management actions
- **`RoleManager.tsx`** - Dynamic role creation, assignment, and management interface
- **`InviteSystem.tsx`** - Member invitation system with email-based invitations and join links
- **`JoinRequestManager.tsx`** - Join request approval/rejection interface for captains
- **`ShipSearch.tsx`** - Advanced ship search with auto-complete and dropdown selection
- **`VolunteerProgress.tsx`** - Real-time volunteer hour tracking and requirement progress display
- **`FormSubmissionCard.tsx`** - Event form submission display with approval/rejection controls
- **`ShipSettingsForm.tsx`** - Ship configuration interface for name, description, and settings
- **`ShipDeletionRequest.tsx`** - Ship deletion request management with Land Steward approval

### API Endpoints
- **`GET /api/ships`** - Public ship listing with pagination, search, and filtering
- **`GET /api/ships/[id]`** - Individual ship details including member list and public information
- **`POST /api/ships/[id]/join`** - Submit join request to ship with optional message
- **`GET /api/captain/ships`** - Captain's ships list with management permissions
- **`GET /api/captain/dashboard`** - Captain dashboard data including members, requests, and statistics
- **`GET /api/captain/members`** - Ship member management with role information
- **`POST /api/captain/members/invite`** - Send member invitations via email with join links
- **`PUT /api/captain/members/[id]/role`** - Update member roles and permissions
- **`DELETE /api/captain/members/[id]`** - Remove members from ship with audit trail
- **`GET /api/captain/join-requests`** - Pending join requests for captain approval
- **`PUT /api/captain/join-requests/[id]`** - Approve or reject join requests
- **`GET /api/captain/forms`** - Event forms created by captain for ship participation
- **`POST /api/captain/forms`** - Create new event forms with volunteer hour tracking
- **`GET /api/captain/forms/submissions`** - Form submissions from ship members
- **`PUT /api/captain/forms/submissions/[id]`** - Approve/reject form submissions with hour credit
- **`GET /api/land-steward/ships`** - All ships overview for administrative management
- **`GET /api/land-steward/applications`** - Captain applications pending review
- **`PUT /api/land-steward/applications/[id]`** - Approve or reject captain applications
- **`GET /api/land-steward/volunteer-requirements`** - Ship volunteer hour requirement monitoring
- **`PUT /api/land-steward/ships/[id]`** - Administrative ship management and configuration
- **`DELETE /api/land-steward/ships/[id]`** - Process ship deletion requests

### Database Models
- **`Ship`** - Core ship model with name, description, member count, captain relationships, and volunteer requirements
- **`ShipMember`** - Ship membership with user relationship, role assignment, join date, and status
- **`ShipRole`** - Dynamic role system with custom role names and permissions beyond default "Member"
- **`CaptainApplication`** - Captain application tracking with Land Steward review and approval workflow
- **`ShipJoinRequest`** - Join request management with captain approval and optional messaging
- **`ShipDeletionRequest`** - Ship deletion workflow with Land Steward approval and reasoning
- **`ShipVolunteerRequirement`** - Ship-specific volunteer hour requirements and tracking
- **`VolunteerHours`** - Integration model tracking volunteer work credited to ships

### Services
- **`shipService.ts`** - Comprehensive ship management service with full CRUD operations, member management, and integration patterns
- **`captainService.ts`** - Captain-specific operations including dashboard management, form creation, and member oversight
- **`landStewardService.ts`** - Administrative service for ship oversight, application processing, and system management

### Hooks
- **`useShips()`** - Ship browsing and public ship operations with search and filtering
- **`useCaptainDashboard()`** - Captain dashboard data management with real-time updates
- **`useShipMembers()`** - Member management with role assignments and invitation tracking
- **`useLandStewardOperations()`** - Land Steward administrative operations and oversight tools

## Common Tasks

### Task 1: How to Join a Ship
1. User browses ships at `/ships` with search and filter options
2. Clicks on ship card to view details at `/ships/[id]`
3. Reviews ship information, member list, and requirements
4. Clicks "Request to Join" button
5. Fills out join request form with optional message
6. System creates ShipJoinRequest record with pending status
7. Captain receives notification and reviews request
8. Captain approves/rejects via `/captain/dashboard` join requests section
9. User receives notification of decision and becomes member if approved
10. Member gains access to ship-specific features and volunteer hour tracking

### Task 2: How to Manage Ship Members as Captain
1. Captain navigates to `/captain/dashboard/members`
2. Views current member list with roles and join dates
3. To invite new members: clicks "Invite Member" button
4. Enters email addresses and optional personal message
5. System sends email invitations with unique join links
6. To manage roles: clicks on member card, selects new role
7. Custom roles can be created beyond default "Member" role
8. To remove members: uses remove action with confirmation
9. All member changes logged with timestamps and captain identification
10. Real-time updates via notifications to affected members

### Task 3: How to Process Captain Applications (Land Steward)
1. Land Steward accesses `/land-steward/applications`
2. Reviews pending captain applications with user details
3. Evaluates application requirements and qualifications
4. Clicks approve/reject with required reasoning
5. Approved applications automatically create new Ship record
6. New captain receives notification and access to captain dashboard
7. Rejected applications notify applicant with feedback
8. All decisions tracked with Land Steward identification and timestamps
9. System maintains application history for audit purposes

## API Integration

### Authentication Requirements
- **Public endpoints**: Ship browsing and details (`/api/ships`, `/api/ships/[id]`)
- **User endpoints**: Join requests require valid JWT token
- **Captain endpoints**: JWT token + captain status verification (user must be captain of accessed ship)
- **Land Steward endpoints**: JWT token + `isLandSteward` role flag
- **Token format**: `Bearer <jwt-token>` in Authorization header

### Request/Response Examples
```typescript
// Ship Join Request
interface JoinShipRequest {
  message?: string; // Optional message to captain
}

// Ship Response (Public)
interface ShipResponse {
  id: string;
  name: string;
  description: string;
  memberCount: number;
  maxMembers?: number;
  isRecruiting: boolean;
  logo?: string;
  captain: {
    id: string;
    username: string;
    displayName: string;
  };
  members: Array<{
    id: string;
    username: string;
    displayName: string;
    role: string;
    joinedAt: string;
  }>;
  volunteerRequirement?: {
    requiredHours: number;
    currentHours: number;
    deadline?: string;
  };
  createdAt: string;
}

// Captain Dashboard Response
interface CaptainDashboardResponse {
  ships: Array<{
    id: string;
    name: string;
    memberCount: number;
    pendingRequests: number;
    volunteerProgress: {
      totalHours: number;
      requiredHours: number;
      percentage: number;
    };
  }>;
  pendingJoinRequests: Array<{
    id: string;
    user: {
      id: string;
      username: string;
      displayName: string;
    };
    ship: {
      id: string;
      name: string;
    };
    message?: string;
    requestedAt: string;
  }>;
  recentActivity: Array<{
    type: 'member_joined' | 'form_submitted' | 'volunteer_hours';
    description: string;
    timestamp: string;
  }>;
}

// Member Invitation Request
interface InviteMemberRequest {
  emails: string[];
  message?: string;
  roleId?: string; // Optional custom role
}

// Form Submission Response
interface FormSubmissionResponse {
  id: string;
  form: {
    id: string;
    title: string;
    volunteerHours: number;
  };
  submitter: {
    id: string;
    username: string;
    displayName: string;
  };
  ship: {
    id: string;
    name: string;
  };
  status: 'pending' | 'approved' | 'rejected';
  submittedAt: string;
  reviewedAt?: string;
  reviewedBy?: {
    id: string;
    username: string;
    displayName: string;
  };
  volunteerHoursAwarded?: number;
}
```

## Database Schema

### Primary Models
```sql
-- Ship table (core ship information)
CREATE TABLE Ship (
  id VARCHAR(191) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  memberCount INT DEFAULT 0,
  maxMembers INT,
  isRecruiting BOOLEAN DEFAULT true,
  logo VARCHAR(255),
  captainId VARCHAR(191) NOT NULL,
  status ENUM('active', 'inactive', 'pending_deletion') DEFAULT 'active',
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (captainId) REFERENCES User(id),
  INDEX idx_ship_captain (captainId),
  INDEX idx_ship_status (status),
  INDEX idx_ship_recruiting (isRecruiting)
);

-- ShipMember table (membership relationships)
CREATE TABLE ShipMember (
  id VARCHAR(191) PRIMARY KEY,
  shipId VARCHAR(191) NOT NULL,
  userId VARCHAR(191) NOT NULL,
  roleId VARCHAR(191),
  status ENUM('active', 'inactive', 'removed') DEFAULT 'active',
  joinedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  removedAt DATETIME,
  removedBy VARCHAR(191), -- Captain who removed member
  FOREIGN KEY (shipId) REFERENCES Ship(id) ON DELETE CASCADE,
  FOREIGN KEY (userId) REFERENCES User(id),
  FOREIGN KEY (roleId) REFERENCES ShipRole(id),
  FOREIGN KEY (removedBy) REFERENCES User(id),
  UNIQUE KEY unique_ship_member (shipId, userId),
  INDEX idx_shipmember_ship (shipId),
  INDEX idx_shipmember_user (userId),
  INDEX idx_shipmember_status (status)
);

-- ShipRole table (dynamic role system)
CREATE TABLE ShipRole (
  id VARCHAR(191) PRIMARY KEY,
  shipId VARCHAR(191) NOT NULL,
  name VARCHAR(255) NOT NULL, -- e.g., "Officer", "Navigator", "Engineer"
  description TEXT,
  permissions JSON, -- Future role permissions
  isDefault BOOLEAN DEFAULT false,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (shipId) REFERENCES Ship(id) ON DELETE CASCADE,
  INDEX idx_shiprole_ship (shipId)
);

-- CaptainApplication table (captain application workflow)
CREATE TABLE CaptainApplication (
  id VARCHAR(191) PRIMARY KEY,
  userId VARCHAR(191) NOT NULL,
  shipName VARCHAR(255) NOT NULL,
  shipDescription TEXT NOT NULL,
  experience TEXT,
  motivation TEXT,
  status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
  reviewedBy VARCHAR(191), -- Land Steward
  reviewedAt DATETIME,
  reviewNotes TEXT,
  shipId VARCHAR(191), -- Created ship if approved
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (userId) REFERENCES User(id),
  FOREIGN KEY (reviewedBy) REFERENCES User(id),
  FOREIGN KEY (shipId) REFERENCES Ship(id),
  INDEX idx_captain_app_user (userId),
  INDEX idx_captain_app_status (status)
);

-- ShipJoinRequest table (join request management)
CREATE TABLE ShipJoinRequest (
  id VARCHAR(191) PRIMARY KEY,
  shipId VARCHAR(191) NOT NULL,
  userId VARCHAR(191) NOT NULL,
  message TEXT,
  status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
  reviewedBy VARCHAR(191), -- Captain who reviewed
  reviewedAt DATETIME,
  reviewNotes TEXT,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (shipId) REFERENCES Ship(id),
  FOREIGN KEY (userId) REFERENCES User(id),
  FOREIGN KEY (reviewedBy) REFERENCES User(id),
  INDEX idx_joinreq_ship (shipId),
  INDEX idx_joinreq_user (userId),
  INDEX idx_joinreq_status (status)
);

-- ShipVolunteerRequirement table (volunteer hour requirements)
CREATE TABLE ShipVolunteerRequirement (
  id VARCHAR(191) PRIMARY KEY,
  shipId VARCHAR(191) NOT NULL,
  requiredHours INT NOT NULL,
  deadline DATETIME,
  description TEXT,
  isActive BOOLEAN DEFAULT true,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (shipId) REFERENCES Ship(id),
  INDEX idx_ship_vol_req (shipId),
  INDEX idx_ship_vol_deadline (deadline)
);

-- ShipDeletionRequest table (ship deletion workflow)
CREATE TABLE ShipDeletionRequest (
  id VARCHAR(191) PRIMARY KEY,
  shipId VARCHAR(191) NOT NULL,
  requestedBy VARCHAR(191) NOT NULL, -- Captain or Land Steward
  reason TEXT NOT NULL,
  status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
  reviewedBy VARCHAR(191), -- Land Steward
  reviewedAt DATETIME,
  reviewNotes TEXT,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (shipId) REFERENCES Ship(id),
  FOREIGN KEY (requestedBy) REFERENCES User(id),
  FOREIGN KEY (reviewedBy) REFERENCES User(id)
);
```

### Relationships
- **Ship ↔ User**: Many-to-one (captain relationship) and many-to-many (member relationships via ShipMember)
- **Ship ↔ ShipMember**: One-to-many (ship has many members)
- **Ship ↔ ShipRole**: One-to-many (ship can have custom roles)
- **Ship ↔ VolunteerHours**: One-to-many (volunteer work credited to ships)
- **Ship ↔ Form**: One-to-many (ships create event forms)
- **CaptainApplication ↔ Ship**: One-to-one (approved application creates ship)

## Related Features
- **[Authentication System](./authentication-system.md)** - Role-based access control for captain and Land Steward operations
- **[Volunteer System](./volunteer-system.md)** - Deep integration with volunteer hour tracking and ship credit system
- **[Banking System](./banking-system.md)** - Captain role integration for banking permissions and operations
- **[Notification System](./notification-system.md)** - Member invitations, join requests, and ship activity notifications
- **[Upload System](./upload-system.md)** - Ship logo uploads and form attachment handling
- **[Event Management System](./event-management-system.md)** - Form creation for ship event participation

## User Roles & Permissions
- **Public Users**: Ship browsing, ship details viewing, join request submission
- **Ship Members**: Access to ship-specific features, form submissions, volunteer hour tracking
- **Ship Captains**: Full ship management, member invitation/removal, role assignment, form creation, join request approval
- **Land Stewards**: Ship oversight, captain application review, ship deletion approval, volunteer requirement management
- **Admins**: Administrative access to ship system configuration and advanced management features

## Recent Changes
- **v5.1.0** - Volunteer Ship Tracking System integration (2025-01-07)
  - Enhanced volunteer signup form with ship search functionality
  - Implemented automatic ship hour tracking and credit assignment
  - Created Land Steward dashboard for monitoring ship volunteer requirements
  - Updated database schema with ship volunteer hour relationships
  - Built ship search component with auto-populate and dropdown selection
- **v5.0.0** - Dynamic ship role system with custom role creation beyond default "Member"
- **v4.5.0** - Ship deletion request workflow with Land Steward approval process
- **v4.4.0** - Enhanced member invitation system with email-based invitations
- **v4.3.0** - Form submission system integration for event participation tracking

## Troubleshooting

### Common Issues
1. **Ship search not working**: Check ship indexing and search algorithm. Verify ship status is 'active' and isRecruiting is properly set.
2. **Member invitation emails not sending**: Verify email service configuration and invitation link generation. Check email template formatting.
3. **Volunteer hours not crediting to ships**: Ensure form submissions are approved and volunteer hour integration is working. Check ShipVolunteerRequirement relationships.
4. **Captain application approval failing**: Verify Land Steward permissions and ship creation process. Check for duplicate ship names.
5. **Join requests not appearing**: Check notification system integration and captain dashboard query filters.

### Debug Information
- **Log locations**: Ship operation logs in server console, member activity logs in database
- **Environment variables**: Email service configuration for invitations, upload paths for ship logos
- **Database queries**: Use Prisma Studio to inspect ship relationships and member status
- **Debugging commands**:
  ```bash
  # Check ship membership
  curl -H "Authorization: Bearer <token>" http://localhost:3000/api/captain/members
  
  # Verify captain status
  curl -H "Authorization: Bearer <token>" http://localhost:3000/api/captain/ships
  
  # Check Land Steward operations
  curl -H "Authorization: Bearer <token>" http://localhost:3000/api/land-steward/ships
  ```

### Performance Considerations
- **Ship Browsing**: Pagination implemented for large ship lists with efficient database queries
- **Member Management**: Optimized member queries with proper indexing on ship and user relationships
- **Volunteer Hour Tracking**: Real-time updates via SSE for volunteer progress display
- **Search Functionality**: Indexed ship names and descriptions for fast search operations
- **Image Handling**: Ship logo uploads optimized with image compression and caching

### Security Measures
- **Role Validation**: Strict captain and Land Steward permission checking on all management operations
- **Ship Access Control**: Members can only access ships they belong to, captains can only manage their ships
- **Join Request Security**: Prevention of duplicate join requests and spam protection
- **Invitation Security**: Unique invitation tokens with expiration for member invitations
- **Audit Trails**: Complete logging of member changes, role assignments, and administrative actions

### Integration Patterns
- **Authentication Integration**: Deep integration with JWT authentication and role-based access control
- **Volunteer System Integration**: Automatic volunteer hour credit assignment to ships from approved form submissions
- **Banking System Integration**: Captain role affects banking permissions and operations
- **Notification System Integration**: Real-time notifications for ship activities, member changes, and administrative actions
- **Form System Integration**: Ships create event participation forms with volunteer hour tracking

---

**File Locations:**
- Pages: `/src/app/ships/`, `/src/app/captain/`, `/src/app/land-steward/`
- Components: `/src/components/ships/`
- API: `/src/app/api/ships/`, `/src/app/api/captain/`, `/src/app/api/land-steward/`
- Services: `/src/services/shipService.ts`, `/src/services/captainService.ts`
- Hooks: `/src/hooks/useShips.ts`, `/src/hooks/useCaptain.ts`
- Types: `/src/types/ship.ts`