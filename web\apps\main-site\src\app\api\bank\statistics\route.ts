import { NextRequest, NextResponse } from "next/server";
import prisma from "../../../../lib/prisma";
import jwt from "jsonwebtoken";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
const JWT_SECRET = process.env.JWT_SECRET || "your-development-secret-key";

// Helper function to verify token
function verifyToken(token: string) {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    return null;
  }
}

export const GET = async (req: NextRequest) => {
  try {
    // Get authorization header
    const authHeader = req.headers.get("authorization");

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Extract token
    const token = authHeader.split(" ")[1];

    // Verify token
    const decoded = verifyToken(token);

    if (!decoded || typeof decoded !== "object" || !decoded.id) {
      return NextResponse.json({ error: "Invalid token" }, { status: 401 });
    }

    // Check if user is a banker
    const user = await prisma.user.findUnique({
      where: { id: decoded.id as string },
    });

    if (!user || !user.isBanker) {
      return NextResponse.json(
        { error: "Unauthorized - Banker role required" },
        { status: 403 },
      );
    }

    // Query the database for bank statistics

    // Get total deposits
    const deposits = await prisma.transaction.findMany({
      where: {
        type: "deposit",
        status: "completed",
      },
    });

    const totalDeposits = {
      count: deposits.length,
      amount: deposits.reduce((sum, tx) => sum + tx.amount, 0),
    };

    // Get total withdrawals
    const withdrawals = await prisma.transaction.findMany({
      where: {
        type: "withdrawal",
        status: "completed",
      },
    });

    const totalWithdrawals = {
      count: withdrawals.length,
      amount: withdrawals.reduce((sum, tx) => sum + tx.amount, 0),
    };

    // Get pending transactions
    const pendingDeposits = await prisma.transaction.count({
      where: {
        type: "deposit",
        status: "pending",
      },
    });

    const pendingWithdrawals = await prisma.transaction.count({
      where: {
        type: "withdrawal",
        status: "pending",
      },
    });

    // Get daily activity for the last 7 days
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const recentTransactions = await prisma.transaction.findMany({
      where: {
        createdAt: {
          gte: sevenDaysAgo,
        },
        status: "completed",
      },
    });

    // Group transactions by day
    const dailyActivityMap = new Map();

    recentTransactions.forEach((tx) => {
      const date = tx.createdAt.toISOString().split("T")[0]; // Get YYYY-MM-DD

      if (!dailyActivityMap.has(date)) {
        dailyActivityMap.set(date, {
          date,
          deposits: 0,
          withdrawals: 0,
          transfers: 0,
        });
      }

      const activity = dailyActivityMap.get(date);

      if (tx.type === "deposit") {
        activity.deposits++;
      } else if (tx.type === "withdrawal") {
        activity.withdrawals++;
      } else if (tx.type === "transfer") {
        activity.transfers++;
      }
    });

    const dailyActivity = Array.from(dailyActivityMap.values()).sort(
      (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime(),
    );

    // Get top users by transaction count
    const userTransactions = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        sentTransactions: {
          select: {
            amount: true,
          },
        },
        receivedTransactions: {
          select: {
            amount: true,
          },
        },
      },
      take: 5,
    });

    const userActivity = userTransactions
      .map((user) => {
        const transactionCount =
          user.sentTransactions.length + user.receivedTransactions.length;

        const totalAmount =
          user.sentTransactions.reduce((sum, tx) => sum + tx.amount, 0) +
          user.receivedTransactions.reduce((sum, tx) => sum + tx.amount, 0);

        return {
          userId: user.id,
          username: user.username,
          transactionCount,
          totalAmount,
        };
      })
      .sort((a, b) => b.transactionCount - a.transactionCount);

    const statistics = {
      totalDeposits,
      totalWithdrawals,
      pendingTransactions: {
        deposits: pendingDeposits,
        withdrawals: pendingWithdrawals,
      },
      dailyActivity,
      userActivity,
    };

    return NextResponse.json(statistics);
  } catch (error) {
    console.error("Error fetching bank statistics:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
};
