# Bank of Styx Database Schema Documentation

## Database Overview

The Bank of Styx database uses **MySQL 8.0** as the primary database with **Prisma ORM v6.6.0** for data access and migrations. This is a comprehensive banking, event management, and community platform that serves approximately 1,000 users with peaks of 100 concurrent users.

### Database Configuration
- **Provider**: MySQL 8.0
- **ORM**: Prisma Client with TypeScript generation
- **Migration System**: Prisma Migrate with version control
- **Connection Pool**: Managed by Prisma with environment-based configuration

## Core Models Overview

### User Management (7 models)
- **User**: Core user accounts with roles and permissions
- **UserCredential**: Multi-authentication method storage
- **UserState**: User preferences and caching
- **VerificationCode**: Email verification and security codes
- **TemporaryPassword**: Generated password management

### Banking System (4 models)
- **Transaction**: Financial transactions and transfers
- **PayCode**: Reusable payment codes with expiration
- **Ledger**: Daily reconciliation and audit trails
- **Notification**: System-wide notifications with SSE integration

### Content Management (3 models)
- **NewsArticle**: News and content publishing
- **NewsCategory**: Article categorization
- **UploadedImage**: File upload tracking with metadata

### Event & Volunteer System (12 models)
- **Event**: Event management with capacity controls
- **EventCategory**: Event categorization
- **VolunteerCategory**: Volunteer role definitions with pay rates
- **VolunteerShift**: Time-based volunteer assignments
- **VolunteerAssignment**: User-shift assignments with status tracking
- **VolunteerSlot**: Individual volunteer positions with hold system
- **VolunteerSlotHold**: Temporary slot reservations
- **VolunteerHours**: Hour tracking with payment processing
- **VolunteerNotificationPreferences**: Notification settings per assignment
- **EventCapacityHold**: Temporary capacity reservations

### Shopping System (10 models)
- **Product**: Items for sale with inventory management
- **ProductCategory**: Product organization
- **Cart**: Shopping cart sessions
- **CartItem**: Individual cart entries with hold tracking
- **Order**: Purchase records with payment integration
- **OrderItem**: Order line items with historical pricing
- **Ticket**: Individual item instances with hold system
- **TicketHold**: Temporary ticket reservations (15-minute expiry)
- **RedemptionCode**: Free product access codes

### Support System (2 models)
- **SupportTicket**: Help desk with priority and assignment
- **TicketNote**: Ticket comments and internal notes

### Ship Management System (9 models)
- **Ship**: Community groups with captain management
- **ShipMember**: Member relationships with custom roles
- **ShipRole**: Custom roles created by captains
- **CaptainApplication**: Ship captain application workflow
- **ShipJoinRequest**: Member invitation and join request system
- **ShipDeletionRequest**: Ship deletion workflow with approval
- **FormTemplate**: Reusable form definitions
- **EventForm**: Event-specific forms with submission tracking
- **FormSubmission**: Form responses with review workflow
- **ShipVolunteerRequirement**: Volunteer hour requirements per ship

## Model Relationships

### User-Centric Relationships
```
User
├── Authentication
│   ├── UserCredential (1:many) - Multiple auth methods
│   ├── UserState (1:1) - Preferences and caching
│   ├── VerificationCode (1:many) - Security codes
│   └── TemporaryPassword (1:1) - Generated passwords
├── Banking
│   ├── sentTransactions (1:many) - Outgoing transfers
│   ├── receivedTransactions (1:many) - Incoming transfers
│   ├── processedTransactions (1:many) - Admin processed
│   ├── createdCodes (1:many) - PayCodes created
│   ├── redeemedCodes (1:many) - PayCodes redeemed
│   └── verifiedLedgers (1:many) - Ledger verification
├── Content
│   ├── newsArticles (1:many) - Authored articles
│   └── notifications (1:many) - User notifications
├── Events & Volunteering
│   ├── createdEvents (1:many) - Events created
│   ├── volunteerShifts (1:many) - Volunteer assignments
│   ├── volunteerHours (1:many) - Hours worked
│   ├── verifiedVolunteerHours (1:many) - Hours verified
│   ├── leadManagerCategory (1:1) - Category management
│   ├── eventCapacityHolds (1:many) - Capacity reservations
│   └── volunteerSlotHolds (1:many) - Slot reservations
├── Shopping
│   ├── carts (1:many) - Shopping sessions
│   ├── orders (1:many) - Purchase history
│   └── ticketHolds (1:many) - Ticket reservations
├── Support
│   ├── submittedTickets (1:many) - User tickets
│   ├── assignedTickets (1:many) - Admin assignments
│   ├── resolvedTickets (1:many) - Admin resolutions
│   └── ticketNotes (1:many) - Ticket comments
└── Ships
    ├── captainedShips (1:many) - Ships as captain
    ├── shipMemberships (1:many) - Ship memberships
    ├── captainApplications (1:many) - Captain applications
    ├── reviewedApplications (1:many) - Application reviews
    ├── shipJoinRequests (1:many) - Join requests received
    ├── initiatedJoinRequests (1:many) - Join requests sent
    ├── requestedShipDeletions (1:many) - Deletion requests
    ├── reviewedShipDeletions (1:many) - Deletion reviews
    ├── createdFormTemplates (1:many) - Form templates
    ├── createdEventForms (1:many) - Event forms
    ├── submittedForms (1:many) - Form submissions
    └── reviewedForms (1:many) - Form reviews
```

### Event System Relationships
```
Event
├── EventCategory (many:1) - Event classification
├── VolunteerCategory (1:many) - Volunteer roles for event
├── VolunteerShift (1:many) - Time-based volunteer slots
├── Product (1:many) - Event-specific products
├── EventCapacityHold (1:many) - Capacity reservations
└── EventForm (1:many) - Event-specific forms

VolunteerShift
├── VolunteerAssignment (1:many) - User assignments
├── VolunteerSlot (1:many) - Individual positions
└── VolunteerCategory (many:1) - Role definition

VolunteerAssignment
├── VolunteerHours (1:1) - Hours worked tracking
├── VolunteerNotificationPreferences (1:1) - Notification settings
└── VolunteerSlot (1:1) - Specific position
```

### Shopping System Relationships
```
Product
├── ProductCategory (many:1) - Product classification
├── Event (many:1, optional) - Event association
├── Ticket (1:many) - Individual product instances
├── CartItem (1:many) - Cart entries
├── OrderItem (1:many) - Order entries
└── RedemptionCode (1:many) - Free access codes

Cart
└── CartItem (1:many)
    ├── Product (many:1) - Item reference
    ├── TicketHold (1:1, optional) - Ticket reservations
    ├── EventCapacityHold (1:1, optional) - Capacity reservations
    └── RedemptionCode (many:1, optional) - Code usage tracking

Order
├── OrderItem (1:many) - Purchase line items
└── Ticket (1:many) - Tickets sold
```

### Ship Management Relationships
```
Ship
├── ShipMember (1:many) - Member relationships
├── ShipRole (1:many) - Custom roles
├── ShipJoinRequest (1:many) - Join/invite requests
├── ShipDeletionRequest (1:many) - Deletion requests
├── FormSubmission (1:many) - Form responses
├── ShipVolunteerRequirement (1:many) - Hour requirements
└── creditedVolunteerHours (1:many) - Volunteer hours credited

ShipMember
├── ShipRole (many:1, optional) - Custom role assignment
├── User (many:1) - Member user account
└── Ship (many:1) - Ship membership

FormSubmission
├── EventForm (many:1) - Form definition
├── Ship (many:1) - Submitting ship
├── ShipVolunteerRequirement (1:1) - Generated requirement
└── User (many:1) - Submitting user
```

## Key Fields by Model

### User Model
```sql
id: String @id @default(uuid()) -- Primary UUID identifier
username: String @unique -- Unique username
email: String @unique -- Unique email address
passwordHash: String -- Bcrypt password hash
balance: Float @default(0) -- Sterling balance
status: String @default("active") -- Account status
isEmailVerified: Boolean @default(false) -- Email verification status

-- Role Flags
isAdmin: Boolean @default(false)
isEditor: Boolean @default(false)
isBanker: Boolean @default(false)
isChatModerator: Boolean @default(false)
isVolunteer: Boolean @default(false)
isVolunteerCoordinator: Boolean @default(false)
isLeadManager: Boolean @default(false)
isSalesManager: Boolean @default(false)
isLandSteward: Boolean @default(false)

-- Notification Preferences
notifyTransfers: Boolean @default(true)
notifyDeposits: Boolean @default(true)
notifyWithdrawals: Boolean @default(true)
notifyNewsEvents: Boolean @default(false)
notifyAuctions: Boolean @default(true)
notifyChat: Boolean @default(true)
notifyAdmin: Boolean @default(true)

-- Connected Accounts
discordConnected: Boolean @default(false)
discordId: String?
facebookConnected: Boolean @default(false)
facebookId: String?
```

### Transaction Model
```sql
id: String @id @default(uuid())
amount: Float @db.Double -- Transaction amount
type: String -- deposit, withdrawal, transfer, volunteer_payment
status: String -- pending, completed, failed, cancelled
description: String? -- Transaction description
note: String? -- Additional notes
senderId: String? -- Sender user ID
recipientId: String? -- Recipient user ID
processedById: String? -- Admin who processed
paymentMethod: String? -- Payment method for deposits/withdrawals
receiptImage: String? -- Receipt image path
ledgerId: String? -- Associated ledger entry
payCodeId: String? -- Associated pay code
createdAt: DateTime @default(now())
processedAt: DateTime? -- Processing timestamp
```

### Event Model
```sql
id: String @id @default(uuid())
name: String -- Event name
description: String @db.Text -- Full description
shortDescription: String? -- Brief description
startDate: DateTime -- Event start time
endDate: DateTime -- Event end time
location: String? -- Physical location
address: String? -- Full address
virtualLink: String? -- Online event link
isVirtual: Boolean @default(false) -- Virtual event flag
image: String? -- Event image path
status: String @default("draft") -- draft, published, cancelled, completed
capacity: Int? -- Maximum attendees
createdById: String -- Creator user ID
categoryId: String -- Event category ID
```

### Product Model
```sql
id: String @id @default(uuid())
name: String -- Product name
description: String? @db.Text -- Full description
shortDescription: String? -- Brief description
price: Float @db.Double -- Price in sterling
image: String? -- Product image path
isActive: Boolean @default(true) -- Available for purchase
affectsCapacity: Boolean @default(true) -- Counts toward event capacity
inventory: Int? -- Stock quantity (null = unlimited)
isAutoGenerated: Boolean @default(false) -- Auto-created for events
isFree: Boolean @default(false) -- Complimentary product
categoryId: String -- Product category ID
eventId: String? -- Associated event ID
```

### VolunteerShift Model
```sql
id: String @id @default(uuid())
title: String -- Shift title
description: String? @db.Text -- Shift description
startTime: DateTime -- Shift start time
endTime: DateTime -- Shift end time
location: String? -- Shift location
maxVolunteers: Int @default(1) -- Maximum volunteers
eventId: String -- Associated event ID
categoryId: String -- Volunteer category ID
isAutomated: Boolean @default(false) -- Auto-generated shift
```

### Ship Model
```sql
id: String @id @default(uuid())
name: String @unique -- Ship name
description: String @db.Text -- Ship description
slogan: String? -- Ship slogan
logo: String? -- Logo file path
tags: Json? -- Custom tags array
status: String @default("active") -- active, inactive, deleted, pending_deletion
captainId: String -- Captain user ID
```

## Business Logic Implementation

### Banking System Logic
```sql
-- Transaction Types
type: "deposit" | "withdrawal" | "transfer" | "volunteer_payment" | "pay_code_redemption"

-- Transaction Status Flow
"pending" → "completed" | "failed" | "cancelled"

-- Balance Updates
-- Implemented with atomic transactions using Prisma $transaction()
-- Prevents race conditions in concurrent balance updates
```

### Hold System Logic
```sql
-- Ticket Hold System (15-minute expiry)
TicketHold.expiresAt: DateTime -- Auto-cleanup via cron jobs
TicketHold → Ticket (1:many) -- Multiple tickets per hold
Ticket.status: "AVAILABLE" | "HELD" | "SOLD" | "CANCELLED"

-- Volunteer Slot Hold System
VolunteerSlotHold.expiresAt: DateTime
VolunteerSlot.status: "AVAILABLE" | "HELD" | "SOLD" | "CANCELLED"

-- Event Capacity Hold System
EventCapacityHold.expiresAt: DateTime
EventCapacityHold.quantity: Int -- Number of capacity units
```

### Volunteer Hour Tracking
```sql
-- Volunteer Hours with Ship Credit System
VolunteerHours.creditShipId: String? -- Ship receiving credit
VolunteerHours.isDockHours: Boolean -- Dock work hours
VolunteerHours.isLandGrant: Boolean -- Land grant hours
VolunteerHours.paymentStatus: "pending" | "processing" | "paid" | "cancelled"

-- Ship Volunteer Requirements
ShipVolunteerRequirement.requiredHours: Float -- Hours required
ShipVolunteerRequirement.completedHours: Float -- Hours completed
ShipVolunteerRequirement.status: "pending" | "in_progress" | "completed" | "overdue"
```

### Form Builder System
```sql
-- Dynamic Form Structure
FormTemplate.formStructure: Json -- Field definitions
EventForm.formStructure: Json -- Event-specific modifications
FormSubmission.submissionData: Json -- User responses
FormSubmission.status: "draft" | "submitted" | "reviewed" | "approved" | "rejected"

-- Form-to-Requirement Pipeline
EventForm.requiredVolunteerHours: Float? -- Hours assigned on approval
FormSubmission → ShipVolunteerRequirement (1:1) -- Automatic requirement creation
```

## Indexes and Constraints

### Primary Keys
All models use UUID primary keys generated via `@default(uuid())`

### Unique Constraints
```sql
-- User Model
@@unique([username])
@@unique([email])

-- Ship Model  
@@unique([name])

-- News Models
@@unique([slug]) -- NewsArticle
@@unique([name]) -- NewsCategory
@@unique([slug]) -- NewsCategory

-- Form System
@@unique([formId, shipId]) -- FormSubmission (one per ship per form)
@@unique([userId, shiftId]) -- VolunteerAssignment
@@unique([userId, shipId]) -- ShipMember
@@unique([shipId, name]) -- ShipRole

-- Product System
@@unique([code]) -- PayCode, RedemptionCode
@@unique([orderNumber]) -- Order
@@unique([entityType, entityId, filename]) -- UploadedImage
```

### Database Indexes
```sql
-- User Relationship Indexes
@@index([userId]) -- Applied to most user-related models

-- Status and State Indexes
@@index([status]) -- Applied to models with workflow states
@@index([read]) -- Notification
@@index([expiresAt]) -- Hold systems

-- Time-based Indexes  
@@index([startDate]) -- Event
@@index([startTime]) -- VolunteerShift
@@index([submissionDeadline]) -- EventForm

-- Search and Filter Indexes
@@index([category]) -- Notification
@@index([categoryId]) -- Most categorized models
@@index([eventId]) -- Event-related models
@@index([shipId]) -- Ship-related models

-- Payment and Financial Indexes
@@index([paymentStatus]) -- VolunteerHours
@@index([orderNumber]) -- Order
```

### Foreign Key Constraints
```sql
-- Cascade Deletes (Strong Relationships)
onDelete: Cascade
- UserCredential → User
- UserState → User  
- ShipMember → Ship
- ShipRole → Ship
- VolunteerShift → Event
- VolunteerAssignment → VolunteerShift
- CartItem → Cart
- OrderItem → Order

-- Set Null (Weak Relationships)
onDelete: SetNull
- TicketHold → CartItem (allows cart cleanup without breaking holds)

-- Restrict (Protected Relationships)
onDelete: Restrict (default)
- Transaction → User (preserves financial history)
- NewsArticle → User (preserves authorship)
```

## Migration Pattern

### Migration Versioning
```
Migrations stored in: prisma/migrations/
Format: YYYYMMDDHHMMSS_migration_name/
        └── migration.sql

Migration Lock File: migration_lock.toml
```

### Key Migration History
```sql
20250426214838_init -- Initial user and auth setup
20250428021018_add_bank_models -- Banking system
20250428171432_add_notification_model -- Notification system
20250502132226_add_user_credentials -- Multi-auth support
20250504161232_add_support_ticket_system -- Support system
20250513050513_add_event_management -- Event system
20250514224427_add_volunteer_models -- Volunteer system
20250523193456_add_cart_and_order_models -- Shopping system
20250530103220_add_ticket_hold_system -- Hold system
20250801051037_add_ships_system -- Ship management
20250803040012_add_form_builder_phase_4 -- Dynamic forms
20250806081829_add_ship_volunteer_hour_tracking -- Hour tracking
```

### Migration Workflow
```bash
# Create migration
npx prisma migrate dev --name migration_name

# Apply migrations to production
npx prisma migrate deploy

# Generate Prisma client
npx prisma generate

# Verify migration status
npx prisma migrate status
```

### Critical Migration Patterns
1. **Add columns with default values** to prevent data loss
2. **Gradual foreign key introduction** with optional relationships first
3. **Index creation after data population** for performance
4. **Data migration scripts** for complex transformations
5. **Backup verification** before major schema changes

## Performance Considerations

### Connection Pooling
- Prisma manages connection pools automatically
- Production: 10-15 connections recommended for current load
- Development: 5 connections sufficient

### Query Optimization
- **Relationship loading**: Use `include` judiciously to prevent N+1 queries
- **Pagination**: Implemented for large datasets (transactions, notifications)
- **Selective field loading**: Use `select` for large text fields
- **Database indexing**: Strategic indexes on frequently queried fields

### Caching Strategy
- **User state caching**: `UserState.globalCacheVersion` for invalidation
- **Balance caching**: Real-time updates via Server-Sent Events
- **Static content**: File uploads cached with versioning

### Monitoring Points
- **Transaction volume**: Monitor daily transaction counts
- **Hold system cleanup**: Automated expiry processing
- **Volunteer hour processing**: Batch payment processing
- **Database connection usage**: Monitor active connections
- **Migration performance**: Track migration execution times

This schema supports the Bank of Styx's comprehensive feature set including banking, events, volunteering, shopping, ship management, and community features while maintaining data integrity and performance at scale.