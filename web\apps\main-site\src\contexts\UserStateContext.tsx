"use client";

import { createContext, useContext, useEffect, useRef, ReactNode } from "react";
import { useAuth } from "./AuthContext";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import fetchClient from "@/lib/fetchClient";

// Define the UserState interface
export interface UserState {
  id: string;
  userId: string;
  lastNewsSync: string | null;
  lastCategorySync: string | null;
  lastNotificationSync: string | null;
  betaFeaturesEnabled: boolean;
  darkModeEnabled: boolean;
  compactViewEnabled: boolean;
  globalCacheVersion: number;
  lastActive: string;
  deviceInfo: string | null;
  pageViews: number;
  articleReads: number;
  customState: any | null;
  hasAccessToAuctions: boolean;
  hasAccessToMerchants: boolean;
  hasAccessToChat: boolean;
  createdAt: string;
  updatedAt: string;
}

// Define the UserStateContext interface
interface UserStateContextType {
  userState: UserState | null;
  isLoading: boolean;
  error: Error | null;
  updateUserState: (data: Partial<UserState>) => Promise<void>;
  refreshUserState: () => void;
  needsFreshData: (dataType: string, maxAgeMinutes?: number) => boolean;
  incrementPageViews: () => Promise<void>;
  incrementArticleReads: () => Promise<void>;
  setCustomState: (key: string, value: any) => Promise<void>;
  getCustomState: (key: string) => any;
  updateNotificationTimestamp: () => Promise<void>;
  needsNotificationRefresh: (maxAgeSeconds?: number) => boolean;
}

// Create the UserStateContext
const UserStateContext = createContext<UserStateContextType | undefined>(
  undefined,
);

// Create the UserStateProvider component
export const UserStateProvider = ({ children }: { children: ReactNode }) => {
  const { user, isAuthenticated } = useAuth();
  const queryClient = useQueryClient();

  // Fetch user state
  const {
    data: userState,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ["userState"],
    queryFn: async () => {
      return fetchClient.get<UserState>("/api/user/state");
    },
    enabled: isAuthenticated,
    staleTime: 60000, // Consider data fresh for 1 minute
  });

  // Update user state mutation
  const updateUserStateMutation = useMutation({
    mutationFn: async (data: Partial<UserState>) => {
      return fetchClient.patch<UserState>("/api/user/state", data);
    },
    onSuccess: (data) => {
      queryClient.setQueryData(["userState"], data);
    },
  });

  // Update user state
  const updateUserState = async (data: Partial<UserState>) => {
    await updateUserStateMutation.mutateAsync(data);
  };

  // Refresh user state
  const refreshUserState = () => {
    refetch();
  };

  // Check if data needs to be refreshed
  const needsFreshData = (dataType: string, maxAgeMinutes = 60) => {
    if (!userState) return true;

    const lastSyncKey = `last${dataType}Sync` as keyof UserState;
    const lastSync = userState[lastSyncKey] as string | null;

    if (!lastSync) return true;

    const lastSyncDate = new Date(lastSync);
    const now = new Date();
    const diffMs = now.getTime() - lastSyncDate.getTime();
    const diffMinutes = diffMs / (1000 * 60);

    return diffMinutes > maxAgeMinutes;
  };

  // Increment page views
  const incrementPageViews = async () => {
    if (!userState) return;
    await updateUserState({
      pageViews: userState.pageViews + 1,
      lastActive: new Date().toISOString(),
    });
  };

  // Increment article reads
  const incrementArticleReads = async () => {
    if (!userState) return;
    await updateUserState({
      articleReads: userState.articleReads + 1,
    });
  };

  // Set custom state
  const setCustomState = async (key: string, value: any) => {
    if (!userState) return;

    const customState = userState.customState || {};
    const updatedCustomState = {
      ...customState,
      [key]: value,
    };

    await updateUserState({
      customState: updatedCustomState,
    });
  };

  // Get custom state
  const getCustomState = (key: string) => {
    if (!userState || !userState.customState) return null;
    return userState.customState[key];
  };

  // Update notification timestamp
  const updateNotificationTimestamp = async () => {
    if (!userState) return;
    await updateUserState({
      lastNotificationSync: new Date().toISOString(),
    });
  };

  // Check if notifications need refresh
  const needsNotificationRefresh = (maxAgeSeconds = 5) => {
    if (!userState || !userState.lastNotificationSync) return true;

    const lastSync = new Date(userState.lastNotificationSync);
    const now = new Date();
    const diffMs = now.getTime() - lastSync.getTime();
    const diffSeconds = diffMs / 1000;

    return diffSeconds > maxAgeSeconds;
  };

  // Auto-refresh when user changes
  const prevUserIdRef = useRef<string | null>(null);

  useEffect(() => {
    // Only refresh if the user ID has changed and the user is authenticated
    if (isAuthenticated && user?.id && user.id !== prevUserIdRef.current) {
      prevUserIdRef.current = user.id;
      refreshUserState();
    }
  }, [isAuthenticated, user?.id, refreshUserState]);

  return (
    <UserStateContext.Provider
      value={{
        userState: userState || null,
        isLoading,
        error,
        updateUserState,
        refreshUserState,
        needsFreshData,
        incrementPageViews,
        incrementArticleReads,
        setCustomState,
        getCustomState,
        updateNotificationTimestamp,
        needsNotificationRefresh,
      }}
    >
      {children}
    </UserStateContext.Provider>
  );
};

// Create a hook to use the UserStateContext
export const useUserState = () => {
  const context = useContext(UserStateContext);
  if (context === undefined) {
    throw new Error("useUserState must be used within a UserStateProvider");
  }

  // For non-authenticated users, provide default implementations of functions
  // that don't require authentication
  if (!context.userState) {
    return {
      ...context,
      needsFreshData: () => true, // Always fetch fresh data for non-authenticated users
    };
  }

  return context;
};
