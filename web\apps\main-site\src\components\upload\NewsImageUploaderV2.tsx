import React, { useState } from "react";
import UniversalImageUploader from "../common/UniversalImageUploader";
import { UploadResponse } from "@/types/upload";
import { uploadConfig } from "@/lib/uploadConfig";

interface NewsImageUploaderV2Props {
  articleId: string;
  onUploadComplete: (response: UploadResponse) => void;
  onUploadStart?: () => void;
  aspectRatio?: number;
  label?: string;
  description?: string;
  className?: string;
}

const NewsImageUploaderV2: React.FC<NewsImageUploaderV2Props> = ({
  articleId,
  onUploadComplete,
  onUploadStart,
  aspectRatio = 16 / 9,
  label = "News Article Image",
  description,
  className = "",
}) => {
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);

  const handleUploadStart = () => {
    setIsUploading(true);
    onUploadStart?.();
  };

  const handleUploadComplete = (response: UploadResponse) => {
    setIsUploading(false);

    if (response.success) {
      // Handle both new format (response.file.url) and legacy format (response.url)
      const imageUrl = response.file?.url || response.url;
      if (imageUrl) {
        setPreviewUrl(imageUrl);
      }
    }

    onUploadComplete(response);
  };

  const handleRemoveImage = () => {
    setPreviewUrl(null);
    // Call onUploadComplete with a "removed" status
    onUploadComplete({
      success: true,
      message: "Image removed",
    });
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <div>
        <h3 className="text-lg font-medium text-gray-900">{label}</h3>
        {description && (
          <p className="text-sm text-gray-500 mt-1">{description}</p>
        )}
      </div>

      {/* Image Preview */}
      {previewUrl && (
        <div className="relative">
          <div className="border border-gray-300 rounded-lg overflow-hidden">
            <img
              src={previewUrl}
              alt="News article preview"
              className="w-full h-48 object-cover"
              style={{ aspectRatio }}
            />
          </div>
          <button
            type="button"
            onClick={handleRemoveImage}
            disabled={isUploading}
            className="absolute top-2 right-2 bg-red-600 text-white rounded-full p-1 hover:bg-red-700 disabled:opacity-50"
          >
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
      )}

      <UniversalImageUploader
        uploadType="news"
        entityId={articleId}
        onUploadComplete={handleUploadComplete}
        onUploadStart={handleUploadStart}
        options={{
          maxSize: uploadConfig.news.maxSize,
          allowedTypes: uploadConfig.news.allowedTypes,
          width: 1200,
          height: Math.round(1200 / aspectRatio),
          quality: uploadConfig.news.quality,
          processImage: uploadConfig.news.processImage,
        }}
      />

      <div className="text-sm text-gray-500 space-y-1">
        <p>
          • Recommended size: 1200x{Math.round(1200 / aspectRatio)}px (
          {aspectRatio === 16 / 9 ? "16:9" : "custom"} aspect ratio)
        </p>
        <p>
          • Max file size:{" "}
          {(uploadConfig.news.maxSize / (1024 * 1024)).toFixed(1)}MB
        </p>
        <p>
          • Accepted formats:{" "}
          {uploadConfig.news.allowedTypes
            .map((type) => type.split("/")[1].toUpperCase())
            .join(", ")}
        </p>
        <p>• Images will be automatically optimized for web delivery</p>
      </div>

      {isUploading && (
        <div className="mt-2 text-sm text-blue-600 flex items-center">
          <svg
            className="animate-spin -ml-1 mr-2 h-4 w-4"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
          Processing news image...
        </div>
      )}
    </div>
  );
};

export default NewsImageUploaderV2;
