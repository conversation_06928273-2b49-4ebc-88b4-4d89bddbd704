import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth";

export const dynamic = 'force-dynamic';

export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const currentUser = await getCurrentUser(request);

    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Check if user is a land steward or admin
    if (!currentUser.isLandSteward && !currentUser.isAdmin) {
      return NextResponse.json(
        { error: "Land Steward or Admin permissions required" },
        { status: 403 }
      );
    }

    const { id } = params;
    const { newCaptainId } = await request.json();

    if (!newCaptainId) {
      return NextResponse.json(
        { error: "New captain ID is required" },
        { status: 400 }
      );
    }

    // Check if ship exists
    const ship = await prisma.ship.findUnique({
      where: { id },
      include: {
        captain: {
          select: {
            id: true,
            username: true,
            displayName: true,
          },
        },
      },
    });

    if (!ship) {
      return NextResponse.json(
        { error: "Ship not found" },
        { status: 404 }
      );
    }

    // Check if new captain exists and is a member of the ship
    const newCaptain = await prisma.user.findUnique({
      where: { id: newCaptainId },
      select: {
        id: true,
        username: true,
        displayName: true,
        avatar: true,
        email: true,
      },
    });

    if (!newCaptain) {
      return NextResponse.json(
        { error: "New captain user not found" },
        { status: 404 }
      );
    }

    // Check if new captain is a member of this ship
    const membershipCheck = await prisma.shipMember.findFirst({
      where: {
        shipId: id,
        userId: newCaptainId,
        status: 'active',
      },
    });

    if (!membershipCheck) {
      return NextResponse.json(
        { error: "New captain must be an active member of the ship" },
        { status: 400 }
      );
    }

    await prisma.$transaction(async (tx) => {
      // Change ship captain
      await tx.ship.update({
        where: { id },
        data: { captainId: newCaptainId },
      });

      // If the old captain is still a member, keep them as a regular member
      // If the new captain was a member, they'll remain but now as captain
      // No need to modify memberships as the captain can still be listed as a member
    });

    return NextResponse.json({
      message: "Captain changed successfully",
      oldCaptain: ship.captain,
      newCaptain,
    });
  } catch (error) {
    console.error("Error changing ship captain:", error);
    return NextResponse.json(
      { error: "Failed to change ship captain" },
      { status: 500 }
    );
  }
}