import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser, userHasRole } from "@/lib/auth";
import { TicketStatus } from "@prisma/client";
import { generateMultipleRedemptionCodes } from "@/lib/redemptionCodes";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// GET /api/sales/products - List all products (sales manager)
export async function GET(req: NextRequest) {
  try {
    // Check if user is authenticated and has sales manager role
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 },
      );
    }

    const hasSalesRole = await userHasRole(req, "salesManager");
    if (!hasSalesRole) {
      return NextResponse.json(
        { error: "Unauthorized - Sales Manager role required" },
        { status: 403 },
      );
    }

    // Get query parameters
    const { searchParams } = new URL(req.url);
    const categoryId = searchParams.get("categoryId");
    const eventId = searchParams.get("eventId");
    const isActive = searchParams.get("isActive") === "true";

    // Build filter
    const filter: any = {};
    if (categoryId) filter.categoryId = categoryId;
    if (eventId) filter.eventId = eventId;
    if (searchParams.has("isActive")) filter.isActive = isActive;

    // Get products with filtering
    const products = await prisma.product.findMany({
      where: filter,
      include: {
        category: true,
        event: {
          select: {
            id: true,
            name: true,
            startDate: true,
            endDate: true,
            capacity: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return NextResponse.json({ products });
  } catch (error) {
    console.error("Error fetching products:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}

// POST /api/sales/products - Create a new product (sales manager)
export async function POST(req: NextRequest) {
  try {
    // Check if user is authenticated and has sales manager role
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 },
      );
    }

    const hasSalesRole = await userHasRole(req, "salesManager");
    if (!hasSalesRole) {
      return NextResponse.json(
        { error: "Unauthorized - Sales Manager role required" },
        { status: 403 },
      );
    }

    // Get request body
    const {
      name,
      description,
      shortDescription,
      price,
      image,
      isActive,
      affectsCapacity,
      inventory,
      categoryId,
      eventId,
      isFree,
    } = await req.json();

    // Validate required fields
    if (!name || !categoryId || price === undefined) {
      return NextResponse.json(
        { error: "Name, categoryId, and price are required" },
        { status: 400 },
      );
    }

    // Validate price
    if (isNaN(price) || price < 0) {
      return NextResponse.json(
        { error: "Price must be a non-negative number" },
        { status: 400 },
      );
    }

    // Check if category exists
    const category = await prisma.productCategory.findUnique({
      where: { id: categoryId },
    });

    if (!category) {
      return NextResponse.json(
        { error: "Category not found" },
        { status: 404 },
      );
    }

    // Check if event exists if eventId is provided
    if (eventId) {
      const event = await prisma.event.findUnique({
        where: { id: eventId },
      });

      if (!event) {
        return NextResponse.json({ error: "Event not found" }, { status: 404 });
      }
    }

    // Validate inventory if provided
    let parsedInventory = null;
    if (inventory !== undefined && inventory !== null && inventory !== "") {
      try {
        parsedInventory = parseInt(inventory);
        if (isNaN(parsedInventory)) {
          return NextResponse.json(
            { error: "Inventory must be a valid number" },
            { status: 400 },
          );
        }
      } catch (error) {
        console.error("Error parsing inventory:", error);
        return NextResponse.json(
          { error: "Inventory must be a valid number" },
          { status: 400 },
        );
      }
    }

    // Create the product with automatic redemption code generation
    const result = await prisma.$transaction(async (tx) => {
      // Create the product
      const product = await tx.product.create({
        data: {
          name,
          description,
          shortDescription,
          price: parseFloat(price),
          image,
          isActive: isActive !== undefined ? isActive : true,
          affectsCapacity:
            affectsCapacity !== undefined ? affectsCapacity : true,
          inventory: parsedInventory,
          categoryId,
          eventId: eventId || null,
          isFree: isFree || false,
        },
      });

      // Auto-generate redemption codes if product is free and has inventory
      if (isFree && parsedInventory && parsedInventory > 0) {
        const codes = generateMultipleRedemptionCodes(parsedInventory);
        const redemptionCodes = codes.map(code => ({
          code,
          productId: product.id,
        }));

        await tx.redemptionCode.createMany({
          data: redemptionCodes,
        });

        // For free products, we need to "reserve" inventory immediately
        // This is done by creating tickets in HELD status that represent the codes
        const tickets = Array(parsedInventory)
          .fill(null)
          .map(() => ({
            productId: product.id,
            status: TicketStatus.HELD, // Mark as held to reduce available inventory
          }));

        await tx.ticket.createMany({
          data: tickets,
        });
      }

      return product;
    });

    return NextResponse.json({ product: result }, { status: 201 });
  } catch (error) {
    console.error("Error creating product:", error);

    // Check for Prisma-specific errors
    if (error && typeof error === "object" && "code" in error) {
      // Handle specific Prisma error codes
      if (error.code === "P2003") {
        return NextResponse.json(
          {
            error:
              "Foreign key constraint failed. Check that the category and event exist.",
          },
          { status: 400 },
        );
      }

      if (error.code === "P2002") {
        return NextResponse.json(
          { error: "A unique constraint would be violated." },
          { status: 400 },
        );
      }
    }

    const errorMessage =
      error instanceof Error ? error.message : "Unknown error occurred";

    return NextResponse.json(
      {
        error: "Internal Server Error",
        details: errorMessage,
      },
      { status: 500 },
    );
  }
}
