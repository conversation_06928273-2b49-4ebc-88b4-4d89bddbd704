import prisma from "../lib/prisma";
import { randomBytes } from "crypto";
import bcrypt from "bcryptjs";
import { sendEmail, EmailData } from "../lib/email";

// Verification code types
export type VerificationType =
  | "EMAIL_VERIFICATION"
  | "PASSWORD_RESET"
  | "ACCOUNT_SECURITY";

// Expiry times in minutes
const EXPIRY_TIMES = {
  EMAIL_VERIFICATION: 60, // 1 hour
  PASSWORD_RESET: 30, // 30 minutes
  ACCOUNT_SECURITY: 15, // 15 minutes
};

/**
 * Generate and store a verification code for a user
 */
export async function generateVerificationCode(
  userId: string,
  type: VerificationType,
  email: string,
): Promise<string> {
  // Generate a random 6-digit code
  const code = randomBytes(3).toString("hex").toUpperCase();

  // Calculate expiry time
  const expiryMinutes = EXPIRY_TIMES[type] || 30;
  const expiresAt = new Date();
  expiresAt.setMinutes(expiresAt.getMinutes() + expiryMinutes);

  // Store the code in the database
  await prisma.verificationCode.upsert({
    where: {
      userId_type: {
        userId,
        type,
      },
    },
    update: {
      code,
      expiresAt,
      attempts: 0,
      verified: false,
    },
    create: {
      userId,
      type,
      code,
      expiresAt,
      attempts: 0,
      verified: false,
    },
  });

  // Send the code via email
  await sendVerificationEmail(email, code, type, expiryMinutes);

  return code;
}

/**
 * Verify a code provided by a user
 */
export async function verifyCode(
  userId: string,
  type: VerificationType,
  code: string,
): Promise<boolean> {
  // Get the verification record
  const verification = await prisma.verificationCode.findUnique({
    where: {
      userId_type: {
        userId,
        type,
      },
    },
  });

  // If no verification found or already verified
  if (!verification || verification.verified) {
    return false;
  }

  // Check if code is expired
  if (verification.expiresAt < new Date()) {
    return false;
  }

  // Increment attempts
  await prisma.verificationCode.update({
    where: {
      id: verification.id,
    },
    data: {
      attempts: {
        increment: 1,
      },
    },
  });

  // Check if too many attempts (max 5)
  if (verification.attempts >= 4) {
    // This will be the 5th attempt
    return false;
  }

  // Check if code matches
  if (verification.code !== code.toUpperCase()) {
    return false;
  }

  // Mark as verified
  await prisma.verificationCode.update({
    where: {
      id: verification.id,
    },
    data: {
      verified: true,
    },
  });

  return true;
}

/**
 * Check if a user has a verified code of a specific type
 */
export async function hasVerifiedCode(
  userId: string,
  type: VerificationType,
): Promise<boolean> {
  const verification = await prisma.verificationCode.findUnique({
    where: {
      userId_type: {
        userId,
        type,
      },
    },
  });

  return !!verification && verification.verified;
}

/**
 * Send a verification email
 */
async function sendVerificationEmail(
  email: string,
  code: string,
  type: VerificationType,
  expiryMinutes: number,
): Promise<void> {
  let subject = "Your Verification Code";
  let text = `Your verification code is: ${code}. This code will expire in ${expiryMinutes} minutes.`;
  let html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <div style="background-color: #2C2F33; color: white; padding: 20px; text-align: center;">
        <h1 style="margin: 0; color: #7289DA;">Bank of Styx</h1>
      </div>
      <div style="padding: 20px; background-color: #36393F; color: #DCDDDE;">
        <h2 style="color: white;">Your Verification Code</h2>
        <p>Please use the following code to verify your account:</p>
        <div style="background-color: #2C2F33; padding: 15px; text-align: center; margin: 20px 0; border-radius: 5px;">
          <span style="font-size: 24px; font-weight: bold; letter-spacing: 5px; color: #7289DA;">${code}</span>
        </div>
        <p>This code will expire in <strong>${expiryMinutes} minutes</strong>.</p>
        <p>If you did not request this code, please ignore this email.</p>
      </div>
      <div style="background-color: #2C2F33; color: #72767D; padding: 15px; text-align: center; font-size: 12px;">
        <p>© ${new Date().getFullYear()} Bank of Styx. All rights reserved.</p>
      </div>
    </div>
  `;

  switch (type) {
    case "EMAIL_VERIFICATION":
      subject = "Verify Your Email Address";
      text = `Please verify your email address by entering this code: ${code}. Please manually copy this code and paste it into the verification field on the website. This code will expire in ${expiryMinutes} minutes.`;
      html = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background-color: #2C2F33; color: white; padding: 20px; text-align: center;">
            <h1 style="margin: 0; color: #7289DA;">Bank of Styx</h1>
          </div>
          <div style="padding: 20px; background-color: #36393F; color: #DCDDDE;">
            <h2 style="color: white;">Verify Your Email Address</h2>
            <p>Please use the following code to verify your email address:</p>
            <div style="background-color: #2C2F33; padding: 15px; text-align: center; margin: 20px 0; border-radius: 5px;">
              <span style="font-size: 24px; font-weight: bold; letter-spacing: 5px; color: #7289DA;">${code}</span>
            </div>
            <p>Please manually copy this code and paste it into the verification field on the website.</p>
            <p>This code will expire in <strong>${expiryMinutes} minutes</strong>.</p>
            <p>If you did not request this code, please ignore this email.</p>
          </div>
          <div style="background-color: #2C2F33; color: #72767D; padding: 15px; text-align: center; font-size: 12px;">
            <p>© ${new Date().getFullYear()} Bank of Styx. All rights reserved.</p>
          </div>
        </div>
      `;
      break;
    case "PASSWORD_RESET":
      subject = "Password Reset Request";
      text = `You requested a password reset. Your verification code is: ${code}. This code will expire in ${expiryMinutes} minutes.`;
      html = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background-color: #2C2F33; color: white; padding: 20px; text-align: center;">
            <h1 style="margin: 0; color: #7289DA;">Bank of Styx</h1>
          </div>
          <div style="padding: 20px; background-color: #36393F; color: #DCDDDE;">
            <h2 style="color: white;">Password Reset Request</h2>
            <p>You requested a password reset. Please use the following code:</p>
            <div style="background-color: #2C2F33; padding: 15px; text-align: center; margin: 20px 0; border-radius: 5px;">
              <span style="font-size: 24px; font-weight: bold; letter-spacing: 5px; color: #7289DA;">${code}</span>
            </div>
            <p>This code will expire in <strong>${expiryMinutes} minutes</strong>.</p>
            <p>If you did not request this password reset, please ignore this email.</p>
          </div>
          <div style="background-color: #2C2F33; color: #72767D; padding: 15px; text-align: center; font-size: 12px;">
            <p>© ${new Date().getFullYear()} Bank of Styx. All rights reserved.</p>
          </div>
        </div>
      `;
      break;
    case "ACCOUNT_SECURITY":
      subject = "Account Security Verification";
      text = `For security purposes, please verify your account by entering this code: ${code}. This code will expire in ${expiryMinutes} minutes.`;
      html = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background-color: #2C2F33; color: white; padding: 20px; text-align: center;">
            <h1 style="margin: 0; color: #7289DA;">Bank of Styx</h1>
          </div>
          <div style="padding: 20px; background-color: #36393F; color: #DCDDDE;">
            <h2 style="color: white;">Account Security Verification</h2>
            <p>For security purposes, please verify your account using the following code:</p>
            <div style="background-color: #2C2F33; padding: 15px; text-align: center; margin: 20px 0; border-radius: 5px;">
              <span style="font-size: 24px; font-weight: bold; letter-spacing: 5px; color: #7289DA;">${code}</span>
            </div>
            <p>This code will expire in <strong>${expiryMinutes} minutes</strong>.</p>
            <p>If you did not request this verification, please contact support immediately.</p>
          </div>
          <div style="background-color: #2C2F33; color: #72767D; padding: 15px; text-align: center; font-size: 12px;">
            <p>© ${new Date().getFullYear()} Bank of Styx. All rights reserved.</p>
          </div>
        </div>
      `;
      break;
  }

  await sendEmail({
    to: email,
    subject,
    text,
    html,
  });
}

/**
 * Generate a secure random password
 */
export function generateSecurePassword(): string {
  // Generate a random 12-character password with letters, numbers, and symbols
  const length = 12;
  const charset =
    "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+";
  let password = "";

  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * charset.length);
    password += charset[randomIndex];
  }

  return password;
}

/**
 * Generate and store a temporary password for a user
 */
export async function generateTemporaryPassword(
  userId: string,
  email: string,
): Promise<string> {
  // Generate a secure password
  const password = generateSecurePassword();

  // Hash the password
  const salt = await bcrypt.genSalt(10);
  const passwordHash = await bcrypt.hash(password, salt);

  // Calculate expiry time (1 day)
  const expiresAt = new Date();
  expiresAt.setDate(expiresAt.getDate() + 1);

  // Store the temporary password
  await prisma.temporaryPassword.upsert({
    where: {
      userId,
    },
    update: {
      passwordHash,
      expiresAt,
      used: false,
    },
    create: {
      userId,
      passwordHash,
      expiresAt,
      used: false,
    },
  });

  // Send the password via email
  await sendTemporaryPasswordEmail(email, password);

  return password;
}

/**
 * Send a temporary password email
 */
async function sendTemporaryPasswordEmail(
  email: string,
  password: string,
): Promise<void> {
  const subject = "Your Temporary Password";
  const text = `
    Your account has been set up with a temporary password: ${password}

    This password will expire in 24 hours. Please log in and change your password as soon as possible.

    For security reasons, we recommend not sharing this password with anyone.
  `;

  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <div style="background-color: #2C2F33; color: white; padding: 20px; text-align: center;">
        <h1 style="margin: 0; color: #7289DA;">Bank of Styx</h1>
      </div>
      <div style="padding: 20px; background-color: #36393F; color: #DCDDDE;">
        <h2 style="color: white;">Your Temporary Password</h2>
        <p>Your account has been set up with a temporary password:</p>
        <div style="background-color: #2C2F33; padding: 15px; text-align: center; margin: 20px 0; border-radius: 5px;">
          <span style="font-size: 18px; font-family: monospace; font-weight: bold; color: #7289DA;">${password}</span>
        </div>
        <p><strong>This password will expire in 24 hours.</strong></p>
        <p>Please log in and change your password as soon as possible.</p>
        <p>For security reasons, we recommend not sharing this password with anyone.</p>
      </div>
      <div style="background-color: #2C2F33; color: #72767D; padding: 15px; text-align: center; font-size: 12px;">
        <p>© ${new Date().getFullYear()} Bank of Styx. All rights reserved.</p>
      </div>
    </div>
  `;

  await sendEmail({
    to: email,
    subject,
    text,
    html,
  });
}

/**
 * Apply a temporary password to a user account
 */
export async function applyTemporaryPassword(userId: string): Promise<boolean> {
  // Get the temporary password
  const tempPassword = await prisma.temporaryPassword.findUnique({
    where: {
      userId,
    },
  });

  // If no temporary password found or already used or expired
  if (
    !tempPassword ||
    tempPassword.used ||
    tempPassword.expiresAt < new Date()
  ) {
    return false;
  }

  // Update the user's password hash
  await prisma.user.update({
    where: {
      id: userId,
    },
    data: {
      passwordHash: tempPassword.passwordHash,
    },
  });

  // Mark the temporary password as used
  await prisma.temporaryPassword.update({
    where: {
      id: tempPassword.id,
    },
    data: {
      used: true,
    },
  });

  return true;
}
