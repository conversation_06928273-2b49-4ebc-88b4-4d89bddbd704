# News Content Management System

## Overview
The News Content Management System is a comprehensive content platform that handles article creation, editing, publishing, and consumption. It features rich text editing with ReactQuill, role-based content management, category organization, analytics tracking, and a sophisticated publishing workflow with draft/publish states and featured content promotion.

## Directory Structure
```
news-content-management-system/
├── src/app/news/                      # News frontend and admin interfaces
│   ├── page.tsx                       # Public news listing with search and filtering
│   ├── layout.tsx                     # News section layout with QueryProvider
│   ├── [slug]/                        # Individual article pages
│   │   └── page.tsx                   # Article display with social sharing
│   └── dashboard/                     # News management dashboard
│       ├── page.tsx                   # Analytics dashboard with statistics
│       ├── articles/                  # Article management
│       │   ├── page.tsx               # Article listing with CRUD operations
│       │   ├── new/                   # Article creation
│       │   └── [id]/                  # Article editing interface
│       ├── categories/                # Category management
│       │   └── page.tsx               # Category CRUD operations
│       └── featured/                  # Featured content management
│           └── page.tsx               # Featured article promotion
├── src/app/api/news/                  # News API endpoints
│   ├── public/                        # Public news APIs
│   │   ├── route.ts                   # Public article listing
│   │   └── [slug]/                    # Individual article by slug
│   ├── articles/                      # Administrative article management
│   │   ├── route.ts                   # Article CRUD operations
│   │   └── [id]/                      # Individual article management
│   ├── categories/                    # Category management APIs
│   └── analytics/                     # News analytics and reporting
├── src/components/news/               # News UI components
│   ├── NewsEditor.tsx                 # ReactQuill rich text editor
│   ├── NewsArticleCard.tsx            # Reusable article display card
│   ├── NewsDashboardLayout.tsx        # Admin layout wrapper
│   ├── CategorySelector.tsx           # Category selection with inline creation
│   ├── FeaturedImageUploader.tsx      # Image upload with preview
│   ├── ArticleViewTracker.js          # Analytics tracking component
│   └── ArticlePreviewModal.tsx        # Article preview before publishing
├── src/services/                      # News business logic
│   ├── newsService.ts                 # Administrative news operations
│   └── publicNewsService.ts           # Public news consumption
├── src/hooks/                         # News React hooks
│   ├── useNews.ts                     # Administrative news management
│   ├── usePublicNews.ts               # Public news browsing
│   └── useStateBasedNews.ts           # Optimized caching hooks
└── src/utils/                         # News utilities
    └── sanitizeHtml.ts                # Content sanitization for security
```

## Key Files & Components

### Frontend Components
- **`NewsEditor.tsx`** - ReactQuill-based rich text editor with dynamic loading, comprehensive toolbar, and content sanitization
- **`NewsArticleCard.tsx`** - Reusable article display component with image support, excerpt preview, and responsive design
- **`NewsDashboardLayout.tsx`** - Administrative layout wrapper with navigation, user context, and permission checking
- **`CategorySelector.tsx`** - Advanced dropdown for category selection with inline creation and real-time validation
- **`FeaturedImageUploader.tsx`** - Image upload component with preview, validation, and file size management
- **`ArticleViewTracker.js`** - Analytics tracking component for article view counting and user engagement
- **`ArticlePreviewModal.tsx`** - Preview modal system for articles before publishing with full rendering

### API Endpoints
- **`GET /api/news/public`** - Paginated public article listing with search, category filtering, and featured content
- **`GET /api/news/public/[slug]`** - Individual article by slug with automatic view counting and related articles
- **`GET /api/news/articles`** - Administrative article listing with all statuses and bulk operations support
- **`POST /api/news/articles`** - Create new articles with rich text content, category assignment, and slug generation
- **`PUT /api/news/articles/[id]`** - Update existing articles with full content modification and status management
- **`DELETE /api/news/articles/[id]`** - Delete articles with proper cleanup and relationship management
- **`PATCH /api/news/articles/[id]/status`** - Toggle article status between draft, published, and paused states
- **`PATCH /api/news/articles/[id]/featured`** - Toggle featured status for homepage promotion and visibility
- **`GET /api/news/categories`** - Category listing with article counts and hierarchical organization
- **`POST /api/news/categories`** - Create categories with unique name validation and slug generation
- **`GET /api/news/analytics`** - Comprehensive analytics including top articles, category distribution, and engagement metrics

### Database Models
- **`NewsArticle`** - Core article model with rich text content, publishing workflow, analytics tracking, and SEO optimization
- **`NewsCategory`** - Category organization with unique naming, descriptions, and article relationship tracking
- **`User`** - Integration with user system for author attribution, editor permissions, and content ownership

### Services
- **`newsService.ts`** - Administrative API service with full CRUD operations, status management, and analytics integration
- **`publicNewsService.ts`** - Public-facing API service for published content consumption and filtering

### Hooks
- **`useNews()`** - Administrative news management with optimistic updates, caching, and error handling
- **`usePublicNews()`** - Public news browsing with pagination, search, and filtering capabilities
- **`useStateBasedNews()`** - Optimized caching hooks for improved performance and state management

## Common Tasks

### Task 1: How to Create and Publish an Article
1. Editor navigates to `/news/dashboard/articles/new` (requires `isEditor` role)
2. Fills in article title, which auto-generates SEO-friendly slug
3. Writes content using ReactQuill rich text editor with formatting options
4. Adds article excerpt for preview cards and search results
5. Selects existing category or creates new one inline via `CategorySelector`
6. Uploads featured image using `FeaturedImageUploader` with preview
7. Saves as draft for later editing or publishes immediately
8. Published articles automatically set `publishedAt` timestamp
9. Optional promotion to featured status for homepage visibility
10. Real-time analytics begin tracking views and engagement

### Task 2: How to Manage Content Categories
1. Editor accesses `/news/dashboard/categories` for category management
2. Views existing categories with article counts and descriptions
3. Creates new categories with unique names and optional descriptions
4. System automatically generates URL-friendly slugs for categories
5. Edits category information with real-time validation
6. Cannot delete categories that have associated articles
7. Categories with articles display count and provide navigation to articles
8. Categories appear in public filtering and navigation automatically
9. Inline category creation available during article editing process

### Task 3: How to Browse and Read Articles (Public)
1. Users visit `/news` for public article browsing interface
2. Articles displayed with pagination, search, and category filtering
3. Featured articles highlighted at top of listing for visibility
4. Click on article card navigates to full article at `/news/[slug]`
5. Article view automatically increments view counter for analytics
6. Social sharing buttons available for Reddit and Bluesky platforms
7. Related articles suggested based on category matching
8. Mobile-optimized reading experience with responsive design
9. SEO-friendly URLs and meta tags for search engine optimization

## API Integration

### Authentication Requirements
- **Public browsing**: No authentication required for published article access
- **Article viewing**: Public access with automatic view counting
- **Content creation**: Valid JWT token + `isEditor` role required for all administrative functions
- **Category management**: Editor role required for category CRUD operations
- **Analytics access**: Editor role required for performance metrics and statistics
- **Token format**: `Bearer <jwt-token>` in Authorization header

### Request/Response Examples
```typescript
// Create Article Request
interface CreateArticleRequest {
  title: string;
  content: string;        // Rich text HTML content
  excerpt: string;        // Article summary for cards
  categoryId: string;     // Category assignment
  image?: string;         // Featured image URL
  status: 'draft' | 'published';
  featured?: boolean;     // Featured content promotion
}

// Article Response
interface ArticleResponse {
  id: string;
  title: string;
  content: string;
  excerpt: string;
  image?: string;
  slug: string;           // SEO-friendly URL slug
  status: 'draft' | 'published' | 'paused';
  featured: boolean;
  views: number;          // Analytics view count
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;   // Publication timestamp
  author: {
    id: string;
    username: string;
    displayName: string;
  };
  category: {
    id: string;
    name: string;
    slug: string;
    description?: string;
  };
}

// Public Article Listing Request
interface PublicArticlesRequest {
  page?: number;          // Pagination
  limit?: number;         // Items per page
  category?: string;      // Category filtering
  search?: string;        // Title/content search
  featured?: boolean;     // Featured content only
  sortBy?: 'createdAt' | 'publishedAt' | 'views' | 'title';
  order?: 'asc' | 'desc';
}

// Article Analytics Response
interface ArticleAnalyticsResponse {
  totalArticles: number;
  publishedArticles: number;
  draftArticles: number;
  pausedArticles: number;
  totalViews: number;
  topArticles: Array<{
    id: string;
    title: string;
    slug: string;
    views: number;
    publishedAt: string;
  }>;
  articlesByCategory: Array<{
    category: {
      id: string;
      name: string;
    };
    count: number;
    totalViews: number;
  }>;
  recentActivity: Array<{
    id: string;
    title: string;
    action: 'created' | 'published' | 'updated';
    timestamp: string;
    author: string;
  }>;
}

// Category Management Request
interface CreateCategoryRequest {
  name: string;           // Unique category name
  description?: string;   // Optional category description
}

// Category Response
interface CategoryResponse {
  id: string;
  name: string;
  slug: string;           // URL-friendly identifier
  description?: string;
  articleCount: number;   // Number of articles in category
  createdAt: string;
  updatedAt: string;
}
```

## Database Schema

### Primary Models
```sql
-- NewsArticle table (core article content)
CREATE TABLE NewsArticle (
  id VARCHAR(191) PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  content LONGTEXT NOT NULL,          -- Rich text HTML content
  excerpt TEXT NOT NULL,              -- Article summary
  image VARCHAR(255),                 -- Featured image URL
  slug VARCHAR(255) UNIQUE NOT NULL,  -- SEO-friendly URL slug
  status ENUM('draft', 'published', 'paused') DEFAULT 'draft',
  featured BOOLEAN DEFAULT false,     -- Featured content flag
  views INT DEFAULT 0,               -- Analytics view counter
  authorId VARCHAR(191) NOT NULL,    -- Content author
  categoryId VARCHAR(191) NOT NULL,  -- Category assignment
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  publishedAt DATETIME,              -- Publication timestamp
  FOREIGN KEY (authorId) REFERENCES User(id),
  FOREIGN KEY (categoryId) REFERENCES NewsCategory(id),
  INDEX idx_article_author (authorId),
  INDEX idx_article_category (categoryId),
  INDEX idx_article_status (status),
  INDEX idx_article_featured (featured),
  INDEX idx_article_published (publishedAt),
  INDEX idx_article_slug (slug),
  INDEX idx_article_views (views)
);

-- NewsCategory table (content organization)
CREATE TABLE NewsCategory (
  id VARCHAR(191) PRIMARY KEY,
  name VARCHAR(255) UNIQUE NOT NULL,  -- Unique category name
  slug VARCHAR(255) UNIQUE NOT NULL,  -- URL-friendly identifier
  description TEXT,                   -- Optional category description
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_category_name (name),
  INDEX idx_category_slug (slug)
);

-- User table extensions (editor permissions)
ALTER TABLE User ADD COLUMN isEditor BOOLEAN DEFAULT false;
```

### Relationships
- **NewsArticle ↔ User**: Many-to-one (articles have authors)
- **NewsArticle ↔ NewsCategory**: Many-to-one (articles belong to categories)
- **NewsCategory ↔ NewsArticle**: One-to-many (categories contain multiple articles)
- **User ↔ Authentication**: Integration with JWT authentication system

## Related Features
- **[Authentication System](./authentication-system.md)** - Editor role-based access control and JWT authentication for content management
- **[Upload System](./upload-system.md)** - Image upload functionality for featured article images with validation and processing
- **[Notification System](./notification-system.md)** - News subscription system and content update notifications
- **[User Management System](./user-management-system.md)** - Author attribution and editor permission management

## User Roles & Permissions
- **Public Users**: Article browsing, reading, and social sharing without authentication requirements
- **Authenticated Users**: Enhanced reading experience with view tracking and potential subscription features
- **Editors**: Complete content management including article creation, editing, publishing, category management, and analytics access
- **Admins**: Full system access including user role management and system configuration for news platform

## Recent Changes
- **v7.1.0** - Enhanced analytics dashboard with comprehensive article performance metrics and category distribution analysis
- **v7.0.0** - ReactQuill rich text editor integration with dynamic loading and comprehensive formatting toolbar
- **v6.9.0** - Featured article system implementation with homepage promotion and visibility controls
- **v6.8.0** - Category management system with inline creation and hierarchical organization
- **v6.7.0** - Social media sharing integration with Reddit and Bluesky platform support
- **v6.6.0** - Article preview system with modal display before publishing
- **v6.5.0** - SEO optimization with automatic slug generation and meta tag management

## Troubleshooting

### Common Issues
1. **Rich text editor not loading**: Check ReactQuill dynamic import and ensure component mounts properly. Verify `use client` directive.
2. **Slug conflicts**: System auto-generates unique slugs, but manual editing may create conflicts. Check slug uniqueness validation.
3. **Image upload failures**: Verify upload endpoint accessibility, file size limits (10MB), and supported formats (JPEG, PNG, GIF, WEBP).
4. **Permission errors**: Ensure user has `isEditor` role and valid JWT token. Check API endpoint authentication middleware.
5. **Category deletion blocked**: Categories with associated articles cannot be deleted. Reassign or delete articles first.
6. **Draft articles appearing publicly**: Verify status filtering in public API endpoints excludes draft and paused content.

### Debug Information
- **Log locations**: Content management logs in server console, editor actions logged with user context
- **Environment variables**: Upload service configuration, rich text editor settings, analytics tracking
- **Database queries**: Use Prisma Studio to inspect article relationships, category assignments, and view counts
- **Debugging commands**:
  ```bash
  # Check article status and relationships
  curl -H "Authorization: Bearer <token>" http://localhost:3000/api/news/articles/{id}
  
  # Verify public article listing
  curl http://localhost:3000/api/news/public?page=1&limit=10
  
  # Check category article counts
  curl http://localhost:3000/api/news/categories
  
  # View analytics data
  curl -H "Authorization: Bearer <token>" http://localhost:3000/api/news/analytics
  ```

### Performance Considerations
- **Rich Text Editor**: ReactQuill loaded dynamically to avoid SSR issues and improve initial page load
- **Image Optimization**: Featured images compressed and cached for optimal loading performance
- **Database Indexing**: Comprehensive indexes on frequently queried fields (status, category, publication date, views)
- **Pagination**: Large article lists use efficient pagination to maintain performance
- **Caching Strategy**: React Query provides optimized caching with automatic invalidation on updates
- **Content Sanitization**: HTML sanitization performed on content to prevent XSS while maintaining formatting

### Security Measures
- **Content Sanitization**: All rich text content sanitized to prevent XSS attacks while preserving formatting
- **Role-based Access**: Strict editor permission checking on all administrative functions
- **Input Validation**: Comprehensive validation on article titles, content, and category data
- **Slug Security**: Auto-generated slugs prevent URL manipulation and ensure unique, safe identifiers
- **Image Upload Security**: File type and size validation for uploaded featured images
- **Authentication Integration**: JWT token validation with role checking on all protected endpoints

### Integration Patterns
- **Authentication Integration**: Seamless integration with JWT authentication system and role-based access control
- **Upload System Integration**: Featured image uploads utilize existing upload infrastructure with validation
- **Analytics Integration**: Article view tracking integrates with broader analytics and user engagement systems
- **Notification Integration**: News updates can trigger notifications to subscribed users via existing notification system
- **State Management**: React Query provides optimized state management with caching and real-time updates

### Content Management Best Practices
- **SEO Optimization**: Automatic slug generation ensures search engine friendly URLs
- **Content Organization**: Category system provides logical content organization and navigation
- **Publishing Workflow**: Draft/published status system enables content review before publication
- **Analytics Tracking**: Comprehensive view tracking and performance metrics for content optimization
- **Mobile Optimization**: Responsive design ensures excellent experience across all device types
- **Social Sharing**: Integrated sharing buttons improve content distribution and engagement

---

**File Locations:**
- Pages: `/src/app/news/`
- Components: `/src/components/news/`
- API: `/src/app/api/news/`
- Services: `/src/services/newsService.ts`, `/src/services/publicNewsService.ts`
- Hooks: `/src/hooks/useNews.ts`, `/src/hooks/usePublicNews.ts`
- Utilities: `/src/utils/sanitizeHtml.ts`
- Types: `/src/types/news.ts`