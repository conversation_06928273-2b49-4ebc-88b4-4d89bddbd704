# API Services

Standalone API modules for the Bank of Styx platform that can be deployed independently from the main application.

## Structure

- **auth/** - Authentication API endpoints and services for login, registration, and OAuth integration
- **middleware.ts** - Shared middleware for API request handling, CORS, and authentication
- **node_modules/** - API-specific dependencies

These API services provide backend functionality that can be scaled and deployed separately from the main Next.js application.
