"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { CaptainDashboardLayout } from "@/components/captain";
import { useCaptainShip } from "@/hooks/captain/useCaptainShip";
import { Button, Card, Modal, Input, Textarea } from "@bank-of-styx/ui";
import { ShipLogoUploader } from "@/components/upload";
import { uploadShipLogo } from "@/lib/shipLogoUpload";
import fetchClient from "@/lib/fetchClient";

interface DeletionRequest {
  id: string;
  status: "pending" | "approved" | "rejected";
  reason?: string;
  message?: string;
  createdAt: string;
  reviewedAt?: string;
  reviewedBy?: {
    displayName: string;
  };
}

export default function CaptainSettingsPage() {
  const { user, isLoading: authLoading, openAuthModal } = useAuth();
  const router = useRouter();
  
  const { ship, isLoading: shipLoading, refetch } = useCaptainShip();
  
  // Local state for settings page
  const [editForm, setEditForm] = useState({
    name: "",
    description: "",
    slogan: "",
    logoFile: null as File | null,
  });
  const [isEditing, setIsEditing] = useState(false);
  const [saving, setSaving] = useState(false);
  
  // Deletion state
  const [showDeletionModal, setShowDeletionModal] = useState(false);
  const [deletionReason, setDeletionReason] = useState("");
  const [deletionRequest, setDeletionRequest] = useState<DeletionRequest | null>(null);
  const [deletionLoading, setDeletionLoading] = useState(false);

  // Authentication check
  React.useEffect(() => {
    if (!authLoading && !user) {
      openAuthModal();
      router.replace("/");
      return;
    }
  }, [user, authLoading, router, openAuthModal]);

  // Initialize form when ship data loads
  useEffect(() => {
    if (ship) {
      setEditForm({
        name: ship.name || "",
        description: ship.description || "",
        slogan: ship.slogan || "",
        logoFile: null,
      });
      fetchDeletionRequest();
    }
  }, [ship]);

  const fetchDeletionRequest = async () => {
    try {
      const data = await fetchClient.get("/api/captain/ship/deletion-request");
      setDeletionRequest(data.deletionRequest);
    } catch (error) {
      console.error("Error fetching deletion request:", error);
    }
  };

  const handleSaveChanges = async () => {
    if (!ship) return;
    
    try {
      setSaving(true);
      let logoPath = ship.logo; // Keep existing logo by default

      // Upload new logo if provided
      if (editForm.logoFile) {
        const uploadResult = await uploadShipLogo(editForm.logoFile);
        
        if (!uploadResult.success) {
          throw new Error(uploadResult.error || "Failed to upload logo");
        }
        
        logoPath = uploadResult.logoUrl;
      }

      await fetchClient.put("/api/captain/ship", {
        name: editForm.name,
        description: editForm.description,
        slogan: editForm.slogan,
        logo: logoPath,
      });

      // Refresh ship data
      await refetch();
      setIsEditing(false);
      setEditForm(prev => ({ ...prev, logoFile: null }));
      
      alert("Ship settings updated successfully!");
    } catch (error: any) {
      console.error("Error saving ship:", error);
      alert(`Error updating ship: ${error.message}`);
    } finally {
      setSaving(false);
    }
  };

  const handleLogoFileSelected = (file: File | null) => {
    setEditForm({ ...editForm, logoFile: file });
  };

  const handleRequestDeletion = async () => {
    try {
      setDeletionLoading(true);
      await fetchClient.post("/api/captain/ship/deletion-request", {
        reason: deletionReason.trim() || undefined,
      });
      
      setShowDeletionModal(false);
      setDeletionReason("");
      fetchDeletionRequest(); // Refresh deletion request status
      
      alert("Ship deletion request submitted successfully!");
    } catch (error: any) {
      console.error("Error requesting ship deletion:", error);
      alert(`Error submitting deletion request: ${error.message}`);
    } finally {
      setDeletionLoading(false);
    }
  };

  const handleCancelDeletionRequest = async () => {
    if (!confirm("Are you sure you want to cancel the deletion request?")) {
      return;
    }

    try {
      await fetchClient.delete("/api/captain/ship/deletion-request");
      fetchDeletionRequest(); // Refresh deletion request status
      
      alert("Deletion request cancelled successfully!");
    } catch (error: any) {
      console.error("Error cancelling deletion request:", error);
      alert(`Error cancelling deletion request: ${error.message}`);
    }
  };

  // Loading state
  if (authLoading || shipLoading) {
    return (
      <div className="min-h-screen bg-secondary-dark flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-400">Loading...</p>
        </div>
      </div>
    );
  }

  if (!ship) {
    return (
      <CaptainDashboardLayout>
        <Card className="p-6 text-center">
          <p className="text-gray-400">Ship not found</p>
        </Card>
      </CaptainDashboardLayout>
    );
  }

  return (
    <CaptainDashboardLayout ship={ship}>
      <div className="space-y-4 sm:space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-xl sm:text-2xl font-bold text-white mb-1">
              Ship Settings
            </h1>
            <p className="text-sm text-gray-400">
              Manage your ship's information and settings
            </p>
          </div>
          <div className="mt-3 sm:mt-0 flex gap-2">
            {isEditing ? (
              <>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => {
                    setIsEditing(false);
                    setEditForm({
                      name: ship.name || "",
                      description: ship.description || "",
                      slogan: ship.slogan || "",
                      logoFile: null,
                    });
                  }}
                  disabled={saving}
                >
                  Cancel
                </Button>
                <Button 
                  variant="primary" 
                  size="sm"
                  onClick={handleSaveChanges}
                  disabled={saving}
                >
                  {saving ? "Saving..." : "Save Changes"}
                </Button>
              </>
            ) : (
              <Button 
                variant="primary" 
                size="sm"
                onClick={() => setIsEditing(true)}
              >
                Edit Ship
              </Button>
            )}
          </div>
        </div>

        {/* Ship Information */}
        <Card className="p-4 sm:p-6">
          <h2 className="text-lg font-semibold text-white mb-4">Ship Information</h2>
          
          <div className="space-y-4">
            {/* Ship Name */}
            <div>
              <label className="block text-gray-300 text-sm font-medium mb-2">Ship Name</label>
              {isEditing ? (
                <Input
                  type="text"
                  value={editForm.name}
                  onChange={(e) => setEditForm({ ...editForm, name: e.target.value })}
                  placeholder="Enter ship name"
                  maxLength={100}
                />
              ) : (
                <p className="text-white bg-secondary-dark/50 p-3 rounded-lg">{ship.name}</p>
              )}
            </div>

            {/* Ship Description */}
            <div>
              <label className="block text-gray-300 text-sm font-medium mb-2">Description</label>
              {isEditing ? (
                <Textarea
                  value={editForm.description}
                  onChange={(e) => setEditForm({ ...editForm, description: e.target.value })}
                  placeholder="Describe your ship..."
                  rows={4}
                  maxLength={500}
                />
              ) : (
                <p className="text-white bg-secondary-dark/50 p-3 rounded-lg min-h-[100px]">
                  {ship.description || "No description provided"}
                </p>
              )}
            </div>

            {/* Ship Slogan */}
            <div>
              <label className="block text-gray-300 text-sm font-medium mb-2">Slogan (Optional)</label>
              {isEditing ? (
                <Input
                  type="text"
                  value={editForm.slogan}
                  onChange={(e) => setEditForm({ ...editForm, slogan: e.target.value })}
                  placeholder="Enter ship slogan"
                  maxLength={200}
                />
              ) : (
                <p className="text-white bg-secondary-dark/50 p-3 rounded-lg">
                  {ship.slogan || "No slogan set"}
                </p>
              )}
            </div>

            {/* Ship Logo */}
            <div>
              <label className="block text-gray-300 text-sm font-medium mb-2">Ship Logo (Optional)</label>
              {isEditing ? (
                <ShipLogoUploader
                  onFileSelected={handleLogoFileSelected}
                  currentLogoUrl={ship.logo}
                  selectedFile={editForm.logoFile}
                />
              ) : (
                <div className="bg-secondary-dark/50 p-3 rounded-lg">
                  {ship.logo ? (
                    <img
                      src={ship.logo}
                      alt={`${ship.name} logo`}
                      className="w-20 h-20 object-cover rounded-lg"
                    />
                  ) : (
                    <p className="text-gray-400">No logo uploaded</p>
                  )}
                </div>
              )}
            </div>
          </div>
        </Card>

        {/* Ship Statistics */}
        <Card className="p-4 sm:p-6">
          <h2 className="text-lg font-semibold text-white mb-4">Ship Statistics</h2>
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-secondary-dark/50 rounded-lg">
              <p className="text-2xl font-bold text-white">{ship.status}</p>
              <p className="text-xs text-gray-400">Status</p>
            </div>
            <div className="text-center p-3 bg-secondary-dark/50 rounded-lg">
              <p className="text-2xl font-bold text-white">
                {new Date(ship.createdAt).getFullYear()}
              </p>
              <p className="text-xs text-gray-400">Founded</p>
            </div>
            <div className="text-center p-3 bg-secondary-dark/50 rounded-lg">
              <p className="text-2xl font-bold text-white">
                {Math.floor((Date.now() - new Date(ship.createdAt).getTime()) / (1000 * 60 * 60 * 24))}
              </p>
              <p className="text-xs text-gray-400">Days Old</p>
            </div>
            <div className="text-center p-3 bg-secondary-dark/50 rounded-lg">
              <p className="text-2xl font-bold text-white">
                {new Date(ship.updatedAt).toLocaleDateString()}
              </p>
              <p className="text-xs text-gray-400">Last Updated</p>
            </div>
          </div>
        </Card>

        {/* Danger Zone */}
        <Card className="p-4 sm:p-6 border-red-500/30 bg-red-900/10">
          <h2 className="text-lg font-semibold text-white mb-4">⚠️ Danger Zone</h2>
          
          {deletionRequest ? (
            <div className={`
              p-4 rounded-lg border mb-4
              ${deletionRequest.status === 'pending' ? 'border-yellow-600 bg-yellow-900/20' :
                deletionRequest.status === 'approved' ? 'border-green-600 bg-green-900/20' :
                deletionRequest.status === 'rejected' ? 'border-red-600 bg-red-900/20' :
                'border-gray-600 bg-gray-900/20'}
            `}>
              <div className="flex items-center gap-2 mb-2">
                <span className={`
                  px-2 py-1 text-xs rounded-full font-medium
                  ${deletionRequest.status === 'pending' ? 'bg-yellow-900 text-yellow-300' :
                    deletionRequest.status === 'approved' ? 'bg-green-900 text-green-300' :
                    deletionRequest.status === 'rejected' ? 'bg-red-900 text-red-300' :
                    'bg-gray-900 text-gray-300'}
                `}>
                  {deletionRequest.status}
                </span>
                <h3 className="text-white font-medium">Ship Deletion Request</h3>
              </div>
              <p className="text-gray-300 text-sm mb-2">
                Submitted: {new Date(deletionRequest.createdAt).toLocaleDateString()}
              </p>
              {deletionRequest.reason && (
                <p className="text-gray-300 text-sm mb-2">
                  <strong>Reason:</strong> {deletionRequest.reason}
                </p>
              )}
              {deletionRequest.message && (
                <p className="text-gray-300 text-sm mb-2">
                  <strong>Admin Response:</strong> {deletionRequest.message}
                </p>
              )}
              {deletionRequest.reviewedAt && (
                <p className="text-gray-300 text-sm mb-2">
                  Reviewed: {new Date(deletionRequest.reviewedAt).toLocaleDateString()}
                  {deletionRequest.reviewedBy && ` by ${deletionRequest.reviewedBy.displayName}`}
                </p>
              )}
              {deletionRequest.status === 'pending' && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCancelDeletionRequest}
                  className="text-red-400 border-red-400 hover:bg-red-400 hover:text-white"
                >
                  Cancel Request
                </Button>
              )}
            </div>
          ) : (
            <div className="space-y-3">
              <p className="text-gray-300 text-sm">
                Request deletion of your ship. This action will be reviewed by a Land Steward.
              </p>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowDeletionModal(true)}
                className="text-red-400 border-red-400 hover:bg-red-400 hover:text-white"
              >
                Request Ship Deletion
              </Button>
            </div>
          )}
        </Card>
      </div>

      {/* Ship Deletion Confirmation Modal */}
      {showDeletionModal && (
        <Modal
          isOpen={showDeletionModal}
          onClose={() => {
            setShowDeletionModal(false);
            setDeletionReason("");
          }}
          title="Request Ship Deletion"
          size="md"
        >
          <div className="space-y-4">
            <div className="p-4 bg-red-900/20 border border-red-500/30 rounded-lg">
              <p className="text-red-300 text-sm">
                <strong>Warning:</strong> You are about to request the permanent deletion of your ship "{ship?.name}". 
                This action cannot be undone and must be approved by a Land Steward.
              </p>
            </div>
            
            <div>
              <label className="block text-gray-300 text-sm font-medium mb-2">
                Reason for deletion (optional)
              </label>
              <Textarea
                value={deletionReason}
                onChange={(e) => setDeletionReason(e.target.value)}
                rows={4}
                placeholder="Please provide a reason for requesting ship deletion..."
              />
            </div>
            
            <div className="flex gap-2 pt-4">
              <Button
                variant="primary"
                onClick={handleRequestDeletion}
                disabled={deletionLoading}
                className="bg-red-600 hover:bg-red-700 border-red-600"
              >
                {deletionLoading ? "Submitting..." : "Submit Deletion Request"}
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  setShowDeletionModal(false);
                  setDeletionReason("");
                }}
                disabled={deletionLoading}
              >
                Cancel
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </CaptainDashboardLayout>
  );
}