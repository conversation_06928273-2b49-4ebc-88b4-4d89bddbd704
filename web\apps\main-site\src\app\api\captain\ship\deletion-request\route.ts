import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth";

export const dynamic = 'force-dynamic';

export async function POST(request: Request) {
  try {
    const currentUser = await getCurrentUser(request);

    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    const { reason } = await request.json();

    // Check if user is a captain
    const captainedShip = await prisma.ship.findFirst({
      where: {
        captainId: currentUser.id,
        status: 'active',
      },
    });

    if (!captainedShip) {
      return NextResponse.json(
        { error: "You are not a captain of any ship" },
        { status: 403 }
      );
    }

    // Check if there's already a pending deletion request
    const existingRequest = await prisma.shipDeletionRequest.findFirst({
      where: {
        shipId: captainedShip.id,
        status: 'pending',
      },
    });

    if (existingRequest) {
      return NextResponse.json(
        { error: "There is already a pending deletion request for this ship" },
        { status: 400 }
      );
    }

    // Create the deletion request
    const deletionRequest = await prisma.shipDeletionRequest.create({
      data: {
        shipId: captainedShip.id,
        requestedById: currentUser.id,
        reason: reason || null,
        status: 'pending',
      },
    });

    // Update ship status to pending_deletion
    await prisma.ship.update({
      where: { id: captainedShip.id },
      data: { status: 'pending_deletion' },
    });

    return NextResponse.json({
      message: "Deletion request submitted successfully",
      requestId: deletionRequest.id,
    });
  } catch (error) {
    console.error("Error creating ship deletion request:", error);
    return NextResponse.json(
      { error: "Failed to submit deletion request" },
      { status: 500 }
    );
  }
}

export async function GET(request: Request) {
  try {
    const currentUser = await getCurrentUser(request);

    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Check if user is a captain
    const captainedShip = await prisma.ship.findFirst({
      where: {
        captainId: currentUser.id,
      },
    });

    if (!captainedShip) {
      return NextResponse.json(
        { error: "You are not a captain of any ship" },
        { status: 403 }
      );
    }

    // Get any deletion request for this ship
    const deletionRequest = await prisma.shipDeletionRequest.findFirst({
      where: {
        shipId: captainedShip.id,
      },
      orderBy: { createdAt: 'desc' },
      include: {
        reviewedBy: {
          select: {
            id: true,
            username: true,
            displayName: true,
          },
        },
      },
    });

    return NextResponse.json({
      ship: {
        id: captainedShip.id,
        name: captainedShip.name,
        status: captainedShip.status,
      },
      deletionRequest: deletionRequest ? {
        id: deletionRequest.id,
        reason: deletionRequest.reason,
        status: deletionRequest.status,
        message: deletionRequest.message,
        createdAt: deletionRequest.createdAt.toISOString(),
        reviewedAt: deletionRequest.reviewedAt?.toISOString(),
        reviewedBy: deletionRequest.reviewedBy,
      } : null,
    });
  } catch (error) {
    console.error("Error fetching ship deletion request:", error);
    return NextResponse.json(
      { error: "Failed to fetch deletion request" },
      { status: 500 }
    );
  }
}

export async function DELETE(request: Request) {
  try {
    const currentUser = await getCurrentUser(request);

    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Check if user is a captain
    const captainedShip = await prisma.ship.findFirst({
      where: {
        captainId: currentUser.id,
      },
    });

    if (!captainedShip) {
      return NextResponse.json(
        { error: "You are not a captain of any ship" },
        { status: 403 }
      );
    }

    // Find and cancel pending deletion request
    const deletionRequest = await prisma.shipDeletionRequest.findFirst({
      where: {
        shipId: captainedShip.id,
        status: 'pending',
      },
    });

    if (!deletionRequest) {
      return NextResponse.json(
        { error: "No pending deletion request found" },
        { status: 404 }
      );
    }

    // Update the deletion request to cancelled
    await prisma.shipDeletionRequest.update({
      where: { id: deletionRequest.id },
      data: { 
        status: 'cancelled',
        reviewedAt: new Date(),
      },
    });

    // Update ship status back to active
    await prisma.ship.update({
      where: { id: captainedShip.id },
      data: { status: 'active' },
    });

    return NextResponse.json({
      message: "Deletion request cancelled successfully",
    });
  } catch (error) {
    console.error("Error cancelling ship deletion request:", error);
    return NextResponse.json(
      { error: "Failed to cancel deletion request" },
      { status: 500 }
    );
  }
}