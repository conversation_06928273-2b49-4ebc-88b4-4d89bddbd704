# Public Volunteer Components

Public-facing volunteer components for users to browse and sign up for volunteer opportunities.

## Components

- **UserVolunteerQuickActions.tsx** - Quick action buttons for volunteer operations
- **VolunteerCategoriesList.tsx** - List view of available volunteer categories
- **VolunteerCategoryCard.tsx** - Card display for individual volunteer categories
- **VolunteerEventsList.tsx** - List of events needing volunteers
- **VolunteerEventCard.tsx** - Card display for volunteer events
- **VolunteerShiftsList.tsx** - List of available volunteer shifts
- **VolunteerShiftCard.tsx** - Individual shift card with signup options
- **VolunteerSignupModal.tsx** - Modal for volunteer shift signup process
- **index.ts** - Component exports

These components provide the user-facing interface for discovering volunteer opportunities, viewing event details, and signing up for volunteer shifts.
