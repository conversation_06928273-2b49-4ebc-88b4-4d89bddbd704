"use client";

import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useBankStatistics } from "../../hooks/useBank";
import { CashierQuickActions } from "./CashierQuickActions";

interface CashierDashboardLayoutProps {
  children: React.ReactNode;
}

export const CashierDashboardLayout: React.FC<CashierDashboardLayoutProps> = ({
  children,
}) => {
  const pathname = usePathname();
  const { data: bankStatistics, isLoading, error } = useBankStatistics();

  const navItems = [
    { name: "Dashboard", path: "/cashier/dashboard" },
    { name: "Deposits", path: "/cashier/dashboard/deposits" },
    { name: "Withdrawals", path: "/cashier/dashboard/withdrawals" },
    { name: "Transactions", path: "/cashier/dashboard/transactions" },
    { name: "Members", path: "/cashier/dashboard/members" },
    { name: "Ledger", path: "/cashier/dashboard/ledger" },
    { name: "Statistics", path: "/cashier/dashboard/statistics" },
  ];

  // Function to check if a path is active
  const isActive = (path: string) => {
    if (path === "/cashier/dashboard") {
      return pathname === "/cashier/dashboard";
    }
    return pathname.startsWith(path);
  };

  return (
    <div className="min-h-screen bg-secondary-dark">
      <div className="container px-2 py-2 md:py-4 lg:py-8 mx-auto">
        <div className="flex items-center justify-between mb-4 md:mb-4">
          <div className="flex items-center">
            <div className="w-10 h-10 md:w-12 md:h-12 rounded-full mr-3 md:mr-4 bg-primary flex items-center justify-center text-white font-bold text-lg md:text-xl">
              C
            </div>
            <div>
              <h1 className="text-xl md:text-2xl lg:text-3xl font-bold text-white">
                Cashier Dashboard
              </h1>
              <p className="text-sm md:text-base text-gray-400">
                Manage bank transactions and user accounts
              </p>
            </div>
          </div>
        </div>

        <div className="flex flex-col md:flex-row gap-4">
          {/* Sidebar Navigation - Hidden on mobile */}
          <aside className="hidden md:block md:w-40 lg:w-40 flex-shrink-0">
            <div className="bg-secondary rounded-lg shadow-md p-4 border border-gray-600">
              <nav>
                <ul className="space-y-4">
                  {navItems.map((item) => (
                    <li key={item.path}>
                      <Link
                        href={item.path}
                        className={`
                          block px-2 py-2 rounded-md font-semibold border border-gray-600 transition-colors text-base
                          ${
                            isActive(item.path)
                              ? "bg-primary text-white font-medium"
                              : "text-white hover:bg-secondary-light"
                          }
                        `}
                      >
                        {item.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </nav>
            </div>

            {/* Stats Card */}
            <div className="bg-secondary rounded-lg shadow-md p-4 mt-4 border border-gray-600">
              <h2 className="text-lg font-semibold mb-3 text-white">
                Quick Stats
              </h2>
              {isLoading ? (
                <div className="text-center py-2">
                  <div className="inline-block animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-primary"></div>
                  <p className="mt-2 text-gray-400 text-sm">Loading...</p>
                </div>
              ) : error ? (
                <div className="text-center py-2">
                  <p className="text-error text-sm">Error loading stats</p>
                </div>
              ) : bankStatistics ? (
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-400 text-sm">
                      Pending Deposits:
                    </span>
                    <span className="font-bold text-warning">
                      {bankStatistics.pendingTransactions.deposits}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400 text-sm">
                      Pending Withdrawals:
                    </span>
                    <span className="font-bold text-warning">
                      {bankStatistics.pendingTransactions.withdrawals}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400 text-sm">
                      Today's Transactions:
                    </span>
                    <span className="font-bold text-white">
                      {bankStatistics.dailyActivity &&
                      bankStatistics.dailyActivity.length > 0
                        ? bankStatistics.dailyActivity[0].deposits +
                          bankStatistics.dailyActivity[0].withdrawals +
                          bankStatistics.dailyActivity[0].transfers
                        : 0}
                    </span>
                  </div>
                </div>
              ) : (
                <div className="text-center py-2">
                  <p className="text-gray-400 text-sm">No stats available</p>
                </div>
              )}
            </div>
          </aside>

          {/* Mobile Navigation - Only visible on mobile */}
          <div className="md:hidden mb-3">
            {/* Mobile Quick Actions */}
            <div className="mb-3">
              <CashierQuickActions />
            </div>

            {/* Mobile Stats Card */}
            <div className="bg-secondary rounded-lg shadow-md p-3 sm:p-4 mb-3 border border-gray-600">
              <h2 className="text-base sm:text-lg font-semibold mb-2 sm:mb-3 text-white">
                Quick Stats
              </h2>
              <div className="grid grid-cols-2 gap-2 sm:gap-3">
                {isLoading ? (
                  <div className="bg-secondary-dark p-2 sm:p-3 rounded-md col-span-2 text-center">
                    <div className="inline-block animate-spin rounded-full h-4 w-4 sm:h-5 sm:w-5 border-t-2 border-b-2 border-primary"></div>
                    <div className="text-gray-400 text-xs sm:text-sm mt-1 sm:mt-2">
                      Loading stats...
                    </div>
                  </div>
                ) : error ? (
                  <div className="bg-secondary-dark p-2 sm:p-3 rounded-md col-span-2 text-center">
                    <div className="text-error text-xs sm:text-sm">
                      Error loading stats
                    </div>
                  </div>
                ) : bankStatistics ? (
                  <>
                    <div className="bg-secondary-dark p-2 sm:p-3 rounded-md">
                      <div className="text-gray-400 text-xs sm:text-sm">
                        Pending Deposits
                      </div>
                      <div className="font-bold text-warning text-base sm:text-lg">
                        {bankStatistics.pendingTransactions.deposits}
                      </div>
                    </div>
                    <div className="bg-secondary-dark p-2 sm:p-3 rounded-md">
                      <div className="text-gray-400 text-xs sm:text-sm">
                        Pending Withdrawals
                      </div>
                      <div className="font-bold text-warning text-base sm:text-lg">
                        {bankStatistics.pendingTransactions.withdrawals}
                      </div>
                    </div>
                    <div className="bg-secondary-dark p-2 sm:p-3 rounded-md col-span-2">
                      <div className="text-gray-400 text-xs sm:text-sm">
                        Today's Transactions
                      </div>
                      <div className="font-bold text-white text-base sm:text-lg">
                        {bankStatistics.dailyActivity &&
                        bankStatistics.dailyActivity.length > 0
                          ? bankStatistics.dailyActivity[0].deposits +
                            bankStatistics.dailyActivity[0].withdrawals +
                            bankStatistics.dailyActivity[0].transfers
                          : 0}
                      </div>
                    </div>
                  </>
                ) : (
                  <div className="bg-secondary-dark p-2 sm:p-3 rounded-md col-span-2 text-center">
                    <div className="text-gray-400 text-xs sm:text-sm">
                      No stats available
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Main Content */}
          <main className="flex-1 w-full mx-auto flex flex-col gap-4">
            {children}
          </main>
        </div>
      </div>
    </div>
  );
};
