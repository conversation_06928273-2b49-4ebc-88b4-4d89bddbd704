import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser, userHasRole } from "@/lib/auth";
import { sendEmail } from "@/lib/email";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// Configuration
const ADMIN_EMAIL = process.env.ADMIN_EMAIL || "<EMAIL>";

// GET endpoint to retrieve tickets with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const currentUser = await getCurrentUser(request);
    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    const isAdmin = await userHasRole(request, "admin");
    if (!isAdmin) {
      return NextResponse.json(
        { error: "Admin privileges required" },
        { status: 403 },
      );
    }

    // Get query parameters
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get("page") || "1");
    const limit = parseInt(url.searchParams.get("limit") || "10");
    const status = url.searchParams.get("status");
    const priority = url.searchParams.get("priority");
    const category = url.searchParams.get("category");
    const search = url.searchParams.get("search");
    const assignedToMe = url.searchParams.get("assignedToMe") === "true";

    // Build filter object
    const filter: any = {};

    if (status) {
      filter.status = status;
    }

    if (priority) {
      filter.priority = priority;
    }

    if (category) {
      filter.category = category;
    }

    if (assignedToMe) {
      filter.assignedToId = currentUser.id;
    }

    if (search) {
      filter.OR = [
        { subject: { contains: search } },
        { message: { contains: search } },
        { email: { contains: search } },
        { name: { contains: search } },
      ];
    }

    // Get total count for pagination
    const total = await prisma.supportTicket.count({
      where: filter,
    });

    // Get tickets with pagination
    const tickets = await prisma.supportTicket.findMany({
      where: filter,
      include: {
        user: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
        assignedTo: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
        resolvedBy: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
        notes: {
          where: {
            isInternal: true,
          },
          select: {
            id: true,
            content: true,
            createdAt: true,
            author: {
              select: {
                id: true,
                username: true,
                displayName: true,
                avatar: true,
              },
            },
          },
          orderBy: {
            createdAt: "desc",
          },
          take: 1,
        },
      },
      orderBy: [
        {
          priority: "desc",
        },
        {
          createdAt: "desc",
        },
      ],
      skip: (page - 1) * limit,
      take: limit,
    });

    // Calculate total pages
    const pages = Math.ceil(total / limit);

    return NextResponse.json({
      tickets,
      pagination: {
        total,
        pages,
        page,
        limit,
      },
    });
  } catch (error) {
    console.error("Error fetching tickets:", error);
    return NextResponse.json(
      { error: "Failed to fetch tickets" },
      { status: 500 },
    );
  }
}

// POST endpoint to create a new ticket
export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const body = await request.json();
    const { subject, message, name, email, phone, category = "general" } = body;

    // Basic validation
    if (!subject || !message) {
      return NextResponse.json(
        { error: "Subject and message are required" },
        { status: 400 },
      );
    }

    if (!email || !/^\S+@\S+\.\S+$/.test(email)) {
      return NextResponse.json(
        { error: "Valid email is required" },
        { status: 400 },
      );
    }

    // Check if user is authenticated
    const currentUser = await getCurrentUser(request);
    let userId = null;

    if (currentUser) {
      userId = currentUser.id;
    }

    // Create the ticket
    const ticket = await prisma.supportTicket.create({
      data: {
        subject,
        message,
        name: name || (currentUser ? currentUser.displayName : null),
        email,
        phone,
        category,
        userId,
      },
    });

    // Send email notification to admin
    const emailText = `
      New Support Ticket Submission:
      
      Ticket ID: ${ticket.id}
      Subject: ${subject}
      Category: ${category}
      
      From: ${name || "Not provided"} (${email})
      Phone: ${phone || "Not provided"}
      
      Message:
      ${message}
    `;

    const emailHtml = `
      <h2>New Support Ticket Submission</h2>
      <p><strong>Ticket ID:</strong> ${ticket.id}</p>
      <p><strong>Subject:</strong> ${subject}</p>
      <p><strong>Category:</strong> ${category}</p>
      <hr>
      <p><strong>From:</strong> ${name || "Not provided"} (${email})</p>
      <p><strong>Phone:</strong> ${phone || "Not provided"}</p>
      <h3>Message:</h3>
      <p>${message.replace(/\n/g, "<br>")}</p>
    `;

    await sendEmail({
      to: ADMIN_EMAIL,
      subject: `New Support Ticket: ${subject}`,
      text: emailText,
      html: emailHtml,
      replyTo: email,
    });

    return NextResponse.json({
      success: true,
      ticket: {
        id: ticket.id,
        subject: ticket.subject,
        status: ticket.status,
        createdAt: ticket.createdAt,
      },
    });
  } catch (error) {
    console.error("Error creating ticket:", error);
    return NextResponse.json(
      { error: "Failed to create ticket" },
      { status: 500 },
    );
  }
}
