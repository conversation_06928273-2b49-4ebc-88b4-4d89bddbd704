# Event-Based Capacity System - Comprehensive Change Report

## Overview
Implementation of shared event capacity management system where products associated with events draw from a common capacity pool instead of individual product inventory limits.

## System Architecture Changes

### Database Schema Modifications

#### 1. Product Model Updates
```prisma
model Product {
  // ... existing fields ...
  eventId    String?  // Associate products with events
  event      Event?   @relation(fields: [eventId], references: [id])
  affectsCapacity Boolean @default(true) // Whether this product counts against event capacity
}
```

#### 2. Event Hold Tracking (New Model)
```prisma
model EventCapacityHold {
  id         String    @id @default(uuid())
  eventId    String
  event      Event     @relation(fields: [eventId], references: [id])
  userId     String
  user       User      @relation(fields: [userId], references: [id])
  cartItemId String    @unique
  cartItem   CartItem  @relation(fields: [cartItemId], references: [id], onDelete: Cascade)
  quantity   Int       // Number of capacity units held
  expiresAt  DateTime
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt

  @@index([eventId])
  @@index([userId])
  @@index([expiresAt])
  @@map("event_capacity_holds")
}
```

### Core System Changes

#### 1. Event Capacity Functions (New File: `src/lib/event-capacity-system.ts`)

**Primary Functions:**
- `getEventAvailableCapacity(eventId: string)`: Returns available spots
- `createEventCapacityHold(userId, eventId, cartItemId, quantity)`: Reserves capacity
- `releaseEventCapacityHold(holdId)`: Releases reserved capacity
- `convertEventHoldToSold(eventId, userId, cartId)`: Converts holds to sales
- `releaseExpiredEventHolds()`: Cleanup expired holds

**Capacity Calculation Logic:**
```typescript
async function getEventAvailableCapacity(eventId: string): Promise<number> {
  const event = await prisma.event.findUnique({
    where: { id: eventId },
    select: { capacity: true }
  });
  
  if (!event?.capacity) return Infinity; // No capacity limit
  
  const [soldTickets, heldCapacity] = await Promise.all([
    // Count sold tickets from associated products
    prisma.ticket.count({
      where: {
        product: { eventId },
        status: TicketStatus.SOLD
      }
    }),
    // Count current holds
    prisma.eventCapacityHold.aggregate({
      where: {
        eventId,
        expiresAt: { gt: new Date() }
      },
      _sum: { quantity: true }
    })
  ]);
  
  const used = soldTickets + (heldCapacity._sum.quantity || 0);
  return Math.max(0, event.capacity - used);
}
```

#### 2. Cart System Modifications

**File: `src/app/api/cart/items/route.ts`**

**Key Changes:**
- Replace individual ticket holds with event capacity holds for event-associated products
- Maintain existing logic for non-event products
- Add event capacity validation

**Modified Logic Flow:**
```typescript
// Check if product is associated with an event
const product = await tx.product.findUnique({
  where: { id: productId },
  include: { event: true }
});

if (product.eventId && product.affectsCapacity) {
  // Use event capacity system
  const availableCapacity = await getEventAvailableCapacity(product.eventId);
  
  if (availableCapacity < quantity) {
    throw new Error("Event capacity exceeded");
  }
  
  // Create event capacity hold instead of individual ticket hold
  const hold = await createEventCapacityHold(
    user.id, 
    product.eventId, 
    cartItem.id, 
    quantity
  );
} else {
  // Use existing individual ticket system
  // ... existing logic ...
}
```

#### 3. Product Creation Modifications

**File: `src/app/api/admin/products/route.ts` (if exists) or `src/app/api/sales/products/route.ts`**

**Changes:**
- Skip `generateTicketsForProduct()` for event-associated products
- Add `eventId` field to product creation form
- Add `affectsCapacity` toggle for products

#### 4. Checkout System Updates

**File: `src/app/api/checkout/webhook/route.ts`**

**Changes:**
- Convert event capacity holds to sold status
- Update event capacity tracking
- Maintain existing individual ticket conversion for non-event products

### API Endpoint Changes

#### 1. New Event Capacity Endpoints
```typescript
// GET /api/events/[id]/capacity - Get available capacity
// GET /api/events/[id]/products - Get associated products
```

#### 2. Modified Product Endpoints
```typescript
// POST /api/sales/products - Add eventId and affectsCapacity fields
// GET /api/products/[id]/availability - Check event vs product availability
```

### Frontend Changes

#### 1. Product Creation Form
**File: `src/app/admin/products/new/page.tsx` (or similar)**
- Add event selection dropdown
- Add "Affects Event Capacity" checkbox
- Show capacity warnings when event is selected

#### 2. Event Management Interface
**File: `src/app/admin/events/[id]/page.tsx`**
- Display current capacity usage
- Show associated products
- Real-time capacity updates

#### 3. Shopping Cart Updates
**File: `src/components/shop/CartItemCard.tsx`**
- Show event capacity warnings
- Display hold expiration for event tickets
- Update availability messages

### Background Jobs & Maintenance

#### 1. Hold Cleanup Job
**File: `src/app/api/cron/release-expired-holds/route.ts`**
- Add event capacity hold cleanup
- Maintain existing individual ticket hold cleanup

#### 2. Capacity Sync Job (New)
**File: `src/app/api/cron/sync-event-capacity/route.ts`**
- Verify event capacity consistency
- Audit capacity vs actual sales
- Alert on discrepancies

### Migration Strategy

#### 1. Database Migration
```sql
-- Add eventId to products
ALTER TABLE products ADD COLUMN eventId VARCHAR(36) NULL;
ALTER TABLE products ADD COLUMN affectsCapacity BOOLEAN DEFAULT TRUE;

-- Create event capacity holds table
CREATE TABLE event_capacity_holds (
  id VARCHAR(36) PRIMARY KEY,
  eventId VARCHAR(36) NOT NULL,
  userId VARCHAR(36) NOT NULL,
  cartItemId VARCHAR(36) UNIQUE NOT NULL,
  quantity INT NOT NULL,
  expiresAt DATETIME NOT NULL,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 2. Data Migration Steps
1. Identify existing products that should be associated with events
2. Set `affectsCapacity = false` for products that shouldn't count against event limits
3. Create event capacity holds for existing cart items with event products
4. Validate data consistency

### Error Handling & Edge Cases

#### 1. Capacity Conflicts
- **Scenario**: Event capacity reduced while holds exist
- **Solution**: Maintain existing holds, prevent new ones until capacity frees up

#### 2. Mixed Cart Items
- **Scenario**: Cart contains both event and non-event products
- **Solution**: Process each type with appropriate system (event capacity vs individual tickets)

#### 3. Event Cancellation
- **Scenario**: Event cancelled with existing holds/sales
- **Solution**: Release all holds, maintain sales history, prevent new purchases

#### 4. Product Reassignment
- **Scenario**: Product moved from one event to another
- **Solution**: Migrate existing holds to new event, validate capacity constraints

### Performance Considerations

#### 1. Database Optimization
- Index event capacity holds by eventId and expiresAt
- Cache event capacity calculations for high-traffic events
- Use database-level constraints to prevent overselling

#### 2. Real-time Updates
- Implement SSE notifications for capacity changes
- Update frontend availability in real-time
- Show capacity warnings before checkout

### Security & Data Integrity

#### 1. Transaction Safety
- All capacity operations wrapped in database transactions
- Atomic updates to prevent race conditions
- Rollback on any failure in the transaction chain

#### 2. Access Control
- Event capacity management restricted to admins
- Product association requires appropriate permissions
- Audit trail for all capacity changes

### Testing Strategy

#### 1. Unit Tests
- Event capacity calculation functions
- Hold creation/release mechanisms
- Edge case handling

#### 2. Integration Tests
- Full cart-to-checkout flow with event products
- Mixed cart scenarios (event + non-event products)
- Concurrent user scenarios

#### 3. Load Testing
- High-traffic event launches
- Concurrent capacity hold attempts
- System behavior at capacity limits

### Monitoring & Analytics

#### 1. Capacity Metrics
- Real-time capacity utilization
- Hold-to-sale conversion rates
- Expired hold frequencies

#### 2. Event Performance
- Time to sell out
- Peak demand periods
- Product popularity within events

#### 3. System Health
- Hold cleanup efficiency
- Database query performance
- Transaction failure rates

## Implementation Priority

### Phase 1: Core Infrastructure
1. Database schema changes
2. Event capacity system functions
3. Basic cart integration

### Phase 2: API Integration
1. Modified cart endpoints
2. Product creation updates
3. Checkout system changes

### Phase 3: Frontend & UX
1. Admin interfaces
2. User-facing capacity indicators
3. Real-time updates

### Phase 4: Optimization & Monitoring
1. Performance tuning
2. Analytics implementation
3. Advanced error handling

## Risk Assessment

### High Risk
- **Data Integrity**: Capacity calculations must be 100% accurate
- **Race Conditions**: Concurrent access to limited capacity
- **Migration Complexity**: Existing data and system compatibility

### Medium Risk
- **Performance Impact**: Additional database queries for capacity checks
- **User Experience**: Capacity limits may frustrate users
- **System Complexity**: Multiple hold systems to maintain

### Low Risk
- **Feature Adoption**: Gradual rollout possible
- **Rollback Capability**: Can disable event capacity per product
- **Monitoring**: Comprehensive logging and alerting possible

## Success Metrics

### Functional Metrics
- Zero oversold events
- < 1% hold expiration rate
- < 500ms capacity check response time

### Business Metrics
- Improved event sell-through rates
- Reduced customer service tickets about overselling
- Better event capacity utilization

### Technical Metrics
- 99.9% transaction success rate
- < 2% system error rate during peak demand
- Successful handling of 10x concurrent users per event capacity