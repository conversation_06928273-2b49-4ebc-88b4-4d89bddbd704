import React from "react";

export interface InputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, "size"> {
  /**
   * Input label
   */
  label?: string;
  /**
   * Input error message
   */
  error?: string;
  /**
   * Input helper text
   */
  helperText?: string;
  /**
   * Input left icon
   */
  leftIcon?: React.ReactNode;
  /**
   * Input right icon
   */
  rightIcon?: React.ReactNode;
  /**
   * Input full width
   */
  fullWidth?: boolean;
  /**
   * Input size
   */
  size?: "sm" | "md" | "lg";
}

export const Input = React.forwardRef<HTMLInputElement, InputProps>(
  (
    {
      label,
      error,
      helperText,
      leftIcon,
      rightIcon,
      fullWidth = false,
      size = "md",
      className = "",
      id,
      ...props
    },
    ref,
  ) => {
    // Generate a random ID if not provided
    const inputId = id || `input-${Math.random().toString(36).substring(2, 9)}`;

    // Size classes
    const sizeClasses = {
      sm: "px-3 py-1.5 text-sm",
      md: "px-4 py-2 text-base",
      lg: "px-4 py-3 text-lg",
    };

    // Width classes
    const widthClasses = fullWidth ? "w-full" : "";

    // Error classes
    const errorClasses = error
      ? "border-accent focus:border-accent focus:ring-accent"
      : "border-gray-600 focus:border-primary focus:ring-primary";

    // Icon padding classes
    const leftIconPadding = leftIcon ? "pl-10" : "";
    const rightIconPadding = rightIcon ? "pr-10" : "";

    return (
      <div className={`${widthClasses}`}>
        {label && (
          <label
            htmlFor={inputId}
            className="block text-sm font-medium text-white mb-1"
          >
            {label}
          </label>
        )}
        <div className="relative">
          {leftIcon && (
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-gray-500">
              {leftIcon}
            </div>
          )}
          <input
            ref={ref}
            id={inputId}
            className={`
              block rounded-md shadow-sm text-white border
              ${sizeClasses[size]}
              ${errorClasses}
              ${leftIconPadding}
              ${rightIconPadding}
              ${widthClasses}
              ${className}
              focus:outline-none focus:ring-2 focus:ring-offset-0
              disabled:bg-secondary-dark disabled:text-disabled disabled:cursor-not-allowed
              bg-[#2C2F33] border border-gray-600
            `}
            aria-invalid={error ? "true" : "false"}
            aria-describedby={
              error
                ? `${inputId}-error`
                : helperText
                ? `${inputId}-helper-text`
                : undefined
            }
            {...props}
          />
          {rightIcon && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none text-gray-500">
              {rightIcon}
            </div>
          )}
        </div>
        {error && (
          <p
            id={`${inputId}-error`}
            className="mt-1 text-sm text-accent"
            role="alert"
          >
            {error}
          </p>
        )}
        {helperText && !error && (
          <p
            id={`${inputId}-helper-text`}
            className="mt-1 text-sm text-gray-400"
          >
            {helperText}
          </p>
        )}
      </div>
    );
  },
);

Input.displayName = "Input";
