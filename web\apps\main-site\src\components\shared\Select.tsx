"use client";

import React from "react";

interface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  label?: string;
  error?: boolean;
  helperText?: string;
  fullWidth?: boolean;
  className?: string;
}

export const Select: React.FC<SelectProps> = ({
  label,
  error = false,
  helperText,
  fullWidth = false,
  className = "",
  children,
  ...props
}) => {
  // Generate a random ID if not provided
  const selectId =
    props.id || `select-${Math.random().toString(36).substring(2, 9)}`;

  // Width classes
  const widthClasses = fullWidth ? "w-full" : "";

  // Error classes
  const errorClasses = error
    ? "border-accent focus:border-accent focus:ring-accent"
    : "border-gray-600 focus:border-primary focus:ring-primary";

  return (
    <div className={`${widthClasses} ${className}`}>
      {label && (
        <label htmlFor={selectId} className="block text-sm font-medium mb-1">
          {label}
        </label>
      )}
      <div className="relative">
        <select
          id={selectId}
          className={`
            block px-4 py-2 bg-secondary 
            border ${errorClasses} rounded-md 
            focus:outline-none focus:ring-2 
            appearance-none ${widthClasses}
          `}
          {...props}
        >
          {children}
        </select>
        <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
          <svg
            className="w-5 h-5 text-gray-400"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 20 20"
            fill="currentColor"
            aria-hidden="true"
          >
            <path
              fillRule="evenodd"
              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
              clipRule="evenodd"
            />
          </svg>
        </div>
      </div>
      {helperText && (
        <p
          className={`mt-1 text-sm ${error ? "text-accent" : "text-gray-400"}`}
        >
          {helperText}
        </p>
      )}
    </div>
  );
};
