import { NextRequest, NextResponse } from "next/server";

// Use named exports for HTTP methods
export const OPTIONS = async (req: NextRequest) => {
  // Handle OPTIONS request for CORS preflight
  const response = new NextResponse(null, { status: 204 });

  response.headers.set("Access-Control-Allow-Origin", "*");
  response.headers.set(
    "Access-Control-Allow-Methods",
    "GET, POST, PUT, DELETE, OPTIONS",
  );
  response.headers.set(
    "Access-Control-Allow-Headers",
    "Content-Type, Authorization",
  );

  return response;
};

// This is a simple test endpoint to check if the Prisma setup is working correctly
export const GET = async (req: NextRequest) => {
  try {
    // Import the Prisma client inside the function to avoid issues with Next.js hot reloading
    const { default: prisma } = await import("../../../../lib/prisma");

    // Try to count users to check database connectivity
    const userCount = await prisma.user.count();

    return NextResponse.json({
      message: "Prisma test successful!",
      userCount,
      time: new Date().toISOString(),
    });
  } catch (error: any) {
    // Type assertion for error
    return NextResponse.json(
      {
        message: "Prisma test failed",
        error: error?.message || String(error),
        stack: error?.stack,
        time: new Date().toISOString(),
      },
      { status: 500 },
    );
  }
};
