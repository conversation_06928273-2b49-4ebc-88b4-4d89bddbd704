"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "../../../../contexts/AuthContext";
import { NewsDashboardLayout } from "../../../../components/news";
import Link from "next/link";
import {
  useArticles,
  useToggleArticleStatus,
  useToggleArticleFeatured,
  useDeleteArticle,
} from "../../../../hooks/useNews";
import { ArticleFilters } from "../../../../services/newsService";

export default function AllArticlesPage() {
  const { user, isLoading: authLoading } = useAuth();
  const router = useRouter();
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState<
    "all" | "published" | "draft" | "paused"
  >("all");
  const [sortBy, setSortBy] = useState<"createdAt" | "title" | "views">(
    "createdAt",
  );
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [viewMode, setViewMode] = useState<"card" | "table">("card");

  // Build API filters
  const filters: ArticleFilters = {
    page,
    limit,
    sortBy,
    order: sortOrder,
    search: searchTerm || undefined,
  };

  // Only add status filter if not 'all'
  if (filterStatus !== "all") {
    filters.status = filterStatus as "published" | "draft" | "paused";
  }

  // Fetch articles with React Query
  const {
    data: articlesData,
    isLoading: articlesLoading,
    isError: articlesError,
    refetch: refetchArticles,
  } = useArticles(filters);

  // Mutation hooks
  const toggleStatusMutation = useToggleArticleStatus();
  const toggleFeaturedMutation = useToggleArticleFeatured();
  const deleteArticleMutation = useDeleteArticle();

  // Check authorization after auth is loaded
  useEffect(() => {
    // Only proceed with checks if auth loading is complete
    if (!authLoading) {
      if (!user || !user.roles?.editor) {
        router.push("/");
      } else {
        // User is authorized
        setIsAuthorized(true);
      }
    }
  }, [user, router, authLoading]);

  // Show loading state or nothing while checking authorization
  if (authLoading || !isAuthorized) {
    return (
      <div className="min-h-screen bg-secondary-dark flex items-center justify-center">
        <div className="bg-secondary-light p-6 rounded-lg shadow-md border border-gray-600">
          <p className="text-white text-lg">Loading...</p>
        </div>
      </div>
    );
  }

  // Handle status toggle
  const handleToggleStatus = (id: string) => {
    try {
      toggleStatusMutation.mutate(id);
    } catch (error) {
      console.error("Error toggling article status:", error);
    }
  };

  // Handle featured toggle
  const handleToggleFeatured = (id: string) => {
    try {
      toggleFeaturedMutation.mutate(id);
    } catch (error) {
      console.error("Error toggling article featured status:", error);
    }
  };

  // Handle article deletion
  const handleDeleteArticle = (id: string) => {
    if (
      window.confirm(
        "Are you sure you want to delete this article? This action cannot be undone.",
      )
    ) {
      try {
        deleteArticleMutation.mutate(id);
      } catch (error) {
        console.error("Error deleting article:", error);
      }
    }
  };

  // Calculate stats for the dashboard layout
  const dashboardData = articlesData
    ? {
        stats: {
          publishedArticles: articlesData.data.filter(
            (a) => a.status === "published",
          ).length,
          draftArticles: articlesData.data.filter((a) => a.status === "draft")
            .length,
          totalViews: articlesData.data.reduce(
            (sum, article) => sum + (article.views || 0),
            0,
          ),
        },
      }
    : null;

  return (
    <NewsDashboardLayout dashboardData={dashboardData}>
      <div className="space-y-6">
        {/* Main Content */}
        <div className="bg-secondary-light rounded-lg shadow-md pb-0 p-2 sm:p-3 border border-gray-600">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-2">
            <div className="flex items-center justify-between w-full sm:w-auto">
              <h2 className="text-xl sm:text-2xl font-bold text-white">
                All Articles
              </h2>

              {/* View toggle buttons - Only on tablet and desktop */}
              <div className="sm:ml-4 flex space-x-2">
                <button
                  onClick={() => setViewMode("card")}
                  className={`hidden sm:flex items-center px-2 py-1 rounded ${
                    viewMode === "card"
                      ? "bg-primary text-white"
                      : "bg-secondary text-gray-400 hover:bg-secondary-dark"
                  }`}
                  aria-label="Card view"
                >
                  <svg
                    className="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zm10 0a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zm10 0a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"
                    />
                  </svg>
                </button>
                <button
                  onClick={() => setViewMode("table")}
                  className={`hidden sm:flex items-center px-2 py-1 rounded ${
                    viewMode === "table"
                      ? "bg-primary text-white"
                      : "bg-secondary text-gray-400 hover:bg-secondary-dark"
                  }`}
                  aria-label="Table view"
                >
                  <svg
                    className="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                    />
                  </svg>
                </button>
              </div>
            </div>

            <Link
              href="/news/dashboard/articles/new"
              className="bg-primary hover:bg-primary-dark text-white px-4 py-2 rounded-md text-sm font-medium w-full sm:w-auto text-center"
            >
              Create New Article
            </Link>
          </div>

          {/* Search and Filters */}
          <div className="space-y-4 mb-4">
            {/* Search Bar */}
            <div className="relative">
              <input
                type="text"
                placeholder="Search articles..."
                className="w-full px-4 py-2 bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <svg
                className="absolute right-3 top-2.5 h-5 w-5 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>

            {/* Filter Buttons for Mobile - Scroll horizontally */}
            <div className="sm:hidden overflow-x-auto pb-1">
              <div className="flex gap-2 min-w-max">
                <button
                  onClick={() => setFilterStatus("all")}
                  className={`px-4 py-2 rounded-md text-sm whitespace-nowrap ${
                    filterStatus === "all"
                      ? "bg-primary text-white"
                      : "bg-secondary text-gray-400 hover:bg-secondary-dark"
                  }`}
                >
                  All Status
                </button>
                <button
                  onClick={() => setFilterStatus("published")}
                  className={`px-4 py-2 rounded-md text-sm whitespace-nowrap ${
                    filterStatus === "published"
                      ? "bg-primary text-white"
                      : "bg-secondary text-gray-400 hover:bg-secondary-dark"
                  }`}
                >
                  Published
                </button>
                <button
                  onClick={() => setFilterStatus("draft")}
                  className={`px-4 py-2 rounded-md text-sm whitespace-nowrap ${
                    filterStatus === "draft"
                      ? "bg-primary text-white"
                      : "bg-secondary text-gray-400 hover:bg-secondary-dark"
                  }`}
                >
                  Draft
                </button>
                <button
                  onClick={() => setFilterStatus("paused")}
                  className={`px-4 py-2 rounded-md text-sm whitespace-nowrap ${
                    filterStatus === "paused"
                      ? "bg-primary text-white"
                      : "bg-secondary text-gray-400 hover:bg-secondary-dark"
                  }`}
                >
                  Paused
                </button>
              </div>
            </div>

            {/* Desktop Filters */}
            <div className="hidden sm:flex justify-between items-center">
              <div className="flex gap-2">
                <select
                  className="px-4 py-2 bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary"
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value as any)}
                >
                  <option value="all">All Status</option>
                  <option value="published">Published</option>
                  <option value="draft">Draft</option>
                  <option value="paused">Paused</option>
                </select>
                <select
                  className="px-4 py-2 bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary"
                  value={`${sortBy}-${sortOrder}`}
                  onChange={(e) => {
                    const [newSortBy, newSortOrder] = e.target.value.split("-");
                    setSortBy(newSortBy as any);
                    setSortOrder(newSortOrder as "asc" | "desc");
                  }}
                >
                  <option value="createdAt-desc">Newest First</option>
                  <option value="createdAt-asc">Oldest First</option>
                  <option value="title-asc">Title (A-Z)</option>
                  <option value="title-desc">Title (Z-A)</option>
                  <option value="views-desc">Most Views</option>
                  <option value="views-asc">Least Views</option>
                </select>
              </div>

              {/* Results count for desktop */}
              {articlesData && (
                <div className="text-sm text-gray-400">
                  Showing {articlesData.data.length} of{" "}
                  {articlesData.meta.totalCount} articles
                </div>
              )}
            </div>

            {/* Sort Options for Mobile */}
            <div className="sm:hidden">
              <select
                className="w-full px-4 py-2 bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary"
                value={`${sortBy}-${sortOrder}`}
                onChange={(e) => {
                  const [newSortBy, newSortOrder] = e.target.value.split("-");
                  setSortBy(newSortBy as any);
                  setSortOrder(newSortOrder as "asc" | "desc");
                }}
              >
                <option value="createdAt-desc">Newest First</option>
                <option value="createdAt-asc">Oldest First</option>
                <option value="title-asc">Title (A-Z)</option>
                <option value="title-desc">Title (Z-A)</option>
                <option value="views-desc">Most Views</option>
                <option value="views-asc">Least Views</option>
              </select>
            </div>
          </div>

          {articlesLoading ? (
            <div className="text-center py-8">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
              <p className="mt-2 text-gray-400">Loading articles...</p>
            </div>
          ) : articlesError ? (
            <div className="bg-accent bg-opacity-20 text-accent border border-accent rounded-md p-4 mb-6">
              <p>Error loading articles. Please try again.</p>
              <button
                onClick={() => refetchArticles()}
                className="mt-2 text-sm underline"
              >
                Retry
              </button>
            </div>
          ) : articlesData?.data.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-400">
                No articles found. Create a new article to get started.
              </p>
            </div>
          ) : (
            <>
              {/* Card View - Now supporting both mobile and optionally desktop */}
              <div
                className={`${
                  viewMode === "card" || !viewMode ? "" : "sm:hidden"
                } space-y-4`}
              >
                {/* For tablet and larger screens, show grid layout when in card view */}
                <div
                  className={`${
                    viewMode === "card"
                      ? "sm:grid sm:grid-cols-2 lg:grid-cols-3 sm:gap-6"
                      : ""
                  }`}
                >
                  {articlesData?.data.map((article) => (
                    <div
                      key={article.id}
                      className={`bg-secondary-dark rounded-lg p-4 border border-gray-600 ${
                        viewMode === "card" ? "mb-4 sm:mb-0 flex flex-col" : ""
                      }`}
                    >
                      <div className="flex items-center gap-3 mb-3">
                        {article.image && (
                          <div className="flex-shrink-0 h-14 w-14 sm:h-16 sm:w-16">
                            <img
                              className="h-full w-full rounded-md object-cover"
                              src={article.image}
                              alt={article.title}
                            />
                          </div>
                        )}
                        <div className="flex-1 min-w-0">
                          <h4 className="text-white font-medium text-sm sm:text-base truncate">
                            {article.title}
                          </h4>
                          <p className="text-gray-400 text-xs sm:text-sm">
                            {article.category.name}
                          </p>
                          <p className="text-gray-400 text-xs">
                            By {article.author.displayName} •{" "}
                            {new Date(article.createdAt).toLocaleDateString()}
                          </p>
                        </div>
                      </div>

                      <div className="flex justify-between items-center mb-3 flex-wrap sm:flex-nowrap gap-2">
                        <span
                          className={`px-2 py-0.5 rounded-full text-xs font-medium ${
                            article.status === "published"
                              ? "bg-green-900 text-green-200"
                              : article.status === "draft"
                              ? "bg-gray-700 text-gray-300"
                              : "bg-yellow-900 text-yellow-200"
                          }`}
                        >
                          {article.status.charAt(0).toUpperCase() +
                            article.status.slice(1)}
                        </span>
                        <div className="flex items-center space-x-2">
                          <span className="text-gray-400 text-xs">
                            Featured:
                          </span>
                          <input
                            type="checkbox"
                            className="h-4 w-4 text-primary focus:ring-primary border-gray-600 rounded"
                            checked={article.featured}
                            onChange={() => handleToggleFeatured(article.id)}
                            disabled={toggleFeaturedMutation.isPending}
                          />
                        </div>
                        <div className="flex items-center space-x-1">
                          <svg
                            className="w-3 h-3 text-gray-400"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth="2"
                              d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                            />
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth="2"
                              d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                            />
                          </svg>
                          <span className="text-gray-400 text-xs">
                            {article.views}
                          </span>
                        </div>
                      </div>

                      <div className="flex space-x-2 pt-2 border-t border-gray-600 mt-auto">
                        <Link
                          href={`/news/dashboard/articles/${article.id}`}
                          className="flex-1 bg-secondary text-center text-sm py-1 rounded-md text-white hover:bg-secondary-dark"
                        >
                          Edit
                        </Link>
                        {article.status !== "draft" && (
                          <button
                            onClick={() => handleToggleStatus(article.id)}
                            className="flex-1 bg-secondary text-center text-sm py-1 rounded-md text-white hover:bg-secondary-dark"
                            disabled={toggleStatusMutation.isPending}
                          >
                            {article.status === "published"
                              ? "Pause"
                              : "Publish"}
                          </button>
                        )}
                        <button
                          className="flex-1 bg-accent bg-opacity-20 text-center text-sm py-1 rounded-md text-white hover:bg-accent hover:bg-opacity-30"
                          onClick={() => handleDeleteArticle(article.id)}
                          disabled={deleteArticleMutation.isPending}
                        >
                          Delete
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Table View - Now optionally hidden on all screens based on viewMode */}
              <div
                className={`${
                  viewMode === "table" ? "block" : "hidden sm:hidden"
                } overflow-x-auto`}
              >
                <table className="min-w-full divide-y divide-gray-600">
                  <thead className="bg-secondary">
                    <tr>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"
                      >
                        Article
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"
                      >
                        Author
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"
                      >
                        Date
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"
                      >
                        Status
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"
                      >
                        Featured
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"
                      >
                        Views
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"
                      >
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-secondary-light divide-y divide-gray-600">
                    {articlesData?.data.map((article) => (
                      <tr key={article.id}>
                        <td className="px-6 py-4">
                          <div className="flex items-center">
                            {article.image && (
                              <div className="flex-shrink-0 h-10 w-10 mr-3">
                                <img
                                  className="h-10 w-10 rounded-md object-cover"
                                  src={article.image}
                                  alt={article.title}
                                />
                              </div>
                            )}
                            <div className="ml-0">
                              <div className="text-sm font-medium text-white">
                                {article.title}
                              </div>
                              <div className="text-xs text-gray-400">
                                {article.category.name}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-white">
                            {article.author.displayName}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-400">
                            {new Date(article.createdAt).toLocaleDateString()}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              article.status === "published"
                                ? "bg-green-900 text-green-200"
                                : article.status === "draft"
                                ? "bg-gray-700 text-gray-300"
                                : "bg-yellow-900 text-yellow-200"
                            }`}
                          >
                            {article.status.charAt(0).toUpperCase() +
                              article.status.slice(1)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <input
                              type="checkbox"
                              className="h-4 w-4 text-primary focus:ring-primary border-gray-600 rounded"
                              checked={article.featured}
                              onChange={() => handleToggleFeatured(article.id)}
                              disabled={toggleFeaturedMutation.isPending}
                            />
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-400">
                            {article.views}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-3">
                            <Link
                              href={`/news/dashboard/articles/${article.id}`}
                              className="text-primary hover:text-primary-light"
                            >
                              Edit
                            </Link>
                            {article.status !== "draft" && (
                              <button
                                onClick={() => handleToggleStatus(article.id)}
                                className="text-primary hover:text-primary-light"
                                disabled={toggleStatusMutation.isPending}
                              >
                                {article.status === "published"
                                  ? "Pause"
                                  : "Publish"}
                              </button>
                            )}
                            <button
                              className="text-accent hover:text-accent-light"
                              onClick={() => handleDeleteArticle(article.id)}
                              disabled={deleteArticleMutation.isPending}
                            >
                              Delete
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Pagination Controls */}
              {articlesData && articlesData.meta.totalPages > 1 && (
                <div className="flex flex-col sm:flex-row justify-between items-center mt-6 gap-3">
                  {/* Mobile pagination indicators */}
                  <div className="sm:hidden text-gray-400 text-sm text-center w-full">
                    Page {articlesData.meta.currentPage} of{" "}
                    {articlesData.meta.totalPages}
                  </div>

                  {/* Desktop showing X of Y */}
                  <div className="hidden sm:block text-sm text-gray-400">
                    Showing {(page - 1) * limit + 1} to{" "}
                    {Math.min(page * limit, articlesData.meta.totalCount)} of{" "}
                    {articlesData.meta.totalCount} articles
                  </div>

                  {/* Pagination buttons */}
                  <div className="flex space-x-2">
                    <button
                      className={`px-3 py-1 rounded-md ${
                        articlesData.meta.hasPrev
                          ? "bg-primary hover:bg-primary-dark text-white"
                          : "bg-gray-700 text-gray-500 cursor-not-allowed"
                      }`}
                      onClick={() => setPage(page - 1)}
                      disabled={!articlesData.meta.hasPrev}
                    >
                      Previous
                    </button>
                    <button
                      className={`px-3 py-1 rounded-md ${
                        articlesData.meta.hasNext
                          ? "bg-primary hover:bg-primary-dark text-white"
                          : "bg-gray-700 text-gray-500 cursor-not-allowed"
                      }`}
                      onClick={() => setPage(page + 1)}
                      disabled={!articlesData.meta.hasNext}
                    >
                      Next
                    </button>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </NewsDashboardLayout>
  );
}
