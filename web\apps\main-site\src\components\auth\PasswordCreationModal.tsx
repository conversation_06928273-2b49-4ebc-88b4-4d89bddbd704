"use client";

import React, { useState } from "react";
import { Modal, Input, Button } from "@bank-of-styx/ui";
import toast from "react-hot-toast";

interface PasswordCreationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (password: string) => Promise<void>;
  isLoading: boolean;
}

export const PasswordCreationModal = ({
  isOpen,
  onClose,
  onConfirm,
  isLoading,
}: PasswordCreationModalProps) => {
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [error, setError] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");

    // Validate passwords
    if (!password) {
      setError("Password is required");
      return;
    }

    if (password.length < 8) {
      setError("Password must be at least 8 characters long");
      return;
    }

    if (password !== confirmPassword) {
      setError("Passwords do not match");
      return;
    }

    try {
      await onConfirm(password);
      resetForm();
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred");
    }
  };

  const resetForm = () => {
    setPassword("");
    setConfirmPassword("");
    setError("");
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="Create Password"
      size="md"
      footer={
        <div className="flex justify-end space-x-2 w-full">
          <Button variant="outline" onClick={handleClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button variant="primary" onClick={handleSubmit} loading={isLoading}>
            Confirm
          </Button>
        </div>
      }
    >
      <div className="py-2">
        <p className="mb-4 text-sm text-gray-300">
          You need to create a password for your account before unlinking
          Discord. This will ensure you can still log in to your account after
          Discord is unlinked.
        </p>

        {error && (
          <div className="mb-4 p-3 text-sm rounded-lg bg-accent bg-opacity-20 text-accent border border-accent">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit}>
          <div className="space-y-4">
            <Input
              type="password"
              label="New Password"
              value={password}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                setPassword(e.target.value)
              }
              fullWidth
              placeholder="Enter a new password"
              autoComplete="new-password"
              required
              minLength={8}
            />

            <Input
              type="password"
              label="Confirm Password"
              value={confirmPassword}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                setConfirmPassword(e.target.value)
              }
              fullWidth
              placeholder="Confirm your password"
              autoComplete="new-password"
              required
              minLength={8}
            />
          </div>
        </form>
      </div>
    </Modal>
  );
};
