"use client";

import React from "react";
import { AdminUser } from "../../services/adminService";

interface UserProfileCardProps {
  user: AdminUser;
  onClick?: () => void;
}

export const UserProfileCard: React.FC<UserProfileCardProps> = ({
  user,
  onClick,
}) => {
  return (
    <div
      className="bg-secondary rounded-lg shadow-md p-2 sm:p-3 border border-gray-600 hover:border-primary transition-colors cursor-pointer"
      onClick={onClick}
    >
      <div className="flex items-center mb-3">
        <div className="flex-shrink-0 h-14 w-14 sm:h-16 sm:w-16">
          <img
            className="h-14 w-14 sm:h-16 sm:w-16 rounded-full object-cover"
            src={user.avatar || "/images/avatars/default.png"}
            alt={user.displayName}
          />
        </div>
        <div className="ml-3 sm:ml-4">
          <h3 className="text-base sm:text-lg font-medium text-white">
            {user.displayName}
          </h3>
          <p className="text-xs sm:text-sm text-gray-400">@{user.username}</p>
          <p className="text-xs sm:text-sm text-gray-400 truncate max-w-[180px] sm:max-w-[220px]">
            {user.email}
          </p>
        </div>
      </div>

      <div className="flex flex-wrap gap-1 sm:gap-2 mb-3">
        {user.roles.admin && (
          <span className="px-2 py-0.5 text-xs rounded-full bg-red-900 text-red-100">
            Admin
          </span>
        )}
        {user.roles.editor && (
          <span className="px-2 py-0.5 text-xs rounded-full bg-blue-900 text-blue-100">
            Editor
          </span>
        )}
        {user.roles.banker && (
          <span className="px-2 py-0.5 text-xs rounded-full bg-green-900 text-green-100">
            Banker
          </span>
        )}
        {user.roles.chatModerator && (
          <span className="px-2 py-0.5 text-xs rounded-full bg-purple-900 text-purple-100">
            Chat Mod
          </span>
        )}
        {user.roles.volunteerCoordinator && (
          <span className="px-2 py-0.5 text-xs rounded-full bg-yellow-900 text-yellow-100">
            Volunteer Coord
          </span>
        )}
        {user.roles.salesManager && (
          <span className="px-2 py-0.5 text-xs rounded-full bg-amber-900 text-amber-100">
            Sales Manager
          </span>
        )}
        {user.roles.leadManager && (
          <span className="px-2 py-0.5 text-xs rounded-full bg-orange-900 text-orange-100">
            Lead Manager
          </span>
        )}
      </div>

      <div className="flex justify-between items-center">
        <span
          className={`px-2 py-0.5 text-xs rounded-full ${
            user.status === "active"
              ? "bg-green-900 text-green-100"
              : user.status === "pending"
              ? "bg-yellow-900 text-yellow-100"
              : user.status === "suspended"
              ? "bg-orange-900 text-orange-100"
              : "bg-red-900 text-red-100"
          }`}
        >
          {user.status.charAt(0).toUpperCase() + user.status.slice(1)}
        </span>
        <span className="text-xs text-gray-400">
          Joined: {new Date(user.createdAt).toLocaleDateString()}
        </span>
      </div>
    </div>
  );
};
