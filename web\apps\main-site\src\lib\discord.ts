// Discord API endpoints and utility functions
export const DISCORD_API_URL = "https://discord.com/api/v10";
export const DISCORD_AUTH_URL = `${DISCORD_API_URL}/oauth2/authorize`;
export const DISCORD_TOKEN_URL = `${DISCORD_API_URL}/oauth2/token`;
export const DISCORD_USER_URL = `${DISCORD_API_URL}/users/@me`;

export interface DiscordTokenResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  refresh_token: string;
  scope: string;
}

export interface DiscordUser {
  id: string;
  username: string;
  discriminator: string;
  avatar: string | null;
  email: string | null;
  verified: boolean;
}

// Generate Discord OAuth URL
export function getDiscordAuthUrl(state?: string) {
  const params = new URLSearchParams({
    client_id: process.env.DISCORD_CLIENT_ID || "",
    redirect_uri: process.env.DISCORD_REDIRECT_URI || "",
    response_type: "code",
    scope: "identify email",
  });

  // Add state parameter if provided
  if (state) {
    params.append("state", state);
  }

  return `${DISCORD_AUTH_URL}?${params.toString()}`;
}

// Exchange code for token
export async function exchangeCodeForToken(
  code: string,
): Promise<DiscordTokenResponse> {
  const params = new URLSearchParams({
    client_id: process.env.DISCORD_CLIENT_ID || "",
    client_secret: process.env.DISCORD_CLIENT_SECRET || "",
    grant_type: "authorization_code",
    code,
    redirect_uri: process.env.DISCORD_REDIRECT_URI || "",
  });

  const response = await fetch(DISCORD_TOKEN_URL, {
    method: "POST",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    body: params.toString(),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(
      error.error_description || "Failed to exchange code for token",
    );
  }

  return response.json();
}

// Get Discord user data
export async function getDiscordUser(
  accessToken: string,
): Promise<DiscordUser> {
  const response = await fetch(DISCORD_USER_URL, {
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || "Failed to get Discord user");
  }

  return response.json();
}
