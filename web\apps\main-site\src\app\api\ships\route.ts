import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";

export const dynamic = 'force-dynamic';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search') || '';
    const sort = searchParams.get('sort') || 'newest';
    const tag = searchParams.get('tag') || '';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '12');
    const skip = (page - 1) * limit;

    // Build search conditions
    const searchConditions: any = {
      status: 'active',
    };

    // Handle search conditions
    const orConditions = [];
    
    if (search) {
      orConditions.push(
        { name: { contains: search } },
        { captain: { username: { contains: search } } },
        { captain: { displayName: { contains: search } } }
      );

      // If search looks like a tag, also search in tags JSON field
      if (search.startsWith('#') || search.length < 20) {
        const tagSearch = search.replace('#', '');
        orConditions.push({
          tags: {
            string_contains: tagSearch
          }
        });
      }
    }

    // Handle specific tag filter
    if (tag) {
      searchConditions.tags = {
        string_contains: tag
      };
    }

    // Apply OR conditions if any exist
    if (orConditions.length > 0) {
      searchConditions.OR = orConditions;
    }

    // Build sorting options
    let orderBy: any = { createdAt: 'desc' }; // default
    switch (sort) {
      case 'oldest':
        orderBy = { createdAt: 'asc' };
        break;
      case 'name':
        orderBy = { name: 'asc' };
        break;
      case 'members':
        orderBy = { members: { _count: 'desc' } };
        break;
      case 'newest':
      default:
        orderBy = { createdAt: 'desc' };
        break;
    }

    // Get ships with pagination
    const [ships, total] = await Promise.all([
      prisma.ship.findMany({
        where: searchConditions,
        include: {
          captain: {
            select: {
              id: true,
              username: true,
              displayName: true,
              avatar: true,
            },
          },
          members: {
            where: { status: 'active' },
            select: { id: true },
          },
          _count: {
            select: {
              members: {
                where: { status: 'active' },
              },
            },
          },
        },
        orderBy,
        skip,
        take: limit,
      }),
      prisma.ship.count({
        where: searchConditions,
      }),
    ]);

    // Transform ships data
    const transformedShips = ships.map(ship => ({
      id: ship.id,
      name: ship.name,
      description: ship.description,
      slogan: ship.slogan,
      logo: ship.logo,
      tags: ship.tags as string[] || [],
      captain: ship.captain,
      memberCount: ship._count.members,
      createdAt: ship.createdAt.toISOString(),
    }));

    return NextResponse.json({
      ships: transformedShips,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching ships:", error);
    return NextResponse.json(
      { error: "Failed to fetch ships" },
      { status: 500 }
    );
  }
}