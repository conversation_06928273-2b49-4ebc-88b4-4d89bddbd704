"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "../../../../../contexts/AuthContext";
import { AdminDashboardLayout } from "../../../../../components/admin";
import {
  getTicketById,
  updateTicket,
  getTicketNotes,
  addTicketNote,
  SupportTicket,
  TicketNote,
} from "../../../../../services/ticketService";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { But<PERSON>, Card } from "@bank-of-styx/ui";
import toast from "react-hot-toast";

export default function TicketDetailPage({
  params,
}: {
  params: { id: string };
}) {
  const { id } = params;
  const { user, isLoading } = useAuth();
  const router = useRouter();
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [noteContent, setNoteContent] = useState("");
  const [isInternal, setIsInternal] = useState(false);
  const [resolution, setResolution] = useState("");
  const queryClient = useQueryClient();

  // Fetch ticket details
  const {
    data: ticket,
    isLoading: isLoadingTicket,
    error: ticketError,
  } = useQuery({
    queryKey: ["ticket", id],
    queryFn: () => getTicketById(id),
    enabled: !!user?.roles?.admin && !!id, // Only fetch if user is admin and id exists
  });

  // Fetch ticket notes
  const {
    data: notes,
    isLoading: isLoadingNotes,
    error: notesError,
  } = useQuery({
    queryKey: ["ticketNotes", id],
    queryFn: () => getTicketNotes(id),
    enabled: !!user?.roles?.admin && !!id, // Only fetch if user is admin and id exists
  });

  // Mutation for updating ticket
  const updateTicketMutation = useMutation({
    mutationFn: (data: {
      status?: "open" | "in_progress" | "resolved" | "closed";
      priority?: "low" | "medium" | "high" | "urgent";
      category?: string;
      assignedToId?: string;
      resolution?: string;
    }) => updateTicket(id, data),
    onSuccess: () => {
      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: ["ticket", id] });
      queryClient.invalidateQueries({ queryKey: ["adminTickets"] });
      toast.success("Ticket updated successfully");
    },
    onError: (error) => {
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to update ticket. Please try again.",
      );
    },
  });

  // Mutation for adding a note
  const addNoteMutation = useMutation({
    mutationFn: (data: { content: string; isInternal: boolean }) =>
      addTicketNote(id, data),
    onSuccess: () => {
      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: ["ticketNotes", id] });
      setNoteContent("");
      toast.success("Note added successfully");
    },
    onError: (error) => {
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to add note. Please try again.",
      );
    },
  });

  // Check authorization after auth is loaded
  useEffect(() => {
    // Only proceed with checks if auth loading is complete
    if (!isLoading) {
      if (!user || !user.roles?.admin) {
        router.push("/");
      } else {
        // User is authorized
        setIsAuthorized(true);
      }
    }
  }, [user, router, isLoading]);

  // Handle status change
  const handleStatusChange = (
    status: "open" | "in_progress" | "resolved" | "closed",
  ) => {
    // If resolving, include resolution
    if (status === "resolved") {
      updateTicketMutation.mutate({ status, resolution });
    } else {
      updateTicketMutation.mutate({ status });
    }
  };

  // Handle priority change
  const handlePriorityChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const priority = e.target.value as "low" | "medium" | "high" | "urgent";
    updateTicketMutation.mutate({ priority });
  };

  // Handle category change
  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    updateTicketMutation.mutate({ category: e.target.value });
  };

  // Handle assign to self
  const handleAssignToSelf = () => {
    if (user) {
      updateTicketMutation.mutate({ assignedToId: user.id });
    }
  };

  // Handle add note
  const handleAddNote = (e: React.FormEvent) => {
    e.preventDefault();
    if (!noteContent.trim()) {
      toast.error("Note content is required");
      return;
    }

    addNoteMutation.mutate({
      content: noteContent,
      isInternal,
    });
  };

  // Show loading state or nothing while checking authorization
  if (isLoading || !isAuthorized) {
    return (
      <div className="min-h-screen bg-secondary-dark flex items-center justify-center">
        <div className="bg-secondary-light p-6 rounded-lg shadow-md border border-gray-600">
          <p className="text-white text-lg">Loading...</p>
        </div>
      </div>
    );
  }

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case "open":
        return "bg-blue-500 bg-opacity-20 text-blue-400 border-blue-500";
      case "in_progress":
        return "bg-yellow-500 bg-opacity-20 text-yellow-400 border-yellow-500";
      case "resolved":
        return "bg-green-500 bg-opacity-20 text-green-400 border-green-500";
      case "closed":
        return "bg-gray-500 bg-opacity-20 text-gray-400 border-gray-500";
      default:
        return "bg-gray-500 bg-opacity-20 text-gray-400 border-gray-500";
    }
  };

  // Get priority badge color
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "low":
        return "bg-blue-500 bg-opacity-20 text-blue-400 border-blue-500";
      case "medium":
        return "bg-yellow-500 bg-opacity-20 text-yellow-400 border-yellow-500";
      case "high":
        return "bg-orange-500 bg-opacity-20 text-orange-400 border-orange-500";
      case "urgent":
        return "bg-red-500 bg-opacity-20 text-red-400 border-red-500";
      default:
        return "bg-gray-500 bg-opacity-20 text-gray-400 border-gray-500";
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  return (
    <AdminDashboardLayout>
      <div className="space-y-6">
        {isLoadingTicket ? (
          <div className="bg-secondary rounded-lg shadow-md p-6 border border-gray-600 text-center">
            <p className="text-white">Loading ticket details...</p>
          </div>
        ) : ticketError ? (
          <div className="bg-secondary rounded-lg shadow-md p-6 border border-gray-600 text-center">
            <p className="text-error">
              Error loading ticket details. Please try again.
            </p>
            <Button
              variant="primary"
              className="mt-4"
              onClick={() => router.push("/admin/dashboard/tickets")}
            >
              Back to Tickets
            </Button>
          </div>
        ) : !ticket ? (
          <div className="bg-secondary rounded-lg shadow-md p-6 border border-gray-600 text-center">
            <p className="text-white">Ticket not found</p>
            <Button
              variant="primary"
              className="mt-4"
              onClick={() => router.push("/admin/dashboard/tickets")}
            >
              Back to Tickets
            </Button>
          </div>
        ) : (
          <>
            {/* Ticket Header */}
            <div className="flex justify-between items-start mb-6">
              <div>
                <h2 className="text-2xl font-bold text-white">
                  {ticket.subject}
                </h2>
                <div className="flex items-center mt-2 space-x-4">
                  <span
                    className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full border ${getStatusColor(
                      ticket.status,
                    )}`}
                  >
                    {ticket.status.replace("_", " ")}
                  </span>
                  <span
                    className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full border ${getPriorityColor(
                      ticket.priority,
                    )}`}
                  >
                    {ticket.priority}
                  </span>
                  <span className="text-gray-400 text-sm">
                    {ticket.category}
                  </span>
                </div>
              </div>
              <Button
                variant="outline"
                onClick={() => router.push("/admin/dashboard/tickets")}
              >
                Back to Tickets
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Ticket Details */}
              <div className="space-y-4">
                <div className="bg-secondary rounded-lg shadow-md p-4 border border-gray-600">
                  <h3 className="text-lg font-semibold text-white mb-3">
                    Ticket Information
                  </h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-gray-400 text-sm">Ticket ID</p>
                      <p className="text-white">{ticket.id}</p>
                    </div>
                    <div>
                      <p className="text-gray-400 text-sm">Created</p>
                      <p className="text-white">
                        {formatDate(ticket.createdAt)}
                      </p>
                    </div>
                    <div>
                      <p className="text-gray-400 text-sm">Last Updated</p>
                      <p className="text-white">
                        {formatDate(ticket.updatedAt)}
                      </p>
                    </div>
                    <div>
                      <p className="text-gray-400 text-sm">Status</p>
                      <select
                        id="status"
                        value={ticket.status}
                        onChange={(e) =>
                          handleStatusChange(
                            e.target.value as
                              | "open"
                              | "in_progress"
                              | "resolved"
                              | "closed",
                          )
                        }
                        className="w-full px-4 py-2 bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary appearance-none"
                      >
                        <option value="open">Open</option>
                        <option value="in_progress">In Progress</option>
                        <option value="resolved">Resolved</option>
                        <option value="closed">Closed</option>
                      </select>
                    </div>
                    <div>
                      <p className="text-gray-400 text-sm">Priority</p>
                      <select
                        id="priority"
                        value={ticket.priority}
                        onChange={handlePriorityChange}
                        className="w-full px-4 py-2 bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary appearance-none"
                      >
                        <option value="low">Low</option>
                        <option value="medium">Medium</option>
                        <option value="high">High</option>
                        <option value="urgent">Urgent</option>
                      </select>
                    </div>
                    <div>
                      <p className="text-gray-400 text-sm">Category</p>
                      <select
                        id="category"
                        value={ticket.category}
                        onChange={handleCategoryChange}
                        className="w-full px-4 py-2 bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary appearance-none"
                      >
                        <option value="general">General</option>
                        <option value="account">Account</option>
                        <option value="banking">Banking</option>
                        <option value="technical">Technical</option>
                        <option value="other">Other</option>
                      </select>
                    </div>
                  </div>
                </div>

                <div className="bg-secondary rounded-lg shadow-md p-4 border border-gray-600">
                  <h3 className="text-lg font-semibold text-white mb-3">
                    Contact Information
                  </h3>
                  <div className="grid grid-cols-1 gap-4">
                    <div>
                      <p className="text-gray-400 text-sm">Name</p>
                      <p className="text-white">{ticket.name || "N/A"}</p>
                    </div>
                    <div>
                      <p className="text-gray-400 text-sm">Email</p>
                      <p className="text-white">{ticket.email}</p>
                    </div>
                    <div>
                      <p className="text-gray-400 text-sm">Phone</p>
                      <p className="text-white">{ticket.phone || "N/A"}</p>
                    </div>
                    {ticket.user && (
                      <div>
                        <p className="text-gray-400 text-sm">User Account</p>
                        <div className="flex items-center mt-1">
                          <div className="flex-shrink-0 h-8 w-8">
                            <img
                              className="h-8 w-8 rounded-full"
                              src={ticket.user.avatar}
                              alt={ticket.user.displayName}
                            />
                          </div>
                          <div className="ml-2 text-white">
                            {ticket.user.displayName} (@
                            {ticket.user.username})
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Message and Assignment */}
              <div className="space-y-4">
                <div className="bg-secondary rounded-lg shadow-md p-4 border border-gray-600">
                  <h3 className="text-lg font-semibold text-white mb-3">
                    Original Message
                  </h3>
                  <div className="whitespace-pre-wrap text-white">
                    {ticket.message}
                  </div>
                </div>

                <div className="bg-secondary rounded-lg shadow-md p-4 border border-gray-600">
                  <h3 className="text-lg font-semibold text-white mb-3">
                    Assignment
                  </h3>
                  {ticket.assignedTo ? (
                    <div>
                      <p className="text-gray-400 text-sm">Assigned To</p>
                      <div className="flex items-center mt-2">
                        <div className="flex-shrink-0 h-10 w-10">
                          <img
                            className="h-10 w-10 rounded-full"
                            src={ticket.assignedTo.avatar}
                            alt={ticket.assignedTo.displayName}
                          />
                        </div>
                        <div className="ml-3">
                          <p className="text-white">
                            {ticket.assignedTo.displayName}
                          </p>
                          <p className="text-gray-400 text-sm">
                            @{ticket.assignedTo.username}
                          </p>
                        </div>
                      </div>
                      <p className="text-gray-400 text-sm mt-2">Assigned At</p>
                      <p className="text-white">
                        {ticket.assignedAt
                          ? formatDate(ticket.assignedAt)
                          : "N/A"}
                      </p>
                    </div>
                  ) : (
                    <div className="text-center py-4">
                      <p className="text-gray-400 mb-3">
                        This ticket is not assigned to anyone
                      </p>
                      <Button variant="primary" onClick={handleAssignToSelf}>
                        Assign
                      </Button>
                    </div>
                  )}
                </div>

                {/* Resolution (only show if status is resolved or closed) */}
                {(ticket.status === "resolved" ||
                  ticket.status === "closed") && (
                  <div className="bg-secondary rounded-lg shadow-md p-4 border border-gray-600">
                    <h3 className="text-lg font-semibold text-white mb-3">
                      Resolution
                    </h3>
                    {ticket.resolution ? (
                      <div className="whitespace-pre-wrap text-white">
                        {ticket.resolution}
                      </div>
                    ) : (
                      <p className="text-gray-400">
                        No resolution details provided
                      </p>
                    )}
                    {ticket.resolvedBy && (
                      <div className="mt-4">
                        <p className="text-gray-400 text-sm">Resolved By</p>
                        <div className="flex items-center mt-2">
                          <div className="flex-shrink-0 h-8 w-8">
                            <img
                              className="h-8 w-8 rounded-full"
                              src={ticket.resolvedBy.avatar}
                              alt={ticket.resolvedBy.displayName}
                            />
                          </div>
                          <div className="ml-2 text-white">
                            {ticket.resolvedBy.displayName}
                          </div>
                        </div>
                        <p className="text-gray-400 text-sm mt-2">
                          Resolved At
                        </p>
                        <p className="text-white">
                          {ticket.resolvedAt
                            ? formatDate(ticket.resolvedAt)
                            : "N/A"}
                        </p>
                      </div>
                    )}
                  </div>
                )}

                {/* Resolution Form (only show if changing to resolved) */}
                {ticket.status !== "resolved" && ticket.status !== "closed" && (
                  <div className="bg-secondary rounded-lg shadow-md p-4 border border-gray-600">
                    <h3 className="text-lg font-semibold text-white mb-3">
                      Resolution Details
                    </h3>
                    <p className="text-gray-400 text-sm mb-2">
                      Enter resolution details to be sent to the user when
                      marking as resolved
                    </p>
                    <textarea
                      value={resolution}
                      onChange={(e) => setResolution(e.target.value)}
                      placeholder="Enter resolution details..."
                      rows={4}
                      className="w-full min-h-[100px] resize-y px-4 py-2 rounded-md shadow-sm text-white
                        border-gray-600 focus:border-primary focus:ring-primary
                        focus:outline-none focus:ring-2 focus:ring-offset-0
                        disabled:bg-secondary-dark disabled:text-disabled disabled:cursor-not-allowed
                        bg-[#2C2F33]"
                    />
                    <div className="mt-3 flex justify-end">
                      <Button
                        variant="primary"
                        onClick={() => handleStatusChange("resolved" as const)}
                        disabled={!resolution.trim()}
                      >
                        Mark as Resolved
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Ticket Notes */}
            <div className="mt-6">
              <h3 className="text-xl font-bold text-white mb-4">
                Ticket Communication
              </h3>

              {/* Notes List */}
              <div className="space-y-4 mb-6">
                {isLoadingNotes ? (
                  <div className="text-center py-4 bg-secondary rounded-lg border border-gray-600">
                    <p className="text-white">Loading notes...</p>
                  </div>
                ) : notesError ? (
                  <div className="text-center py-4 bg-secondary rounded-lg border border-gray-600">
                    <p className="text-error">
                      Error loading notes. Please try again.
                    </p>
                  </div>
                ) : !notes || notes.length === 0 ? (
                  <div className="text-center py-4 bg-secondary rounded-lg border border-gray-600">
                    <p className="text-white">No notes yet</p>
                  </div>
                ) : (
                  notes.map((note) => (
                    <div
                      key={note.id}
                      className={`p-4 rounded-lg border ${
                        note.isInternal
                          ? "bg-secondary-dark border-gray-600"
                          : "bg-secondary border-primary"
                      }`}
                    >
                      <div className="flex justify-between items-start mb-2">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-8 w-8">
                            <img
                              className="h-8 w-8 rounded-full"
                              src={note.author?.avatar}
                              alt={note.author?.displayName}
                            />
                          </div>
                          <div className="ml-2">
                            <p className="text-white font-medium">
                              {note.author?.displayName}
                            </p>
                            <p className="text-gray-400 text-xs">
                              {formatDate(note.createdAt)}
                            </p>
                          </div>
                        </div>
                        {note.isInternal && (
                          <span className="px-2 py-1 text-xs rounded-full bg-yellow-500 bg-opacity-20 text-yellow-400 border border-yellow-500">
                            Internal Note
                          </span>
                        )}
                      </div>
                      <div className="mt-2 text-white whitespace-pre-wrap">
                        {note.content}
                      </div>
                    </div>
                  ))
                )}
              </div>

              {/* Add Note Form */}
              <div className="bg-secondary rounded-lg shadow-md p-4 border border-gray-600">
                <h4 className="text-lg font-semibold text-white mb-3">
                  Add Note
                </h4>
                <form onSubmit={handleAddNote}>
                  <div className="space-y-4">
                    <div>
                      <textarea
                        value={noteContent}
                        onChange={(e) => setNoteContent(e.target.value)}
                        placeholder="Enter your note..."
                        rows={4}
                        className="w-full min-h-[120px] resize-y px-4 py-2 rounded-md shadow-sm text-white
                          border-gray-600 focus:border-primary focus:ring-primary
                          focus:outline-none focus:ring-2 focus:ring-offset-0
                          disabled:bg-secondary-dark disabled:text-disabled disabled:cursor-not-allowed
                          bg-[#2C2F33]"
                        required
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="isInternal"
                          checked={isInternal}
                          onChange={(e) => setIsInternal(e.target.checked)}
                          className="h-4 w-4 text-primary focus:ring-primary border-gray-600 rounded"
                        />
                        <label
                          htmlFor="isInternal"
                          className="ml-2 text-sm text-white"
                        >
                          Internal note (not visible to user)
                        </label>
                      </div>
                      <Button
                        variant="primary"
                        type="submit"
                        disabled={!noteContent.trim()}
                      >
                        Add Note
                      </Button>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </>
        )}
      </div>
    </AdminDashboardLayout>
  );
}
