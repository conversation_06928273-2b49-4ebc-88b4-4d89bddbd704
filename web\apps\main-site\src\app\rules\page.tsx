"use client";

import React, { useState } from "react";
import { <PERSON>, Button } from "@bank-of-styx/ui";
import { useRouter } from "next/navigation";
import Link from "next/link";

export default function RulesPage() {
  const router = useRouter();
  const [selectedRuleSet, setSelectedRuleSet] = useState<string>("");
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const handleRuleSetChange = (value: string) => {
    setSelectedRuleSet(value);
    setIsDropdownOpen(false);

    if (value) {
      router.push(`/rules/${value}`);
    }
  };

  const ruleOptions = [
    { value: "auctions", label: "Auction Rules" },
    { value: "merchants", label: "Merchant Rules" },
    { value: "chat", label: "Chat Rules" },
  ];

  return (
    <div className="container mx-auto px-4 py-4 md:py-6 lg:py-8 max-w-3xl">
      {/* Mantra Banner */}
      <div className="bg-primary bg-opacity-20 border border-primary rounded-lg p-4 mb-6 text-center">
        <h2 className="text-xl md:text-2xl font-bold text-white mb-1">
          Bank of Styx Motto
        </h2>
        <p className="text-lg md:text-xl italic text-white">
          &quot;Do as you will, yet harm none&quot;
        </p>
      </div>

      {/* Main Rules Dashboard */}
      <Card title="Rules & Guidelines" className="mb-6" headerAction={null}>
        <div className="space-y-4">
          <p className="text-white">
            Welcome to the Bank of Styx community. Our rules are designed to
            create a fair, respectful, and enjoyable environment for all
            members. Please take the time to familiarize yourself with our
            guidelines before participating in auctions, merchant activities, or
            community chats.
          </p>

          {/* Rules Summary */}
          <div className="bg-secondary p-4 rounded-lg border border-gray-600">
            <h3 className="text-lg font-semibold mb-2 text-white">
              Key Principles
            </h3>
            <ul className="list-disc pl-6 space-y-1 text-white">
              <li>Respect all community members and administrators</li>
              <li>Conduct transactions honestly and transparently</li>
              <li>
                Follow specific guidelines for auctions, merchants, and chat
              </li>
              <li>
                Report issues to administrators rather than escalating publicly
              </li>
              <li>Remember that participation is at your own risk</li>
            </ul>
          </div>
        </div>
      </Card>

      {/* Rules Navigation */}
      <Card
        title="Browse Rules by Category"
        className="mb-6"
        headerAction={null}
      >
        <div className="space-y-4">
          {/* Mobile-first dropdown for rule selection */}
          <div className="relative">
            <button
              type="button"
              onClick={() => setIsDropdownOpen(!isDropdownOpen)}
              className="flex items-center justify-between w-full px-4 py-3 text-white bg-secondary border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
            >
              <span>
                {selectedRuleSet
                  ? ruleOptions.find((opt) => opt.value === selectedRuleSet)
                      ?.label
                  : "Select Rule Category"}
              </span>
              <svg
                className={`w-5 h-5 text-gray-400 transition-transform ${
                  isDropdownOpen ? "transform rotate-180" : ""
                }`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </button>

            {isDropdownOpen && (
              <div className="absolute z-10 w-full mt-1 bg-secondary border border-gray-600 rounded-md shadow-lg">
                <ul className="py-1">
                  {ruleOptions.map((option) => (
                    <li
                      key={option.value}
                      onClick={() => handleRuleSetChange(option.value)}
                      className="px-4 py-2 cursor-pointer hover:bg-secondary-light text-white"
                    >
                      {option.label}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>

          {/* Rule category cards - visible on larger screens */}
          <div className="hidden md:grid md:grid-cols-3 gap-4 mt-4">
            {ruleOptions.map((option) => (
              <Link
                href={`/rules/${option.value}`}
                key={option.value}
                className="block"
              >
                <div className="bg-secondary hover:bg-secondary-light p-4 rounded-lg border border-gray-600 text-center transition-colors cursor-pointer h-full flex flex-col justify-between">
                  <div className="mb-2">
                    {option.value === "auctions" && (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-8 w-8 mx-auto text-primary"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
                        />
                      </svg>
                    )}
                    {option.value === "merchants" && (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-8 w-8 mx-auto text-primary"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"
                        />
                      </svg>
                    )}
                    {option.value === "chat" && (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-8 w-8 mx-auto text-primary"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                        />
                      </svg>
                    )}
                  </div>
                  <h3 className="text-lg font-semibold text-white">
                    {option.label}
                  </h3>
                  <p className="text-sm text-gray-300 mt-2">
                    {option.value === "auctions" &&
                      "Guidelines for buying and selling through auctions"}
                    {option.value === "merchants" &&
                      "Requirements for merchant listings and conduct"}
                    {option.value === "chat" &&
                      "Community standards for chat participation"}
                  </p>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </Card>

      {/* Important Notice */}
      <Card title="Important Notice" className="mb-6" headerAction={null}>
        <div className="p-4 bg-accent bg-opacity-20 border border-accent rounded-lg">
          <p className="text-white">
            <strong>Reminder:</strong> The Bank of Styx operates with fictional
            currency (Sterling) and is not responsible for disputes between
            members. All transactions and interactions are at your own risk.
            Administrators reserve the right to remove members who violate our
            community standards.
          </p>
        </div>
      </Card>

      {/* Contact Information */}
      <div className="text-center py-4">
        <p className="text-white mb-2">
          Have questions about our rules? Contact an administrator for
          clarification.
        </p>
        <Link href="/help">
          <Button variant="primary" size="sm">
            Contact Support
          </Button>
        </Link>
      </div>
    </div>
  );
}
