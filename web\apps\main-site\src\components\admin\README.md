# Admin Components

Administrative interface components for system management and user administration.

## Components

- **AdminDashboardLayout.tsx** - Main layout component for admin dashboard pages
- **AddUserModal.tsx** - Modal component for creating new user accounts
- **UserActionButtons.tsx** - Action buttons for user management operations
- **UserProfileCard.tsx** - Card component displaying user profile information
- **UserProfileModal.tsx** - Modal for viewing and editing user profiles
- **UserRoleBadges.tsx** - Badge components for displaying user roles and permissions
- **UserStatusBadge.tsx** - Status indicators for user account states
- **CSVExportButton.tsx** - Data export functionality for administrative reports
- **index.ts** - Component exports for the admin module

These components provide a comprehensive administrative interface for managing users, viewing system data, and performing administrative tasks.
