import React, { useState } from "react";
import { toast } from "react-hot-toast";
import { EditUsesModal } from "./modals/EditUsesModal";
import { PayCode } from "../../../../../services/bankService";
import {
  useActiveCodes,
  useRedeemedCodes,
  useUpdatePayCode,
  useUpdatePayCodeUses,
  useCancelPayCode,
} from "../../../../../hooks/useBank";

// No props needed for this component
type ManagePayCodeProps = Record<string, never>;

export const ManagePayCode: React.FC<ManagePayCodeProps> = () => {
  const [showEditUsesModal, setShowEditUsesModal] = useState(false);
  const [selectedCode, setSelectedCode] = useState<PayCode | null>(null);
  const [newMaxUses, setNewMaxUses] = useState("");

  // Hooks for API calls
  const updatePayCodeMutation = useUpdatePayCode();
  const updatePayCodeUsesMutation = useUpdatePayCodeUses();
  const cancelPayCodeMutation = useCancelPayCode();
  const { data: activeCodesData, isLoading: isActiveCodesLoading } =
    useActiveCodes();
  const { data: redeemedCodesData, isLoading: isRedeemedCodesLoading } =
    useRedeemedCodes();

  const handleCancelCode = (code: PayCode) => {
    if (confirm(`Are you sure you want to cancel pay code ${code.code}?`)) {
      cancelPayCodeMutation.mutate(code.id, {
        onSuccess: () => {
          toast.success("Pay code cancelled successfully");
        },
        onError: (error: any) => {
          toast.error(error.message || "Failed to cancel pay code");
        },
      });
    }
  };

  const handleTogglePause = (code: PayCode) => {
    const newStatus = code.status === "paused" ? "active" : "paused";
    updatePayCodeMutation.mutate(
      { id: code.id, data: { status: newStatus } },
      {
        onSuccess: () => {
          toast.success(
            `Pay code ${
              newStatus === "paused" ? "paused" : "activated"
            } successfully`,
          );
        },
        onError: (error: any) => {
          toast.error(
            error.message ||
              `Failed to ${
                newStatus === "paused" ? "pause" : "activate"
              } pay code`,
          );
        },
      },
    );
  };

  const handleEditUses = (code: PayCode) => {
    setSelectedCode(code);
    setNewMaxUses(code.maxUses?.toString() || "1");
    setShowEditUsesModal(true);
  };

  const handleSaveUses = () => {
    if (selectedCode) {
      updatePayCodeUsesMutation.mutate(
        { id: selectedCode.id, maxUses: parseInt(newMaxUses) },
        {
          onSuccess: () => {
            toast.success(`Maximum uses updated to ${newMaxUses}`);
            setShowEditUsesModal(false);
            setSelectedCode(null);
          },
          onError: (error: any) => {
            toast.error(error.message || "Failed to update maximum uses");
          },
        },
      );
    }
  };

  const handleCancelEditUses = () => {
    setShowEditUsesModal(false);
    setSelectedCode(null);
  };

  return (
    <>
      <h2 className="text-2xl font-bold text-white mb-6">Manage Pay-Codes</h2>

      <div className="mb-6">
        <h3 className="text-lg font-semibold text-white mb-3">
          Active Pay-Codes
        </h3>

        {isActiveCodesLoading ? (
          <div className="text-center py-6 text-gray-400">
            Loading active pay codes...
          </div>
        ) : activeCodesData && activeCodesData.length > 0 ? (
          <div className="divide-y divide-gray-600">
            {activeCodesData.map((code) => (
              <div key={code.id} className="py-4">
                <div className="flex justify-between items-start">
                  <div>
                    <div className="text-white font-medium">
                      {code.code}
                      {code.status === "paused" && (
                        <span className="ml-2 text-xs bg-warning text-white px-2 py-0.5 rounded">
                          PAUSED
                        </span>
                      )}
                    </div>
                    <div className="text-sm text-gray-400 mt-1">
                      Created: {new Date(code.createdAt).toLocaleDateString()}
                    </div>
                    <div className="text-sm text-gray-400 mt-1">
                      Expires: {new Date(code.expiresAt).toLocaleDateString()}
                    </div>
                    <div className="text-sm text-gray-400 mt-1">
                      Uses:{" "}
                      <span className="font-medium">{code.uses || 0}</span> /{" "}
                      <span className="font-medium">{code.maxUses || 1}</span>
                    </div>
                  </div>
                  <div className="flex flex-col items-end">
                    <div className="font-bold text-primary">
                      NS {code.amount}
                    </div>
                    <div className="flex space-x-2 mt-2">
                      <button
                        onClick={() => handleTogglePause(code)}
                        className={`text-xs px-2 py-1 rounded ${
                          code.status === "paused"
                            ? "bg-success text-white hover:bg-success-dark"
                            : "bg-warning text-white hover:bg-warning-dark"
                        }`}
                      >
                        {code.status === "paused" ? "Activate" : "Pause"}
                      </button>
                      <button
                        onClick={() => handleEditUses(code)}
                        className="text-xs bg-secondary text-white px-2 py-1 rounded hover:bg-hover"
                      >
                        Edit Uses
                      </button>
                      <button
                        onClick={() => handleCancelCode(code)}
                        className="text-xs bg-accent text-white px-2 py-1 rounded hover:bg-accent-dark"
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-6 text-gray-400">
            No active Pay-Codes.
          </div>
        )}
      </div>

      <div>
        <h3 className="text-lg font-semibold text-white mb-3">
          Redeemed Pay-Codes
        </h3>

        {isRedeemedCodesLoading ? (
          <div className="text-center py-6 text-gray-400">
            Loading redeemed pay codes...
          </div>
        ) : redeemedCodesData && redeemedCodesData.length > 0 ? (
          <div className="divide-y divide-gray-600">
            {redeemedCodesData.map((code) => (
              <div key={code.id} className="py-4">
                <div className="flex justify-between items-start">
                  <div>
                    <div className="text-white font-medium">{code.code}</div>
                    <div className="text-sm text-gray-400 mt-1">
                      Created: {new Date(code.createdAt).toLocaleDateString()}
                    </div>
                    <div className="text-sm text-gray-400 mt-1">
                      Redeemed:{" "}
                      {code.redeemedAt
                        ? new Date(code.redeemedAt).toLocaleDateString()
                        : "N/A"}
                    </div>
                    <div className="text-sm text-gray-400 mt-1">
                      Redeemed by:{" "}
                      {code.redeemedBy ? code.redeemedBy.username : "N/A"}
                    </div>
                  </div>
                  <div className="font-bold text-gray-400">
                    NS {code.amount}
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-6 text-gray-400">
            No redeemed Pay-Codes.
          </div>
        )}
      </div>

      {/* Edit Uses Modal */}
      {showEditUsesModal && selectedCode && (
        <EditUsesModal
          selectedCode={selectedCode}
          newMaxUses={newMaxUses}
          setNewMaxUses={setNewMaxUses}
          onSave={handleSaveUses}
          onCancel={handleCancelEditUses}
        />
      )}
    </>
  );
};
