import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// GET /api/events - List published events with filtering
export async function GET(req: NextRequest) {
  try {
    const url = new URL(req.url);

    // Parse query parameters
    const page = parseInt(url.searchParams.get("page") || "1");
    const limit = parseInt(url.searchParams.get("limit") || "10");
    const categoryId = url.searchParams.get("categoryId");
    const search = url.searchParams.get("search");
    const upcoming = url.searchParams.get("upcoming") === "true";
    const featured = url.searchParams.get("featured") === "true";

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Build filter conditions
    const where: any = {
      status: "published", // Only show published events
    };

    if (categoryId) {
      where.categoryId = categoryId;
    }

    if (search) {
      where.OR = [
        { name: { contains: search } },
        { description: { contains: search } },
        { shortDescription: { contains: search } },
        { location: { contains: search } },
      ];
    }

    // Filter for upcoming events (events that haven't ended yet)
    if (upcoming) {
      where.endDate = {
        gte: new Date(),
      };
    }

    // Get total count for pagination
    const total = await prisma.event.count({ where });

    // Prepare orderBy based on parameters
    const orderBy: any = { startDate: "asc" };

    // Get events with pagination and filtering
    const events = await prisma.event.findMany({
      where,
      include: {
        category: true,
      },
      orderBy,
      skip,
      take: limit,
    });

    // Calculate pagination metadata
    const totalPages = Math.ceil(total / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return NextResponse.json({
      events,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNextPage,
        hasPrevPage,
      },
    });
  } catch (error) {
    console.error("Error fetching events:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}
