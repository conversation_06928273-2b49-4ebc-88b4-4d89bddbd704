import React, { useState } from "react";
import { toast } from "react-hot-toast";
import { CreatePayCodeConfirmationModal } from "./modals/CreatePayCodeConfirmationModal";
import { GeneratedCodeSuccessModal } from "./modals/GeneratedCodeSuccessModal";
import { PayCode } from "../../../../../services/bankService";
import { useCreatePayCode } from "../../../../../hooks/useBank";

// No props needed for this component
type CreatePayCodeProps = Record<string, never>;

export const CreatePayCode: React.FC<CreatePayCodeProps> = () => {
  // State management
  const [amount, setAmount] = useState("");
  const [expiryDays, setExpiryDays] = useState("3");
  const [maxUses, setMaxUses] = useState("1");
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [showGeneratedCodeModal, setShowGeneratedCodeModal] = useState(false);
  const [generatedCode, setGeneratedCode] = useState<PayCode | null>(null);

  // Hooks for API calls
  const createPayCodeMutation = useCreatePayCode();

  const handleCreateSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setShowConfirmation(true);
  };

  const handleConfirm = () => {
    // Calculate expiry date
    const expiryDate = new Date();
    expiryDate.setDate(expiryDate.getDate() + parseInt(expiryDays));

    // Create the pay code
    createPayCodeMutation.mutate(
      {
        amount: parseInt(amount),
        expiresAt: expiryDate.toISOString(),
        maxUses: parseInt(maxUses),
      },
      {
        onSuccess: (data) => {
          // Store the generated code and show the success modal
          setGeneratedCode(data);
          setShowConfirmation(false);
          setShowGeneratedCodeModal(true);

          // Reset form
          setAmount("");
          setExpiryDays("3");
          setMaxUses("1");
        },
        onError: (error: any) => {
          setShowConfirmation(false);
          toast.error(error.message || "Failed to create pay code");
        },
      },
    );
  };

  const handleCancel = () => {
    setShowConfirmation(false);
  };

  return (
    <>
      <h2 className="text-2xl font-bold text-white mb-6">Create Pay-Code</h2>

      <div className="mb-4 p-3 bg-secondary rounded-md text-sm text-gray-300">
        <p className="flex items-start">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 mr-2 flex-shrink-0 text-primary"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          <span>
            Create a pay code to <strong>receive payment</strong> from others.
            When someone redeems your code, they will pay you the specified
            amount. You can create pay codes for any amount since you&apos;re
            receiving money, not spending it.
          </span>
        </p>
      </div>

      <form onSubmit={handleCreateSubmit}>
        <div className="space-y-4">
          <div>
            <label
              htmlFor="amount"
              className="block text-white font-medium mb-2"
            >
              Amount
            </label>
            <div className="relative flex items-center">
              <span className="absolute left-3 text-gray-400 select-none pointer-events-none">
                NS
              </span>
              <input
                type="number"
                id="amount"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary"
                placeholder="0"
                min="1"
                step="1"
                required
              />
            </div>
            <p className="text-xs text-gray-400 mt-1">
              Amount you will receive when someone redeems this code (no balance
              check as you&apos;re receiving money)
            </p>
          </div>

          <div>
            <label
              htmlFor="maxUses"
              className="block text-white font-medium mb-2"
            >
              Maximum Uses
            </label>
            <select
              id="maxUses"
              value={maxUses}
              onChange={(e) => setMaxUses(e.target.value)}
              className="w-full px-4 py-2 bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary"
              required
            >
              <option value="1" className="bg-secondary text-white">
                1 use (single-use)
              </option>
              <option value="5" className="bg-secondary text-white">
                5 uses
              </option>
              <option value="10" className="bg-secondary text-white">
                10 uses
              </option>
              <option value="25" className="bg-secondary text-white">
                25 uses
              </option>
              <option value="50" className="bg-secondary text-white">
                50 uses
              </option>
            </select>
            <p className="text-xs text-gray-400 mt-1">
              Number of times this code can be redeemed before it expires
            </p>
          </div>

          <div>
            <label
              htmlFor="expiry"
              className="block text-white font-medium mb-2"
            >
              Expiry
            </label>
            <select
              id="expiry"
              value={expiryDays}
              onChange={(e) => setExpiryDays(e.target.value)}
              className="w-full px-4 py-2 bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary"
              required
            >
              <option value="1" className="bg-secondary text-white">
                1 day
              </option>
              <option value="3" className="bg-secondary text-white">
                3 days
              </option>
              <option value="7" className="bg-secondary text-white">
                7 days
              </option>
              <option value="14" className="bg-secondary text-white">
                14 days
              </option>
              <option value="30" className="bg-secondary text-white">
                30 days
              </option>
            </select>
            <p className="text-xs text-gray-400 mt-1">
              Time until this code expires and can no longer be used
            </p>
          </div>

          <div className="flex justify-end space-x-4 pt-4">
            <button
              type="button"
              onClick={() => {
                setAmount("");
                setExpiryDays("3");
                setMaxUses("1");
              }}
              className="px-4 py-2 border border-gray-600 rounded-md text-white hover:bg-secondary"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark"
            >
              Create Pay-Code
            </button>
          </div>
        </div>
      </form>

      {/* Confirmation Modal */}
      {showConfirmation && (
        <CreatePayCodeConfirmationModal
          amount={amount}
          maxUses={maxUses}
          expiryDays={expiryDays}
          onConfirm={handleConfirm}
          onCancel={handleCancel}
        />
      )}

      {/* Success Modal */}
      {showGeneratedCodeModal && generatedCode && (
        <GeneratedCodeSuccessModal
          generatedCode={generatedCode}
          onClose={() => setShowGeneratedCodeModal(false)}
        />
      )}
    </>
  );
};
