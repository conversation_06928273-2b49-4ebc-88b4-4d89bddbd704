# Volunteer Ship Tracking System - Changelog

**Release Date:** 2025-01-07
**Version:** Phase 4.1 - Ship Volunteer Hour Tracking

## 🆕 New Features

### Volunteer Signup Form Enhancements
- **Ship Auto-Population**: User's ship automatically populates in "staying with" field if they belong to one
- **Ship Search Functionality**: Real-time search dropdown for selecting ships
  - Shows ship name and captain information
  - Debounced search with minimum 2 characters
  - "None/Open Camping" option available
- **Updated Form Layout**: Reorganized fields to match requested template:
  - Pronouns/Preferred Name moved after Last Name
  - E-mail field repositioned
  - Ship selection fields with search capabilities
- **Enhanced Validation**: Updated validation to work with ship selections

### Ship Search Component
- **New Component**: `ShipSearch.tsx` - Reusable ship selection component
- **Features**:
  - Type-ahead search with dropdown results
  - Displays ship name and captain details
  - Option to select "None/Open Camping"
  - Keyboard navigation support (Escape to close)
  - Click-outside-to-close functionality

### Database Schema Updates
- **Enhanced VolunteerHours Model**:
  - Added `creditShipId` field to track which ship gets credit
  - Added `isDockHours` boolean field
  - Added `isLandGrant` boolean field
  - New relationship: `creditShip` linking to Ship model
- **New Ship Relationship**: Ships now have `creditedVolunteerHours` relationship
- **Migration**: `20250806081829_add_ship_volunteer_hour_tracking`

### Land Steward Dashboard
- **New Tab**: "Volunteer Requirements" added to land steward dashboard
- **Dedicated Page**: `/land-steward/volunteer-requirements`
  - Overview of all ship volunteer requirements
  - Progress tracking with visual progress bars
  - Status filtering (all, pending, in_progress, completed)
  - Summary statistics dashboard
  - Pagination support
  - Overdue detection (>30 days old)

### API Enhancements
- **Updated Volunteer Signup API** (`/api/volunteer/public/shifts/[id]/signup`):
  - Now captures `stayingWithShipId` and `landGrantCreditShipId`
  - Stores ship information in assignment metadata
- **Enhanced Attendance Processing** (`/api/volunteer/lead/shifts/[id]/attendance`):
  - Automatically links completed hours to credit ships
  - Updates ship volunteer requirements progress
  - Processes dock and land grant hour classifications
- **New Land Steward API** (`/api/land-steward/volunteer-requirements`):
  - GET endpoint with filtering and pagination
  - Returns comprehensive requirement data with progress calculations

### React Hooks & Services
- **New Hook**: `useShipSearch(query)` - Real-time ship search functionality
- **New Hook**: `useUserShip()` - Retrieves user's current ship membership
- **New Hook**: `useDebounce(value, delay)` - Generic debouncing utility
- **Enhanced Types**: Extended `VolunteerSignupFormData` with ship ID fields

## 🔧 Technical Improvements

### Volunteer Hour Processing
- **Automatic Ship Credit**: Hours are automatically credited to specified ships upon completion
- **Requirement Updates**: Ship volunteer requirements are automatically updated when hours are completed
- **Progress Calculation**: Real-time calculation of completion percentages
- **Status Management**: Automatic progression from pending → in_progress → completed

### Data Flow
1. **Signup**: User selects ships during volunteer signup
2. **Assignment**: Ship IDs stored in volunteer assignment metadata
3. **Completion**: When hours are marked complete, they're linked to credit ship
4. **Tracking**: Ship requirements are automatically updated with completed hours
5. **Monitoring**: Land stewards can monitor all ship requirements and progress

### Error Handling
- **Graceful Fallbacks**: Ship search handles empty results and loading states
- **Validation**: Enhanced form validation for ship selections
- **Error Boundaries**: Proper error handling in ship selection components

## 📊 Land Steward Features

### Dashboard Statistics
- **Total Requirements**: Overview of all ship volunteer requirements
- **Status Breakdown**: Count of pending, in progress, and completed requirements
- **Overdue Tracking**: Identification of requirements past 30 days
- **Progress Monitoring**: Visual progress bars showing completion percentage

### Filtering & Navigation
- **Status Filters**: Filter requirements by completion status
- **Search Capability**: Built on existing ship search infrastructure  
- **Pagination**: Handle large numbers of ship requirements
- **Detail Views**: Click through to view detailed ship information

## 🗃️ Database Changes

### New Fields
```sql
-- VolunteerHours table additions
ALTER TABLE volunteer_hours ADD COLUMN creditShipId VARCHAR(191);
ALTER TABLE volunteer_hours ADD COLUMN isDockHours BOOLEAN DEFAULT FALSE;
ALTER TABLE volunteer_hours ADD COLUMN isLandGrant BOOLEAN DEFAULT FALSE;
```

### New Relationships
- `VolunteerHours.creditShip` → `Ship` (many-to-one)
- `Ship.creditedVolunteerHours` → `VolunteerHours[]` (one-to-many)

## 🎯 Business Impact

### For Volunteers
- **Easier Signup**: Auto-populated ship information
- **Better UX**: Search-enabled ship selection
- **Clear Attribution**: Explicit ship credit assignment

### For Ship Captains
- **Hour Tracking**: Visibility into volunteer hours credited to their ship
- **Requirement Monitoring**: Track progress toward volunteer commitments

### For Land Stewards
- **Compliance Monitoring**: Comprehensive view of all ship volunteer requirements
- **Progress Tracking**: Real-time updates on requirement completion
- **Overdue Management**: Easy identification of ships behind on commitments

## 🔄 Migration Notes

### Existing Data
- Existing volunteer assignments are unaffected
- New ship tracking applies only to future volunteer signups
- Historical hours remain unchanged but can be manually attributed if needed

### Backward Compatibility
- All existing volunteer functionality remains intact
- New fields are optional and default to appropriate values
- API responses include new fields but maintain existing structure

## 🚀 Future Enhancements

### Potential Additions
- **Ship Dashboard**: Individual ship view of their volunteer hours and requirements
- **Automated Notifications**: Alert ships when they're behind on requirements
- **Reporting**: Export capabilities for ship volunteer hour reports
- **Integration**: Link with event forms that create volunteer requirements

---

**Files Modified:**
- `src/components/volunteer/public/VolunteerSignupModal.tsx`
- `src/components/volunteer/public/ShipSearch.tsx` (new)
- `src/hooks/usePublicVolunteer.ts`
- `src/hooks/useDebounce.ts` (new)
- `src/app/api/volunteer/public/shifts/[id]/signup/route.ts`
- `src/app/api/volunteer/lead/shifts/[id]/attendance/route.ts`
- `src/app/api/land-steward/volunteer-requirements/route.ts` (new)
- `src/app/land-steward/volunteer-requirements/page.tsx` (new)
- `src/app/land-steward/page.tsx`
- `prisma/schema.prisma`
- `src/app/api/captain/members/invite/route.ts` (bug fix)

**Database Migration:**
- `migrations/20250806081829_add_ship_volunteer_hour_tracking/migration.sql`