"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Button, Input, Textarea, Checkbox, Card } from "@bank-of-styx/ui";
import {
  useCreateProductCategory,
  useUpdateProductCategory,
} from "@/hooks/useProductCategories";
import { ProductCategory } from "@/services/productService";
import { toast } from "react-hot-toast";

interface CategoryFormProps {
  initialData?: ProductCategory;
  isEditing?: boolean;
  onSuccess?: () => void;
}

interface CategoryFormData {
  name: string;
  description: string;
  isActive: boolean;
}

export const CategoryForm: React.FC<CategoryFormProps> = ({
  initialData,
  isEditing = false,
  onSuccess,
}) => {
  const router = useRouter();
  const createCategory = useCreateProductCategory();
  const updateCategory = useUpdateProductCategory();

  // Form state
  const [formData, setFormData] = useState<CategoryFormData>({
    name: "",
    description: "",
    isActive: true,
  });

  // Form validation
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Initialize form with data if editing
  useEffect(() => {
    if (isEditing && initialData) {
      setFormData({
        name: initialData.name,
        description: initialData.description || "",
        isActive: initialData.isActive,
      });
    }
  }, [isEditing, initialData]);

  // Handle form input changes
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value, type } = e.target;

    if (type === "checkbox") {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData((prev) => ({ ...prev, [name]: checked }));
    } else {
      setFormData((prev) => ({ ...prev, [name]: value }));
    }

    // Clear error when field is edited
    if (errors[name]) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = "Category name is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      if (isEditing && initialData) {
        await updateCategory.mutateAsync({
          id: initialData.id,
          data: {
            name: formData.name,
            description: formData.description,
            isActive: formData.isActive,
          },
        });
        toast.success("Category updated successfully");
      } else {
        await createCategory.mutateAsync({
          name: formData.name,
          description: formData.description,
          isActive: formData.isActive,
        });
        toast.success("Category created successfully");
      }

      if (onSuccess) {
        onSuccess();
      } else {
        router.push("/sales/categories");
      }
    } catch (error) {
      console.error("Error saving category:", error);
      toast.error("Failed to save category");
    }
  };

  return (
    <Card
      title={isEditing ? "Edit Category" : "Create New Category"}
      className="mb-6"
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Basic Information */}
        <div>
          <Input
            label="Category Name"
            name="name"
            value={formData.name}
            onChange={handleChange}
            error={errors.name}
            helperText={errors.name}
            fullWidth
          />
        </div>

        {/* Description */}
        <div>
          <Textarea
            label="Description"
            name="description"
            value={formData.description}
            onChange={handleChange}
            rows={4}
            fullWidth
          />
        </div>

        {/* Active Status */}
        <div className="flex items-center">
          <Checkbox
            name="isActive"
            checked={formData.isActive}
            onChange={handleChange}
            label="Active (visible to customers)"
          />
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-2 mt-6">
          <Button
            variant="outline"
            onClick={() => router.push("/sales/categories")}
            type="button"
          >
            Cancel
          </Button>
          <Button
            variant="primary"
            type="submit"
            loading={createCategory.isPending || updateCategory.isPending}
          >
            {isEditing ? "Update Category" : "Create Category"}
          </Button>
        </div>
      </form>
    </Card>
  );
};
