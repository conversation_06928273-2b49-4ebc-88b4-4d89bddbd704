# System Utilities & Cron Jobs Documentation

## Overview
The Bank of Styx platform includes comprehensive system utilities for automated maintenance, background processing, development testing, and system setup. These utilities ensure reliable operation, data integrity, and operational efficiency across the entire platform.

## Cron Jobs & Scheduled Tasks

### Background Processing Endpoint
**Location**: `/api/cron/release-expired-holds`

#### Expired Holds Cleanup System
The platform implements a 15-minute hold system for tickets and capacity reservations. This cron job manages the automatic cleanup of expired holds to prevent overselling and maintain data integrity.

**Processing Flow**:
```typescript
1. Event Capacity Holds Release
   ↓
2. Ticket Holds Release  
   ↓
3. Volunteer Slot Holds Release
   ↓
4. Database Transaction Commit
   ↓
5. Cleanup Results Logging
```

**What Gets Cleaned**:
- **Event Capacity Holds**: Expired event capacity reservations
- **Ticket Holds**: Individual ticket reservations (shopping cart items)  
- **Volunteer Slot Holds**: Volunteer shift reservations
- **Status Updates**: Changes `HELD` status back to `AVAILABLE`

**Security & Authentication**:
```typescript
// Requires CRON_SECRET_KEY environment variable
const authHeader = req.headers.get("authorization");
if (authHeader !== `Bearer ${process.env.CRON_SECRET_KEY}`) {
  return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
}
```

**Response Format**:
```typescript
{
  eventCapacityHoldsProcessed: number,
  eventCapacityReleased: number,
  ticketHoldsProcessed: number,
  volunteerHoldsProcessed: number,
  ticketsReleased: number,
  slotsReleased: number,
  timestamp: Date
}
```

### Scheduled Execution
The cron job should be configured to run every 5-10 minutes to ensure timely cleanup:

```bash
# Example cron configuration (every 5 minutes)
*/5 * * * * curl -X POST https://your-domain.com/api/cron/release-expired-holds \
  -H "Authorization: Bearer $CRON_SECRET_KEY"
```

## System Setup Utilities

### Setup API Endpoints
**Location**: `/api/setup/`

#### Database Initialization (`/api/setup/first-time-seed`)
- **Purpose**: Initial database seeding for new deployments  
- **Features**: Creates default categories, admin users, and system data
- **Usage**: One-time deployment setup
- **Security**: Admin-only access for production safety

#### Category Validation (`/api/setup/check-categories`)
- **Purpose**: Validates required system categories exist
- **Features**: Checks volunteer categories, event categories, news categories
- **Usage**: System health checks and deployment validation
- **Returns**: Missing categories and recommendations

**Setup Operations Include**:
- Initial system configuration
- Database schema validation  
- Administrative account creation
- System health checks and validation
- Configuration management
- Development and testing setup

## Development & Testing Utilities

### Testing API Endpoints  
**Location**: `/api/test/`

#### Performance Testing (`/api/test/sse-performance`)
- **Purpose**: Server-Sent Events performance benchmarking
- **Features**: Connection testing, throughput measurement
- **Usage**: Development performance validation
- **Metrics**: Response times, connection stability

#### General Testing Endpoints
- **Authentication Testing**: Validates JWT token handling
- **Database Connection Testing**: Verifies Prisma connectivity  
- **Integration Testing**: Cross-system functionality validation
- **Error Handling Testing**: Simulates error conditions

**Test Operations Include**:
- API functionality validation
- Database connection testing
- Authentication system verification  
- Integration testing utilities
- Performance benchmarking
- Error handling validation

### Development Pages
**Location**: `/test/`

#### Real-time Testing (`/test/realtime`)
- **Purpose**: Server-Sent Events connection testing
- **Features**: Live connection monitoring, event streaming
- **Usage**: Development debugging for notification system

#### Upload V2 Testing (`/test/upload-v2`)  
- **Purpose**: File upload system testing
- **Features**: Multi-type upload testing, validation checks
- **Usage**: Development validation of upload functionality

#### Performance Monitoring (`/test/performance`)
- **Purpose**: Client-side performance monitoring
- **Features**: Load time analysis, component performance
- **Usage**: Development optimization and debugging

## Event Capacity Management System

### Core Functionality
**Location**: `/lib/event-capacity-system.ts`

The event capacity system manages ticket availability and prevents overselling through sophisticated hold mechanisms.

#### Key Functions

**Available Capacity Calculation**:
```typescript
getEventAvailableCapacity(eventId: string): Promise<number>
// Returns: available spots considering sold tickets + active holds
```

**Capacity Hold Creation**:
```typescript  
createEventCapacityHold(eventId: string, quantity: number, userId: string)
// Creates 15-minute hold for event capacity
```

**Expired Holds Cleanup**:
```typescript
releaseExpiredEventCapacityHolds(): Promise<CleanupResult>
// Releases expired capacity holds and updates availability
```

#### Hold System Logic
1. **Hold Creation**: User adds tickets to cart → capacity hold created
2. **Hold Maintenance**: 15-minute timer on all holds
3. **Hold Extension**: User activity extends hold duration  
4. **Hold Release**: Expired holds automatically released via cron
5. **Capacity Update**: Real-time availability calculation

## Image & File Processing Utilities

### Image Optimization System
**Location**: `/lib/imageProcessingV2.ts`

#### Core Processing Functions
- **Web Optimization**: Automatic compression and format conversion
- **Dimension Detection**: Width/height extraction for database storage
- **Quality Control**: Configurable compression levels per upload type
- **Format Conversion**: WebP conversion for optimal web performance

#### Processing Pipeline
```typescript
1. File Validation & Security Checks
2. Image Dimension Extraction  
3. Format Optimization (WebP conversion)
4. Compression with Quality Control
5. Thumbnail Generation (future-ready)
6. Database Metadata Storage
```

## Database Utilities

### Transaction Management
- **Atomic Operations**: Critical operations wrapped in database transactions
- **Consistency Checks**: Automated data integrity validation
- **Cleanup Routines**: Regular maintenance of orphaned records
- **Performance Optimization**: Query optimization and index management

### Backup & Maintenance
```sql
-- Example maintenance queries run by system utilities
DELETE FROM ticketHold WHERE expiresAt < NOW() - INTERVAL 1 DAY;
DELETE FROM eventCapacityHold WHERE expiresAt < NOW() - INTERVAL 1 DAY;  
DELETE FROM volunteerSlotHold WHERE expiresAt < NOW() - INTERVAL 1 DAY;
```

## Security & Monitoring

### Security Features
- **API Key Authentication**: Cron jobs require secret key authentication
- **Rate Limiting**: Built-in protection against abuse
- **Access Control**: Role-based permissions for setup utilities
- **Audit Logging**: Comprehensive logging of system operations

### Monitoring Capabilities  
- **Health Checks**: System status validation endpoints
- **Performance Metrics**: Response time and throughput monitoring
- **Error Tracking**: Comprehensive error logging and reporting
- **Usage Analytics**: System usage pattern analysis

## Environment Configuration

### Required Environment Variables
```env
# Cron Job Authentication
CRON_SECRET_KEY=your-secure-cron-secret-key

# System Setup Configuration  
ADMIN_EMAIL=<EMAIL>
FIRST_TIME_SETUP=false

# Performance Monitoring
ENABLE_PERFORMANCE_MONITORING=true
LOG_LEVEL=info
```

### Production Deployment
```bash
# Setup cron job for expired holds cleanup
*/5 * * * * /usr/bin/curl -X POST https://your-domain.com/api/cron/release-expired-holds \
  -H "Authorization: Bearer $CRON_SECRET_KEY" \
  -s >> /var/log/cron-cleanup.log 2>&1

# Verify system setup post-deployment  
curl https://your-domain.com/api/setup/check-categories \
  -H "Authorization: Bearer $ADMIN_TOKEN"
```

## Development Usage

### Local Development Testing
```bash
# Test cron job functionality
curl -X POST http://localhost:3000/api/cron/release-expired-holds \
  -H "Authorization: Bearer $CRON_SECRET_KEY"

# Validate system setup
curl http://localhost:3000/api/setup/check-categories

# Test performance endpoints
curl http://localhost:3000/api/test/sse-performance
```

### Integration Testing
```typescript
// Example test integration
const response = await fetch('/api/test/auth', {
  headers: {
    'Authorization': `Bearer ${validToken}`
  }
});
const result = await response.json();
// Validates authentication system functionality
```

## Operational Guidelines

### Cron Job Monitoring
- **Success Logging**: Track successful cleanup operations
- **Error Alerting**: Monitor for failed cleanup attempts  
- **Performance Metrics**: Track cleanup duration and affected records
- **Capacity Monitoring**: Ensure adequate system resources

### System Maintenance
- **Regular Health Checks**: Weekly validation of system components
- **Performance Optimization**: Monthly analysis of system performance
- **Security Audits**: Quarterly review of access controls and logs
- **Backup Verification**: Regular testing of backup and recovery procedures

## Troubleshooting

### Common Issues

#### Cron Job Failures
```bash
# Check cron job logs
tail -f /var/log/cron-cleanup.log

# Validate secret key configuration
echo $CRON_SECRET_KEY

# Test manual execution
curl -X POST http://localhost:3000/api/cron/release-expired-holds \
  -H "Authorization: Bearer $CRON_SECRET_KEY" \
  -v
```

#### Performance Issues
```bash
# Monitor system performance
curl http://localhost:3000/api/test/sse-performance

# Check database performance  
npx prisma studio # Monitor query performance

# Validate capacity system
curl http://localhost:3000/api/setup/check-categories
```

---

**File Locations:**
- **Cron Jobs**: `/src/app/api/cron/`
- **Setup Utilities**: `/src/app/api/setup/`
- **Testing Endpoints**: `/src/app/api/test/`
- **Capacity System**: `/src/lib/event-capacity-system.ts`
- **Image Processing**: `/src/lib/imageProcessingV2.ts`

**Dependencies:**
- **Prisma**: Database operations and transactions
- **Node.js Cron**: Scheduled task execution
- **Sharp**: Image processing and optimization
- **Next.js**: API endpoint hosting and management