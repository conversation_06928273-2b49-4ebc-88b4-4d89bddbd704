﻿# Bank of Styx Website - Feature-Based File Tree Summary
# Generated on 08/07/2025 12:35:25
# This file provides organized access to feature-specific file trees

## Overview
The Bank of Styx website has been organized into feature-based file trees to improve
navigation and understanding of the codebase structure. Each feature contains all
relevant files including pages, API endpoints, components, and related utilities.

## HIGH PRIORITY FEATURES (Core Systems)
These are the essential systems that drive the Bank of Styx functionality:

### Authentication System
Complete authentication system including login, registration, Discord integration, and user management
File: [feature-authentication-system.txt](feature-authentication-system.txt)

### Banking System
Core banking functionality including accounts, transactions, pay codes, deposits, withdrawals, and cashier operations
File: [feature-banking-system.txt](feature-banking-system.txt)

### Ship Management System
Ship creation, management, captain dashboard, crew management, and ship-related operations
File: [feature-ship-management-system.txt](feature-ship-management-system.txt)

### Volunteer System
Volunteer management, applications, assignments, hour tracking, and land steward operations
File: [feature-volunteer-system.txt](feature-volunteer-system.txt)

### Core Infrastructure
Shared components, UI library, configuration, utilities, hooks, and core infrastructure
File: [feature-core-infrastructure.txt](feature-core-infrastructure.txt)

### Database Schema & Migrations
Database schema, migrations, and database-related configuration
File: [feature-database-schema.txt](feature-database-schema.txt)

## MEDIUM PRIORITY FEATURES (Extended Functionality)
Important features that extend the core functionality:

### Admin System
Administrative dashboard, user management, system configuration, and administrative tools
File: [feature-admin-system.txt](feature-admin-system.txt)

### Shopping & Sales System
E-commerce functionality including product catalog, shopping cart, checkout, order management, and sales dashboard
File: [feature-shopping-system.txt](feature-shopping-system.txt)

### News & Content System
News article management, content publishing, and news dashboard
File: [feature-news-system.txt](feature-news-system.txt)

### Events System
Event management, event categories, event registration, and event-related operations
File: [feature-events-system.txt](feature-events-system.txt)

### Notification System
Real-time notification system and notification management
File: [feature-notification-system.txt](feature-notification-system.txt)

## LOW PRIORITY FEATURES (Supporting Systems)
Supporting features and utilities:

### User Settings & Profile System
User profile management, settings, preferences, and user-related API endpoints
File: [feature-user-settings-system.txt](feature-user-settings-system.txt)

### Support System
Support ticket system, help pages, and contact functionality
File: [feature-support-system.txt](feature-support-system.txt)

### Static Pages & Content
Static pages including about, rules, homepage, and global layout files
File: [feature-static-pages.txt](feature-static-pages.txt)

### System Utilities & Testing
Testing utilities, cron jobs, system setup, upload handling, and image processing
File: [feature-system-utilities.txt](feature-system-utilities.txt)

## ADDITIONAL RESOURCES

### Complete File Tree
The complete file tree with all files and directories (for reference)
File: [website-file-tree.txt](../website-file-tree.txt)

### Documentation
For detailed feature documentation, see the [docs/features/](../../docs/features/) directory

### Feature Count Summary
- **High Priority**: 6 features
- **Medium Priority**: 5 features
- **Low Priority**: 4 features
- **Total Features**: 15 features

