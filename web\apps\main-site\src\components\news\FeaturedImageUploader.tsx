"use client";

import React, { useState, useRef, useEffect } from "react";
import { resizeImageForPreview } from "@/lib/clientImageProcessing";
import fetchClient from "@/lib/fetchClient";

interface FeaturedImageUploaderProps {
  imageUrl: string;
  onChange: (imageUrl: string) => void;
  error?: boolean;
  errorMessage?: string;
  disabled?: boolean;
}

interface UploadResponse {
  file?: { url: string };
  url?: string;
}

export const FeaturedImageUploader: React.FC<FeaturedImageUploaderProps> = ({
  imageUrl,
  onChange,
  error = false,
  errorMessage = "Featured image is required",
  disabled = false,
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Clean up preview URL when component unmounts or when imageUrl changes
  useEffect(() => {
    return () => {
      // Only revoke if it's an object URL (starts with blob:)
      if (previewUrl && previewUrl.startsWith("blob:")) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [previewUrl]);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (disabled) return;

    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file type
    const allowedTypes = ["image/jpeg", "image/png", "image/gif", "image/webp"];
    if (!allowedTypes.includes(file.type)) {
      setUploadError(
        "File type not supported. Please upload an image (JPEG, PNG, GIF, WEBP)",
      );
      return;
    }

    // Validate file size (10MB max)
    const maxSize = 10 * 1024 * 1024; // 10MB in bytes
    if (file.size > maxSize) {
      setUploadError("File size exceeds 10MB limit");
      return;
    }

    // Generate preview immediately
    try {
      // Resize the image for preview (max width 400px, max height 300px)
      const resizedImageUrl = await resizeImageForPreview(file, 400, 300);
      setPreviewUrl(resizedImageUrl);
    } catch (error) {
      console.error("Error resizing image:", error);
      // Fallback to original method if resizing fails
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
    }

    setIsUploading(true);
    setUploadError(null);

    try {
      // Create form data
      const formData = new FormData();
      formData.append("file", file);

      // Upload the file using fetchClient
      const data = (await fetchClient.request("/api/uploads", {
        method: "POST",
        body: formData,
        // Don't set Content-Type header for FormData as the browser will set it with the boundary
      })) as UploadResponse;
      // Safely access the URL - handle different response structures
      if (data.file && data.file.url) {
        onChange(data.file.url);
      } else if (data.url) {
        onChange(data.url);
      } else {
        // If we can't find a valid URL, throw an error
        throw new Error("Invalid response format: URL not found in response");
      }
    } catch (error) {
      console.error("Error uploading image:", error);
      setUploadError(
        error instanceof Error ? error.message : "Failed to upload image",
      );
    } finally {
      setIsUploading(false);
    }
  };
  const handleBrowseClick = () => {
    if (disabled) return;

    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleRemoveImage = () => {
    if (disabled || isUploading) return;

    // Clear the preview URL
    if (previewUrl) {
      if (previewUrl.startsWith("blob:")) {
        URL.revokeObjectURL(previewUrl);
      }
      setPreviewUrl(null);
    }

    // Clear the image URL
    onChange("");
  };

  return (
    <div
      className={`border ${
        error ? "border-accent" : "border-gray-600"
      } border-dashed rounded-md p-4`}
    >
      {" "}
      {imageUrl || previewUrl ? (
        <div className="relative">
          <div className="mb-2">
            <p className="text-sm text-gray-400 mb-1">
              Preview (how it will appear in news articles):
            </p>
            <div
              className="border border-gray-600 rounded-md overflow-hidden"
              style={{
                height:
                  "15.6rem" /* Match the 30% larger height in NewsArticleCard */,
                maxHeight: "15.6rem" /* Ensure maximum height constraint */,
              }}
            >
              <img
                src={imageUrl || previewUrl || ""}
                alt="Featured Image"
                className="w-full h-full object-cover"
              />
              {isUploading && (
                <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                  <div className="flex flex-col items-center">
                    <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary mb-3"></div>
                    <p className="text-sm text-white">Uploading image...</p>
                  </div>
                </div>
              )}
            </div>
          </div>

          <div className="mt-3 flex justify-end">
            <button
              type="button"
              onClick={handleRemoveImage}
              className={`px-3 py-1 text-sm ${
                disabled || isUploading
                  ? "bg-gray-600 cursor-not-allowed"
                  : "bg-accent hover:bg-accent/80"
              } text-white rounded-md`}
              disabled={disabled || isUploading}
            >
              Remove
            </button>
          </div>
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center pt-5 pb-6">
          {isUploading ? (
            <div className="flex flex-col items-center">
              <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary mb-3"></div>
              <p className="text-sm text-gray-400">Uploading image...</p>
            </div>
          ) : (
            <>
              <svg
                className="w-10 h-10 text-gray-400 mb-3"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                />
              </svg>{" "}
              <button
                type="button"
                onClick={handleBrowseClick}
                className={`px-4 py-2 ${
                  disabled
                    ? "bg-gray-600 cursor-not-allowed"
                    : "bg-secondary hover:bg-secondary-dark"
                } text-white rounded-md`}
                disabled={disabled}
              >
                Browse Files
              </button>
              <p className="text-xs text-gray-400 mt-2">
                JPEG, PNG, GIF or WEBP (MAX. 10MB)
              </p>
            </>
          )}
          <input
            ref={fileInputRef}
            type="file"
            className="hidden"
            accept="image/jpeg,image/png,image/gif,image/webp"
            onChange={handleFileChange}
          />
        </div>
      )}
      {(uploadError || (error && errorMessage)) && (
        <p className="mt-1 text-sm text-accent text-center">
          {uploadError || errorMessage}
        </p>
      )}
    </div>
  );
};
