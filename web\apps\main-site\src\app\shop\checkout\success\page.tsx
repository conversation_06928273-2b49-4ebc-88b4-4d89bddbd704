"use client";

import { useEffect, useState } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import { useOrder } from "@/hooks/useOrders";
import { <PERSON>, But<PERSON>, Spinner } from "@bank-of-styx/ui";
import Link from "next/link";
import { useAuth } from "@/contexts/AuthContext";

export default function CheckoutSuccessPage() {
  const { isAuthenticated } = useAuth();
  const searchParams = useSearchParams();
  const router = useRouter();
  const orderId = searchParams.get("orderId");
  const [isLoading, setIsLoading] = useState(true);

  const { data: orderData, isLoading: isLoadingOrder } = useOrder(
    orderId || "",
  );

  useEffect(() => {
    if (!isAuthenticated) {
      router.push("/");
      return;
    }

    if (!orderId) {
      router.push("/shop");
      return;
    }

    // Give some time for the webhook to process the payment
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 3000);

    return () => clearTimeout(timer);
  }, [isAuthenticated, orderId, router]);

  if (!isAuthenticated || !orderId) {
    return null;
  }

  if (isLoading || isLoadingOrder) {
    return (
      <div className="container mx-auto px-4 py-16 flex flex-col items-center">
        <Spinner size="lg" />
        <p className="mt-4 text-lg">Processing your order...</p>
      </div>
    );
  }

  const order = orderData?.order;

  return (
    <div className="container mx-auto px-4 py-8">
      <Card className="max-w-2xl mx-auto p-8 text-center">
        <div className="mb-6">
          <div className="w-20 h-20 bg-success bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-10 w-10 text-success"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 13l4 4L19 7"
              />
            </svg>
          </div>
          <h1 className="text-3xl font-bold mb-2">Thank You!</h1>
          <p className="text-lg mb-4">
            Your order has been placed successfully.
          </p>
          <p className="text-text-muted">
            Order #{order?.orderNumber} •{" "}
            {new Date(order?.createdAt || "").toLocaleDateString()}
          </p>
        </div>

        <div className="border-t border-border-subtle py-4 mb-6">
          <h2 className="text-xl font-semibold mb-4">Order Summary</h2>
          <div className="space-y-2 mb-4">
            {order?.items.map((item) => (
              <div key={item.id} className="flex justify-between">
                <span>
                  {item.quantity} x {item.name}
                </span>
                <span>${(item.price * item.quantity).toFixed(2)}</span>
              </div>
            ))}
          </div>
          <div className="border-t border-border-subtle pt-4 flex justify-between font-bold">
            <span>Total</span>
            <span>${order?.total.toFixed(2)}</span>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row justify-center gap-4">
          <Link href="/shop/orders">
            <Button variant="outline">View My Orders</Button>
          </Link>
          <Link href="/shop">
            <Button variant="primary">Continue Shopping</Button>
          </Link>
        </div>
      </Card>
    </div>
  );
}
