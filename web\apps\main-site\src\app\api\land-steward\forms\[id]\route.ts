import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth";
import prisma from "@/lib/prisma";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    
    if (!user || !user.isLandSteward) {
      return NextResponse.json(
        { error: "Unauthorized. Land Steward access required." },
        { status: 403 }
      );
    }

    const formId = params.id;

    const form = await prisma.eventForm.findUnique({
      where: { id: formId },
      include: {
        event: {
          select: {
            id: true,
            name: true,
            startDate: true,
            endDate: true,
          },
        },
        template: {
          select: {
            id: true,
            name: true,
          },
        },
        createdBy: {
          select: {
            id: true,
            username: true,
            displayName: true,
          },
        },
        submissions: {
          include: {
            ship: {
              select: {
                id: true,
                name: true,
              },
            },
            submittedBy: {
              select: {
                id: true,
                username: true,
                displayName: true,
              },
            },
          },
          orderBy: {
            submittedAt: "desc",
          },
        },
      },
    });

    if (!form) {
      return NextResponse.json(
        { error: "Form not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(form);
  } catch (error) {
    console.error("Error fetching event form:", error);
    return NextResponse.json(
      { error: "Failed to fetch event form" },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    
    if (!user || !user.isLandSteward) {
      return NextResponse.json(
        { error: "Unauthorized. Land Steward access required." },
        { status: 403 }
      );
    }

    const formId = params.id;

    const body = await request.json();
    const { name, description, formStructure, status, submissionDeadline } = body;

    // Check if form exists
    const existingForm = await prisma.eventForm.findUnique({
      where: { id: formId },
      include: {
        _count: {
          select: {
            submissions: true,
          },
        },
      },
    });

    if (!existingForm) {
      return NextResponse.json(
        { error: "Form not found" },
        { status: 404 }
      );
    }

    // Don't allow structure changes if there are submissions
    if (formStructure && existingForm._count.submissions > 0) {
      return NextResponse.json(
        { error: "Cannot modify form structure after submissions have been received" },
        { status: 400 }
      );
    }

    // Validate form structure if provided
    if (formStructure) {
      if (!Array.isArray(formStructure) || formStructure.length === 0) {
        return NextResponse.json(
          { error: "Form structure must be a non-empty array of fields" },
          { status: 400 }
        );
      }

      // Validate each field in the structure
      for (const field of formStructure) {
        if (!field.id || !field.type || !field.label) {
          return NextResponse.json(
            { error: "Each field must have id, type, and label" },
            { status: 400 }
          );
        }

        const validTypes = [
          "text", "textarea", "select", "checkbox", "multi_select", 
          "file_upload", "user_select", "multi_user_select", "volunteer_hours"
        ];
        
        if (!validTypes.includes(field.type)) {
          return NextResponse.json(
            { error: `Invalid field type: ${field.type}` },
            { status: 400 }
          );
        }
      }
    }

    // Validate status
    if (status && !["draft", "active", "closed"].includes(status)) {
      return NextResponse.json(
        { error: "Invalid status. Must be draft, active, or closed" },
        { status: 400 }
      );
    }

    const updatedForm = await prisma.eventForm.update({
      where: { id: formId },
      data: {
        ...(name && { name }),
        ...(description !== undefined && { description }),
        ...(formStructure && { formStructure }),
        ...(status && { status }),
        ...(submissionDeadline !== undefined && { 
          submissionDeadline: submissionDeadline ? new Date(submissionDeadline) : null 
        }),
      },
      include: {
        event: {
          select: {
            id: true,
            name: true,
            startDate: true,
            endDate: true,
          },
        },
        template: {
          select: {
            id: true,
            name: true,
          },
        },
        createdBy: {
          select: {
            id: true,
            username: true,
            displayName: true,
          },
        },
      },
    });

    return NextResponse.json(updatedForm);
  } catch (error) {
    console.error("Error updating event form:", error);
    return NextResponse.json(
      { error: "Failed to update event form" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    
    if (!user || !user.isLandSteward) {
      return NextResponse.json(
        { error: "Unauthorized. Land Steward access required." },
        { status: 403 }
      );
    }

    const formId = params.id;

    // Check if form exists and has submissions
    const form = await prisma.eventForm.findUnique({
      where: { id: formId },
      include: {
        _count: {
          select: {
            submissions: true,
          },
        },
      },
    });

    if (!form) {
      return NextResponse.json(
        { error: "Form not found" },
        { status: 404 }
      );
    }

    if (form._count.submissions > 0) {
      return NextResponse.json(
        { error: "Cannot delete form that has submissions. Consider closing it instead." },
        { status: 400 }
      );
    }

    await prisma.eventForm.delete({
      where: { id: formId },
    });

    return NextResponse.json({ message: "Form deleted successfully" });
  } catch (error) {
    console.error("Error deleting event form:", error);
    return NextResponse.json(
      { error: "Failed to delete event form" },
      { status: 500 }
    );
  }
}