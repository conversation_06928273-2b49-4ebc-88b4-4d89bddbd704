# Banking System

## Overview
The banking system is a comprehensive financial management platform with transactions, pay codes, real-time balance updates, and role-based access control.

## Core Components

### Transaction System
The transaction system supports multiple types with different processing flows:

#### Transaction Types
- **Transfer**: Direct user-to-user transfers (immediate)
- **Withdrawal**: User requests cash withdrawal (requires banker approval)
- **Donation**: Charitable contributions (immediate)
- **Deposit**: Cash deposits processed by cashiers
- **Pay Code**: Redeemable payment codes

#### Transaction Flow
```typescript
// All transactions use Prisma transactions for atomicity
const transaction = await prisma.$transaction(async (prisma) => {
  // Create transaction record
  const newTransaction = await prisma.transaction.create({
    data: {
      amount: amount,
      type: type,
      status: status, // 'completed', 'pending', 'cancelled'
      description: description,
      senderId: userId,
      recipientId: recipientUser?.id,
    }
  });

  // Update balances
  await prisma.user.update({
    where: { id: userId },
    data: { balance: { decrement: amount } },
  });

  if (recipientUser) {
    await prisma.user.update({
      where: { id: recipientUser.id },
      data: { balance: { increment: amount } },
    });
  }

  return newTransaction;
});
```

### Pay Code System
A unique payment system using redeemable codes with format `BOS-XXXX-XXXX`:

#### Code Generation
```typescript
function generatePayCode() {
  const randomBytes = crypto.randomBytes(4);
  const firstPart = randomBytes.toString("hex").substring(0, 4).toUpperCase();
  const secondPart = crypto.randomBytes(4).toString("hex").substring(0, 4).toUpperCase();
  return `BOS-${firstPart}-${secondPart}`;
}
```

#### Pay Code Features
- **Expiration**: Time-based expiration
- **Usage Limits**: Optional maximum uses
- **Status Tracking**: Active, redeemed, expired, cancelled
- **Collision Prevention**: Ensures unique codes
- **Transaction Linking**: Creates transaction records for tracking

#### Pay Code Creation
```typescript
// Ensure unique code
while (codeExists) {
  const existingCode = await prisma.payCode.findUnique({
    where: { code },
  });
  
  if (!existingCode) {
    codeExists = false;
  } else {
    code = generatePayCode();
  }
}

// Create pay code and tracking transaction
const result = await prisma.$transaction(async (tx) => {
  const newPayCode = await tx.payCode.create({
    data: {
      code,
      amount,
      expiresAt: new Date(expiresAt),
      status: "active",
      createdById: userId,
      maxUses: maxUses || null,
    }
  });

  // Create tracking transaction (no balance change for creator)
  const transaction = await tx.transaction.create({
    data: {
      amount: amount,
      type: "paycode_create",
      status: "completed",
      description: `Pay code created: ${code}`,
      senderId: userId,
      payCodeId: newPayCode.id,
    }
  });

  return { payCode: newPayCode, transaction };
});
```

### Balance Management
- **Real-time Updates**: Immediate balance changes via database transactions
- **Escrow System**: Withdrawal amounts deducted immediately, returned if cancelled
- **Atomic Operations**: All balance changes use Prisma transactions
- **SSE Notifications**: Real-time balance updates pushed to clients

### Role-based Access Control

#### User Roles
- **Banker**: Can approve/reject withdrawals, manage deposits
- **Cashier**: Can process deposits, view member accounts
- **Admin**: Full system access
- **User**: Basic transaction capabilities

#### Permission Patterns
```typescript
// Role verification in endpoints
const user = await getCurrentUser(req);
if (!user.isBanker) {
  return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
}
```

### Notification System Integration
Every transaction triggers relevant notifications:

#### Transfer Notifications
```typescript
// Sender notification
await createNotification(userId, {
  category: "transaction",
  type: "transfer_sent",
  title: "Transfer Sent",
  message: `You sent NS ${amount.toFixed(0)} to @${recipientUser.username}`,
  priority: "medium",
  transactionId: transaction.id,
});

// Recipient notification
await createNotification(recipientUser.id, {
  category: "transaction",
  type: "transfer_received",
  title: "Transfer Received",
  message: `You received NS ${amount.toFixed(0)} from @${user.username}`,
  priority: "medium",
  transactionId: transaction.id,
});
```

#### Withdrawal Approval Flow
```typescript
// User notification
await createNotification(userId, {
  category: "transaction",
  type: "withdrawal_request",
  title: "Withdrawal Request Submitted",
  message: `Your withdrawal request of NS ${amount.toFixed(0)} is pending approval`,
  link: "/bank/dashboard/withdraw",
  priority: "medium",
});

// Banker notifications
const bankers = await prisma.user.findMany({
  where: { isBanker: true, status: "active" }
});

for (const banker of bankers) {
  await createNotification(banker.id, {
    category: "transaction",
    type: "withdrawal_request",
    title: "New Withdrawal Request",
    message: `New withdrawal request of NS ${amount.toFixed(0)} from ${user.username}`,
    link: "/bank/cashier/pending-transactions",
    priority: "high",
  });
}
```

## Security Features

### Input Validation
- **Amount Validation**: Must be positive numbers
- **Balance Checks**: Prevents overdrafts
- **User Verification**: Username/recipient validation
- **Token Authentication**: JWT verification on all endpoints

### Transaction Integrity
- **Database Transactions**: Atomic operations prevent partial updates
- **Status Tracking**: Comprehensive transaction states
- **Audit Trail**: Complete transaction history with timestamps
- **Error Handling**: Graceful failure with balance restoration

### Anti-fraud Measures
- **Unique Pay Codes**: Collision prevention
- **Expiration Enforcement**: Time-limited codes
- **Usage Limits**: Prevents code abuse
- **Role Verification**: Prevents unauthorized operations

## Database Schema Patterns

### Transaction Table
```sql
Transaction {
  id: String (UUID)
  amount: Decimal
  type: String (transfer, withdrawal, donation, deposit, paycode_create, paycode_redeem)
  status: String (pending, completed, cancelled, rejected)
  description: String
  note: String?
  senderId: String?
  recipientId: String?
  payCodeId: String?
  processedById: String?
  paymentMethod: String?
  createdAt: DateTime
  updatedAt: DateTime
  processedAt: DateTime?
}
```

### Pay Code Table
```sql
PayCode {
  id: String (UUID)
  code: String (unique, format: BOS-XXXX-XXXX)
  amount: Decimal
  status: String (active, redeemed, expired, cancelled)
  expiresAt: DateTime
  maxUses: Int?
  currentUses: Int (default: 0)
  createdById: String
  redeemedById: String?
  createdAt: DateTime
  redeemedAt: DateTime?
}
```

## API Endpoints

### Transaction Management
- `GET /api/bank/transactions` - User transaction history
- `POST /api/bank/transactions` - Create new transaction
- `GET /api/bank/account-summary` - User balance and summary

### Pay Code System
- `GET /api/bank/pay-codes` - User's pay codes
- `POST /api/bank/pay-codes` - Create new pay code
- `POST /api/bank/pay-codes/redeem` - Redeem pay code
- `GET /api/bank/pay-codes/validate/[code]` - Validate pay code

### Cashier Operations
- `GET /api/bank/cashier/pending-transactions` - Pending withdrawals
- `POST /api/bank/cashier/transactions/[id]` - Approve/reject withdrawal
- `GET /api/bank/cashier/members/[id]` - Member account details

## Currency Format
- **Currency**: "NS" (Nexus Styx)
- **Format**: Whole numbers (no decimal display)
- **Storage**: Decimal type for precision
- **Display**: `NS ${amount.toFixed(0)}`

## Important Files
- `src/app/api/bank/transactions/route.ts` - Core transaction logic
- `src/app/api/bank/pay-codes/route.ts` - Pay code management
- `src/app/api/bank/cashier/` - Cashier operations
- `src/lib/notifications.ts` - Transaction notifications
- `prisma/schema.prisma` - Database schema