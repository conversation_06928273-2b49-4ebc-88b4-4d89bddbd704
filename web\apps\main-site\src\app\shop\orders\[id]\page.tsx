"use client";

import { useOrder } from "@/hooks/useOrders";
import { <PERSON>, Spin<PERSON>, <PERSON><PERSON> } from "@bank-of-styx/ui";
import Link from "next/link";
import Image from "next/image";
import { useAuth } from "@/contexts/AuthContext";
import { useRouter } from "next/navigation";

export default function OrderDetailPage({
  params,
}: {
  params: { id: string };
}) {
  const { id } = params;
  const { user, isAuthenticated, openAuthModal } = useAuth();
  const router = useRouter();
  const { data, isLoading, error } = useOrder(id);

  if (!isAuthenticated) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="p-8 text-center">
          <h2 className="text-2xl font-bold mb-4">Sign In Required</h2>
          <p className="mb-6">Please sign in to view your order details.</p>
          <Button variant="primary" onClick={openAuthModal}>
            Sign In
          </Button>
        </Card>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8 flex justify-center">
        <Spinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="p-8 text-center">
          <h2 className="text-2xl font-bold mb-4">Error</h2>
          <p className="mb-6">
            Failed to load order details. Please try again later.
          </p>
          <Button variant="primary" onClick={() => window.location.reload()}>
            Retry
          </Button>
        </Card>
      </div>
    );
  }

  const order = data?.order;

  if (!order) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="p-8 text-center">
          <h2 className="text-2xl font-bold mb-4">Order Not Found</h2>
          <p className="mb-6">
            The order you're looking for doesn't exist or you don't have
            permission to view it.
          </p>
          <Link href="/shop/orders">
            <Button variant="primary">Back to Orders</Button>
          </Link>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Order #{order.orderNumber}</h1>
        <Link href="/shop/orders">
          <Button variant="outline">Back to Orders</Button>
        </Link>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Order Items */}
        <div className="lg:col-span-2">
          <Card title="Order Items">
            <div className="divide-y divide-border-subtle">
              {order.items.map((item) => (
                <div key={item.id} className="p-4 flex flex-col md:flex-row">
                  {/* Product Image */}
                  <div className="w-full md:w-24 h-24 flex-shrink-0 mb-4 md:mb-0">
                    {item.product && item.product.image ? (
                      <Image
                        src={item.product.image}
                        alt={item.name}
                        width={96}
                        height={96}
                        className="w-full h-full object-cover rounded-md"
                      />
                    ) : (
                      <div className="w-full h-full bg-secondary flex items-center justify-center rounded-md">
                        <span className="text-text-muted text-sm">
                          No image
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Product Details */}
                  <div className="flex-grow md:ml-4">
                    <div className="flex flex-col md:flex-row md:justify-between">
                      <div>
                        <h3 className="font-semibold">{item.name}</h3>
                        {item.product && (
                          <p className="text-text-muted text-sm">
                            {item.product.category.name}
                          </p>
                        )}
                        {item.product && item.product.event && (
                          <p className="text-text-muted text-sm">
                            Event: {item.product.event.name}
                          </p>
                        )}
                      </div>
                      <div className="mt-2 md:mt-0 text-right">
                        <p className="font-semibold">
                          ${item.price.toFixed(2)}
                        </p>
                        <p className="text-text-muted text-sm">
                          Quantity: {item.quantity}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </div>

        {/* Order Summary */}
        <div className="lg:col-span-1">
          <Card title="Order Summary" className="mb-6">
            <div className="p-4 space-y-4">
              <div className="flex justify-between">
                <span>Subtotal</span>
                <span>${order.subtotal.toFixed(2)}</span>
              </div>
              {order.tax && (
                <div className="flex justify-between">
                  <span>Tax</span>
                  <span>${order.tax.toFixed(2)}</span>
                </div>
              )}
              {order.discount && (
                <div className="flex justify-between">
                  <span>Discount</span>
                  <span>-${order.discount.toFixed(2)}</span>
                </div>
              )}
              <div className="border-t border-border-subtle pt-4 flex justify-between font-bold">
                <span>Total</span>
                <span>${order.total.toFixed(2)}</span>
              </div>
            </div>
          </Card>

          <Card title="Order Information" className="mb-6">
            <div className="p-4 space-y-4">
              <div>
                <p className="text-text-muted">Order Date</p>
                <p>{new Date(order.createdAt).toLocaleString()}</p>
              </div>
              <div>
                <p className="text-text-muted">Status</p>
                <p
                  className={`font-semibold ${
                    order.status === "paid"
                      ? "text-success"
                      : order.status === "pending"
                      ? "text-warning"
                      : order.status === "cancelled"
                      ? "text-accent"
                      : "text-info"
                  }`}
                >
                  {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                </p>
              </div>
              {order.paymentMethod && (
                <div>
                  <p className="text-text-muted">Payment Method</p>
                  <p>{order.paymentMethod}</p>
                </div>
              )}
            </div>
          </Card>

          <div className="mt-6">
            <Link href="/shop">
              <Button variant="primary" fullWidth>
                Continue Shopping
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
