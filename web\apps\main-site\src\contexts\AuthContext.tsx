"use client";

import {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";
import { useRouter } from "next/navigation";
import { BankUser } from "../services/bankService";
import { useQueryClient } from "@tanstack/react-query";
import { notificationPreferencesKey } from "../hooks/useNotificationPreferences";
import toast from "react-hot-toast";
import fetchClient from "@/lib/fetchClient";

interface AuthContextType {
  user: BankUser | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (
    username: string,
    email: string,
    password: string,
  ) => Promise<void>;
  logout: () => void;
  loginWithDiscord: () => Promise<void>;
  connectDiscord: () => Promise<void>;
  disconnectDiscord: () => Promise<void>;
  setPassword: (password: string) => Promise<void>;
  openAuthModal: () => void;
  closeAuthModal: () => void;
  isAuthModalOpen: boolean;
  isPasswordModalOpen: boolean;
  openPasswordModal: () => void;
  closePasswordModal: () => void;
  refreshUserData: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<BankUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [isPasswordModalOpen, setIsPasswordModalOpen] = useState(false);
  const [pendingDiscordDisconnect, setPendingDiscordDisconnect] =
    useState(false);
  const router = useRouter();
  const queryClient = useQueryClient();

  // Function to refresh user data from the server
  const refreshUserData = async () => {
    try {
      const token = localStorage.getItem("auth_token");

      if (!token) {
        return;
      }

      // Check if we already have a request in progress
      const userDataKey = ["userData"];
      const existingQuery = queryClient.getQueryState(userDataKey);

      // If we already have a query in progress or fresh data, use that instead
      if (
        existingQuery &&
        existingQuery.status === "success" &&
        !existingQuery.fetchStatus
      ) {
        // Type assertion since we know the structure of our data
        const userData = existingQuery.data as { user: BankUser };
        if (userData && userData.user) {
          setUser(userData.user);
          return;
        }
      }

      // Use React Query to fetch and cache user data
      const userData = await queryClient.fetchQuery<{ user: BankUser }>({
        queryKey: userDataKey,
        queryFn: async () => {
          return fetchClient.get<{ user: BankUser }>("/api/auth/me");
        },
        staleTime: 5 * 60 * 1000, // 5 minutes
      });

      setUser(userData.user);

      // Invalidate notification preferences to ensure they're refreshed
      queryClient.invalidateQueries({ queryKey: notificationPreferencesKey });
    } catch (error) {
      console.error("Failed to refresh user data:", error);
      // Token is invalid, clear it
      localStorage.removeItem("auth_token");
      setUser(null);
    }
  };

  // Check if user is already authenticated on mount
  useEffect(() => {
    const checkAuth = async () => {
      setIsLoading(true);
      try {
        await refreshUserData();
      } catch (error) {
        console.error("Authentication check failed:", error);
        localStorage.removeItem("auth_token");
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, []);

  const login = async (email: string, password: string) => {
    setIsLoading(true);
    try {
      const data = await fetchClient.post<{ token: string; user: BankUser }>(
        "/api/auth/login",
        { email, password },
      );

      // Store token in localStorage
      localStorage.setItem("auth_token", data.token);

      // Set user data
      setUser(data.user);

      // Invalidate notification preferences to ensure they're refreshed
      queryClient.invalidateQueries({ queryKey: notificationPreferencesKey });

      // Close the auth modal
      setIsAuthModalOpen(false);

      // Redirect to dashboard if on bank page
      if (window.location.pathname === "/bank") {
        router.push("/bank/dashboard");
      }
    } catch (error) {
      console.error("Login failed:", error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (
    username: string,
    email: string,
    password: string,
  ) => {
    setIsLoading(true);
    try {
      const data = await fetchClient.post<{ token: string; user: BankUser }>(
        "/api/auth/register",
        {
          username,
          email,
          password,
        },
      );

      // Store token in localStorage
      localStorage.setItem("auth_token", data.token);

      // Set user data
      setUser(data.user);

      // Invalidate notification preferences to ensure they're refreshed
      queryClient.invalidateQueries({ queryKey: notificationPreferencesKey });

      // Close the auth modal
      setIsAuthModalOpen(false);

      // Show success toast notification
      toast.success(
        "Account created successfully! You can customize your avatar and notification settings in the settings page.",
        { duration: 6000 },
      );

      // Redirect to dashboard if on bank page
      if (window.location.pathname === "/bank") {
        router.push("/bank/dashboard");
      }
    } catch (error) {
      console.error("Registration failed:", error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    // Remove token from localStorage
    localStorage.removeItem("auth_token");

    // Clear user data
    setUser(null);

    // Always redirect to home page on logout
    router.push("/");
  };

  // Implement Discord OAuth flow for login
  const loginWithDiscord = async () => {
    try {
      setIsLoading(true);

      // Redirect to the Discord OAuth endpoint
      window.location.href = "/api/auth/discord";
    } catch (error) {
      console.error("Discord login failed:", error);
      setIsLoading(false);
      throw error;
    }
  };

  // Connect Discord to existing account
  const connectDiscord = async () => {
    try {
      setIsLoading(true);

      // Get the current URL to return to after authentication
      const returnUrl = encodeURIComponent(window.location.pathname);

      // Redirect to the Discord OAuth endpoint with connect=true parameter
      window.location.href = `/api/auth/discord?connect=true&returnUrl=${returnUrl}`;
    } catch (error) {
      console.error("Discord connection failed:", error);
      setIsLoading(false);
      throw error;
    }
  };

  // Set password for account
  const setPassword = async (password: string) => {
    try {
      setIsLoading(true);

      await fetchClient.post("/api/auth/set-password", { password });

      // Refresh user data to update UI
      await refreshUserData();

      // If there's a pending Discord disconnect, proceed with it
      if (pendingDiscordDisconnect) {
        await performDiscordDisconnect();
        setPendingDiscordDisconnect(false);
      }

      // Close the password modal
      setIsPasswordModalOpen(false);

      toast.success("Password set successfully");
    } catch (error) {
      console.error("Set password failed:", error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Perform the actual Discord disconnection
  const performDiscordDisconnect = async () => {
    try {
      setIsLoading(true);

      await fetchClient.post("/api/auth/discord/disconnect", {});

      // Refresh user data to update UI
      await refreshUserData();

      toast.success("Discord account disconnected successfully");
    } catch (error) {
      console.error("Discord disconnection failed:", error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Disconnect Discord from account with password check
  const disconnectDiscord = async () => {
    try {
      setIsLoading(true);

      // Check if user has a password set
      // We'll use the API to determine if the user needs to set a password
      const data = await fetchClient.get<{ hasPassword: boolean }>(
        "/api/auth/has-password",
      );

      if (!data.hasPassword) {
        // No password set, show password creation modal
        setPendingDiscordDisconnect(true);
        setIsPasswordModalOpen(true);
        return;
      }

      // User has a password, proceed with disconnection
      await performDiscordDisconnect();
    } catch (error) {
      console.error("Discord disconnection failed:", error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const openAuthModal = () => {
    setIsAuthModalOpen(true);
  };

  const closeAuthModal = () => {
    setIsAuthModalOpen(false);
  };

  const openPasswordModal = () => {
    setIsPasswordModalOpen(true);
  };

  const closePasswordModal = () => {
    setIsPasswordModalOpen(false);
    // If there was a pending Discord disconnect, cancel it
    if (pendingDiscordDisconnect) {
      setPendingDiscordDisconnect(false);
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        isAuthenticated: !!user,
        isLoading,
        login,
        register,
        logout,
        loginWithDiscord,
        connectDiscord,
        disconnectDiscord,
        setPassword,
        openAuthModal,
        closeAuthModal,
        isAuthModalOpen,
        isPasswordModalOpen,
        openPasswordModal,
        closePasswordModal,
        refreshUserData,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
