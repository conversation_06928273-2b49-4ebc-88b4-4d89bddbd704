"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import { format } from "date-fns";
import fetchClient from "@/lib/fetchClient";

// Define types
interface Event {
  id: string;
  name: string;
  shortDescription: string | null;
  description: string;
  startDate: string;
  endDate: string;
  location: string | null;
  address?: string | null;
  isVirtual: boolean;
  virtualLink?: string | null;
  image: string | null;
  capacity?: number;
  category: {
    id: string;
    name: string;
    color: string | null;
  };
}

export default function EventDetailPage({
  params,
}: {
  params: { id: string };
}) {
  const router = useRouter();
  const { id } = params;

  const [event, setEvent] = useState<Event | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Format date range
  const formatDateRange = (startDate: string, endDate: string) => {
    const start = new Date(startDate);
    const end = new Date(endDate);

    // Same day event
    if (start.toDateString() === end.toDateString()) {
      return (
        <div>
          <div
            className="font-medium"
            style={{ color: "var(--color-text-primary)" }}
          >
            {format(start, "EEEE, MMMM d, yyyy")}
          </div>
          <div style={{ color: "var(--color-text-secondary)" }}>
            {format(start, "h:mm a")} - {format(end, "h:mm a")}
          </div>
        </div>
      );
    }

    // Multi-day event
    return (
      <div>
        <div
          className="font-medium"
          style={{ color: "var(--color-text-primary)" }}
        >
          {format(start, "MMMM d")} - {format(end, "MMMM d, yyyy")}
        </div>
        <div style={{ color: "var(--color-text-secondary)" }}>
          {format(start, "h:mm a")} - {format(end, "h:mm a")}
        </div>
      </div>
    );
  };

  // Fetch event data
  const fetchEvent = async () => {
    try {
      const data = await fetchClient.get<Event>(`/api/events/${id}`);
      setEvent(data);
      setError(null);
    } catch (err: any) {
      console.error("Error fetching event:", err);
      setError(err.message || "Failed to load event. Please try again.");
      setEvent(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchEvent();
  }, [id]);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <div
            className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-current border-r-transparent"
            style={{ color: "var(--color-primary)" }}
          ></div>
          <p className="mt-2" style={{ color: "var(--color-text-secondary)" }}>
            Loading event details...
          </p>
        </div>
      </div>
    );
  }

  if (!event) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div
          className="mb-6 px-4 py-3 rounded"
          style={{
            backgroundColor: "var(--color-error-light)",
            color: "var(--color-error-dark)",
            border: "1px solid var(--color-error)",
          }}
        >
          {error || "Event not found"}
        </div>
        <div className="text-center mt-4">
          <Link
            href="/events"
            className="inline-flex items-center px-4 py-2 rounded transition-colors"
            style={{
              backgroundColor: "var(--color-bg-button)",
              color: "var(--color-text-on-button)",
            }}
          >
            Back to Events
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Event header */}
      <Link
        href="/events"
        className="px-4 py-2 rounded transition-colors"
        style={{
          backgroundColor: "var(--color-bg-button)",
          color: "var(--color-text-on-button)",
          justifyItems: "right",
        }}
      >
        ← Back to Events
      </Link>
      <div className="bg-secondary rounded-lg overflow-hidden shadow-md mb-8">
        {/* Event image */}
        <div className="relative h-64 md:h-96">
          {event.image ? (
            <Image
              src={event.image}
              alt={event.name}
              fill
              sizes="(max-width: 768px) 100vw, 1200px"
              className="object-cover"
              priority
            />
          ) : (
            <div
              className="w-full h-full flex items-center justify-center"
              style={{ backgroundColor: "var(--color-bg-subtle)" }}
            >
              <span style={{ color: "var(--color-text-secondary)" }}>
                No image available
              </span>
            </div>
          )}

          {/* Category badge */}
          <div
            className="absolute top-4 right-4 px-3 py-1 text-sm font-semibold rounded-full"
            style={{
              backgroundColor: event.category.color || "var(--color-bg-tag)",
              color: "var(--color-text-on-tag)",
            }}
          >
            {event.category.name}
          </div>
        </div>

        {/* Event details */}
        <div className="p-6">
          <h1
            className="text-3xl font-bold mb-6"
            style={{ color: "var(--color-text-primary)" }}
          >
            {event.name}
          </h1>

          {/* Event metadata */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div className="flex items-start">
              <div
                className="rounded-full p-3 mr-4 flex-shrink-0"
                style={{ backgroundColor: "var(--color-bg-subtle)" }}
              >
                <svg
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  style={{ color: "var(--color-text-secondary)" }}
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                  />
                </svg>
              </div>
              <div>
                <h3
                  className="text-sm mb-1"
                  style={{ color: "var(--color-text-secondary)" }}
                >
                  Date & Time
                </h3>
                {formatDateRange(event.startDate, event.endDate)}
              </div>
            </div>

            <div className="flex items-start">
              <div
                className="rounded-full p-3 mr-4 flex-shrink-0"
                style={{ backgroundColor: "var(--color-bg-subtle)" }}
              >
                <svg
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  style={{ color: "var(--color-text-secondary)" }}
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                  />
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                  />
                </svg>
              </div>
              <div>
                <h3
                  className="text-sm mb-1"
                  style={{ color: "var(--color-text-secondary)" }}
                >
                  Location
                </h3>
                {event.isVirtual ? (
                  <div>
                    <div
                      className="font-medium"
                      style={{ color: "var(--color-text-primary)" }}
                    >
                      Virtual Event
                    </div>
                    {event.virtualLink && (
                      <a
                        href={event.virtualLink}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="mt-1 inline-block"
                        style={{ color: "var(--color-primary)" }}
                      >
                        Join Event
                      </a>
                    )}
                  </div>
                ) : (
                  <div>
                    <div
                      className="font-medium"
                      style={{ color: "var(--color-text-primary)" }}
                    >
                      {event.location || "Location TBA"}
                    </div>
                    {event.address && (
                      <div style={{ color: "var(--color-text-secondary)" }}>
                        {event.address}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>

            {event.capacity && (
              <div className="flex items-start">
                <div
                  className="rounded-full p-3 mr-4 flex-shrink-0"
                  style={{ backgroundColor: "var(--color-bg-subtle)" }}
                >
                  <svg
                    className="h-6 w-6"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    style={{ color: "var(--color-text-secondary)" }}
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                    />
                  </svg>
                </div>
                <div>
                  <h3
                    className="text-sm mb-1"
                    style={{ color: "var(--color-text-secondary)" }}
                  >
                    Capacity
                  </h3>
                  <div
                    className="font-medium"
                    style={{ color: "var(--color-text-primary)" }}
                  >
                    {event.capacity} attendees
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Event description */}
          <div>
            <h2
              className="text-xl font-semibold mb-4"
              style={{ color: "var(--color-text-primary)" }}
            >
              About This Event
            </h2>
            <div
              className="prose max-w-none"
              style={{ color: "var(--color-text-secondary)" }}
            >
              {event.description.split("\n").map((paragraph, index) => (
                <p key={index} className="mb-4">
                  {paragraph}
                </p>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Action buttons */}
      <div className="flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0 sm:space-x-4"></div>
    </div>
  );
}
