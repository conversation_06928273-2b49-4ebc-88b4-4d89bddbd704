# API Services

API service functions for communicating with backend endpoints and managing data operations.

## Service Categories

This directory contains service modules for:

- **Authentication** - Login, registration, and OAuth services
- **Banking** - Account operations, transactions, and balance management
- **User Management** - Profile updates and account settings
- **News** - Article creation, editing, and content management
- **Events** - Event management and calendar operations
- **Volunteer** - Volunteer shift management and hour tracking
- **Shopping** - Cart operations, checkout, and order management
- **Support** - Ticket creation and management
- **Notifications** - Real-time notification handling

Each service module encapsulates HTTP requests, data transformation, and error handling for specific feature areas.
