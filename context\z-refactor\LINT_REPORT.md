# Lint Report for Bank of Styx Project (Updated July 23, 2025)

## Summary

The linting process identified several issues across the codebase. Below is a categorized summary of the findings after recent fixes:

## Error Categories

### 1. React Issues
- **Unescaped Entities**: 43 instances of unescaped characters (`'` and `"`) in JSX that should be replaced with their HTML entity equivalents (7 fixed)
- **Unknown Properties**: 0 instances of unknown properties (`jsx` and `global`) in React components (2 fixed)

### 2. TypeScript Issues
- **Explicit Any Types**: 139 instances of `any` type usage that should be replaced with more specific types
- **No Non-null Assertions**: 36 instances of forbidden non-null assertions (`!`)
- **Unused Variables**: 103 instances of variables defined but never used
- **Empty Interfaces**: 0 instances of empty interfaces that are equivalent to `{}` (3 fixed)
- **Inferrable Types**: 38 instances of types that are trivially inferred and should be removed
- **Missing React Hook Dependencies**: 7 instances of React Hooks with missing dependencies
- **Empty Functions**: 1 instance of unexpected empty arrow function

### 3. Code Style Issues
- **Prefer Const**: 0 instances where variables are never reassigned and should use `const` instead (7 fixed)
- **Unexpected var**: 1 instance of `var` usage that should be replaced with `let` or `const` (Note: This is in a global declaration and may be a false positive)
- **Default Export Style**: 3 instances where objects should be assigned to variables before exporting as module default

### 4. Performance Issues
- **Image Optimization**: 47 instances where `<img>` tags should be replaced with Next.js `<Image />` component for better performance

## Files with Most Issues

The following files have the highest number of linting issues:
1. `src/lib/heartbeatService.ts` - Multiple non-null assertions and type issues
2. `src/app/api/bank/cashier/transactions/[id]/route.ts` - Multiple non-null assertions
3. `src/app/admin/dashboard/tickets/page.tsx` - Multiple unused variables and image optimization issues
4. `src/app/admin/events/page.tsx` - Multiple unused variables and hook dependency issues
5. `src/hooks/useBank.ts` - Multiple inferrable type issues

## Recommendations

1. **Address React Unescaped Entities**:
   - Replace `'` with `&apos;`, `&lsquo;`, `&#39;`, or `&rsquo;`
   - Replace `"` with `&quot;`, `&ldquo;`, `&#34;`, or `&rdquo;`

2. **Improve TypeScript Usage**:
   - Replace `any` types with more specific types
   - Remove non-null assertions and handle nullability properly
   - Clean up unused variables
   - Fix React Hook dependencies
   - Remove inferrable types annotations

3. **Optimize Images**:
   - Replace `<img>` tags with Next.js `<Image />` component

4. **Code Style Improvements**:
   - Follow proper export patterns
   - Replace `var` with `let` or `const`
   - Fix empty functions

## Latest Lint Results (July 23, 2025)

The most recent lint run identified the following issues:

### Unescaped Entities Errors (43)
- `src/app/cashier/dashboard/page.tsx:134:20` - Unescaped `'` character
- `src/app/news/dashboard/articles/[id]/page.tsx:222:76` - Unescaped `'` character
- `src/app/sales/categories/[id]/edit/page.tsx:59:29` - Unescaped `'` character
- `src/app/sales/categories/[id]/edit/page.tsx:59:53` - Unescaped `'` character
- `src/app/sales/orders/[id]/page.tsx:80:26` - Unescaped `'` character
- `src/app/sales/orders/[id]/page.tsx:80:47` - Unescaped `'` character
- `src/app/sales/products/[id]/edit/page.tsx:59:28` - Unescaped `'` character
- `src/app/sales/products/[id]/edit/page.tsx:59:52` - Unescaped `'` character
- `src/app/settings/page.tsx:861:72` - Unescaped `'` character
- `src/app/shop/orders/page.tsx:60:40` - Unescaped `'` character
- `src/app/shop/orders/[id]/page.tsx:66:26` - Unescaped `'` character
- `src/app/shop/orders/[id]/page.tsx:66:47` - Unescaped `'` character
- `src/app/shop/orders/[id]/page.tsx:66:66` - Unescaped `'` character
- `src/app/shop/products/[id]/page.tsx:47:28` - Unescaped `'` character
- `src/app/shop/products/[id]/page.tsx:47:49` - Unescaped `'` character
- `src/app/shop/search/page.tsx:39:22` - Unescaped `'` character
- `src/app/shop/search/page.tsx:39:52` - Unescaped `"` character
- `src/app/shop/search/page.tsx:39:60` - Unescaped `"` character
- `src/app/shop/search/page.tsx:50:51` - Unescaped `"` character
- `src/app/shop/search/page.tsx:50:59` - Unescaped `"` character
- `src/components/admin/UserProfileModal.tsx:461:35` - Unescaped `'` character
- `src/components/auth/AuthModal.tsx:291:20` - Unescaped `'` character
- `src/components/auth/EmailVerificationForm.tsx:82:71` - Unescaped `'` character
- `src/components/auth/GeneratedPasswordDisplay.tsx:37:13` - Unescaped `'` character
- `src/components/cashier/CashierDashboardLayout.tsx:117:28` - Unescaped `'` character
- `src/components/cashier/CashierDashboardLayout.tsx:183:30` - Unescaped `'` character
- `src/components/cashier/MemberSearch.tsx:196:39` - Unescaped `"` character
- `src/components/cashier/MemberSearch.tsx:196:56` - Unescaped `"` character
- `src/components/news/ArticlePreviewModal.tsx:99:38` - Unescaped `'` character
- `src/components/sales/SalesDashboardMain.tsx:116:57` - Unescaped `'` character
- `src/components/volunteer/lead/LeadDashboardStats.tsx:97:44` - Unescaped `'` character
- `src/components/volunteer/public/VolunteerSignupModal.tsx:236:33` - Unescaped `'` character
- `src/components/volunteer/public/VolunteerSignupModal.tsx:236:54` - Unescaped `"` character
- `src/components/volunteer/public/VolunteerSignupModal.tsx:236:58` - Unescaped `"` character
- `src/components/volunteer/public/VolunteerSignupModal.tsx:255:63` - Unescaped `"` character
- `src/components/volunteer/public/VolunteerSignupModal.tsx:255:67` - Unescaped `"` character
- `src/components/volunteer/public/VolunteerSignupModal.tsx:275:35` - Unescaped `"` character
- `src/components/volunteer/public/VolunteerSignupModal.tsx:275:38` - Unescaped `"` character
- `src/components/volunteer/public/VolunteerSignupModal.tsx:399:57` - Unescaped `'` character

### TypeScript Inferrable Types Errors (31)
- `src/hooks/useBank.ts` - 11 instances of inferrable types
- `src/lib/clientImageProcessing.ts` - 2 instances of inferrable types
- `src/lib/connectionStore.ts` - 8 instances of inferrable types
- `src/lib/imageProcessingV2.ts` - 2 instances of inferrable types
- `src/lib/ticket-system.ts` - 2 instances of inferrable types
- `src/services/bankService.ts` - 11 instances of inferrable types
- `src/utils/loremIpsum.ts` - 2 instances of inferrable types

### Empty Function Error (1)
- `src/hooks/useConfirm.tsx:21:17` - Unexpected empty arrow function

### Unexpected var Error (1)
- `src/lib/heartbeatService.ts:44:3` - Unexpected var, use let or const instead

## Next Steps

1. Address the unescaped entities errors (43 instances)
2. Fix the inferrable types issues (31 instances)
3. Review the empty function in useConfirm.tsx
4. Fix the "Unexpected var" error in heartbeatService.ts
5. Then focus on remaining warnings
6. Consider implementing a pre-commit hook to prevent new linting issues
7. Update the ESLint configuration to enforce stricter rules if needed

This report was updated on July 23, 2025.