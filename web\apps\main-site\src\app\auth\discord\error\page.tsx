"use client";

import { useSearchParams, useRouter } from "next/navigation";

export default function DiscordErrorPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const errorCode = searchParams.get("error") || "unknown_error";

  const errorMessages: Record<string, string> = {
    missing_code: "Authorization code was missing from the request.",
    email_required: "Email access is required to sign in with Discord.",
    server_error: "A server error occurred during authentication.",
    unknown_error: "An unknown error occurred during authentication.",
  };

  const errorMessage = errorMessages[errorCode] || errorMessages.unknown_error;

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      <div className="text-center max-w-md">
        <div className="bg-red-100 text-red-800 p-4 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-8 w-8"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </div>
        <h2 className="text-xl font-semibold mb-2">
          Discord Authentication Failed
        </h2>
        <p className="text-text-secondary mb-6">{errorMessage}</p>

        <div className="flex flex-col space-y-3">
          <button
            onClick={() => router.push("/api/auth/discord")}
            className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark transition-colors"
          >
            Try Again
          </button>
          <button
            onClick={() => router.push("/")}
            className="px-4 py-2 bg-secondary text-white rounded hover:bg-secondary-dark transition-colors"
          >
            Return to Home
          </button>
        </div>
      </div>
    </div>
  );
}
