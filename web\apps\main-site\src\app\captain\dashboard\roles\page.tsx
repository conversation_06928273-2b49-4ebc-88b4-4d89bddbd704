"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { CaptainDashboardLayout } from "@/components/captain";
import { useCaptainShip } from "@/hooks/captain/useCaptainShip";
import { <PERSON><PERSON>, <PERSON> } from "@bank-of-styx/ui";
import RoleCreationForm from "@/components/ships/RoleCreationForm";
import { getCaptainRoles, createShipRole, deleteShipRole, type ShipRole } from "@/services/captainService";

export default function CaptainRolesPage() {
  const { user, isLoading: authLoading, openAuthModal } = useAuth();
  const router = useRouter();
  
  const { ship, isLoading: shipLoading } = useCaptainShip();
  
  // Local state for roles page
  const [roles, setRoles] = useState<ShipRole[]>([]);
  const [rolesLoading, setRolesLoading] = useState(false);
  const [showCreateForm, setShowCreateForm] = useState(false);

  // Authentication check
  React.useEffect(() => {
    if (!authLoading && !user) {
      openAuthModal();
      router.replace("/");
      return;
    }
  }, [user, authLoading, router, openAuthModal]);

  // Fetch roles
  useEffect(() => {
    if (ship) {
      fetchRoles();
    }
  }, [ship]);

  const fetchRoles = async () => {
    try {
      setRolesLoading(true);
      const data = await getCaptainRoles();
      setRoles(data);
    } catch (error) {
      console.error("Error fetching roles:", error);
    } finally {
      setRolesLoading(false);
    }
  };

  const handleCreateRole = async (name: string, description?: string) => {
    try {
      await createShipRole(name, description);
      await fetchRoles(); // Refresh roles list
      setShowCreateForm(false);
    } catch (error) {
      console.error("Error creating role:", error);
      throw error;
    }
  };

  const handleDeleteRole = async (roleId: string) => {
    if (!confirm("Are you sure you want to delete this role? Members with this role will be set to 'Member'.")) {
      return;
    }

    try {
      await deleteShipRole(roleId);
      await fetchRoles(); // Refresh roles list
    } catch (error) {
      console.error("Error deleting role:", error);
      alert("Failed to delete role. It may still be in use.");
    }
  };

  // Loading state
  if (authLoading || shipLoading) {
    return (
      <div className="min-h-screen bg-secondary-dark flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-400">Loading...</p>
        </div>
      </div>
    );
  }

  if (!ship) {
    return (
      <CaptainDashboardLayout>
        <Card className="p-6 text-center">
          <p className="text-gray-400">Ship not found</p>
        </Card>
      </CaptainDashboardLayout>
    );
  }

  return (
    <CaptainDashboardLayout ship={ship}>
      <div className="space-y-4 sm:space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-xl sm:text-2xl font-bold text-white mb-1">
              Ship Roles
            </h1>
            <p className="text-sm text-gray-400">
              Create and manage custom roles for your crew
            </p>
          </div>
          <div className="mt-3 sm:mt-0 flex gap-2">
            <Button 
              variant="outline" 
              size="sm"
              onClick={fetchRoles}
              disabled={rolesLoading}
            >
              {rolesLoading ? "Refreshing..." : "Refresh"}
            </Button>
            <Button 
              variant="primary" 
              size="sm"
              onClick={() => setShowCreateForm(!showCreateForm)}
            >
              {showCreateForm ? "Cancel" : "Create Role"}
            </Button>
          </div>
        </div>

        {/* Create Role Form */}
        {showCreateForm && (
          <Card className="p-4 sm:p-6">
            <h2 className="text-lg font-semibold text-white mb-4">Create New Role</h2>
            <RoleCreationForm
              onCreateRole={handleCreateRole}
              onCancel={() => setShowCreateForm(false)}
            />
          </Card>
        )}

        {/* Roles List */}
        <Card className="p-4 sm:p-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
            <h2 className="text-lg font-semibold text-white mb-2 sm:mb-0">
              Custom Roles ({Array.isArray(roles) ? roles.length : 0})
            </h2>
            <div className="text-sm text-gray-400">
              Roles you've created for your ship
            </div>
          </div>

          {rolesLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-gray-400">Loading roles...</p>
            </div>
          ) : !Array.isArray(roles) || roles.length === 0 ? (
            <div className="text-center py-8">
              <div className="w-16 h-16 bg-secondary-dark rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">⚡</span>
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">No Custom Roles</h3>
              <p className="text-gray-400 mb-4">
                Create custom roles to better organize your crew
              </p>
              <Button 
                variant="primary" 
                size="sm"
                onClick={() => setShowCreateForm(true)}
              >
                Create Your First Role
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {roles.map((role) => (
                <Card key={role.id} className="p-4 bg-secondary-dark/50">
                  <div className="flex items-start justify-between mb-3">
                    <div className="min-w-0 flex-1">
                      <h3 className="font-semibold text-white truncate">{role.name}</h3>
                      {role.description && (
                        <p className="text-sm text-gray-400 mt-1 line-clamp-2">
                          {role.description}
                        </p>
                      )}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteRole(role.id)}
                      className="ml-2 text-red-400 border-red-400 hover:bg-red-400 hover:text-white"
                    >
                      <span className="text-xs">🗑️</span>
                    </Button>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-400">
                      {role._count?.members || 0} members
                    </span>
                    <div className="flex items-center gap-1">
                      <span className="w-2 h-2 bg-primary rounded-full"></span>
                      <span className="text-xs text-gray-400">Active</span>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          )}
        </Card>

      </div>
    </CaptainDashboardLayout>
  );
}