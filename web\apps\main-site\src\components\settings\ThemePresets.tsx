"use client";

import React from "react";

interface ThemePreset {
  name: string;
  description: string;
  colors: Record<string, string>;
}

interface ThemePresetsProps {
  onApplyPreset: (colors: Record<string, string>) => void;
}

// Predefined theme presets
const themePresets: ThemePreset[] = [
  {
    name: "Discord Light",
    description: "A light theme with blue accents",
    colors: {
      "--color-primary": "#4752C4", // Darker blue for better contrast on light backgrounds
      "--color-primary-dark": "#3A43B1", // Even darker for hover states
      "--color-primary-light": "#5865F2", // Original primary color
      "--color-secondary": "#F2F3F5",
      "--color-secondary-dark": "#E3E5E8",
      "--color-secondary-light": "#FFFFFF",
      "--color-accent": "#D03A3D", // Darker red for better contrast
      "--color-accent-dark": "#B22D30",
      "--color-accent-light": "#ED4245",
      "--color-success": "#2E8B57", // Darker green for better contrast
      "--color-warning": "#E67E22", // Adjusted orange
      "--color-error": "#D32F2F", // Darker red for errors
      "--color-info": "#4752C4", // Matches primary
      "--color-text-primary": "#1A1C20", // Darker text for better contrast
      "--color-text-secondary": "#4F5660",
      "--color-text-muted": "#747F8D",
      "--color-text-disabled": "rgba(116, 127, 141, 0.75)",
      "--color-border-dark": "#C7CCD1",
      "--color-border-primary": "#4752C4", // Matches primary
      "--color-border-accent": "#D03A3D", // Matches accent
      "--color-border-subtle": "#E3E5E8",
      "--color-bg-page": "#F5F7FA", // Slightly lighter background
      "--color-bg-card": "#FFFFFF",
      "--color-bg-input": "#F2F3F5",
      "--color-bg-hover": "#E9ECEF", // Slightly darker for better contrast
    },
  },
  {
    name: "Crimson Gold",
    description: "A dark theme with crimson and gold accents",
    colors: {
      "--color-primary": "#FFD700", // Gold
      "--color-primary-dark": "#DAA520", // Darker gold
      "--color-primary-light": "#FFDF5E", // Lighter gold
      "--color-secondary": "#2D1A1A", // Dark burgundy
      "--color-secondary-dark": "#1F1212", // Darker burgundy
      "--color-secondary-light": "#3D2929", // Lighter burgundy
      "--color-accent": "#DC143C", // Crimson
      "--color-accent-dark": "#B01030", // Darker crimson
      "--color-accent-light": "#F0566B", // Lighter crimson
      "--color-success": "#2E8B57", // Sea green
      "--color-warning": "#FF8C00", // Dark orange
      "--color-error": "#B22222", // Firebrick red
      "--color-info": "#FFD700", // Gold
      "--color-text-primary": "#FFFFFF", // White text
      "--color-text-secondary": "#D4AF37", // Gold text
      "--color-text-muted": "#B8860B", // Darker gold text
      "--color-text-disabled": "rgba(184, 134, 11, 0.75)",
      "--color-border-dark": "#4A2929", // Dark burgundy border
      "--color-border-primary": "#FFD700", // Gold border
      "--color-border-accent": "#DC143C", // Crimson border
      "--color-border-subtle": "#2D1A1A", // Dark burgundy
      "--color-bg-page": "#1F1212", // Darker burgundy background
      "--color-bg-card": "#3D2929", // Lighter burgundy
      "--color-bg-input": "#2D1A1A", // Dark burgundy
      "--color-bg-hover": "#4A2929", // Hover color
    },
  },
  {
    name: "Discord Dark",
    description: "The default dark theme inspired by Discord",
    colors: {
      "--color-primary": "#5865F2",
      "--color-primary-dark": "#4752C4",
      "--color-primary-light": "#7984F5",
      "--color-secondary": "#2C2F33",
      "--color-secondary-dark": "#23272A",
      "--color-secondary-light": "#36393F",
      "--color-accent": "#ED4245",
      "--color-accent-dark": "#D03A3D",
      "--color-accent-light": "#F25D60",
      "--color-success": "#43B581",
      "--color-warning": "#FAA61A",
      "--color-error": "#F04747",
      "--color-info": "#5865F2",
      "--color-text-primary": "#FFFFFF",
      "--color-text-secondary": "#99AAB5",
      "--color-text-muted": "#72767d",
      "--color-text-disabled": "rgba(114, 118, 125, 0.75)",
      "--color-border-dark": "#4B5563",
      "--color-border-primary": "#5865F2",
      "--color-border-accent": "#ED4245",
      "--color-border-subtle": "#2C2F33",
      "--color-bg-page": "#23272A",
      "--color-bg-card": "#36393F",
      "--color-bg-input": "#2C2F33",
      "--color-bg-hover": "#474B52",
    },
  },
  {
    name: "Midnight Blue",
    description: "A dark blue theme with purple accents",
    colors: {
      "--color-primary": "#7289DA",
      "--color-primary-dark": "#5B6EAE",
      "--color-primary-light": "#8EA1E1",
      "--color-secondary": "#2A2D3E",
      "--color-secondary-dark": "#1A1B26",
      "--color-secondary-light": "#3A3F5A",
      "--color-accent": "#BB9AF7",
      "--color-accent-dark": "#9D7CD8",
      "--color-accent-light": "#C4A7F5",
      "--color-success": "#9ECE6A",
      "--color-warning": "#E0AF68",
      "--color-error": "#F7768E",
      "--color-info": "#7AA2F7",
      "--color-text-primary": "#FFFFFF",
      "--color-text-secondary": "#A9B1D6",
      "--color-text-muted": "#787C99",
      "--color-text-disabled": "rgba(120, 124, 153, 0.75)",
      "--color-border-dark": "#414868",
      "--color-border-primary": "#7289DA",
      "--color-border-accent": "#BB9AF7",
      "--color-border-subtle": "#2A2D3E",
      "--color-bg-page": "#1A1B26",
      "--color-bg-card": "#3A3F5A",
      "--color-bg-input": "#2A2D3E",
      "--color-bg-hover": "#414868",
    },
  },
  {
    name: "Emerald Dark",
    description: "A dark theme with green accents",
    colors: {
      "--color-primary": "#10B981",
      "--color-primary-dark": "#059669",
      "--color-primary-light": "#34D399",
      "--color-secondary": "#1F2937",
      "--color-secondary-dark": "#111827",
      "--color-secondary-light": "#374151",
      "--color-accent": "#F87171",
      "--color-accent-dark": "#EF4444",
      "--color-accent-light": "#FCA5A5",
      "--color-success": "#10B981",
      "--color-warning": "#F59E0B",
      "--color-error": "#EF4444",
      "--color-info": "#3B82F6",
      "--color-text-primary": "#F9FAFB",
      "--color-text-secondary": "#D1D5DB",
      "--color-text-muted": "#9CA3AF",
      "--color-text-disabled": "rgba(156, 163, 175, 0.75)",
      "--color-border-dark": "#4B5563",
      "--color-border-primary": "#10B981",
      "--color-border-accent": "#F87171",
      "--color-border-subtle": "#1F2937",
      "--color-bg-page": "#111827",
      "--color-bg-card": "#374151",
      "--color-bg-input": "#1F2937",
      "--color-bg-hover": "#4B5563",
    },
  },

  {
    name: "Pirate Gold",
    description: "A dark theme with gold accents for the Bank of Styx",
    colors: {
      "--color-primary": "#FFD700",
      "--color-primary-dark": "#DAA520",
      "--color-primary-light": "#FFDF5E",
      "--color-secondary": "#1C1C1C",
      "--color-secondary-dark": "#0F0F0F",
      "--color-secondary-light": "#2A2A2A",
      "--color-accent": "#C41E3A",
      "--color-accent-dark": "#A01A2F",
      "--color-accent-light": "#E63E58",
      "--color-success": "#2E8B57",
      "--color-warning": "#FF8C00",
      "--color-error": "#B22222",
      "--color-info": "#FFD700",
      "--color-text-primary": "#FFFFFF",
      "--color-text-secondary": "#D4AF37",
      "--color-text-muted": "#B8860B",
      "--color-text-disabled": "rgba(184, 134, 11, 0.75)",
      "--color-border-dark": "#3A3A3A",
      "--color-border-primary": "#FFD700",
      "--color-border-accent": "#C41E3A",
      "--color-border-subtle": "#1C1C1C",
      "--color-bg-page": "#0F0F0F",
      "--color-bg-card": "#2A2A2A",
      "--color-bg-input": "#1C1C1C",
      "--color-bg-hover": "#3A3A3A",
    },
  },
  {
    name: "Seafoam",
    description: "A soothing teal theme with ocean-inspired colors",
    colors: {
      "--color-primary": "#20B2AA",
      "--color-primary-dark": "#188F89",
      "--color-primary-light": "#5FD3CD",
      "--color-secondary": "#263238",
      "--color-secondary-dark": "#1C2529",
      "--color-secondary-light": "#37474F",
      "--color-accent": "#FF6B6B",
      "--color-accent-dark": "#E05252",
      "--color-accent-light": "#FF9999",
      "--color-success": "#48C774",
      "--color-warning": "#FFD166",
      "--color-error": "#F44336",
      "--color-info": "#20B2AA",
      "--color-text-primary": "#FFFFFF",
      "--color-text-secondary": "#B2DFDB",
      "--color-text-muted": "#80CBC4",
      "--color-text-disabled": "rgba(128, 203, 196, 0.75)",
      "--color-border-dark": "#455A64",
      "--color-border-primary": "#20B2AA",
      "--color-border-accent": "#FF6B6B",
      "--color-border-subtle": "#263238",
      "--color-bg-page": "#1C2529",
      "--color-bg-card": "#37474F",
      "--color-bg-input": "#263238",
      "--color-bg-hover": "#455A64",
    },
  },
  {
    name: "Royal Purple",
    description: "A regal theme with purple and gold accents",
    colors: {
      "--color-primary": "#9C27B0",
      "--color-primary-dark": "#7B1FA2",
      "--color-primary-light": "#BA68C8",
      "--color-secondary": "#212121",
      "--color-secondary-dark": "#121212",
      "--color-secondary-light": "#303030",
      "--color-accent": "#FFD700",
      "--color-accent-dark": "#DAA520",
      "--color-accent-light": "#FFDF5E",
      "--color-success": "#66BB6A",
      "--color-warning": "#FFA726",
      "--color-error": "#EF5350",
      "--color-info": "#9C27B0",
      "--color-text-primary": "#FFFFFF",
      "--color-text-secondary": "#E1BEE7",
      "--color-text-muted": "#CE93D8",
      "--color-text-disabled": "rgba(206, 147, 216, 0.75)",
      "--color-border-dark": "#424242",
      "--color-border-primary": "#9C27B0",
      "--color-border-accent": "#FFD700",
      "--color-border-subtle": "#212121",
      "--color-bg-page": "#121212",
      "--color-bg-card": "#303030",
      "--color-bg-input": "#212121",
      "--color-bg-hover": "#424242",
    },
  },
];

export const ThemePresets: React.FC<ThemePresetsProps> = ({
  onApplyPreset,
}) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {themePresets.map((preset) => (
        <div
          key={preset.name}
          className="border border-gray-600 rounded-lg p-4 hover:border-primary cursor-pointer transition-colors"
          onClick={() => onApplyPreset(preset.colors)}
          style={{
            background: `linear-gradient(135deg, ${preset.colors["--color-secondary-dark"]} 0%, ${preset.colors["--color-secondary"]} 100%)`,
          }}
        >
          <div className="flex justify-between items-center mb-3">
            <h3 className="font-semibold">{preset.name}</h3>
            <div className="flex space-x-1">
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: preset.colors["--color-primary"] }}
              ></div>
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: preset.colors["--color-accent"] }}
              ></div>
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: preset.colors["--color-success"] }}
              ></div>
            </div>
          </div>
          <p className="text-sm text-text-secondary">{preset.description}</p>
          <button className="mt-3 w-full py-1 px-3 bg-secondary hover:bg-secondary-light rounded text-sm transition-colors">
            Apply Theme
          </button>
        </div>
      ))}
    </div>
  );
};
