"use client";

import React, { useState, useEffect } from "react";
import { DashboardLayout } from "../../../../components/bank";
import { useAuth } from "../../../../contexts/AuthContext";
import { AvatarUploadModal } from "../../../../components/user/AvatarUploadModal";
import { PasswordCreationModal } from "../../../../components/auth/PasswordCreationModal";
import toast from "react-hot-toast";

export default function SettingsPage() {
  const {
    user,
    isAuthenticated,
    isLoading,
    refreshUserData,
    connectDiscord,
    disconnectDiscord,
    setPassword,
    isPasswordModalOpen,
    closePasswordModal,
  } = useAuth();

  const [activeTab, setActiveTab] = useState<
    "profile" | "preferences" | "security"
  >("profile");

  // Initialize with empty values
  const [displayName, setDisplayName] = useState("");
  const [email, setEmail] = useState("");
  const [defaultView, setDefaultView] = useState<
    "dashboard" | "transactions" | "transfer" | "pay-code" | "donate"
  >("dashboard");
  const [notifications, setNotifications] = useState({
    transfers: false,
    deposits: false,
    withdrawals: false,
    newsAndEvents: false,
  });
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");

  // Handle token from Discord connection and update form values when user data changes
  useEffect(() => {
    // Check for token in URL (for Discord connection)
    if (typeof window !== "undefined") {
      const searchParams = new URLSearchParams(window.location.search);
      const token = searchParams.get("token");
      const connected = searchParams.get("connected");

      // If we have a token from Discord connection, store it and refresh user data
      if (token && connected === "true") {
        localStorage.setItem("auth_token", token);
        refreshUserData().then(() => {
          // Remove the token from the URL to prevent it from being shared
          const newUrl = window.location.pathname;
          window.history.replaceState({}, document.title, newUrl);
          toast.success("Discord account connected successfully!");
        });
      }
    }

    // Update form values when user data changes
    if (user) {
      setDisplayName(user.displayName);
      setEmail(user.email);
      setDefaultView(user.preferences.defaultView);
      setNotifications(user.preferences.notifications);
    }
  }, [user, refreshUserData]);

  const handleProfileSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Only proceed if user is logged in
    if (!user || !isAuthenticated) {
      toast.error("You must be logged in to update your profile");
      return;
    }

    try {
      // In a real implementation, this would call the API
      // For now, just show a toast notification
      toast.success("Profile updated successfully!");

      // Refresh user data
      await refreshUserData();
    } catch (error: any) {
      toast.error(error.message || "Failed to update profile");
      console.error("Profile update error:", error);
    }
  };

  const handlePreferencesSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Only proceed if user is logged in
    if (!user || !isAuthenticated) {
      toast.error("You must be logged in to update your preferences");
      return;
    }

    try {
      // In a real implementation, this would call the API
      // For now, just show a toast notification
      toast.success("Preferences updated successfully!");

      // Refresh user data
      await refreshUserData();
    } catch (error: any) {
      toast.error(error.message || "Failed to update preferences");
      console.error("Preferences update error:", error);
    }
  };

  const handleSecuritySubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Only proceed if user is logged in
    if (!user || !isAuthenticated) {
      toast.error("You must be logged in to change your password");
      return;
    }

    if (newPassword !== confirmPassword) {
      toast.error("New passwords do not match!");
      return;
    }

    try {
      // In a real implementation, this would call the API
      // For now, just show a toast notification
      toast.success("Password changed successfully!");
      setCurrentPassword("");
      setNewPassword("");
      setConfirmPassword("");
    } catch (error: any) {
      toast.error(error.message || "Failed to update password");
      console.error("Password update error:", error);
    }
  };

  const handleNotificationChange = (key: keyof typeof notifications) => {
    setNotifications({
      ...notifications,
      [key]: !notifications[key],
    });
  };

  return (
    <DashboardLayout>
      {/* Password Creation Modal */}
      <PasswordCreationModal
        isOpen={isPasswordModalOpen}
        onClose={closePasswordModal}
        onConfirm={setPassword}
        isLoading={isLoading}
      />

      <div className="bg-secondary-light rounded-lg shadow-md overflow-hidden mb-6">
        <div className="flex border-b border-gray-600">
          <button
            className={`flex-1 py-3 px-4 text-center font-medium ${
              activeTab === "profile"
                ? "bg-secondary text-primary border-b-2 border-primary"
                : "text-white hover:bg-secondary"
            }`}
            onClick={() => setActiveTab("profile")}
          >
            Profile
          </button>
          <button
            className={`flex-1 py-3 px-4 text-center font-medium ${
              activeTab === "preferences"
                ? "bg-secondary text-primary border-b-2 border-primary"
                : "text-white hover:bg-secondary"
            }`}
            onClick={() => setActiveTab("preferences")}
          >
            Preferences
          </button>
          <button
            className={`flex-1 py-3 px-4 text-center font-medium ${
              activeTab === "security"
                ? "bg-secondary text-primary border-b-2 border-primary"
                : "text-white hover:bg-secondary"
            }`}
            onClick={() => setActiveTab("security")}
          >
            Security
          </button>
        </div>

        <div className="p-6">
          {activeTab === "profile" && (
            <>
              <h2 className="text-2xl font-bold text-white mb-6">
                Profile Settings
              </h2>

              <form onSubmit={handleProfileSubmit}>
                <div className="space-y-4">
                  <div className="flex items-center mb-6">
                    <div className="mr-4">
                      {isLoading ? (
                        <div className="w-20 h-20 rounded-full bg-gray-700 animate-pulse"></div>
                      ) : (
                        <img
                          src={
                            user ? user.avatar : "/images/avatars/default.png"
                          }
                          alt={user ? user.displayName : "User"}
                          className="w-20 h-20 rounded-full"
                        />
                      )}
                    </div>
                    <div>
                      {isLoading ? (
                        <div className="h-6 w-32 bg-gray-700 animate-pulse rounded mb-2"></div>
                      ) : (
                        <h3 className="text-lg font-medium text-white">
                          {user ? `@${user.username}` : ""}
                        </h3>
                      )}
                      <button
                        type="button"
                        className="text-primary hover:text-primary-light text-sm"
                        disabled={!isAuthenticated}
                      >
                        Change Avatar
                      </button>
                    </div>
                  </div>

                  <div>
                    <label
                      htmlFor="displayName"
                      className="block text-white font-medium mb-2"
                    >
                      Display Name
                    </label>
                    <input
                      type="text"
                      id="displayName"
                      value={displayName}
                      onChange={(e) => setDisplayName(e.target.value)}
                      className="w-full px-4 py-2 bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary"
                      required
                    />
                  </div>

                  <div>
                    <label
                      htmlFor="email"
                      className="block text-white font-medium mb-2"
                    >
                      Email
                    </label>
                    <input
                      type="email"
                      id="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="w-full px-4 py-2 bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary"
                      required
                    />
                  </div>

                  <div className="pt-4">
                    <h3 className="text-lg font-medium text-white mb-3">
                      Connected Accounts
                    </h3>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <div className="flex items-center">
                          <svg
                            className="w-6 h-6 text-primary mr-2"
                            fill="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path d="M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3847-.4058-.874-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.946 2.4189-2.1568 2.4189Z" />
                          </svg>
                          <span className="text-white">Discord</span>
                        </div>
                        {isLoading ? (
                          <div className="h-6 w-24 bg-gray-700 animate-pulse rounded"></div>
                        ) : user && user.connectedAccounts.discord ? (
                          <div className="flex items-center">
                            <span className="text-success text-sm mr-2">
                              Connected
                            </span>
                            <button
                              type="button"
                              className="text-accent hover:text-accent-light text-sm"
                              disabled={!isAuthenticated}
                              onClick={async () => {
                                try {
                                  await disconnectDiscord();
                                  // Note: Toast is already shown in the disconnectDiscord function
                                } catch (error) {
                                  console.error(
                                    "Failed to disconnect Discord:",
                                    error,
                                  );
                                  toast.error(
                                    "Failed to disconnect Discord account",
                                  );
                                }
                              }}
                            >
                              Disconnect
                            </button>
                          </div>
                        ) : (
                          <button
                            type="button"
                            className="text-success hover:text-success-light text-sm"
                            disabled={!isAuthenticated}
                            onClick={async () => {
                              try {
                                await connectDiscord();
                              } catch (error) {
                                console.error(
                                  "Failed to connect Discord:",
                                  error,
                                );
                                toast.error(
                                  "Failed to connect Discord account",
                                );
                              }
                            }}
                          >
                            Connect
                          </button>
                        )}
                      </div>

                      <div className="flex justify-between items-center">
                        <div className="flex items-center">
                          <svg
                            className="w-6 h-6 text-primary mr-2"
                            fill="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
                          </svg>
                          <span className="text-white">Facebook</span>
                        </div>
                        {isLoading ? (
                          <div className="h-6 w-24 bg-gray-700 animate-pulse rounded"></div>
                        ) : user && user.connectedAccounts.facebook ? (
                          <div className="flex items-center">
                            <span className="text-success text-sm mr-2">
                              Connected
                            </span>
                            <button
                              type="button"
                              className="text-accent hover:text-accent-light text-sm"
                              disabled={!isAuthenticated}
                            >
                              Disconnect
                            </button>
                          </div>
                        ) : (
                          <button
                            type="button"
                            className="text-primary hover:text-primary-light text-sm"
                            disabled={!isAuthenticated}
                          >
                            Connect
                          </button>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-end space-x-4 pt-4">
                    <button
                      type="button"
                      onClick={() => {
                        if (user) {
                          setDisplayName(user.displayName);
                          setEmail(user.email);
                        } else {
                          setDisplayName("");
                          setEmail("");
                        }
                      }}
                      className="px-4 py-2 border border-gray-600 rounded-md text-white hover:bg-secondary"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark"
                    >
                      Save Changes
                    </button>
                  </div>
                </div>
              </form>
            </>
          )}

          {activeTab === "preferences" && (
            <>
              <h2 className="text-2xl font-bold text-white mb-6">
                Preferences
              </h2>

              <form onSubmit={handlePreferencesSubmit}>
                <div className="space-y-4">
                  <div>
                    <label
                      htmlFor="defaultView"
                      className="block text-white font-medium mb-2"
                    >
                      Default Dashboard View
                    </label>
                    <select
                      id="defaultView"
                      value={defaultView}
                      onChange={(e) =>
                        setDefaultView(
                          e.target.value as "dashboard" | "transactions",
                        )
                      }
                      className="w-full px-4 py-2 bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary"
                    >
                      <option
                        value="dashboard"
                        className="bg-secondary text-white"
                      >
                        Dashboard
                      </option>
                      <option
                        value="transactions"
                        className="bg-secondary text-white"
                      >
                        Transactions
                      </option>
                    </select>
                  </div>

                  <div className="pt-4">
                    <h3 className="text-lg font-medium text-white mb-3">
                      Notifications
                    </h3>
                    <div className="space-y-3">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="notifyTransfers"
                          checked={notifications.transfers}
                          onChange={() => handleNotificationChange("transfers")}
                          className="h-4 w-4 text-primary focus:ring-primary border-gray-600 rounded"
                        />
                        <label
                          htmlFor="notifyTransfers"
                          className="ml-2 block text-white"
                        >
                          Transfers
                        </label>
                      </div>

                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="notifyDeposits"
                          checked={notifications.deposits}
                          onChange={() => handleNotificationChange("deposits")}
                          className="h-4 w-4 text-primary focus:ring-primary border-gray-600 rounded"
                        />
                        <label
                          htmlFor="notifyDeposits"
                          className="ml-2 block text-white"
                        >
                          Deposits
                        </label>
                      </div>

                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="notifyWithdrawals"
                          checked={notifications.withdrawals}
                          onChange={() =>
                            handleNotificationChange("withdrawals")
                          }
                          className="h-4 w-4 text-primary focus:ring-primary border-gray-600 rounded"
                        />
                        <label
                          htmlFor="notifyWithdrawals"
                          className="ml-2 block text-white"
                        >
                          Withdrawals
                        </label>
                      </div>

                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="notifyNewsEvents"
                          checked={notifications.newsAndEvents}
                          onChange={() =>
                            handleNotificationChange("newsAndEvents")
                          }
                          className="h-4 w-4 text-primary focus:ring-primary border-gray-600 rounded"
                        />
                        <label
                          htmlFor="notifyNewsEvents"
                          className="ml-2 block text-white"
                        >
                          News and Events
                        </label>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-end space-x-4 pt-4">
                    <button
                      type="button"
                      onClick={() => {
                        if (user) {
                          setDefaultView(user.preferences.defaultView);
                          setNotifications(user.preferences.notifications);
                        } else {
                          setDefaultView("dashboard");
                          setNotifications({
                            transfers: false,
                            deposits: false,
                            withdrawals: false,
                            newsAndEvents: false,
                          });
                        }
                      }}
                      className="px-4 py-2 border border-gray-600 rounded-md text-white hover:bg-secondary"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark"
                    >
                      Save Changes
                    </button>
                  </div>
                </div>
              </form>
            </>
          )}

          {activeTab === "security" && (
            <>
              <h2 className="text-2xl font-bold text-white mb-6">Security</h2>

              <form onSubmit={handleSecuritySubmit}>
                <div className="space-y-4">
                  <div>
                    <label
                      htmlFor="currentPassword"
                      className="block text-white font-medium mb-2"
                    >
                      Current Password
                    </label>
                    <input
                      type="password"
                      id="currentPassword"
                      value={currentPassword}
                      onChange={(e) => setCurrentPassword(e.target.value)}
                      className="w-full px-4 py-2 bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary"
                      required
                    />
                  </div>

                  <div>
                    <label
                      htmlFor="newPassword"
                      className="block text-white font-medium mb-2"
                    >
                      New Password
                    </label>
                    <input
                      type="password"
                      id="newPassword"
                      value={newPassword}
                      onChange={(e) => setNewPassword(e.target.value)}
                      className="w-full px-4 py-2 bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary"
                      required
                    />
                  </div>

                  <div>
                    <label
                      htmlFor="confirmPassword"
                      className="block text-white font-medium mb-2"
                    >
                      Confirm New Password
                    </label>
                    <input
                      type="password"
                      id="confirmPassword"
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      className="w-full px-4 py-2 bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary"
                      required
                    />
                  </div>

                  <div className="flex justify-end space-x-4 pt-4">
                    <button
                      type="button"
                      onClick={() => {
                        setCurrentPassword("");
                        setNewPassword("");
                        setConfirmPassword("");
                      }}
                      className="px-4 py-2 border border-gray-600 rounded-md text-white hover:bg-secondary"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark"
                    >
                      Change Password
                    </button>
                  </div>
                </div>
              </form>

              <div className="mt-8 pt-6 border-t border-gray-600">
                <h3 className="text-lg font-medium text-white mb-4">
                  Account Actions
                </h3>
                <button
                  type="button"
                  className="text-accent hover:text-accent-light font-medium"
                  onClick={() => {
                    if (
                      confirm(
                        "Are you sure you want to deactivate your account? This action cannot be undone.",
                      )
                    ) {
                      alert(
                        "This is a placeholder - no actual deactivation occurred.",
                      );
                    }
                  }}
                >
                  Deactivate Account
                </button>
              </div>
            </>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
