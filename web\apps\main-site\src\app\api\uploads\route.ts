import { NextRequest, NextResponse } from "next/server";
import { saveImage } from "@/services/imageUploadService";

export async function POST(request: NextRequest) {
  try {
    // Check if request is multipart/form-data
    const contentType = request.headers.get("content-type");
    if (!contentType || !contentType.includes("multipart/form-data")) {
      return NextResponse.json(
        { error: "Invalid content type, must be multipart/form-data" },
        { status: 400 },
      );
    } // Parse form data
    const formData = await request.formData();
    const file = formData.get("file") as File;
    const entityIdValue = formData.get("entityId");
    const entityId = entityIdValue ? String(entityIdValue) : undefined;
    const entityTypeValue = formData.get("entityType");
    const entityType = entityTypeValue ? String(entityTypeValue) : "news";

    // Validate file
    if (!file) {
      return NextResponse.json({ error: "No file provided" }, { status: 400 });
    }

    // Validate file type
    const allowedTypes = ["image/jpeg", "image/png", "image/gif", "image/webp"];
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        {
          error:
            "File type not supported. Please upload an image (JPEG, PNG, GIF, WEBP)",
        },
        { status: 400 },
      );
    }

    // Validate file size (10MB max)
    const maxSize = 10 * 1024 * 1024; // 10MB in bytes
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: "File size exceeds 10MB limit" },
        { status: 400 },
      );
    }

    const result = await saveImage(file, {
      entityType,
      entityId,
      directory: entityType || "general",
    });

    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    console.error("Upload error:", error);
    return NextResponse.json({ error: "Upload failed" }, { status: 500 });
  }
}
