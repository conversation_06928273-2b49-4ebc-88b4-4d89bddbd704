"use client";

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Button } from '@bank-of-styx/ui';

interface ShipInvite {
  id: string;
  type: string;
  status: string;
  message?: string;
  createdAt: string;
  ship: {
    id: string;
    name: string;
    description: string;
    logo?: string;
    captain: {
      id: string;
      username: string;
      displayName: string;
      avatar: string;
    };
  };
  requestedBy: {
    id: string;
    username: string;
    displayName: string;
    avatar: string;
  };
}

interface ShipInviteCardProps {
  invite: ShipInvite;
  onRespond: (inviteId: string, response: 'accepted' | 'declined') => Promise<void>;
  loading?: boolean;
}

export default function ShipInviteCard({ 
  invite, 
  onRespond, 
  loading = false 
}: ShipInviteCardProps) {
  const [actionLoading, setActionLoading] = useState(false);

  const handleRespond = async (response: 'accepted' | 'declined') => {
    setActionLoading(true);
    try {
      await onRespond(invite.id, response);
    } catch (error) {
      console.error('Error responding to invite:', error);
    } finally {
      setActionLoading(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden hover:shadow-lg transition-shadow">
      {/* Header with ship logo and basic info */}
      <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50">
        <div className="flex items-center space-x-4">
          <div className="flex-shrink-0">
            <Image
              src={invite.ship.logo || '/images/default-ship.png'}
              alt={invite.ship.name}
              width={60}
              height={60}
              className="w-15 h-15 rounded-lg object-cover shadow-sm"
            />
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2">
              <h3 className="text-xl font-bold text-gray-900 truncate">
                {invite.ship.name}
              </h3>
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                Ship Invitation
              </span>
            </div>
            
            <div className="mt-1 flex items-center space-x-2 text-sm text-gray-600">
              <Image
                src={invite.requestedBy.avatar}
                alt={invite.requestedBy.displayName}
                width={20}
                height={20}
                className="w-5 h-5 rounded-full object-cover"
              />
              <span>
                Invited by <strong>{invite.requestedBy.displayName}</strong>
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Ship description and invite message */}
      <div className="p-4">
        <p className="text-gray-700 text-sm leading-relaxed mb-3">
          {invite.ship.description}
        </p>
        
        {invite.message && (
          <div className="bg-blue-50 border-l-4 border-blue-400 p-3 mb-4">
            <p className="text-sm text-blue-800 italic">
              "{invite.message}"
            </p>
          </div>
        )}

        {/* Captain info */}
        <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg mb-4">
          <Image
            src={invite.ship.captain.avatar}
            alt={invite.ship.captain.displayName}
            width={32}
            height={32}
            className="w-8 h-8 rounded-full object-cover"
          />
          <div>
            <div className="text-sm font-medium text-gray-900">
              Captain: {invite.ship.captain.displayName}
            </div>
            <div className="text-xs text-gray-500">
              @{invite.ship.captain.username}
            </div>
          </div>
        </div>

        {/* Invitation timestamp */}
        <div className="text-xs text-gray-500 mb-4">
          Received on {new Date(invite.createdAt).toLocaleDateString()} at{' '}
          {new Date(invite.createdAt).toLocaleTimeString()}
        </div>
      </div>

      {/* Action buttons */}
      <div className="px-4 py-3 bg-gray-50 border-t border-gray-200">
        <div className="flex items-center justify-between">
          <Link
            href={`/ships/${invite.ship.id}`}
            className="text-sm text-blue-600 hover:text-blue-700 font-medium"
          >
            View Ship Details →
          </Link>
          
          <div className="flex space-x-3">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleRespond('declined')}
              disabled={loading || actionLoading}
              className="text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              {actionLoading ? 'Processing...' : 'Decline'}
            </Button>
            
            <Button
              size="sm"
              onClick={() => handleRespond('accepted')}
              disabled={loading || actionLoading}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              {actionLoading ? 'Processing...' : 'Accept Invitation'}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}