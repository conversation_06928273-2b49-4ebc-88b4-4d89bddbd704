﻿# Bank of Styx Website - Authentication System
# Generated on 08/07/2025 12:35:25
# Priority: High
# Complete authentication system including login, registration, Discord integration, and user management
# Root directory: C:\Users\<USER>\projects\test\web

## Directories and Files

### Files
C:\Users\<USER>\projects\test\web\apps\api\auth\
C:\Users\<USER>\projects\test\web\apps\api\auth\login\
C:\Users\<USER>\projects\test\web\apps\api\auth\login\route.ts
C:\Users\<USER>\projects\test\web\apps\api\auth\me\
C:\Users\<USER>\projects\test\web\apps\api\auth\me\route.ts
C:\Users\<USER>\projects\test\web\apps\api\auth\README.md
C:\Users\<USER>\projects\test\web\apps\api\auth\register\
C:\Users\<USER>\projects\test\web\apps\api\auth\register\route.ts
C:\Users\<USER>\projects\test\web\apps\api\auth\test\
C:\Users\<USER>\projects\test\web\apps\api\auth\test\route.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\auth\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\auth\discord\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\auth\discord\callback\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\auth\discord\callback\route.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\auth\discord\disconnect\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\auth\discord\disconnect\route.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\auth\discord\route.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\auth\has-password\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\auth\has-password\route.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\auth\login\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\auth\login\route.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\auth\me\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\auth\me\route.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\auth\prisma-test\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\auth\prisma-test\route.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\auth\README.md
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\auth\register\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\auth\register\route.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\auth\set-password\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\auth\set-password\route.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\auth\test\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\auth\test\route.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\auth\verify-email\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\auth\verify-email\route.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\auth\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\auth\discord\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\auth\discord\callback\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\auth\discord\callback\page.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\auth\discord\error\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\auth\discord\error\page.tsx

