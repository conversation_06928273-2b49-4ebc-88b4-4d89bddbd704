# React Contexts

React Context providers for global state management across the Bank of Styx application.

## Purpose

This directory contains Context providers that manage application-wide state including:

- User authentication state
- Banking account information
- Notification system state
- Theme and UI preferences
- Real-time data subscriptions

Contexts provide a way to share data between components without prop drilling and complement the TanStack Query state management for server state.
