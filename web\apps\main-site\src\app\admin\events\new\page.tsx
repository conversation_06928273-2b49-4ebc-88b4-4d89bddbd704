"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useColorTheme } from "@/contexts/ColorThemeContext";
import { useAuth } from "@/contexts/AuthContext";
import { ImageUploader } from "@/components/common/ImageUploader";
import fetchClient from "@/lib/fetchClient";

// Define types
interface Category {
  id: string;
  name: string;
  description: string | null;
  color: string | null;
}

export default function CreateEventPage() {
  const router = useRouter();
  const { isDarkMode } = useColorTheme();
  const { user, isAuthenticated } = useAuth();
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Define the form data type with optional fields
  interface EventFormData {
    name: string;
    description: string;
    shortDescription: string;
    startDate: string;
    startTime?: string; // Make optional so it can be deleted
    endDate: string;
    endTime?: string; // Make optional so it can be deleted
    location: string;
    address: string;
    virtualLink: string;
    isVirtual: boolean;
    image: string;
    status: string;
    capacity: string;
    categoryId: string;
  }

  // Form state
  const [formData, setFormData] = useState<EventFormData>({
    name: "",
    description: "",
    shortDescription: "",
    startDate: "",
    startTime: "09:00",
    endDate: "",
    endTime: "17:00",
    location: "",
    address: "",
    virtualLink: "",
    isVirtual: false,
    image: "",
    status: "draft",
    capacity: "",
    categoryId: "",
  });

  // State for image upload
  const [uploadError, setUploadError] = useState<string | null>(null);

  // Fetch categories
  const fetchCategories = async () => {
    try {
      const data = await fetchClient.get<Category[]>(
        "/api/admin/event-categories",
      );
      setCategories(data);

      // Set default category if available
      if (data.length > 0 && !formData.categoryId) {
        setFormData((prev) => ({ ...prev, categoryId: data[0].id }));
      }
    } catch (err) {
      console.error("Error loading categories:", err);
      setError("Failed to load categories. Please try again.");
    }
  };

  // Handle form input changes
  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >,
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Handle checkbox changes
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData((prev) => ({ ...prev, [name]: checked }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      // Combine date and time
      const startDateTime = new Date(
        `${formData.startDate}T${formData.startTime}`,
      );
      const endDateTime = new Date(`${formData.endDate}T${formData.endTime}`);

      // Validate dates
      if (isNaN(startDateTime.getTime()) || isNaN(endDateTime.getTime())) {
        throw new Error("Invalid date or time format");
      }

      if (endDateTime < startDateTime) {
        throw new Error("End date/time must be after start date/time");
      }

      // Create a copy of the form data to modify
      const { startTime, endTime, ...restFormData } = formData;

      // Prepare data for API without the fields we don't want to send
      const eventData = {
        ...restFormData,
        startDate: startDateTime.toISOString(),
        endDate: endDateTime.toISOString(),
        capacity: formData.capacity ? parseInt(formData.capacity) : null,
      };

      // Submit to API
      await fetchClient.post("/api/admin/events", eventData);
      setSuccess("Event created successfully!");

      // Redirect to events list after a short delay
      setTimeout(() => {
        router.push("/admin/events");
      }, 1500);
    } catch (err: any) {
      console.error("Error creating event:", err);
      setError(err.message || "Failed to create event. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Initialize
  useEffect(() => {
    fetchCategories();
  }, []);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1
          className="text-2xl font-bold"
          style={{ color: "var(--color-text-primary)" }}
        >
          Create New Event
        </h1>
        <Link
          href="/admin/events"
          className="px-4 py-2 rounded transition-colors"
          style={{
            backgroundColor: "var(--color-bg-button-secondary)",
            color: "var(--color-text-on-button)",
          }}
        >
          Back to Events
        </Link>
      </div>

      {/* Error message */}
      {error && (
        <div
          className="mb-6 px-4 py-3 rounded"
          style={{
            backgroundColor: "var(--color-error-light)",
            color: "var(--color-error-dark)",
            border: "1px solid var(--color-error)",
          }}
        >
          {error}
        </div>
      )}

      {/* Success message */}
      {success && (
        <div
          className="mb-6 px-4 py-3 rounded"
          style={{
            backgroundColor: "var(--color-success-light)",
            color: "var(--color-success-dark)",
            border: "1px solid var(--color-success)",
          }}
        >
          {success}
        </div>
      )}

      {/* Event form */}
      <div
        className="rounded-lg p-6"
        style={{
          backgroundColor: "var(--color-bg-surface)",
          boxShadow: "var(--shadow-sm)",
        }}
      >
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Event name */}
            <div className="col-span-2">
              <label
                className="block text-sm font-medium mb-2"
                style={{ color: "var(--color-text-secondary)" }}
              >
                Event Name *
              </label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
                className="w-full rounded px-4 py-2 transition-colors"
                style={{
                  backgroundColor: "var(--color-bg-input)",
                  border: "1px solid var(--color-border-input)",
                  color: "var(--color-text-primary)",
                }}
                placeholder="Enter event name"
              />
            </div>

            {/* Short description */}
            <div className="col-span-2">
              <label
                className="block text-sm font-medium mb-2"
                style={{ color: "var(--color-text-secondary)" }}
              >
                Short Description
              </label>
              <input
                type="text"
                name="shortDescription"
                value={formData.shortDescription}
                onChange={handleInputChange}
                className="w-full rounded px-4 py-2 transition-colors"
                style={{
                  backgroundColor: "var(--color-bg-input)",
                  border: "1px solid var(--color-border-input)",
                  color: "var(--color-text-primary)",
                }}
                placeholder="Brief description (max 500 characters)"
                maxLength={500}
              />
              <p
                className="text-xs mt-1"
                style={{ color: "var(--color-text-secondary)" }}
              >
                {formData.shortDescription.length}/500 characters
              </p>
            </div>

            {/* Full description */}
            <div className="col-span-2">
              <label
                className="block text-sm font-medium mb-2"
                style={{ color: "var(--color-text-secondary)" }}
              >
                Full Description *
              </label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                required
                rows={6}
                className="w-full rounded px-4 py-2 transition-colors"
                style={{
                  backgroundColor: "var(--color-bg-input)",
                  border: "1px solid var(--color-border-input)",
                  color: "var(--color-text-primary)",
                  resize: "vertical",
                }}
                placeholder="Detailed event description"
              />
            </div>

            {/* Start date and time */}
            <div>
              <label
                className="block text-sm font-medium mb-2"
                style={{ color: "var(--color-text-secondary)" }}
              >
                Start Date *
              </label>
              <input
                type="date"
                name="startDate"
                value={formData.startDate}
                onChange={handleInputChange}
                required
                className="w-full rounded px-4 py-2 transition-colors"
                style={{
                  backgroundColor: "var(--color-bg-input)",
                  border: "1px solid var(--color-border-input)",
                  color: "var(--color-text-primary)",
                }}
              />
            </div>

            <div>
              <label
                className="block text-sm font-medium mb-2"
                style={{ color: "var(--color-text-secondary)" }}
              >
                Start Time *
              </label>
              <input
                type="time"
                name="startTime"
                value={formData.startTime}
                onChange={handleInputChange}
                required
                className="w-full rounded px-4 py-2 transition-colors"
                style={{
                  backgroundColor: "var(--color-bg-input)",
                  border: "1px solid var(--color-border-input)",
                  color: "var(--color-text-primary)",
                }}
              />
            </div>

            {/* End date and time */}
            <div>
              <label
                className="block text-sm font-medium mb-2"
                style={{ color: "var(--color-text-secondary)" }}
              >
                End Date *
              </label>
              <input
                type="date"
                name="endDate"
                value={formData.endDate}
                onChange={handleInputChange}
                required
                className="w-full rounded px-4 py-2 transition-colors"
                style={{
                  backgroundColor: "var(--color-bg-input)",
                  border: "1px solid var(--color-border-input)",
                  color: "var(--color-text-primary)",
                }}
              />
            </div>

            <div>
              <label
                className="block text-sm font-medium mb-2"
                style={{ color: "var(--color-text-secondary)" }}
              >
                End Time *
              </label>
              <input
                type="time"
                name="endTime"
                value={formData.endTime}
                onChange={handleInputChange}
                required
                className="w-full rounded px-4 py-2 transition-colors"
                style={{
                  backgroundColor: "var(--color-bg-input)",
                  border: "1px solid var(--color-border-input)",
                  color: "var(--color-text-primary)",
                }}
              />
            </div>

            {/* Virtual event toggle */}
            <div className="col-span-2">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isVirtual"
                  name="isVirtual"
                  checked={formData.isVirtual}
                  onChange={handleCheckboxChange}
                  className="h-4 w-4 rounded transition-colors"
                  style={{
                    borderColor: "var(--color-border-input)",
                    color: "var(--color-primary)",
                  }}
                />
                <label
                  htmlFor="isVirtual"
                  className="ml-2 text-sm"
                  style={{ color: "var(--color-text-primary)" }}
                >
                  This is a virtual event
                </label>
              </div>
            </div>

            {/* Location fields */}
            {!formData.isVirtual && (
              <>
                <div>
                  <label
                    className="block text-sm font-medium mb-2"
                    style={{ color: "var(--color-text-secondary)" }}
                  >
                    Location *
                  </label>
                  <input
                    type="text"
                    name="location"
                    value={formData.location}
                    onChange={handleInputChange}
                    className="w-full rounded px-4 py-2 transition-colors"
                    style={{
                      backgroundColor: "var(--color-bg-input)",
                      border: "1px solid var(--color-border-input)",
                      color: "var(--color-text-primary)",
                    }}
                    placeholder="Event venue or location name"
                  />
                </div>

                <div>
                  <label
                    className="block text-sm font-medium mb-2"
                    style={{ color: "var(--color-text-secondary)" }}
                  >
                    Address
                  </label>
                  <input
                    type="text"
                    name="address"
                    value={formData.address}
                    onChange={handleInputChange}
                    className="w-full rounded px-4 py-2 transition-colors"
                    style={{
                      backgroundColor: "var(--color-bg-input)",
                      border: "1px solid var(--color-border-input)",
                      color: "var(--color-text-primary)",
                    }}
                    placeholder="Full address"
                  />
                </div>
              </>
            )}

            {/* Virtual link */}
            {formData.isVirtual && (
              <div className="col-span-2">
                <label
                  className="block text-sm font-medium mb-2"
                  style={{ color: "var(--color-text-secondary)" }}
                >
                  Virtual Event Link
                </label>
                <input
                  type="url"
                  name="virtualLink"
                  value={formData.virtualLink}
                  onChange={handleInputChange}
                  className="w-full rounded px-4 py-2 transition-colors"
                  style={{
                    backgroundColor: "var(--color-bg-input)",
                    border: "1px solid var(--color-border-input)",
                    color: "var(--color-text-primary)",
                  }}
                  placeholder="Zoom, Teams, or other meeting link"
                />
              </div>
            )}

            {/* Event Image Upload */}
            <div className="col-span-2">
              <label
                className="block text-sm font-medium mb-2"
                style={{ color: "var(--color-text-secondary)" }}
              >
                Event Image
              </label>
              <div className="flex flex-col space-y-2">
                <ImageUploader
                  onImageUploaded={(imageUrl) => {
                    setFormData((prev) => ({ ...prev, image: imageUrl }));
                    setUploadError(null);
                  }}
                  onError={(error) => setUploadError(error)}
                  buttonText="Upload Event Image"
                  acceptedFormats="image/jpeg, image/png, image/webp"
                  maxSizeMB={5}
                />
                {uploadError && (
                  <p
                    className="text-sm"
                    style={{ color: "var(--color-error)" }}
                  >
                    {uploadError}
                  </p>
                )}
                {formData.image && (
                  <div className="mt-2">
                    <p
                      className="text-sm"
                      style={{ color: "var(--color-text-secondary)" }}
                    >
                      Image uploaded successfully
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Category */}
            <div>
              <label
                className="block text-sm font-medium mb-2"
                style={{ color: "var(--color-text-secondary)" }}
              >
                Category *
              </label>
              <select
                name="categoryId"
                value={formData.categoryId}
                onChange={handleInputChange}
                required
                className="w-full rounded px-4 py-2 transition-colors"
                style={{
                  backgroundColor: "var(--color-bg-input)",
                  border: "1px solid var(--color-border-input)",
                  color: "var(--color-text-primary)",
                }}
              >
                <option value="">Select a category</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Capacity */}
            <div>
              <label
                className="block text-sm font-medium mb-2"
                style={{ color: "var(--color-text-secondary)" }}
              >
                Capacity
              </label>
              <input
                type="number"
                name="capacity"
                value={formData.capacity}
                onChange={handleInputChange}
                min="1"
                className="w-full rounded px-4 py-2 transition-colors"
                style={{
                  backgroundColor: "var(--color-bg-input)",
                  border: "1px solid var(--color-border-input)",
                  color: "var(--color-text-primary)",
                }}
                placeholder="Maximum number of attendees"
              />
            </div>

            {/* Status */}
            <div>
              <label
                className="block text-sm font-medium mb-2"
                style={{ color: "var(--color-text-secondary)" }}
              >
                Status
              </label>
              <select
                name="status"
                value={formData.status}
                onChange={handleInputChange}
                className="w-full rounded px-4 py-2 transition-colors"
                style={{
                  backgroundColor: "var(--color-bg-input)",
                  border: "1px solid var(--color-border-input)",
                  color: "var(--color-text-primary)",
                }}
              >
                <option value="draft">Draft</option>
                <option value="published">Published</option>
              </select>
            </div>
          </div>

          {/* Submit button */}
          <div className="mt-8">
            <button
              type="submit"
              disabled={loading}
              className="w-full md:w-auto px-6 py-2 rounded font-medium transition-colors"
              style={{
                backgroundColor: loading
                  ? "var(--color-primary-light)"
                  : "var(--color-primary)",
                color: "var(--color-text-on-primary)",
                cursor: loading ? "not-allowed" : "pointer",
              }}
            >
              {loading ? "Creating Event..." : "Create Event"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
