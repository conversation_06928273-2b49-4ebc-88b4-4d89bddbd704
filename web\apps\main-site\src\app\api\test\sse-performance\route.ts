import { NextRequest, NextResponse } from "next/server";
import { connectionStore } from "@/lib/connectionStore";
import { getCurrentUser } from "@/lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';


/**
 * API endpoint for testing SSE performance
 * This endpoint sends a test message to the specified user's SSE connections
 */
export async function POST(req: NextRequest) {
  try {
    // Get the current user
    const user = await getCurrentUser(req);

    if (!user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    // Get the timestamp from the request
    const { timestamp } = await req.json();

    // Send a test message to the user's SSE connections
    const sentCount = await connectionStore.sendToUser(user.id, {
      type: "test_message",
      message: "This is a test message for performance measurement",
      sentTime: timestamp,
      timestamp: new Date().toISOString(),
    });

    return NextResponse.json({
      success: true,
      sentCount,
      userId: user.id,
    });
  } catch (error) {
    console.error("[SSE Performance Test] Error:", error);
    return NextResponse.json(
      { error: "Failed to send test message" },
      { status: 500 },
    );
  }
}
