"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { CaptainDashboardLayout } from "@/components/captain";
import { useCaptainShip } from "@/hooks/captain/useCaptainShip";
import { <PERSON><PERSON>, <PERSON> } from "@bank-of-styx/ui";
import MemberManagementTable from "@/components/ships/MemberManagementTable";
import { getCaptainRoles, updateMemberRole, removeMember, type Member, type ShipRole } from "@/services/captainService";

export default function CaptainMembersPage() {
  const { user, isLoading: authLoading, openAuthModal } = useAuth();
  const router = useRouter();
  
  const { ship, members: hookMembers, isLoading: shipLoading, refetch } = useCaptainShip();
  
  // Local state for members page  
  const [roles, setRoles] = useState<ShipRole[]>([]);
  const [rolesLoading, setRolesLoading] = useState(false);

  // Use hookMembers directly instead of local state
  const members = hookMembers || [];
  const membersLoading = shipLoading;

  // Authentication check
  React.useEffect(() => {
    if (!authLoading && !user) {
      openAuthModal();
      router.replace("/");
      return;
    }
  }, [user, authLoading, router, openAuthModal]);

  // Fetch roles when ship is available
  useEffect(() => {
    if (ship) {
      fetchRoles();
    }
  }, [ship]); // fetchRoles is stable function, safe to omit

  const fetchMembers = async () => {
    try {
      await refetch(); // Refetch the main dashboard data
    } catch (error) {
      console.error("Error fetching members:", error);
    }
  };

  const fetchRoles = async () => {
    try {
      setRolesLoading(true);
      const data = await getCaptainRoles();
      setRoles(data);
    } catch (error) {
      console.error("Error fetching roles:", error);
    } finally {
      setRolesLoading(false);
    }
  };

  const handleUpdateMemberRole = async (userId: string, roleId: string | null, roleName: string) => {
    try {
      await updateMemberRole(userId, roleId, roleName);
      fetchMembers(); // Refresh members list
    } catch (error) {
      console.error("Error updating member role:", error);
      throw error;
    }
  };

  const handleRemoveMember = async (userId: string, reason?: string) => {
    try {
      await removeMember(userId, reason);
      fetchMembers(); // Refresh members list
    } catch (error) {
      console.error("Error removing member:", error);
      throw error;
    }
  };

  // Loading state
  if (authLoading || shipLoading) {
    return (
      <div className="min-h-screen bg-secondary-dark flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-400">Loading...</p>
        </div>
      </div>
    );
  }

  if (!ship) {
    return (
      <CaptainDashboardLayout>
        <Card className="p-6 text-center">
          <p className="text-gray-400">Ship not found</p>
        </Card>
      </CaptainDashboardLayout>
    );
  }

  return (
    <CaptainDashboardLayout ship={ship}>
      <div className="space-y-3 sm:space-y-4">
        {/* Page Header with Compact Stats */}
        <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between">
          <div className="mb-3 sm:mb-0">
            <h1 className="text-xl sm:text-2xl font-bold text-white mb-1">
              Ship Members
            </h1>
            <p className="text-sm text-gray-400">
              Manage your crew members and their roles
            </p>
          </div>
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
            {/* Compact Stats in Single Row */}
            <div className="flex gap-2 sm:gap-3">
              <div className="bg-secondary-light rounded px-2 py-1 min-w-0">
                <p className="text-lg font-bold text-white leading-tight">{Array.isArray(members) ? members.length : 0}</p>
                <p className="text-xs text-gray-400">Total</p>
              </div>
              <div className="bg-secondary-light rounded px-2 py-1 min-w-0">
                <p className="text-lg font-bold text-white leading-tight">
                  {Array.isArray(members) ? members.filter(m => m.status === 'active').length : 0}
                </p>
                <p className="text-xs text-gray-400">Active</p>
              </div>
              <div className="bg-secondary-light rounded px-2 py-1 min-w-0">
                <p className="text-lg font-bold text-white leading-tight">
                  {Array.isArray(members) ? members.filter(m => m.role === 'Officer').length : 0}
                </p>
                <p className="text-xs text-gray-400">Officers</p>
              </div>
              <div className="bg-secondary-light rounded px-2 py-1 min-w-0">
                <p className="text-lg font-bold text-white leading-tight">
                  {Array.isArray(members) ? members.filter(m => m.customRole).length : 0}
                </p>
                <p className="text-xs text-gray-400">Custom</p>
              </div>
            </div>
            <Button 
              variant="outline" 
              size="sm"
              onClick={fetchMembers}
              disabled={membersLoading}
              className="whitespace-nowrap"
            >
              {membersLoading ? "Refreshing..." : "Refresh"}
            </Button>
          </div>
        </div>

        {/* Members Table */}
        <Card className="p-2 sm:p-2">
          <div className="overflow-x-auto -mx-1">
            <MemberManagementTable
              members={members}
              roles={roles}
              captainId={user?.id || ''}
              onUpdateMemberRole={handleUpdateMemberRole}
              onRemoveMember={handleRemoveMember}
              loading={membersLoading}
            />
          </div>
        </Card>
      </div>
    </CaptainDashboardLayout>
  );
}