"use client";

import { useEffect, useState } from "react";
import { useRouter, useParams } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { SalesDashboardLayout } from "@/components/sales/SalesDashboardLayout";
import { <PERSON>, <PERSON><PERSON>, Spinner } from "@bank-of-styx/ui";
import { Badge } from "@/components/shared";
import { RedemptionCodesSection } from "@/components/sales/RedemptionCodesSection";
import Link from "next/link";
import fetchClient from "@/lib/fetchClient";
// Using a simple arrow left symbol instead of heroicons
const ArrowLeftIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
  </svg>
);

interface ProductStatsData {
  product: {
    id: string;
    name: string;
    price: number;
    category: string;
    event: string | null;
    isFree: boolean;
    redemptionCodes?: {
      id: string;
      code: string;
      isActive: boolean;
      createdAt: string;
    }[];
  };
  stats: {
    totalQuantitySold: number;
    totalRevenue: number;
    totalOrders: number;
    averageOrderValue: number;
    uniqueCustomers: number;
  };
  customers: Array<{
    user: {
      id: string;
      username: string;
      email: string;
      displayName: string | null;
    };
    totalQuantity: number;
    totalSpent: number;
    orderCount: number;
    orders: Array<{
      orderId: string;
      orderNumber: string;
      quantity: number;
      price: number;
      total: number;
      date: string;
    }>;
  }>;
  monthlyChart: Array<{
    month: string;
    quantity: number;
    revenue: number;
  }>;
  recentSales: Array<{
    id: string;
    orderNumber: string;
    customer: {
      id: string;
      username: string;
      displayName: string | null;
    };
    quantity: number;
    price: number;
    total: number;
    date: string;
  }>;
}

export default function ProductStatsPage() {
  const { user, isLoading: authLoading } = useAuth();
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [data, setData] = useState<ProductStatsData | null>(null);
  const [productData, setProductData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const params = useParams();
  const productId = params.id as string;

  // Check authorization
  useEffect(() => {
    if (!authLoading) {
      if (!user || !user.roles?.salesManager) {
        router.push("/");
      } else {
        setIsAuthorized(true);
      }
    }
  }, [user, authLoading, router]);

  // Fetch product stats
  useEffect(() => {
    if (isAuthorized && productId) {
      fetchProductStats();
    }
  }, [isAuthorized, productId]);

  const fetchProductStats = async () => {
    try {
      setIsLoading(true);
      const [statsResult, productResult] = await Promise.all([
        fetchClient.get<ProductStatsData>(`/api/sales/products/${productId}/stats`),
        fetchClient.get<{ product: any }>(`/api/sales/products/${productId}`)
      ]);
      setData(statsResult);
      setProductData(productResult.product);
    } catch (error) {
      console.error("Error fetching product data:", error);
      setError(error instanceof Error ? error.message : "Unknown error");
    } finally {
      setIsLoading(false);
    }
  };

  if (authLoading || !isAuthorized) {
    return <div className="p-8 text-center">Loading...</div>;
  }

  if (isLoading) {
    return (
      <SalesDashboardLayout>
        <div className="flex justify-center p-8">
          <Spinner size="lg" />
        </div>
      </SalesDashboardLayout>
    );
  }

  if (error || !data) {
    return (
      <SalesDashboardLayout>
        <div className="p-6 text-center">
          <p className="text-accent mb-4">Error loading product stats</p>
          <Button onClick={fetchProductStats} variant="secondary">
            Try Again
          </Button>
        </div>
      </SalesDashboardLayout>
    );
  }

  const formatCurrency = (amount: number) => `$${amount.toFixed(2)}`;
  const formatDate = (dateString: string) => new Date(dateString).toLocaleDateString();

  return (
    <SalesDashboardLayout>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <Link href="/sales/products">
            <Button variant="secondary" size="sm">
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back to Products
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold">{data.product.name} - Sales Stats</h1>
            <p className="text-text-muted">
              {data.product.category} • {formatCurrency(data.product.price)}
              {data.product.event && ` • Event: ${data.product.event}`}
            </p>
          </div>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
        <Card className="p-6">
          <div className="text-2xl font-bold text-primary">
            {data.stats.totalQuantitySold}
          </div>
          <div className="text-sm text-text-muted">Items Sold</div>
        </Card>
        
        <Card className="p-6">
          <div className="text-2xl font-bold text-success">
            {formatCurrency(data.stats.totalRevenue)}
          </div>
          <div className="text-sm text-text-muted">Total Revenue</div>
        </Card>
        
        <Card className="p-6">
          <div className="text-2xl font-bold text-accent">
            {data.stats.totalOrders}
          </div>
          <div className="text-sm text-text-muted">Total Orders</div>
        </Card>
        
        <Card className="p-6">
          <div className="text-2xl font-bold text-warning">
            {formatCurrency(data.stats.averageOrderValue)}
          </div>
          <div className="text-sm text-text-muted">Avg Order Value</div>
        </Card>
        
        <Card className="p-6">
          <div className="text-2xl font-bold text-info">
            {data.stats.uniqueCustomers}
          </div>
          <div className="text-sm text-text-muted">Unique Customers</div>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Top Customers */}
        <Card>
          <div className="p-6 border-b border-border-subtle">
            <h3 className="text-lg font-semibold">Top Customers</h3>
          </div>
          <div className="p-6">
            {data.customers.length === 0 ? (
              <p className="text-text-muted text-center py-4">No customers yet</p>
            ) : (
              <div className="space-y-4">
                {data.customers.slice(0, 10).map((customer, index) => (
                  <div key={customer.user.id} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center text-white text-sm font-semibold">
                        {index + 1}
                      </div>
                      <div>
                        <div className="font-medium">
                          {customer.user.displayName || customer.user.username}
                        </div>
                        <div className="text-sm text-text-muted">
                          {customer.orderCount} orders • {customer.totalQuantity} items
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold text-success">
                        {formatCurrency(customer.totalSpent)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </Card>

        {/* Recent Sales */}
        <Card>
          <div className="p-6 border-b border-border-subtle">
            <h3 className="text-lg font-semibold">Recent Sales</h3>
          </div>
          <div className="p-6">
            {data.recentSales.length === 0 ? (
              <p className="text-text-muted text-center py-4">No sales yet</p>
            ) : (
              <div className="space-y-4">
                {data.recentSales.map((sale) => (
                  <div key={sale.id} className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">
                        {sale.customer.displayName || sale.customer.username}
                      </div>
                      <div className="text-sm text-text-muted">
                        Order #{sale.orderNumber} • {formatDate(sale.date)}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold">
                        {sale.quantity} × {formatCurrency(sale.price)}
                      </div>
                      <div className="text-sm text-success">
                        {formatCurrency(sale.total)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </Card>
      </div>

      {/* All Customers Table */}
      <Card className="mt-8">
        <div className="p-6 border-b border-border-subtle">
          <h3 className="text-lg font-semibold">All Customers</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-secondary-dark">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-text-muted uppercase tracking-wider">
                  Customer
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-text-muted uppercase tracking-wider">
                  Email
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-text-muted uppercase tracking-wider">
                  Orders
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-text-muted uppercase tracking-wider">
                  Quantity
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-text-muted uppercase tracking-wider">
                  Total Spent
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-border-subtle">
              {data.customers.map((customer) => (
                <tr key={customer.user.id} className="hover:bg-secondary-light">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="font-medium">
                      {customer.user.displayName || customer.user.username}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-text-muted">
                    {customer.user.email}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    <Badge variant="secondary">{customer.orderCount}</Badge>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    {customer.totalQuantity}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right font-semibold text-success">
                    {formatCurrency(customer.totalSpent)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>

      {/* Redemption Codes Section */}
      {productData && (
        <RedemptionCodesSection
          productId={productId}
          productName={data.product.name}
          isFree={productData.isFree || false}
          redemptionCodes={productData.redemptionCodes || []}
          productInventory={productData.inventory}
          onRefresh={fetchProductStats}
        />
      )}
    </SalesDashboardLayout>
  );
}