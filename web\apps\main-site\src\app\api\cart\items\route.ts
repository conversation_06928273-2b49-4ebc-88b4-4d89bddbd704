import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth";
import { getAvailableTicketCount } from "@/lib/ticket-system";
import { 
  isProductEventCapacityBased, 
  isEventCapacityAvailable, 
  createEventCapacityHold 
} from "@/lib/event-capacity-system";
import { TicketStatus } from "@prisma/client";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// POST /api/cart/items - Add item to cart
export async function POST(req: NextRequest) {
  try {
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    const { productId, quantity } = await req.json();

    if (!productId || !quantity || quantity < 1) {
      return NextResponse.json(
        { error: "Invalid product or quantity" },
        { status: 400 },
      );
    }

    // Start a transaction to ensure consistency
    const result = await prisma.$transaction(async (tx) => {
      // Check if product exists and is active
      const product = await tx.product.findFirst({
        where: {
          id: productId,
          isActive: true,
        },
        include: {
          event: {
            select: {
              id: true,
              name: true,
              capacity: true
            }
          }
        }
      });

      if (!product) {
        throw new Error("Product not found or inactive");
      }

      // Check if this product uses event capacity system
      const eventCapacityInfo = await isProductEventCapacityBased(productId, tx);
      
      if (eventCapacityInfo.isEventBased && eventCapacityInfo.eventId) {
        // Use event capacity system
        const hasCapacity = await isEventCapacityAvailable(eventCapacityInfo.eventId, quantity, tx);
        if (!hasCapacity) {
          throw new Error("Not enough event capacity available");
        }
      } else {
        // Use traditional individual ticket system
        const availableTickets = await getAvailableTicketCount(productId);
        if (availableTickets < quantity) {
          throw new Error("Not enough tickets available");
        }
      }

      // Find or create cart
      let cart = await tx.cart.findFirst({
        where: { userId: user.id },
      });

      if (!cart) {
        cart = await tx.cart.create({
          data: { userId: user.id },
        });
      }

      // Check if item already exists in cart
      const existingItem = await tx.cartItem.findFirst({
        where: {
          cartId: cart.id,
          productId,
        },
        include: {
          ticketHold: true,
          eventCapacityHold: true,
        },
      });

      if (existingItem) {
        // Update existing item quantity
        const updatedItem = await tx.cartItem.update({
          where: { id: existingItem.id },
          data: {
            quantity: existingItem.quantity + quantity,
            updatedAt: new Date(),
          },
          include: {
            product: true,
          },
        });

        if (eventCapacityInfo.isEventBased && eventCapacityInfo.eventId) {
          // Create event capacity hold for additional quantity
          const hold = await createEventCapacityHold(
            user.id,
            eventCapacityInfo.eventId,
            updatedItem.id,
            quantity,
            tx // Pass transaction context
          );

          return {
            item: updatedItem,
            holdType: "event",
            eventId: eventCapacityInfo.eventId,
            ticketCount: quantity,
            holdExpiresAt: hold.expiresAt,
          };
        } else {
          // Use traditional ticket hold system
          const availableTickets = await tx.ticket.findMany({
            where: {
              productId,
              status: TicketStatus.AVAILABLE,
            },
            take: quantity,
          });

          if (availableTickets.length < quantity) {
            throw new Error("Not enough tickets available");
          }

          // Create the hold
          const hold = await tx.ticketHold.create({
            data: {
              userId: user.id,
              cartItemId: updatedItem.id,
              expiresAt: new Date(Date.now() + 15 * 60 * 1000), // 15 minutes
            },
          });

          // Update tickets to mark them as held
          await tx.ticket.updateMany({
            where: {
              id: { in: availableTickets.map((ticket) => ticket.id) },
            },
            data: {
              status: TicketStatus.HELD,
              holdId: hold.id,
            },
          });

          return {
            item: updatedItem,
            holdType: "individual",
            ticketCount: availableTickets.length,
            holdExpiresAt: hold.expiresAt,
          };
        }
      } else {
        // Create new cart item
        const cartItem = await tx.cartItem.create({
          data: {
            cartId: cart.id,
            productId,
            quantity,
          },
          include: {
            product: true,
          },
        });

        if (eventCapacityInfo.isEventBased && eventCapacityInfo.eventId) {
          // Create event capacity hold
          const hold = await createEventCapacityHold(
            user.id,
            eventCapacityInfo.eventId,
            cartItem.id,
            quantity,
            tx // Pass transaction context
          );

          return {
            item: cartItem,
            holdType: "event",
            eventId: eventCapacityInfo.eventId,
            ticketCount: quantity,
            holdExpiresAt: hold.expiresAt,
          };
        } else {
          // Use traditional ticket hold system
          const availableTickets = await tx.ticket.findMany({
            where: {
              productId,
              status: TicketStatus.AVAILABLE,
            },
            take: quantity,
          });

          if (availableTickets.length < quantity) {
            throw new Error("Not enough tickets available");
          }

          // Create the hold
          const hold = await tx.ticketHold.create({
            data: {
              userId: user.id,
              cartItemId: cartItem.id,
              expiresAt: new Date(Date.now() + 15 * 60 * 1000), // 15 minutes
            },
          });

          // Update tickets to mark them as held
          await tx.ticket.updateMany({
            where: {
              id: { in: availableTickets.map((ticket) => ticket.id) },
            },
            data: {
              status: TicketStatus.HELD,
              holdId: hold.id,
            },
          });

          return {
            item: cartItem,
            holdType: "individual",
            ticketCount: availableTickets.length,
            holdExpiresAt: hold.expiresAt,
          };
        }
      }
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error("Error adding item to cart:", error);
    return NextResponse.json(
      { error: "Failed to add item to cart" },
      { status: 500 },
    );
  }
}
