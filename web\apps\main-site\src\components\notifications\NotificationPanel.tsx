"use client";

import React, { useRef, useEffect } from "react";
import { useNotifications } from "../../contexts/NotificationContext";
import NotificationItem from "./NotificationItem";
import { formatDistanceToNow } from "date-fns";

export const NotificationPanel: React.FC = () => {
  const {
    notifications,
    unreadCount,
    isLoading,
    markAllAsRead,
    isNotificationPanelOpen,
    closeNotificationPanel,
  } = useNotifications();

  const panelRef = useRef<HTMLDivElement>(null);

  // <PERSON>le click outside to close panel
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        panelRef.current &&
        !panelRef.current.contains(event.target as Node)
      ) {
        closeNotificationPanel();
      }
    };

    if (isNotificationPanelOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isNotificationPanelOpen, closeNotificationPanel]);

  // Group notifications by date
  const groupedNotifications = notifications.reduce(
    (groups, notification) => {
      const date = new Date(notification.createdAt).toLocaleDateString();
      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(notification);
      return groups;
    },
    {} as Record<string, typeof notifications>,
  );

  if (!isNotificationPanelOpen) {
    return null;
  }

  return (
    <div className="fixed inset-0 z-50 overflow-hidden flex justify-end">
      <div
        className="absolute inset-0 bg-black bg-opacity-50"
        onClick={closeNotificationPanel}
      ></div>
      <div
        ref={panelRef}
        className="relative w-full max-w-md bg-secondary-dark shadow-xl flex flex-col h-full"
      >
        <div className="p-4 border-b border-gray-600 flex justify-between items-center">
          <h2 className="text-xl font-bold text-white">Notifications</h2>
          <div className="flex items-center space-x-2">
            {unreadCount > 0 && (
              <button
                onClick={() => markAllAsRead()}
                className="text-sm text-primary hover:text-primary-light"
              >
                Mark all as read
              </button>
            )}
            <button
              onClick={closeNotificationPanel}
              className="text-gray-400 hover:text-white"
              aria-label="Close notifications"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        </div>

        <div className="flex-1 overflow-y-auto p-4">
          {isLoading ? (
            <div className="flex justify-center items-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
            </div>
          ) : notifications.length === 0 ? (
            <div className="text-center py-8">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-12 w-12 mx-auto text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
                />
              </svg>
              <p className="mt-2 text-gray-400">No notifications yet</p>
            </div>
          ) : (
            <div className="space-y-6">
              {Object.entries(groupedNotifications).map(([date, items]) => (
                <div key={date}>
                  <h3 className="text-sm font-medium text-gray-400 mb-2">
                    {new Date(date).toLocaleDateString() ===
                    new Date().toLocaleDateString()
                      ? "Today"
                      : new Date(date).toLocaleDateString() ===
                        new Date(Date.now() - 86400000).toLocaleDateString()
                      ? "Yesterday"
                      : date}
                  </h3>
                  <div className="space-y-2">
                    {items.map((notification) => (
                      <NotificationItem
                        key={notification.id}
                        notification={notification}
                        timeAgo={formatDistanceToNow(
                          new Date(notification.createdAt),
                          { addSuffix: true },
                        )}
                      />
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="p-4 border-t border-gray-600">
          <a
            href="/settings/notifications"
            className="block text-center text-primary hover:text-primary-light"
            onClick={closeNotificationPanel}
          >
            Notification Settings
          </a>
        </div>
      </div>
    </div>
  );
};

export default NotificationPanel;
