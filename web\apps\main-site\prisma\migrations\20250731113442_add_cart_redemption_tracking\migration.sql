-- AlterTable
ALTER TABLE `cart_items` ADD COLUMN `isCodeRedemption` BOOLEAN NOT NULL DEFAULT false,
    ADD COLUMN `redemptionCodeId` VARCHAR(191) NULL;

-- CreateIndex
CREATE INDEX `cart_items_redemptionCodeId_idx` ON `cart_items`(`redemptionCodeId`);

-- AddForeignKey
ALTER TABLE `cart_items` ADD CONSTRAINT `cart_items_redemptionCodeId_fkey` FOREIGN KEY (`redemptionCodeId`) REFERENCES `redemption_codes`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
