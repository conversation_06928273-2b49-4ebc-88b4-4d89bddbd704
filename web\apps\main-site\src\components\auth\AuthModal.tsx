"use client";

import { useState, useEffect } from "react";
import { Modal, Input, Button } from "@bank-of-styx/ui";
import { useAuth } from "../../contexts/AuthContext";
import "./AuthModal.css";

export const AuthModal = () => {
  const {
    isAuthModalOpen,
    closeAuthModal,
    login,
    register,
    loginWithDiscord,
    isLoading,
  } = useAuth();

  const [isLogin, setIsLogin] = useState(true);
  const [email, setEmail] = useState("");
  const [confirmEmail, setConfirmEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [username, setUsername] = useState("");
  const [error, setError] = useState("");
  const [fieldErrors, setFieldErrors] = useState<{
    email?: string;
    confirmEmail?: string;
    password?: string;
    confirmPassword?: string;
  }>({});

  const resetForm = () => {
    setEmail("");
    setConfirmEmail("");
    setPassword("");
    setConfirmPassword("");
    setUsername("");
    setError("");
    setFieldErrors({});
  };

  const handleClose = () => {
    closeAuthModal();
    // Reset form when modal is closed
    resetForm();
  };

  // Validate form fields for registration
  const validateRegistrationForm = () => {
    const errors: {
      email?: string;
      confirmEmail?: string;
      password?: string;
      confirmPassword?: string;
    } = {};

    // Email validation
    if (email !== confirmEmail) {
      errors.confirmEmail = "Emails do not match";
    }

    // Password validation
    if (password !== confirmPassword) {
      errors.confirmPassword = "Passwords do not match";
    }

    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setFieldErrors({});

    try {
      if (isLogin) {
        await login(email, password);
      } else {
        if (!username) {
          setError("Username is required");
          return;
        }

        // Validate registration form fields
        if (!validateRegistrationForm()) {
          return;
        }

        await register(username, email, password);
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "An error occurred";

      // Customize error message for username/email already exists
      if (errorMessage.includes("already exists")) {
        setError(
          "Username or email is unavailable. If problems persist, please head to the help page and contact admins for assistance.",
        );
      } else {
        setError(errorMessage);
      }
    }
  };

  const handleDiscordLogin = async () => {
    try {
      await loginWithDiscord();
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to connect to Discord",
      );
    }
  };

  const toggleMode = () => {
    setIsLogin(!isLogin);
    setError("");
    setFieldErrors({});
  };

  // Add a useEffect to apply styles directly to the modal elements
  useEffect(() => {
    if (isAuthModalOpen) {
      // Apply styles to modal elements that we can't directly target with our component
      setTimeout(() => {
        // Get the modal container
        const modalContainer = document.querySelector('[role="dialog"]');
        if (modalContainer) {
          modalContainer.classList.add("auth-modal-container");
          modalContainer.classList.remove("bg-white");
          modalContainer.classList.add("bg-secondary-light");
        }

        // Get the modal header
        const modalHeader = document.querySelector(
          '[role="dialog"] > div:first-child',
        );
        if (modalHeader) {
          modalHeader.classList.add("auth-modal-header");
        }

        // Get all input labels
        const inputLabels = document.querySelectorAll('[role="dialog"] label');
        inputLabels.forEach((label) => {
          label.classList.add("text-text-primary");
        });

        // Get all inputs
        const inputs = document.querySelectorAll('[role="dialog"] input');
        inputs.forEach((input) => {
          input.classList.add(
            "bg-secondary",
            "text-text-primary",
            "border-gray-600",
          );
          input.classList.remove("border-gray-300");
        });
      }, 0);
    }
  }, [isAuthModalOpen]);

  return (
    <Modal
      isOpen={isAuthModalOpen}
      onClose={handleClose}
      title={isLogin ? "Sign In" : "Create Account"}
      size="md"
      footer={null} // Explicitly provide a footer prop as required
    >
      <div className="py-2 bg-secondary-light">
        {error && (
          <div className="mb-4 p-3 text-sm rounded-lg bg-error bg-opacity-20 text-white border border-error">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit}>
          {!isLogin && (
            <div className="mb-4">
              <Input
                label="Username"
                type="text"
                required
                fullWidth
                value={username}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setUsername(e.target.value)
                }
                placeholder="Your username"
              />
            </div>
          )}

          <div className="mb-4">
            <Input
              label="Email"
              type="email"
              required
              fullWidth
              value={email}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                setEmail(e.target.value)
              }
              placeholder="<EMAIL>"
              error={fieldErrors.email}
            />
          </div>

          {!isLogin && (
            <div className="mb-4">
              <Input
                label="Confirm Email"
                type="email"
                required
                fullWidth
                value={confirmEmail}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setConfirmEmail(e.target.value)
                }
                placeholder="Confirm your email"
                error={fieldErrors.confirmEmail}
              />
            </div>
          )}

          <div className="mb-4">
            <Input
              label="Password"
              type="password"
              required
              fullWidth
              value={password}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                setPassword(e.target.value)
              }
              placeholder="********"
              error={fieldErrors.password}
            />
          </div>

          {!isLogin && (
            <div className="mb-6">
              <Input
                label="Confirm Password"
                type="password"
                required
                fullWidth
                value={confirmPassword}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setConfirmPassword(e.target.value)
                }
                placeholder="Confirm your password"
                error={fieldErrors.confirmPassword}
              />
            </div>
          )}

          <Button type="submit" variant="primary" fullWidth loading={isLoading}>
            {isLogin ? "Sign In" : "Create Account"}
          </Button>

          <div className="text-center my-4 text-text-secondary flex items-center justify-center auth-modal-divider">
            <div className="h-px bg-gray-600 flex-grow mr-4"></div>
            <span>or</span>
            <div className="h-px bg-gray-600 flex-grow ml-4"></div>
          </div>

          <Button
            type="button"
            variant="outline"
            fullWidth
            onClick={handleDiscordLogin}
            loading={isLoading}
            className="bg-primary bg-opacity-10 hover:bg-opacity-20 border-primary auth-discord-button"
          >
            <svg
              className="h-5 w-5 mr-2 text-primary-light"
              viewBox="0 0 24 24"
              fill="currentColor"
            >
              <path d="M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0 12.64 12.64 0 0 0-.617-1.25.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057 19.9 19.9 0 0 0 5.993 3.03.078.078 0 0 0 .084-.028c.462-.63.874-1.295 1.226-1.994a.076.076 0 0 0-.041-.106 13.107 13.107 0 0 1-1.872-.892.077.077 0 0 1-.008-.128 10.2 10.2 0 0 0 .372-.292.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.12.098.246.198.373.292a.077.077 0 0 1-.006.127 12.299 12.299 0 0 1-1.873.892.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028 19.839 19.839 0 0 0 6.002-3.03.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.956-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.955-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.946 2.418-2.157 2.418z" />
            </svg>
            <span className="text-primary-light">Continue with Discord</span>
          </Button>

          <p className="mt-6 text-sm text-center text-text-secondary auth-toggle-text">
            {isLogin ? (
              <>
                Don't have an account?{" "}
                <button
                  type="button"
                  className="text-primary hover:text-primary-light transition-colors auth-toggle-button"
                  onClick={toggleMode}
                >
                  Create one
                </button>
              </>
            ) : (
              <>
                Already have an account?{" "}
                <button
                  type="button"
                  className="text-primary hover:text-primary-light transition-colors auth-toggle-button"
                  onClick={toggleMode}
                >
                  Sign in
                </button>
              </>
            )}
          </p>
        </form>
      </div>
    </Modal>
  );
};
