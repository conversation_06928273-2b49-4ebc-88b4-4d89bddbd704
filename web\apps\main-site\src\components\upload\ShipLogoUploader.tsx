import React, { useState, useRef } from "react";
import { uploadConfig } from "@/lib/uploadConfig";

interface ShipLogoUploaderProps {
  onFileSelected: (file: File | null) => void;
  currentLogoUrl?: string;
  className?: string;
  selectedFile?: File | null;
  error?: string;
}

const ShipLogoUploader: React.FC<ShipLogoUploaderProps> = ({
  onFileSelected,
  currentLogoUrl,
  className = "",
  selectedFile,
  error,
}) => {
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    
    // Create preview URL for selected file
    if (file) {
      // Clean up previous preview URL
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
    } else {
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
      setPreviewUrl(null);
    }
    
    // Notify parent component
    onFileSelected(file);
  };

  // Clean up preview URL on unmount
  React.useEffect(() => {
    return () => {
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [previewUrl]);

  const displayUrl = previewUrl || currentLogoUrl;

  return (
    <div className={className}>
      <div className="space-y-3">
        {/* Preview Area */}
        {displayUrl && (
          <div className="bg-secondary-dark p-4 rounded-lg">
            <div className="flex items-center gap-4">
              <img
                src={displayUrl}
                alt="Ship logo preview"
                className="w-16 h-16 object-contain rounded border border-gray-600"
              />
              <div>
                <p className="text-white text-sm font-medium">
                  {previewUrl ? "Selected Logo" : "Current Logo"}
                </p>
                {selectedFile && (
                  <p className="text-gray-400 text-xs">
                    {selectedFile.name} ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
                  </p>
                )}
              </div>
            </div>
          </div>
        )}

        {/* File Input */}
        <div>
          <input
            ref={fileInputRef}
            type="file"
            accept={uploadConfig["ship-logo"].allowedTypes.join(",")}
            onChange={handleFileChange}
            className="block w-full text-gray-400 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-white hover:file:bg-primary-dark"
          />
          <p className="text-gray-400 text-sm mt-2">
            Upload a logo for your ship. Accepted formats: JPEG, PNG, GIF. Max size: 5MB.
            Recommended: Square aspect ratio for best results.
          </p>
        </div>

        {/* Selected File Info */}
        {selectedFile && (
          <div className="bg-secondary-dark p-3 rounded-md">
            <p className="text-white text-sm">
              Selected: {selectedFile.name} ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
            </p>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <p className="text-red-400 text-sm">{error}</p>
        )}
      </div>
    </div>
  );
};

export default ShipLogoUploader;