import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth";
import prisma from "@/lib/prisma";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const formId = params.id;

    // Get the user's ship
    const shipMember = await prisma.shipMember.findFirst({
      where: {
        userId: user.id,
        status: "active",
      },
      include: {
        ship: {
          select: {
            id: true,
            name: true,
            captainId: true,
          },
        },
      },
    });

    if (!shipMember) {
      return NextResponse.json(
        { error: "You must be a member of a ship to access forms" },
        { status: 403 }
      );
    }

    // Check if user is the captain
    const isCaptain = shipMember.ship.captainId === user.id;
    
    if (!isCaptain) {
      return NextResponse.json(
        { error: "Only ship captains can access forms" },
        { status: 403 }
      );
    }

    // Get the form
    const form = await prisma.eventForm.findUnique({
      where: { id: formId },
      include: {
        event: {
          select: {
            id: true,
            name: true,
            startDate: true,
            endDate: true,
          },
        },
        submissions: {
          where: {
            shipId: shipMember.ship.id,
          },
          orderBy: {
            submittedAt: "desc",
          },
          take: 1,
        },
      },
    });

    if (!form) {
      return NextResponse.json(
        { error: "Form not found" },
        { status: 404 }
      );
    }

    // Check if form is available (active and deadline not passed)
    const now = new Date();
    const isAvailable = form.status === "active" && 
      (!form.submissionDeadline || form.submissionDeadline >= now);

    return NextResponse.json({
      ...form,
      ship: shipMember.ship,
      isAvailable,
      existingSubmission: form.submissions[0] || null,
    });
  } catch (error) {
    console.error("Error fetching form:", error);
    return NextResponse.json(
      { error: "Failed to fetch form" },
      { status: 500 }
    );
  }
}