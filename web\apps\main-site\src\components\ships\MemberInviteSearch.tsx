"use client";

import { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import { Button, Modal } from '@bank-of-styx/ui';

interface User {
  id: string;
  username: string;
  displayName: string;
  avatar: string;
  email: string;
  currentShip?: {
    id: string;
    name: string;
  } | null;
  isAvailable: boolean;
}

interface ShipRole {
  id: string;
  name: string;
  description?: string;
}

interface MemberInviteSearchProps {
  roles: ShipRole[];
  onInviteUser: (userId: string, message?: string, roleId?: string) => Promise<void>;
  loading?: boolean;
}

export default function MemberInviteSearch({
  roles,
  onInviteUser,
  loading = false
}: MemberInviteSearchProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<User[]>([]);
  const [searchLoading, setSearchLoading] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [inviteMessage, setInviteMessage] = useState('');
  const [selectedRoleId, setSelectedRoleId] = useState<string>('');
  const [inviteLoading, setInviteLoading] = useState(false);
  
  const searchTimeoutRef = useRef<NodeJS.Timeout>();

  const searchUsers = async (query: string) => {
    if (query.trim().length < 2) {
      setSearchResults([]);
      return;
    }

    setSearchLoading(true);
    try {
      const response = await fetch(`/api/users/search?q=${encodeURIComponent(query)}&limit=10`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem("auth_token")}`
        }
      });

      if (response.ok) {
        const users = await response.json();
        setSearchResults(users || []);
      } else {
        console.error('Error searching users:', response.statusText);
        setSearchResults([]);
      }
    } catch (error) {
      console.error('Error searching users:', error);
      setSearchResults([]);
    } finally {
      setSearchLoading(false);
    }
  };

  useEffect(() => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    searchTimeoutRef.current = setTimeout(() => {
      searchUsers(searchQuery);
    }, 300);

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [searchQuery]);

  const handleInviteUser = async () => {
    if (!selectedUser) return;

    setInviteLoading(true);
    try {
      await onInviteUser(
        selectedUser.id, 
        inviteMessage.trim() || undefined,
        selectedRoleId || undefined
      );
      
      // Reset form and close modal
      setSelectedUser(null);
      setInviteMessage('');
      setSelectedRoleId('');
      setIsModalOpen(false);
      setSearchQuery('');
      setSearchResults([]);
    } catch (error) {
      console.error('Error inviting user:', error);
    } finally {
      setInviteLoading(false);
    }
  };

  const openInviteModal = (user: User) => {
    setSelectedUser(user);
    setInviteMessage(`You've been invited to join our ship!`);
    setSelectedRoleId('');
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedUser(null);
    setInviteMessage('');
    setSelectedRoleId('');
  };

  return (
    <>
      <div className="bg-secondary rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-600">
          <h3 className="text-lg font-semibold text-white">Invite New Members</h3>
          <p className="text-sm text-gray-400 mt-1">
            Search for users to invite to your ship
          </p>
        </div>
        
        <div className="p-6">
          <div className="relative">
            <input
              type="text"
              placeholder="Search by username or display name..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full bg-input border border-secondary-dark rounded-md px-4 py-2 pr-10 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary"
              disabled={loading}
            />
            {searchLoading && (
              <div className="absolute right-3 top-2.5">
                <div className="animate-spin h-5 w-5 border-2 border-primary border-t-transparent rounded-full"></div>
              </div>
            )}
          </div>

          {searchQuery.trim().length > 0 && searchQuery.trim().length < 2 && (
            <p className="text-sm text-gray-400 mt-2">
              Type at least 2 characters to search
            </p>
          )}

          {searchResults.length > 0 && (
            <div className="mt-4 space-y-2 max-h-96 overflow-y-auto">
              {searchResults.map((user) => (
                <div
                  key={user.id}
                  className="flex items-center justify-between p-3 border border-gray-600 rounded-lg hover:bg-secondary-light"
                >
                  <div className="flex items-center space-x-3">
                    <Image
                      src={user.avatar}
                      alt={user.displayName}
                      width={40}
                      height={40}
                      className="w-10 h-10 rounded-full object-cover"
                    />
                    <div>
                      <div className="font-medium text-white">
                        {user.displayName}
                      </div>
                      <div className="text-sm text-gray-400">
                        @{user.username}
                      </div>
                      {user.currentShip && (
                        <div className="text-xs text-orange-600">
                          Member of {user.currentShip.name}
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {!user.isAvailable && (
                      <span className="text-xs text-orange-600 font-medium">
                        Has Ship
                      </span>
                    )}
                    <Button
                      size="sm"
                      onClick={() => openInviteModal(user)}
                      disabled={loading}
                      variant={user.isAvailable ? "primary" : "outline"}
                    >
                      {user.isAvailable ? 'Invite' : 'Invite Anyway'}
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}

          {searchQuery.trim().length >= 2 && searchResults.length === 0 && !searchLoading && (
            <div className="text-center py-8 text-gray-400">
              <p className="text-white">No users found matching your search.</p>
              <p className="text-sm mt-1">Try a different search term.</p>
            </div>
          )}
        </div>
      </div>

      {/* Invite Modal */}
      <Modal
        isOpen={isModalOpen}
        onClose={closeModal}
        size="md"
      >
        <div className="p-6">
          <h3 className="text-lg font-semibold text-white mb-4">
            Invite {selectedUser?.displayName} to Ship
          </h3>
          
          {selectedUser && (
            <div className="mb-6">
              <div className="flex items-center space-x-3 p-3 bg-secondary-dark rounded-lg">
                <Image
                  src={selectedUser.avatar}
                  alt={selectedUser.displayName}
                  width={40}
                  height={40}
                  className="w-10 h-10 rounded-full object-cover"
                />
                <div>
                  <div className="font-medium text-white">
                    {selectedUser.displayName}
                  </div>
                  <div className="text-sm text-gray-400">
                    @{selectedUser.username}
                  </div>
                  {selectedUser.currentShip && (
                    <div className="text-xs text-orange-600">
                      Currently member of {selectedUser.currentShip.name}
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Invitation Message
              </label>
              <textarea
                value={inviteMessage}
                onChange={(e) => setInviteMessage(e.target.value)}
                className="w-full bg-input border border-secondary-dark rounded-md px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary"
                placeholder="Add a personal message to your invitation..."
                rows={3}
                maxLength={500}
              />
            </div>

            {roles.length > 0 && (
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Assign Role (Optional)
                </label>
                <select
                  value={selectedRoleId}
                  onChange={(e) => setSelectedRoleId(e.target.value)}
                  className="w-full bg-input border border-secondary-dark rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-primary"
                >
                  <option value="">Member (Default Role)</option>
                  {roles.map((role) => (
                    <option key={role.id} value={role.id}>
                      {role.name}
                    </option>
                  ))}
                </select>
              </div>
            )}
          </div>

          <div className="flex justify-end space-x-3 mt-6">
            <Button
              variant="outline"
              onClick={closeModal}
              disabled={inviteLoading}
            >
              Cancel
            </Button>
            <Button
              onClick={handleInviteUser}
              disabled={inviteLoading || !selectedUser}
            >
              {inviteLoading ? 'Sending Invite...' : 'Send Invitation'}
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
}