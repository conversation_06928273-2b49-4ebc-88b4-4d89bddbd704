import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth";

export const dynamic = 'force-dynamic';

export async function GET(request: Request) {
  try {
    const currentUser = await getCurrentUser(request);

    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Check if user is a captain
    const captainedShip = await prisma.ship.findFirst({
      where: {
        captainId: currentUser.id,
        status: 'active',
      },
      select: {
        id: true,
        name: true,
        logo: true,
        _count: {
          select: { members: true },
        },
      },
    });

    // Check if user is a member of any ship
    const membership = await prisma.shipMember.findFirst({
      where: {
        userId: currentUser.id,
        status: 'active',
      },
      include: {
        ship: {
          select: {
            id: true,
            name: true,
            logo: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      isCaptain: !!captainedShip,
      isMember: !!membership,
      captainedShip: captainedShip ? {
        ...captainedShip,
        memberCount: captainedShip._count.members,
      } : null,
      membershipShip: membership?.ship || null,
    });
  } catch (error) {
    console.error("Error fetching user ship status:", error);
    return NextResponse.json(
      { error: "Failed to fetch ship status" },
      { status: 500 }
    );
  }
}