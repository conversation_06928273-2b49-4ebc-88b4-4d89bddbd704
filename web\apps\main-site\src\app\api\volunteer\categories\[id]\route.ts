import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser, userHasRole } from "@/lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// GET /api/volunteer/categories/[id] - Get a specific category
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const { id } = params;

    // Check if user is authenticated and has volunteer coordinator role
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 },
      );
    }

    const hasCoordinatorRole = await userHasRole(req, "volunteerCoordinator");
    if (!hasCoordinatorRole) {
      return NextResponse.json(
        { error: "Unauthorized - Coordinator role required" },
        { status: 403 },
      );
    }

    // Get the category
    const category = await prisma.volunteerCategory.findUnique({
      where: { id },
      include: {
        leadManager: {
          select: {
            id: true,
            displayName: true,
            email: true,
            avatar: true,
          },
        },
        shifts: {
          select: {
            id: true,
            _count: {
              select: {
                assignments: true,
              },
            },
          },
        },
      },
    });

    if (!category) {
      return NextResponse.json(
        { error: "Category not found" },
        { status: 404 },
      );
    }

    // Calculate statistics
    const totalShifts = category.shifts ? category.shifts.length : 0;
    const totalSignups = category.shifts
      ? category.shifts.reduce(
          (sum: number, shift: any) => sum + shift._count.assignments,
          0,
        )
      : 0;
    const completedShifts = 0; // Placeholder

    const categoryWithStats = {
      ...category,
      stats: {
        totalShifts,
        totalSignups,
        completedShifts,
      },
    };

    return NextResponse.json(categoryWithStats);
  } catch (error) {
    console.error("Error fetching category:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch category",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    );
  }
}

// PUT /api/volunteer/categories/[id] - Update a category
export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const { id } = params;
    const { name, description, payRate, leadManagerId } = await req.json();

    // Check if user is authenticated and has volunteer coordinator role
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 },
      );
    }

    const hasCoordinatorRole = await userHasRole(req, "volunteerCoordinator");
    if (!hasCoordinatorRole) {
      return NextResponse.json(
        { error: "Unauthorized - Coordinator role required" },
        { status: 403 },
      );
    }

    // Validate required fields
    if (!name || name.trim() === "") {
      return NextResponse.json(
        { error: "Category name is required" },
        { status: 400 },
      );
    }

    // Check if category exists
    const existingCategory = await prisma.volunteerCategory.findUnique({
      where: { id },
      include: {
        leadManager: true,
      },
    });

    if (!existingCategory) {
      return NextResponse.json(
        { error: "Category not found" },
        { status: 404 },
      );
    }

    // Check if another category with the same name exists for this event
    // Using a more compatible approach for case-insensitive search
    const duplicateCategory = await prisma.volunteerCategory.findFirst({
      where: {
        eventId: existingCategory.eventId,
        name: {
          contains: name,
        },
        NOT: {
          id,
        },
      },
    });

    // Manual case-insensitive check since Prisma might not support mode: "insensitive"
    const duplicateExists =
      duplicateCategory &&
      duplicateCategory.name.toLowerCase() === name.toLowerCase();

    if (duplicateExists) {
      return NextResponse.json(
        {
          error:
            "Another category with this name already exists for this event",
        },
        { status: 409 },
      );
    }

    // Handle lead manager changes
    let updateLeadManager = false;
    const previousLeadManagerId = existingCategory.leadManagerId;

    if (leadManagerId !== existingCategory.leadManagerId) {
      updateLeadManager = true;
    }

    // Update the category
    const updatedCategory = await prisma.volunteerCategory.update({
      where: { id },
      data: {
        name,
        description,
        payRate: payRate ? parseFloat(payRate) : null,
        leadManagerId: leadManagerId || null,
      },
      include: {
        leadManager: {
          select: {
            id: true,
            displayName: true,
            email: true,
            avatar: true,
          },
        },
      },
    });

    // Update user roles if lead manager changed
    if (updateLeadManager) {
      // If there was a previous lead manager, update their record
      if (previousLeadManagerId) {
        const otherCategories = await prisma.volunteerCategory.findMany({
          where: {
            leadManagerId: previousLeadManagerId,
            NOT: {
              id,
            },
          },
        });

        // If they're not a lead manager for any other category, remove the role and category ID
        if (otherCategories.length === 0) {
          await prisma.user.update({
            where: { id: previousLeadManagerId },
            data: {
              isLeadManager: false,
              leadManagerCategoryId: null, // Clear the category ID reference
            },
          });
        }
      }

      // If a new lead manager was assigned, update their role and category ID
      if (leadManagerId) {
        await prisma.user.update({
          where: { id: leadManagerId },
          data: {
            isLeadManager: true,
            leadManagerCategoryId: id, // Set the category ID reference
          },
        });
      }
    }

    return NextResponse.json(updatedCategory);
  } catch (error) {
    console.error("Error updating category:", error);
    return NextResponse.json(
      {
        error: "Failed to update category",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    );
  }
}

// DELETE /api/volunteer/categories/[id] - Delete a category
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const { id } = params;

    // Check if user is authenticated and has volunteer coordinator role
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 },
      );
    }

    const hasCoordinatorRole = await userHasRole(req, "volunteerCoordinator");
    if (!hasCoordinatorRole) {
      return NextResponse.json(
        { error: "Unauthorized - Coordinator role required" },
        { status: 403 },
      );
    }

    // Check if category exists
    const category = await prisma.volunteerCategory.findUnique({
      where: { id },
      include: {
        shifts: true,
      },
    });

    if (!category) {
      return NextResponse.json(
        { error: "Category not found" },
        { status: 404 },
      );
    }

    // Check if there are any shifts associated with this category
    if (category.shifts.length > 0) {
      return NextResponse.json(
        { error: "Cannot delete category with associated shifts" },
        { status: 400 },
      );
    }

    // Get the lead manager ID before deleting
    const leadManagerId = category.leadManagerId;

    // Delete the category
    await prisma.volunteerCategory.delete({
      where: { id },
    });

    // If there was a lead manager, check if they're still a lead manager for any other category
    if (leadManagerId) {
      const otherCategories = await prisma.volunteerCategory.findMany({
        where: {
          leadManagerId,
          NOT: {
            id, // Exclude the category being deleted
          },
        },
      });

      // If they're not a lead manager for any other category, remove the role and category ID
      if (otherCategories.length === 0) {
        await prisma.user.update({
          where: { id: leadManagerId },
          data: {
            isLeadManager: false,
            leadManagerCategoryId: null, // Clear the category ID reference
          },
        });
      } else if (otherCategories.length > 0) {
        // If they are still a lead manager for other categories, update their category ID to one of the remaining categories
        await prisma.user.update({
          where: { id: leadManagerId },
          data: {
            leadManagerCategoryId: otherCategories[0].id, // Set to the first available category
          },
        });
      }
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting category:", error);
    return NextResponse.json(
      {
        error: "Failed to delete category",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    );
  }
}
