/**
 * <PERSON><PERSON><PERSON> to fix lead manager category relationships
 *
 * This script fixes inconsistencies in the bidirectional relationship between
 * users and volunteer categories. It ensures that users with the lead manager role
 * have the correct leadManagerCategoryId set.
 *
 * Usage:
 * 1. Run with: npx ts-node -r tsconfig-paths/register src/scripts/fix-lead-manager-categories.ts
 */

import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function fixLeadManagerCategories() {
  console.log("Starting lead manager category relationship fix...");

  try {
    // Find all categories with lead managers assigned
    const categoriesWithLeadManagers = await prisma.volunteerCategory.findMany({
      where: {
        leadManagerId: {
          not: null,
        },
      },
      include: {
        leadManager: true,
      },
    });

    console.log(
      `Found ${categoriesWithLeadManagers.length} categories with lead managers assigned`,
    );

    // Process each category and update the corresponding user if needed
    for (const category of categoriesWithLeadManagers) {
      if (!category.leadManagerId) continue;

      const user = category.leadManager;

      if (!user) {
        console.log(
          `Warning: Category ${category.id} has leadManagerId ${category.leadManagerId} but user not found`,
        );
        continue;
      }

      // Check if the user's leadManagerCategoryId is not set or doesn't match
      if (user.leadManagerCategoryId !== category.id) {
        console.log(
          `Fixing user ${user.id} (${user.displayName}): Setting leadManagerCategoryId to ${category.id}`,
        );

        // Update the user to set the correct category ID
        await prisma.user.update({
          where: { id: user.id },
          data: {
            leadManagerCategoryId: category.id,
            isLeadManager: true, // Ensure the role flag is set
          },
        });
      } else {
        console.log(
          `User ${user.id} (${user.displayName}) already has correct leadManagerCategoryId`,
        );
      }
    }

    // Find users with isLeadManager=true but no leadManagerCategoryId
    const leadManagersWithoutCategory = await prisma.user.findMany({
      where: {
        isLeadManager: true,
        leadManagerCategoryId: null,
      },
    });

    console.log(
      `Found ${leadManagersWithoutCategory.length} lead managers without a category assigned`,
    );

    // For each lead manager without a category, check if they have a category assigned to them
    for (const user of leadManagersWithoutCategory) {
      const assignedCategory = await prisma.volunteerCategory.findFirst({
        where: {
          leadManagerId: user.id,
        },
      });

      if (assignedCategory) {
        console.log(
          `Fixing user ${user.id} (${user.displayName}): Setting leadManagerCategoryId to ${assignedCategory.id}`,
        );

        // Update the user to set the correct category ID
        await prisma.user.update({
          where: { id: user.id },
          data: {
            leadManagerCategoryId: assignedCategory.id,
          },
        });
      } else {
        console.log(
          `Warning: User ${user.id} (${user.displayName}) has lead manager role but no category is assigned to them`,
        );

        // Optionally, you could remove the lead manager role if no category is assigned
        // Uncomment the following code to do this:
        /*
        await prisma.user.update({
          where: { id: user.id },
          data: {
            isLeadManager: false,
          },
        });
        console.log(`Removed lead manager role from user ${user.id} (${user.displayName}) as they have no assigned category`);
        */
      }
    }

    console.log(
      "Lead manager category relationship fix completed successfully",
    );
  } catch (error) {
    console.error("Error fixing lead manager categories:", error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the fix function
fixLeadManagerCategories()
  .then(() => console.log("Script completed"))
  .catch((error) => console.error("Script failed:", error));
