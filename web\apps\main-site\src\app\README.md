# App Router Pages & API Routes

Next.js 13+ App Router pages and API endpoints providing the complete Bank of Styx platform functionality.

## Pages

- **about/** - About pages and platform information
- **admin/** - Administrative dashboard and management interfaces
- **auth/** - Authentication pages (login, register, Discord OAuth)
- **bank/** - Banking dashboard and account management
- **cashier/** - Cashier portal for staff transaction processing
- **events/** - Event management and calendar system
- **help/** - Help documentation and support pages
- **news/** - News system with article management
- **rules/** - Platform rules and terms of service
- **sales/** - Sales management portal for staff
- **settings/** - User account settings and preferences
- **shop/** - Shopping system with cart and checkout
- **test/** - Development testing pages
- **volunteer/** - Volunteer management system

## API Routes

- **api/** - Backend API endpoints for all platform functionality
- **route/** - Additional routing configurations

## Root Files

- **layout.tsx** - Root layout component with navigation and providers
- **page.tsx** - Homepage component
- **globals.css** - Global CSS styles and TailwindCSS imports

Each directory follows Next.js App Router conventions with `page.tsx`, `layout.tsx`, and `loading.tsx` files as needed.
