"use client";

import { useState } from 'react';
import { But<PERSON>, Modal } from '@bank-of-styx/ui';

interface ShipRole {
  id: string;
  name: string;
  description?: string;
  _count?: {
    members: number;
  };
}

interface RoleCreationFormProps {
  roles: ShipRole[];
  onCreateRole: (name: string, description: string) => Promise<void>;
  onUpdateRole: (roleId: string, name: string, description: string) => Promise<void>;
  onDeleteRole: (roleId: string) => Promise<void>;
  loading?: boolean;
}

export default function RoleCreationForm({
  roles,
  onCreateRole,
  onUpdateRole,
  onDeleteRole,
  loading = false
}: RoleCreationFormProps) {
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedRole, setSelectedRole] = useState<ShipRole | null>(null);
  
  const [formData, setFormData] = useState({
    name: '',
    description: ''
  });
  
  const [actionLoading, setActionLoading] = useState(false);

  const resetForm = () => {
    setFormData({ name: '', description: '' });
    setSelectedRole(null);
  };

  const handleCreateRole = async () => {
    if (!formData.name.trim()) return;

    setActionLoading(true);
    try {
      await onCreateRole(formData.name.trim(), formData.description.trim());
      setIsCreateModalOpen(false);
      resetForm();
    } catch (error) {
      console.error('Error creating role:', error);
    } finally {
      setActionLoading(false);
    }
  };

  const handleUpdateRole = async () => {
    if (!selectedRole || !formData.name.trim()) return;

    setActionLoading(true);
    try {
      await onUpdateRole(selectedRole.id, formData.name.trim(), formData.description.trim());
      setIsEditModalOpen(false);
      resetForm();
    } catch (error) {
      console.error('Error updating role:', error);
    } finally {
      setActionLoading(false);
    }
  };

  const handleDeleteRole = async () => {
    if (!selectedRole) return;

    setActionLoading(true);
    try {
      await onDeleteRole(selectedRole.id);
      setIsDeleteModalOpen(false);
      resetForm();
    } catch (error) {
      console.error('Error deleting role:', error);
    } finally {
      setActionLoading(false);
    }
  };

  const openCreateModal = () => {
    resetForm();
    setIsCreateModalOpen(true);
  };

  const openEditModal = (role: ShipRole) => {
    setSelectedRole(role);
    setFormData({
      name: role.name,
      description: role.description || ''
    });
    setIsEditModalOpen(true);
  };

  const openDeleteModal = (role: ShipRole) => {
    setSelectedRole(role);
    setIsDeleteModalOpen(true);
  };

  if (loading) {
    return (
      <div className="bg-secondary rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Ship Roles</h3>
        <div className="animate-pulse space-y-4">
          {[1, 2].map(i => (
            <div key={i} className="flex items-center justify-between p-4 border border-gray-600 rounded-lg">
              <div className="space-y-2">
                <div className="h-4 bg-secondary-dark rounded w-24"></div>
                <div className="h-3 bg-secondary-dark rounded w-32"></div>
              </div>
              <div className="flex space-x-2">
                <div className="w-16 h-8 bg-secondary-dark rounded"></div>
                <div className="w-16 h-8 bg-secondary-dark rounded"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="bg-secondary rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-600 flex justify-between items-center">
          <h3 className="text-lg font-semibold text-white">Ship Roles ({roles?.length || 0})</h3>
          <Button onClick={openCreateModal}>
            Create Role
          </Button>
        </div>
        
        <div className="p-6">
          {!roles || roles.length === 0 ? (
            <div className="text-center py-8 text-gray-400">
              <p className="text-white">No custom roles created yet.</p>
              <p className="text-sm mt-1">Create roles to better organize your crew!</p>
            </div>
          ) : (
            <div className="space-y-4">
              {roles?.map((role) => (
                <div
                  key={role.id}
                  className="flex items-center justify-between p-4 border border-gray-600 rounded-lg hover:bg-secondary-light"
                >
                  <div className="flex-1">
                    <h4 className="font-medium text-white">{role.name}</h4>
                    {role.description && (
                      <p className="text-sm text-gray-300 mt-1">{role.description}</p>
                    )}
                    <p className="text-xs text-gray-400 mt-1">
                      {role._count?.members || 0} member(s) assigned
                    </p>
                  </div>
                  
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => openEditModal(role)}
                    >
                      Edit
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => openDeleteModal(role)}
                      className="text-red-400 hover:text-red-300 hover:bg-red-900/30 border-red-700"
                      disabled={(role._count?.members || 0) > 0}
                      title={(role._count?.members || 0) > 0 ? "Cannot delete role with assigned members" : "Delete role"}
                    >
                      Delete
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Create Role Modal */}
      <Modal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        size="md"
      >
        <div className="p-6">
          <h3 className="text-lg font-semibold mb-4">Create New Role</h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Role Name *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="e.g., First Mate, Navigator, Cook"
                maxLength={50}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Description
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                className="w-full bg-input border border-secondary-dark rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-primary"
                placeholder="Describe the role's responsibilities..."
                rows={3}
                maxLength={200}
              />
            </div>
          </div>

          <div className="flex justify-end space-x-3 mt-6">
            <Button
              variant="outline"
              onClick={() => setIsCreateModalOpen(false)}
              disabled={actionLoading}
            >
              Cancel
            </Button>
            <Button
              onClick={handleCreateRole}
              disabled={actionLoading || !formData.name.trim()}
            >
              {actionLoading ? 'Creating...' : 'Create Role'}
            </Button>
          </div>
        </div>
      </Modal>

      {/* Edit Role Modal */}
      <Modal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        size="md"
      >
        <div className="p-6">
          <h3 className="text-lg font-semibold text-white mb-4">
            Edit Role: {selectedRole?.name}
          </h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Role Name *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className="w-full bg-input border border-secondary-dark rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-primary"
                placeholder="e.g., First Mate, Navigator, Cook"
                maxLength={50}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Description
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                className="w-full bg-input border border-secondary-dark rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-primary"
                placeholder="Describe the role's responsibilities..."
                rows={3}
                maxLength={200}
              />
            </div>
          </div>

          <div className="flex justify-end space-x-3 mt-6">
            <Button
              variant="outline"
              onClick={() => setIsEditModalOpen(false)}
              disabled={actionLoading}
            >
              Cancel
            </Button>
            <Button
              onClick={handleUpdateRole}
              disabled={actionLoading || !formData.name.trim()}
            >
              {actionLoading ? 'Updating...' : 'Update Role'}
            </Button>
          </div>
        </div>
      </Modal>

      {/* Delete Role Modal */}
      <Modal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        size="md"
      >
        <div className="p-6">
          <h3 className="text-lg font-semibold mb-4 text-red-400">
            Delete Role
          </h3>
          
          <p className="text-gray-300 mb-6">
            Are you sure you want to delete the role <strong className="text-white">{selectedRole?.name}</strong>? 
            This action cannot be undone.
          </p>

          <div className="flex justify-end space-x-3">
            <Button
              variant="outline"
              onClick={() => setIsDeleteModalOpen(false)}
              disabled={actionLoading}
            >
              Cancel
            </Button>
            <Button
              onClick={handleDeleteRole}
              disabled={actionLoading}
              className="bg-red-600 hover:bg-red-700 text-white border-red-600"
            >
              {actionLoading ? 'Deleting...' : 'Delete Role'}
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
}