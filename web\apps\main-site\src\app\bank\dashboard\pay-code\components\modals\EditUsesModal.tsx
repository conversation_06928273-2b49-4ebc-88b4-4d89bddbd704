import React from "react";
import { PayCode } from "../../../../../../services/bankService";

interface EditUsesModalProps {
  selectedCode: PayCode;
  newMaxUses: string;
  setNewMaxUses: (value: string) => void;
  onSave: () => void;
  onCancel: () => void;
}

export const EditUsesModal: React.FC<EditUsesModalProps> = ({
  selectedCode,
  newMaxUses,
  setNewMaxUses,
  onSave,
  onCancel,
}) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-secondary-light rounded-lg p-6 max-w-md w-full">
        <h3 className="text-xl font-bold text-white mb-4">Edit Maximum Uses</h3>
        <p className="mb-4 text-white">
          Pay-Code: <span className="font-bold">{selectedCode.code}</span>
        </p>
        <p className="mb-4 text-white">
          Current uses:{" "}
          <span className="font-bold">{selectedCode.uses || 0}</span> /{" "}
          <span className="font-bold">{selectedCode.maxUses || 1}</span>
        </p>

        <div className="mb-4">
          <label
            htmlFor="newMaxUses"
            className="block text-white font-medium mb-2"
          >
            New Maximum Uses
          </label>
          <select
            id="newMaxUses"
            value={newMaxUses}
            onChange={(e) => setNewMaxUses(e.target.value)}
            className="w-full px-4 py-2 bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary"
            required
          >
            <option value="1" className="bg-secondary text-white">
              1 use (single-use)
            </option>
            <option value="5" className="bg-secondary text-white">
              5 uses
            </option>
            <option value="10" className="bg-secondary text-white">
              10 uses
            </option>
            <option value="25" className="bg-secondary text-white">
              25 uses
            </option>
            <option value="50" className="bg-secondary text-white">
              50 uses
            </option>
          </select>
        </div>

        <div className="flex justify-end space-x-4">
          <button
            onClick={onCancel}
            className="px-4 py-2 border border-gray-600 rounded-md text-white hover:bg-secondary"
          >
            Cancel
          </button>
          <button
            onClick={onSave}
            className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark"
          >
            Save Changes
          </button>
        </div>
      </div>
    </div>
  );
};
