"use client";

import { useEffect, useState, useCallback } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { Button } from "@bank-of-styx/ui";
import {
  VolunteerDashboardLayout,
  EventSelector,
  CategoryList,
  CategoryForm,
  ConfirmationModal,
} from "@/components/volunteer";
import {
  VolunteerCategory,
  useVolunteerCategoriesByEvent,
  useDeleteVolunteerCategory,
} from "@/hooks/useVolunteerCategories";

export default function CategoriesPage() {
  const { user, isLoading: authLoading } = useAuth();
  const [isAuthorized, setIsAuthorized] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();

  // State
  const [selectedEventId, setSelectedEventId] = useState<string | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingCategory, setEditingCategory] =
    useState<VolunteerCategory | null>(null);
  const [deletingCategory, setDeletingCategory] =
    useState<VolunteerCategory | null>(null);

  // Get event ID from URL if available
  useEffect(() => {
    const eventId = searchParams.get("eventId");
    if (eventId) {
      console.log("Category Management: Setting event ID from URL:", eventId);
      setSelectedEventId(eventId);
    }
  }, [searchParams]);

  // Check if user is authorized to access this page
  useEffect(() => {
    if (!authLoading) {
      if (!user || !user.roles?.volunteerCoordinator) {
        router.push("/");
      } else {
        setIsAuthorized(true);
      }
    }
  }, [user, authLoading, router]);

  // Fetch categories for the selected event
  const {
    data: categories = [] as VolunteerCategory[],
    isLoading: categoriesLoading,
    error: categoriesError,
    refetch: refetchCategories,
  } = useVolunteerCategoriesByEvent(selectedEventId);

  // Delete category mutation
  const deleteMutation = useDeleteVolunteerCategory(selectedEventId);

  // Handle event selection
  const handleEventSelect = useCallback(
    (eventId: string) => {
      setSelectedEventId(eventId);
      router.push(`/volunteer/dashboard/categories?eventId=${eventId}`);

      // Reset state when changing events
      setShowCreateForm(false);
      setEditingCategory(null);
      setDeletingCategory(null);
    },
    [router],
  );

  // Handle create category button click
  const handleCreateClick = () => {
    setShowCreateForm(true);
    setEditingCategory(null);
  };

  // Handle edit category
  const handleEditCategory = (category: VolunteerCategory) => {
    setEditingCategory(category);
    setShowCreateForm(false);
  };

  // Handle delete category
  const handleDeleteCategory = (category: VolunteerCategory) => {
    setDeletingCategory(category);
  };

  // Handle form success
  const handleFormSuccess = () => {
    setShowCreateForm(false);
    setEditingCategory(null);
    refetchCategories();
  };

  // Handle form cancel
  const handleFormCancel = () => {
    setShowCreateForm(false);
    setEditingCategory(null);
  };

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    if (!deletingCategory) return;

    try {
      await deleteMutation.mutateAsync(deletingCategory.id);
      setDeletingCategory(null);
      refetchCategories();
    } catch (error) {
      console.error("Error deleting category:", error);
    }
  };

  // Show loading state while checking authorization
  if (authLoading || !isAuthorized) {
    return (
      <div className="min-h-screen bg-secondary-dark flex items-center justify-center">
        <div className="bg-secondary-light p-6 rounded-lg shadow-md border border-gray-600">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary mx-auto"></div>
          <p className="text-white text-lg mt-4">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <VolunteerDashboardLayout>
      <div>
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-white mb-2">
            Category Management
          </h1>
          <p className="text-gray-400">
            Create and manage volunteer categories for events.
          </p>
        </div>

        {/* Event Selector */}
        <div className="mb-6">
          <EventSelector
            onEventSelect={handleEventSelect}
            className="mb-4"
            initialEventId={selectedEventId || ""}
          />

          {/* Create Category Button */}
          {selectedEventId && !showCreateForm && !editingCategory && (
            <div className="flex justify-end">
              <Button onClick={handleCreateClick} variant="primary">
                Create Category
              </Button>
            </div>
          )}
        </div>

        {/* Create/Edit Form */}
        {selectedEventId && (showCreateForm || editingCategory) && (
          <div className="mb-6">
            <CategoryForm
              eventId={selectedEventId}
              category={editingCategory}
              onSuccess={handleFormSuccess}
              onCancel={handleFormCancel}
            />
          </div>
        )}

        {/* Categories List */}
        {selectedEventId && !showCreateForm && !editingCategory && (
          <CategoryList
            categories={categories}
            isLoading={categoriesLoading}
            error={categoriesError as Error}
            onEdit={handleEditCategory}
            onDelete={handleDeleteCategory}
          />
        )}

        {/* No Event Selected Message */}
        {!selectedEventId && (
          <div className="bg-secondary-light rounded-lg shadow-md p-6 border border-gray-600 text-center">
            <p className="text-white mb-4">
              Please select an event to manage its categories.
            </p>
            <p className="text-gray-400">
              Use the event selector above to choose an event.
            </p>
          </div>
        )}

        {/* Delete Confirmation Modal */}
        <ConfirmationModal
          isOpen={!!deletingCategory}
          onClose={() => setDeletingCategory(null)}
          onConfirm={handleDeleteConfirm}
          title="Delete Category"
          message={`Are you sure you want to delete the category "${deletingCategory?.name}"? This action cannot be undone.`}
          confirmText="Delete"
          cancelText="Cancel"
          isLoading={deleteMutation.isPending}
          variant="danger"
        />
      </div>
    </VolunteerDashboardLayout>
  );
}
