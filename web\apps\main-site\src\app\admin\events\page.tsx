"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import { useColorTheme } from "@/contexts/ColorThemeContext";
import { useAuth } from "@/contexts/AuthContext";
import fetchClient from "@/lib/fetchClient";
import { EventStatsModal, AdminDashboardLayout } from "@/components/admin";

// Define types
interface Event {
  id: string;
  name: string;
  shortDescription: string | null;
  startDate: string;
  endDate: string;
  location: string | null;
  status: string;
  category: {
    id: string;
    name: string;
    color: string | null;
  };
}

interface Pagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

interface EventsResponse {
  events: Event[];
  pagination: Pagination;
}

interface Category {
  id: string;
  name: string;
  description: string | null;
  color: string | null;
}

export default function AdminEventsPage() {
  const router = useRouter();
  const { isDarkMode } = useColorTheme();
  const { user, isAuthenticated } = useAuth();
  const [events, setEvents] = useState<Event[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [pagination, setPagination] = useState<Pagination>({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPrevPage: false,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Filter states
  const [search, setSearch] = useState("");
  const [status, setStatus] = useState("");
  const [categoryId, setCategoryId] = useState("");

  // Stats modal state
  const [statsModalOpen, setStatsModalOpen] = useState(false);
  const [selectedEventId, setSelectedEventId] = useState<string>("");
  const [selectedEventName, setSelectedEventName] = useState<string>("");

  // Fetch events
  const fetchEvents = async (page = 1) => {
    setLoading(true);
    try {
      // Build query parameters
      const params: Record<string, string> = {
        page: page.toString(),
        limit: pagination.limit.toString(),
      };

      if (search) params.search = search;
      if (status) params.status = status;
      if (categoryId) params.categoryId = categoryId;

      const data = await fetchClient.get<EventsResponse>("/api/admin/events", {
        params,
      });
      setEvents(data.events);
      setPagination(data.pagination);
      setError(null);
    } catch (err) {
      setError("Error loading events. Please try again.");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  // Fetch categories
  const fetchCategories = async () => {
    try {
      const data = await fetchClient.get<Category[]>(
        "/api/admin/event-categories",
      );
      setCategories(data);
    } catch (err) {
      console.error("Error loading categories:", err);
    }
  };

  // Delete event
  const handleDeleteEvent = async (id: string) => {
    if (!confirm("Are you sure you want to delete this event?")) {
      return;
    }

    try {
      await fetchClient.delete(`/api/admin/events/${id}`);

      // Refresh events list
      fetchEvents(pagination.page);
    } catch (err) {
      setError("Error deleting event. Please try again.");
      console.error(err);
    }
  };

  // Handle filter changes
  const handleFilterChange = () => {
    fetchEvents(1); // Reset to first page when filters change
  };

  // Handle pagination
  const handlePageChange = (newPage: number) => {
    fetchEvents(newPage);
  };

  // Handle stats modal
  const handleOpenStats = (event: Event) => {
    setSelectedEventId(event.id);
    setSelectedEventName(event.name);
    setStatsModalOpen(true);
  };

  const handleCloseStats = () => {
    setStatsModalOpen(false);
    setSelectedEventId("");
    setSelectedEventName("");
  };

  // Initialize
  useEffect(() => {
    fetchEvents();
    fetchCategories();
  }, []);

  return (
    <AdminDashboardLayout>
      <div className="px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1
          className="text-2xl font-bold"
          style={{ color: "var(--color-text-primary)" }}
        >
          Event Management
        </h1>
        <Link
          href="/admin/events/new"
          className="px-4 py-2 rounded transition-colors"
          style={{
            backgroundColor: "var(--color-primary)",
            color: "var(--color-text-on-primary)",
          }}
        >
          Create New Event
        </Link>
      </div>

      {/* Filters */}
      <div
        className="rounded-lg p-6 mb-6"
        style={{
          backgroundColor: "var(--color-bg-surface)",
          boxShadow: "var(--shadow-sm)",
        }}
      >
        <h2
          className="text-lg font-semibold mb-4"
          style={{ color: "var(--color-text-primary)" }}
        >
          Filters
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div>
            <label
              className="block text-sm font-medium mb-2"
              style={{ color: "var(--color-text-secondary)" }}
            >
              Search
            </label>
            <input
              type="text"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="w-full rounded px-4 py-2"
              style={{
                backgroundColor: "var(--color-bg-input)",
                border: "1px solid var(--color-border-input)",
                color: "var(--color-text-primary)",
              }}
              placeholder="Search events..."
            />
          </div>

          {/* Status filter */}
          <div>
            <label
              className="block text-sm font-medium mb-2"
              style={{ color: "var(--color-text-secondary)" }}
            >
              Status
            </label>
            <select
              value={status}
              onChange={(e) => setStatus(e.target.value)}
              className="w-full rounded px-4 py-2"
              style={{
                backgroundColor: "var(--color-bg-input)",
                border: "1px solid var(--color-border-input)",
                color: "var(--color-text-primary)",
              }}
            >
              <option value="">All Statuses</option>
              <option value="draft">Draft</option>
              <option value="published">Published</option>
              <option value="cancelled">Cancelled</option>
              <option value="completed">Completed</option>
            </select>
          </div>

          {/* Category filter */}
          <div>
            <label
              className="block text-sm font-medium mb-2"
              style={{ color: "var(--color-text-secondary)" }}
            >
              Category
            </label>
            <select
              value={categoryId}
              onChange={(e) => setCategoryId(e.target.value)}
              className="w-full rounded px-4 py-2"
              style={{
                backgroundColor: "var(--color-bg-input)",
                border: "1px solid var(--color-border-input)",
                color: "var(--color-text-primary)",
              }}
            >
              <option value="">All Categories</option>
              {categories.map((category) => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>

          {/* Apply filters button */}
          <div className="flex items-end">
            <button
              onClick={handleFilterChange}
              className="px-4 py-2 rounded transition-colors"
              style={{
                backgroundColor: "var(--color-primary)",
                color: "var(--color-text-on-primary)",
              }}
            >
              Apply Filters
            </button>
          </div>
        </div>
      </div>

      {/* Error message */}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {/* Events table */}
      <div
        className="rounded-lg overflow-hidden"
        style={{
          backgroundColor: "var(--color-bg-surface)",
          boxShadow: "var(--shadow-sm)",
        }}
      >
        <table
          className="min-w-full divide-y"
          style={{ borderColor: "var(--color-border-subtle)" }}
        >
          <thead style={{ backgroundColor: "var(--color-bg-header)" }}>
            <tr>
              <th
                className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider"
                style={{ color: "var(--color-text-secondary)" }}
              >
                Event
              </th>
              <th
                className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider"
                style={{ color: "var(--color-text-secondary)" }}
              >
                Date
              </th>
              <th
                className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider"
                style={{ color: "var(--color-text-secondary)" }}
              >
                Location
              </th>
              <th
                className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider"
                style={{ color: "var(--color-text-secondary)" }}
              >
                Category
              </th>
              <th
                className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider"
                style={{ color: "var(--color-text-secondary)" }}
              >
                Status
              </th>
              <th
                className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider"
                style={{ color: "var(--color-text-secondary)" }}
              >
                Actions
              </th>
            </tr>
          </thead>
          <tbody
            className="divide-y"
            style={{
              backgroundColor: "var(--color-bg-surface)",
              color: "var(--color-text-primary)",
              borderColor: "var(--color-border-subtle)",
            }}
          >
            {loading ? (
              <tr>
                <td
                  colSpan={6}
                  className="px-6 py-4 text-center"
                  style={{ color: "var(--color-text-secondary)" }}
                >
                  Loading events...
                </td>
              </tr>
            ) : events.length === 0 ? (
              <tr>
                <td
                  colSpan={6}
                  className="px-6 py-4 text-center"
                  style={{ color: "var(--color-text-secondary)" }}
                >
                  No events found. Create your first event!
                </td>
              </tr>
            ) : (
              events.map((event) => (
                <tr key={event.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div
                      className="text-sm font-medium"
                      style={{ color: "var(--color-text-primary)" }}
                    >
                      {event.name}
                    </div>
                    <div
                      className="text-sm"
                      style={{ color: "var(--color-text-secondary)" }}
                    >
                      {event.shortDescription}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div
                      className="text-sm"
                      style={{ color: "var(--color-text-primary)" }}
                    >
                      {format(new Date(event.startDate), "MMM d, yyyy")}
                    </div>
                    <div
                      className="text-sm"
                      style={{ color: "var(--color-text-secondary)" }}
                    >
                      {format(new Date(event.startDate), "h:mm a")} -{" "}
                      {format(new Date(event.endDate), "h:mm a")}
                    </div>
                  </td>
                  <td
                    className="px-6 py-4 whitespace-nowrap text-sm"
                    style={{ color: "var(--color-text-secondary)" }}
                  >
                    {event.location || "Virtual"}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full"
                      style={{
                        backgroundColor:
                          event.category.color || "var(--color-bg-tag)",
                        color: "var(--color-text-on-tag)",
                      }}
                    >
                      {event.category.name}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full"
                      style={{
                        backgroundColor:
                          event.status === "published"
                            ? "var(--color-success-light)"
                            : event.status === "draft"
                            ? "var(--color-warning-light)"
                            : event.status === "cancelled"
                            ? "var(--color-error-light)"
                            : "var(--color-bg-tag)",
                        color:
                          event.status === "published"
                            ? "var(--color-success-dark)"
                            : event.status === "draft"
                            ? "var(--color-warning-dark)"
                            : event.status === "cancelled"
                            ? "var(--color-error-dark)"
                            : "var(--color-text-secondary)",
                      }}
                    >
                      {event.status.charAt(0).toUpperCase() +
                        event.status.slice(1)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button
                      onClick={() => handleOpenStats(event)}
                      className="mr-4 hover:underline"
                      style={{ color: "var(--color-info)" }}
                    >
                      Stats
                    </button>
                    <Link
                      href={`/admin/events/${event.id}`}
                      className="mr-4 hover:underline"
                      style={{ color: "var(--color-primary)" }}
                    >
                      Edit
                    </Link>
                    <button
                      onClick={() => handleDeleteEvent(event.id)}
                      className="hover:underline"
                      style={{ color: "var(--color-error)" }}
                    >
                      Delete
                    </button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {!loading && pagination.totalPages > 1 && (
        <div className="flex justify-between items-center mt-6">
          <div
            className="text-sm"
            style={{ color: "var(--color-text-secondary)" }}
          >
            Showing{" "}
            <span className="font-medium">
              {(pagination.page - 1) * pagination.limit + 1}
            </span>{" "}
            to{" "}
            <span className="font-medium">
              {Math.min(pagination.page * pagination.limit, pagination.total)}
            </span>{" "}
            of <span className="font-medium">{pagination.total}</span> events
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => handlePageChange(pagination.page - 1)}
              disabled={!pagination.hasPrevPage}
              className="px-4 py-2 rounded transition-colors"
              style={{
                backgroundColor: pagination.hasPrevPage
                  ? "var(--color-bg-button)"
                  : "var(--color-bg-button-disabled)",
                color: pagination.hasPrevPage
                  ? "var(--color-text-on-button)"
                  : "var(--color-text-disabled)",
                cursor: pagination.hasPrevPage ? "pointer" : "not-allowed",
              }}
            >
              Previous
            </button>
            <button
              onClick={() => handlePageChange(pagination.page + 1)}
              disabled={!pagination.hasNextPage}
              className="px-4 py-2 rounded transition-colors"
              style={{
                backgroundColor: pagination.hasNextPage
                  ? "var(--color-bg-button)"
                  : "var(--color-bg-button-disabled)",
                color: pagination.hasNextPage
                  ? "var(--color-text-on-button)"
                  : "var(--color-text-disabled)",
                cursor: pagination.hasNextPage ? "pointer" : "not-allowed",
              }}
            >
              Next
            </button>
          </div>
        </div>
      )}

      {/* Stats Modal */}
      <EventStatsModal
        isOpen={statsModalOpen}
        onClose={handleCloseStats}
        eventId={selectedEventId}
        eventName={selectedEventName}
      />
      </div>
    </AdminDashboardLayout>
  );
}
