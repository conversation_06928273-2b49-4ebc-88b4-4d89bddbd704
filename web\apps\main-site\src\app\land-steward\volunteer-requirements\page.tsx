"use client";

import React, { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { <PERSON><PERSON>, <PERSON>, Pagination } from "@bank-of-styx/ui";
import fetchClient from "@/lib/fetchClient";

interface Ship {
  id: string;
  name: string;
  captain: {
    id: string;
    displayName: string;
    email: string;
  };
  memberCount: number;
}

interface FormSubmission {
  id: string;
  formId: string;
  status: string;
  submittedAt: string;
  submissionData: any;
}

interface VolunteerRequirement {
  id: string;
  shipId: string;
  ship: Ship;
  formSubmission: FormSubmission;
  requiredHours: number;
  completedHours: number;
  completionPercentage: number;
  remainingHours: number;
  status: string;
  isOverdue: boolean;
  createdAt: string;
  updatedAt: string;
}

interface SummaryStats {
  total: number;
  pending: number;
  inProgress: number;
  completed: number;
  overdue: number;
}

interface RequirementsResponse {
  requirements: VolunteerRequirement[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
  summaryStats: SummaryStats;
}

export default function VolunteerRequirementsPage() {
  const [currentPage, setCurrentPage] = useState(1);
  const [statusFilter, setStatusFilter] = useState('all');

  const { data, isLoading, error } = useQuery({
    queryKey: ['volunteer-requirements', currentPage, statusFilter],
    queryFn: async () => {
      return fetchClient.get<RequirementsResponse>(
        `/api/land-steward/volunteer-requirements?page=${currentPage}&status=${statusFilter}`
      );
    },
  });

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-600';
      case 'in_progress':
        return 'bg-blue-600';
      case 'pending':
        return 'bg-yellow-600';
      case 'overdue':
        return 'bg-red-600';
      default:
        return 'bg-gray-600';
    }
  };

  const getStatusText = (requirement: VolunteerRequirement) => {
    if (requirement.isOverdue && requirement.status === 'pending') {
      return 'Overdue';
    }
    return requirement.status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
          <p className="ml-2 text-white">Loading volunteer requirements...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <Card className="p-6 bg-red-900/20 border-red-600">
          <p className="text-red-300">Failed to load volunteer requirements</p>
        </Card>
      </div>
    );
  }

  const { requirements, pagination, summaryStats } = data || {
    requirements: [],
    pagination: { page: 1, limit: 20, total: 0, pages: 0 },
    summaryStats: { total: 0, pending: 0, inProgress: 0, completed: 0, overdue: 0 },
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-white mb-2">
          Ship Volunteer Requirements
        </h1>
        <p className="text-gray-400">
          Monitor volunteer hour requirements for all ships
        </p>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card className="p-4 bg-secondary border-gray-600">
          <div className="text-2xl font-bold text-white">{summaryStats.total}</div>
          <div className="text-gray-400">Total Requirements</div>
        </Card>
        <Card className="p-4 bg-secondary border-gray-600">
          <div className="text-2xl font-bold text-yellow-400">{summaryStats.pending}</div>
          <div className="text-gray-400">Pending</div>
        </Card>
        <Card className="p-4 bg-secondary border-gray-600">
          <div className="text-2xl font-bold text-blue-400">{summaryStats.inProgress}</div>
          <div className="text-gray-400">In Progress</div>
        </Card>
        <Card className="p-4 bg-secondary border-gray-600">
          <div className="text-2xl font-bold text-green-400">{summaryStats.completed}</div>
          <div className="text-gray-400">Completed</div>
        </Card>
        <Card className="p-4 bg-secondary border-gray-600">
          <div className="text-2xl font-bold text-red-400">{summaryStats.overdue}</div>
          <div className="text-gray-400">Overdue</div>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex flex-wrap gap-4 items-center">
        <div className="flex gap-2">
          <Button
            variant={statusFilter === 'all' ? 'primary' : 'secondary'}
            size="sm"
            onClick={() => setStatusFilter('all')}
          >
            All
          </Button>
          <Button
            variant={statusFilter === 'pending' ? 'primary' : 'secondary'}
            size="sm"
            onClick={() => setStatusFilter('pending')}
          >
            Pending
          </Button>
          <Button
            variant={statusFilter === 'in_progress' ? 'primary' : 'secondary'}
            size="sm"
            onClick={() => setStatusFilter('in_progress')}
          >
            In Progress
          </Button>
          <Button
            variant={statusFilter === 'completed' ? 'primary' : 'secondary'}
            size="sm"
            onClick={() => setStatusFilter('completed')}
          >
            Completed
          </Button>
        </div>
      </div>

      {/* Requirements List */}
      <div className="space-y-4">
        {requirements.length === 0 ? (
          <Card className="p-8 bg-secondary border-gray-600 text-center">
            <p className="text-gray-400">No volunteer requirements found</p>
          </Card>
        ) : (
          requirements.map((requirement) => (
            <Card key={requirement.id} className="p-6 bg-secondary border-gray-600">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="text-lg font-semibold text-white">
                      {requirement.ship.name}
                    </h3>
                    <span
                      className={`px-2 py-1 rounded-full text-xs font-medium text-white ${
                        requirement.isOverdue ? 'bg-red-600' : getStatusBadgeColor(requirement.status)
                      }`}
                    >
                      {getStatusText(requirement)}
                    </span>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-300 mb-4">
                    <div>
                      <span className="text-gray-500">Captain:</span> {requirement.ship.captain.displayName}
                    </div>
                    <div>
                      <span className="text-gray-500">Members:</span> {requirement.ship.memberCount}
                    </div>
                    <div>
                      <span className="text-gray-500">Created:</span>{' '}
                      {new Date(requirement.createdAt).toLocaleDateString()}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between items-center text-sm">
                      <span className="text-gray-400">Progress</span>
                      <span className="text-white">
                        {requirement.completedHours.toFixed(1)} / {requirement.requiredHours.toFixed(1)} hours
                        ({requirement.completionPercentage}%)
                      </span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${
                          requirement.status === 'completed' 
                            ? 'bg-green-500' 
                            : requirement.isOverdue 
                              ? 'bg-red-500' 
                              : 'bg-blue-500'
                        }`}
                        style={{
                          width: `${Math.min(100, requirement.completionPercentage)}%`,
                        }}
                      ></div>
                    </div>
                    {requirement.remainingHours > 0 && (
                      <p className="text-sm text-gray-400">
                        {requirement.remainingHours.toFixed(1)} hours remaining
                      </p>
                    )}
                  </div>
                </div>

                <div className="ml-4 flex flex-col gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      // TODO: Navigate to ship detail page or volunteer hours detail
                      console.log('View ship details:', requirement.ship.id);
                    }}
                  >
                    View Details
                  </Button>
                </div>
              </div>
            </Card>
          ))
        )}
      </div>

      {/* Pagination */}
      {pagination.pages > 1 && (
        <div className="flex justify-center">
          <Pagination
            currentPage={pagination.page}
            totalPages={pagination.pages}
            onPageChange={setCurrentPage}
          />
        </div>
      )}
    </div>
  );
}