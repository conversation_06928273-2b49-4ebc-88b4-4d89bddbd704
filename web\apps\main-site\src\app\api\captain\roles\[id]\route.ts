import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import prisma from '@/lib/prisma';

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const captainId = user.id;
    const roleId = params.id;

    const { name, description } = await request.json();

    if (!name || name.trim().length === 0) {
      return NextResponse.json({ error: 'Role name is required' }, { status: 400 });
    }

    // Verify captain owns a ship and role exists
    const ship = await prisma.ship.findFirst({
      where: {
        captainId,
        status: 'active',
        roles: {
          some: {
            id: roleId
          }
        }
      }
    });

    if (!ship) {
      return NextResponse.json({ error: 'Ship or role not found, or you are not the captain' }, { status: 404 });
    }

    // Check if another role with this name already exists (excluding current role)
    const existingRole = await prisma.shipRole.findFirst({
      where: {
        shipId: ship.id,
        name: name.trim(),
        id: {
          not: roleId
        }
      }
    });

    if (existingRole) {
      return NextResponse.json({ error: 'A role with this name already exists' }, { status: 400 });
    }

    // Update the role
    const updatedRole = await prisma.shipRole.update({
      where: {
        id: roleId
      },
      data: {
        name: name.trim(),
        description: description?.trim() || null
      },
      include: {
        _count: {
          select: {
            members: {
              where: {
                status: 'active'
              }
            }
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      role: updatedRole
    });

  } catch (error) {
    console.error('Error updating ship role:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const captainId = user.id;
    const roleId = params.id;

    // Verify captain owns a ship and role exists
    const ship = await prisma.ship.findFirst({
      where: {
        captainId,
        status: 'active',
        roles: {
          some: {
            id: roleId
          }
        }
      }
    });

    if (!ship) {
      return NextResponse.json({ error: 'Ship or role not found, or you are not the captain' }, { status: 404 });
    }

    // Check if any members are currently assigned to this role
    const membersWithRole = await prisma.shipMember.count({
      where: {
        roleId,
        status: 'active'
      }
    });

    if (membersWithRole > 0) {
      return NextResponse.json({ 
        error: `Cannot delete role: ${membersWithRole} active member(s) are assigned to this role. Please reassign them first.` 
      }, { status: 400 });
    }

    // Delete the role
    await prisma.shipRole.delete({
      where: {
        id: roleId
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Role deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting ship role:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}