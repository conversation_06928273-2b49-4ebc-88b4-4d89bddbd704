# Shopping & Sales System

## Overview
The Shopping & Sales System is a comprehensive e-commerce platform that handles product management, shopping cart functionality with inventory holds, order processing with Stripe payments, and redemption codes. It provides both public shopping interfaces for customers and administrative sales management tools for staff, featuring a sophisticated 15-minute inventory hold system and dual inventory management approaches.

## Directory Structure
```
shopping-sales-system/
├── src/app/shop/                      # Public shopping interface
│   ├── page.tsx                       # Product browsing and category filtering
│   ├── cart/                          # Shopping cart management
│   │   └── page.tsx                   # Cart with hold timers and quantity controls
│   ├── checkout/                      # Payment processing
│   │   └── page.tsx                   # Stripe integration and order completion
│   ├── orders/                        # Order history and tracking
│   │   ├── page.tsx                   # User order listing
│   │   └── [id]/                      # Individual order details
│   ├── products/                      # Product details
│   │   └── [id]/                      # Individual product pages
│   └── search/                        # Product search results
├── src/app/sales/                     # Sales management interface
│   ├── dashboard/                     # Sales manager dashboard
│   │   └── page.tsx                   # Sales overview and metrics
│   ├── products/                      # Product management
│   │   ├── page.tsx                   # Product listing and management
│   │   ├── create/                    # New product creation
│   │   └── [id]/                      # Product editing and analytics
│   ├── orders/                        # Order management
│   │   ├── page.tsx                   # All orders with customer details
│   │   └── [id]/                      # Individual order management
│   └── categories/                    # Category management
│       ├── page.tsx                   # Category listing
│       ├── create/                    # New category creation
│       └── [id]/edit                  # Category editing
├── src/app/api/products/              # Public product APIs
├── src/app/api/cart/                  # Cart management APIs
├── src/app/api/checkout/              # Payment processing APIs
├── src/app/api/orders/                # Order tracking APIs
├── src/app/api/shop/                  # Redemption code APIs
├── src/app/api/sales/                 # Sales management APIs
├── src/components/shop/               # Shopping UI components
│   ├── ProductCard.tsx                # Product display cards
│   ├── CartHoldTimer.tsx              # 15-minute countdown timer
│   ├── RedemptionCodeInput.tsx        # Gift-themed code entry
│   ├── CategoryFilter.tsx             # Dynamic category filtering
│   └── CheckoutForm.tsx               # Stripe payment integration
├── src/components/sales/              # Sales management components
│   ├── SalesDashboardMain.tsx         # Sales metrics and quick actions
│   ├── ProductList.tsx                # Product management table
│   ├── OrderList.tsx                  # Order management interface
│   └── RedemptionCodesSection.tsx     # Code management tools
├── src/services/                      # Business logic services
│   ├── productService.ts              # Product operations
│   ├── cartService.ts                 # Cart and hold management
│   └── orderService.ts                # Order processing
├── src/hooks/                         # React state management
│   ├── useCart.ts                     # Cart state with hold timers
│   ├── useProducts.ts                 # Product data management
│   └── useOrders.ts                   # Order management hooks
└── src/lib/                           # Core utilities
    ├── stripe.ts                      # Client-side Stripe integration
    ├── stripe-server.ts               # Server-side payment processing
    ├── redemptionCodes.ts             # Code generation utilities
    ├── ticket-system.ts               # Individual inventory holds
    └── event-capacity-system.ts       # Event-based capacity management
```

## Key Files & Components

### Frontend Components
- **`ProductCard.tsx`** - Product display with category badges, inventory status, and out-of-stock indicators
- **`CartHoldTimer.tsx`** - Critical 15-minute countdown timer with visual status indicators and hold extension functionality
- **`RedemptionCodeInput.tsx`** - Gift-themed UI for code entry with automatic formatting and validation
- **`CategoryFilter.tsx`** - Dynamic category filtering interface integrated with product search
- **`CheckoutForm.tsx`** - Stripe Elements payment form with error handling and order completion workflow
- **`SalesDashboardMain.tsx`** - Sales metrics dashboard with statistics cards and quick action buttons
- **`ProductList.tsx`** - Comprehensive product management table with filtering and inline operations
- **`OrderList.tsx`** - Order management interface with pagination, status filtering, and customer information
- **`RedemptionCodesSection.tsx`** - Bulk code generation and management with copy-to-clipboard functionality

### API Endpoints
- **`GET /api/products`** - Product listing with category/event filtering, excludes free products from public view
- **`GET /api/products/[id]`** - Individual product details with category and event relationships
- **`GET /api/products/search`** - Product search functionality with filtering capabilities
- **`GET /api/cart`** - Retrieve user's cart with held items and expiration timers
- **`POST /api/cart/items`** - Add items with automatic inventory hold creation (15-minute expiration)
- **`PUT /api/cart/items/[id]`** - Update quantities with automatic hold adjustment
- **`DELETE /api/cart/items/[id]`** - Remove items and release associated inventory holds
- **`POST /api/cart/refresh-holds`** - Extend 15-minute holds before expiration
- **`POST /api/checkout/create-payment-intent`** - Stripe payment intent setup with cart validation
- **`POST /api/checkout/webhook`** - Stripe webhook handler for order fulfillment and inventory updates
- **`GET /api/orders`** - User order history with pagination and status filtering
- **`GET /api/orders/[id]`** - Individual order details with item breakdown
- **`POST /api/shop/redeem-code`** - Validate and apply redemption codes with capacity checking
- **`GET /api/sales/products`** - All products including free ones (sales manager access)
- **`POST /api/sales/products`** - Create products with automatic redemption code generation
- **`GET /api/sales/orders`** - All orders with customer details and sales analytics
- **`GET/POST /api/sales/product-categories`** - Category management with full CRUD operations

### Database Models
- **`Product`** - Core product model with pricing, inventory, category relationships, and free product support
- **`Cart`** - User shopping cart with item relationships and timestamp tracking
- **`CartItem`** - Individual cart items with quantity, redemption tracking, and hold relationships
- **`Order`** - Order processing with Stripe integration, status tracking, and user relationships
- **`OrderItem`** - Order line items with historical pricing and product information
- **`Ticket`** - Individual inventory units with hold system and status management
- **`TicketHold`** - 15-minute inventory holds preventing overselling during checkout
- **`EventCapacityHold`** - Event-based capacity holds for high-volume events
- **`RedemptionCode`** - Gift code system with product relationships and usage tracking
- **`ProductCategory`** - Product organization with hierarchical structure

### Services
- **`productService.ts`** - Product CRUD operations, search functionality, and inventory management
- **`cartService.ts`** - Cart management, hold system integration, and expiration handling
- **`orderService.ts`** - Order processing, Stripe integration, and fulfillment workflows

### Hooks
- **`useCart()`** - Cart state management with real-time hold timers and automatic updates
- **`useProducts()`** - Product data management with search, filtering, and category operations
- **`useOrders()`** - Order management with TanStack Query integration and optimistic updates

## Common Tasks

### Task 1: How to Shop for Products
1. User browses products at `/shop` with category filtering and search
2. Clicks on `ProductCard` to view detailed product information
3. Reviews product details including price, description, and availability
4. Clicks "Add to Cart" which triggers automatic 15-minute inventory hold
5. System creates `CartItem` record and associated `TicketHold` or `EventCapacityHold`
6. User can continue shopping or proceed to cart
7. Cart accessible at `/shop/cart` shows held items with countdown timers
8. User can adjust quantities (releases/creates additional holds automatically)
9. Proceeds to checkout when ready within hold expiration window

### Task 2: How to Complete Checkout and Payment
1. User navigates to `/shop/checkout` (authentication required)
2. System validates cart items and confirms all holds are still active
3. Creates pending `Order` record with current cart contents
4. Stripe payment intent created with order total
5. User enters payment information via Stripe Elements form
6. Payment processed through Stripe with confirmation handling
7. Stripe webhook confirms payment and updates order status to 'paid'
8. Held inventory converted to 'SOLD' status permanently
9. User's cart cleared and order confirmation displayed
10. Email confirmation sent with order details and tracking information

### Task 3: How to Use Redemption Codes
1. User enters redemption code in gift-themed `RedemptionCodeInput` component
2. System validates code format and checks database for active code
3. Verifies associated product availability and event capacity limits
4. Creates special `CartItem` with `isCodeRedemption: true` flag
5. Adds free product to cart (price contribution: $0)
6. Marks redemption code as used (deactivates from further use)
7. Free items appear in cart but cannot have quantities modified
8. Checkout process treats free items normally but excludes from payment total
9. Order completion includes both paid and redeemed items

### Task 4: How to Manage Products (Sales Manager)
1. Sales manager accesses `/sales/dashboard` (requires `salesManager` role)
2. Navigates to `/sales/products` for comprehensive product management
3. Creates new products via `/sales/products/create` with:
   - Basic information (name, description, pricing)
   - Category assignment and event association
   - Inventory settings (limited quantity vs unlimited)
   - Free product designation (triggers automatic code generation)
4. For free products, system automatically generates redemption codes
5. Manages existing products with inline editing and status updates
6. Views product analytics at `/sales/products/[id]/stats`
7. Bulk operations available for efficient product management

## API Integration

### Authentication Requirements
- **Public browsing**: Product catalog and search require no authentication
- **Shopping cart**: Valid JWT token required for cart operations and checkout
- **Order access**: JWT token required for personal order history
- **Sales management**: JWT token + `salesManager` role required for administrative functions
- **Token format**: `Bearer <jwt-token>` in Authorization header

### Request/Response Examples
```typescript
// Add to Cart Request
interface AddToCartRequest {
  productId: string;
  quantity: number;
}

// Cart Response with Hold Information
interface CartResponse {
  id: string;
  items: Array<{
    id: string;
    quantity: number;
    isCodeRedemption: boolean;
    product: {
      id: string;
      name: string;
      price: number;
      image?: string;
      category: {
        id: string;
        name: string;
      };
    };
    hold?: {
      id: string;
      expiresAt: string;
      timeRemaining: number; // seconds
      status: 'active' | 'expiring' | 'expired';
    };
  }>;
  subtotal: number;
  totalItems: number;
}

// Checkout Payment Intent Request
interface CreatePaymentIntentRequest {
  cartId: string;
  paymentMethodId?: string;
}

// Order Response
interface OrderResponse {
  id: string;
  orderNumber: string;
  status: 'pending' | 'paid' | 'fulfilled' | 'cancelled' | 'refunded';
  total: number;
  subtotal: number;
  paymentMethod?: string;
  items: Array<{
    id: string;
    productName: string;
    quantity: number;
    price: number;
    description?: string;
  }>;
  createdAt: string;
  user: {
    id: string;
    username: string;
    displayName: string;
  };
}

// Redemption Code Request
interface RedeemCodeRequest {
  code: string;
}

// Product Management Request
interface CreateProductRequest {
  name: string;
  description?: string;
  shortDescription?: string;
  price: number;
  inventory?: number; // null for unlimited
  isActive: boolean;
  isFree: boolean;
  categoryId: string;
  eventId?: string;
  image?: string;
}

// Sales Analytics Response
interface SalesAnalyticsResponse {
  totalProducts: number;
  activeProducts: number;
  totalOrders: number;
  totalRevenue: number;
  recentOrders: Array<{
    id: string;
    orderNumber: string;
    total: number;
    customerName: string;
    createdAt: string;
  }>;
  topProducts: Array<{
    id: string;
    name: string;
    totalSold: number;
    revenue: number;
  }>;
}
```

## Database Schema

### Primary Models
```sql
-- Product table (core product information)
CREATE TABLE Product (
  id VARCHAR(191) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  shortDescription VARCHAR(500),
  price DECIMAL(10,2) NOT NULL,
  image VARCHAR(255),
  isActive BOOLEAN DEFAULT true,
  affectsCapacity BOOLEAN DEFAULT false,
  inventory INT, -- NULL for unlimited inventory
  isFree BOOLEAN DEFAULT false,
  isAutoGenerated BOOLEAN DEFAULT false, -- For event-generated products
  categoryId VARCHAR(191) NOT NULL,
  eventId VARCHAR(191),
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (categoryId) REFERENCES ProductCategory(id),
  FOREIGN KEY (eventId) REFERENCES Event(id),
  INDEX idx_product_category (categoryId),
  INDEX idx_product_event (eventId),
  INDEX idx_product_active (isActive),
  INDEX idx_product_free (isFree)
);

-- Shopping cart and cart items
CREATE TABLE Cart (
  id VARCHAR(191) PRIMARY KEY,
  userId VARCHAR(191) NOT NULL,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (userId) REFERENCES User(id),
  INDEX idx_cart_user (userId)
);

CREATE TABLE CartItem (
  id VARCHAR(191) PRIMARY KEY,
  cartId VARCHAR(191) NOT NULL,
  productId VARCHAR(191) NOT NULL,
  quantity INT NOT NULL,
  isCodeRedemption BOOLEAN DEFAULT false,
  redemptionCodeId VARCHAR(191),
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (cartId) REFERENCES Cart(id) ON DELETE CASCADE,
  FOREIGN KEY (productId) REFERENCES Product(id),
  FOREIGN KEY (redemptionCodeId) REFERENCES RedemptionCode(id),
  INDEX idx_cartitem_cart (cartId),
  INDEX idx_cartitem_product (productId)
);

-- Order processing
CREATE TABLE `Order` (
  id VARCHAR(191) PRIMARY KEY,
  orderNumber VARCHAR(50) UNIQUE NOT NULL,
  status ENUM('pending', 'paid', 'fulfilled', 'cancelled', 'refunded') DEFAULT 'pending',
  total DECIMAL(10,2) NOT NULL,
  subtotal DECIMAL(10,2) NOT NULL,
  tax DECIMAL(10,2),
  discount DECIMAL(10,2),
  paymentMethod VARCHAR(100),
  paymentIntentId VARCHAR(255), -- Stripe payment intent ID
  notes TEXT,
  userId VARCHAR(191) NOT NULL,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (userId) REFERENCES User(id),
  INDEX idx_order_user (userId),
  INDEX idx_order_status (status),
  INDEX idx_order_created (createdAt)
);

CREATE TABLE OrderItem (
  id VARCHAR(191) PRIMARY KEY,
  orderId VARCHAR(191) NOT NULL,
  productId VARCHAR(191) NOT NULL,
  quantity INT NOT NULL,
  price DECIMAL(10,2) NOT NULL, -- Price at time of purchase
  name VARCHAR(255) NOT NULL,   -- Product name at time of purchase
  description TEXT,             -- Product description at time of purchase
  FOREIGN KEY (orderId) REFERENCES `Order`(id) ON DELETE CASCADE,
  FOREIGN KEY (productId) REFERENCES Product(id),
  INDEX idx_orderitem_order (orderId),
  INDEX idx_orderitem_product (productId)
);

-- Hold system for inventory management
CREATE TABLE Ticket (
  id VARCHAR(191) PRIMARY KEY,
  productId VARCHAR(191) NOT NULL,
  status ENUM('AVAILABLE', 'HELD', 'SOLD', 'CANCELLED') DEFAULT 'AVAILABLE',
  holdId VARCHAR(191),
  orderId VARCHAR(191),
  seatInfo JSON, -- Additional seat/ticket information
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (productId) REFERENCES Product(id),
  FOREIGN KEY (holdId) REFERENCES TicketHold(id),
  FOREIGN KEY (orderId) REFERENCES `Order`(id),
  INDEX idx_ticket_product (productId),
  INDEX idx_ticket_status (status),
  INDEX idx_ticket_hold (holdId)
);

CREATE TABLE TicketHold (
  id VARCHAR(191) PRIMARY KEY,
  userId VARCHAR(191) NOT NULL,
  cartItemId VARCHAR(191) NOT NULL,
  expiresAt DATETIME NOT NULL, -- 15-minute expiration
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (userId) REFERENCES User(id),
  FOREIGN KEY (cartItemId) REFERENCES CartItem(id) ON DELETE CASCADE,
  INDEX idx_hold_user (userId),
  INDEX idx_hold_expires (expiresAt),
  INDEX idx_hold_cartitem (cartItemId)
);

-- Event capacity-based holds (alternative to individual tickets)
CREATE TABLE EventCapacityHold (
  id VARCHAR(191) PRIMARY KEY,
  eventId VARCHAR(191) NOT NULL,
  userId VARCHAR(191) NOT NULL,
  cartItemId VARCHAR(191) NOT NULL,
  quantity INT NOT NULL,
  expiresAt DATETIME NOT NULL,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (eventId) REFERENCES Event(id),
  FOREIGN KEY (userId) REFERENCES User(id),
  FOREIGN KEY (cartItemId) REFERENCES CartItem(id) ON DELETE CASCADE,
  INDEX idx_cap_hold_event (eventId),
  INDEX idx_cap_hold_user (userId),
  INDEX idx_cap_hold_expires (expiresAt)
);

-- Redemption code system
CREATE TABLE RedemptionCode (
  id VARCHAR(191) PRIMARY KEY,
  code VARCHAR(50) UNIQUE NOT NULL,
  productId VARCHAR(191) NOT NULL,
  isActive BOOLEAN DEFAULT true,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (productId) REFERENCES Product(id),
  INDEX idx_redemption_code (code),
  INDEX idx_redemption_product (productId),
  INDEX idx_redemption_active (isActive)
);

-- Product categories
CREATE TABLE ProductCategory (
  id VARCHAR(191) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  isActive BOOLEAN DEFAULT true,
  sortOrder INT DEFAULT 0,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_category_active (isActive),
  INDEX idx_category_sort (sortOrder)
);
```

### Relationships
- **Product ↔ ProductCategory**: Many-to-one (products belong to categories)
- **Product ↔ Event**: Many-to-one (products can be associated with events)
- **Cart ↔ User**: One-to-one (each user has one active cart)
- **CartItem ↔ Cart/Product**: Many-to-one relationships with quantity tracking
- **Order ↔ User**: Many-to-one (users can have multiple orders)
- **OrderItem ↔ Order/Product**: Many-to-one with historical pricing preservation
- **Ticket ↔ Product**: Many-to-one (individual inventory units)
- **TicketHold ↔ User/CartItem**: Many-to-one (15-minute inventory reservations)
- **RedemptionCode ↔ Product**: Many-to-one (multiple codes per free product)

## Related Features
- **[Authentication System](./authentication-system.md)** - User authentication for cart access and role-based sales management
- **[Banking System](./banking-system.md)** - Revenue tracking and financial integration for completed orders
- **[Event Management System](./event-management-system.md)** - Event-based products and capacity management integration
- **[Notification System](./notification-system.md)** - Order confirmations, payment status updates, and hold expiration warnings

## User Roles & Permissions
- **Public Users**: Product browsing, search functionality, product detail viewing
- **Authenticated Users**: Shopping cart access, order placement, redemption code usage, order history
- **Sales Managers**: Complete product management, order oversight, category management, redemption code administration
- **Admins**: Full system access including advanced analytics and system configuration

## Recent Changes
- **v6.2.0** - Enhanced hold system with dual inventory management (individual tickets vs event capacity)
- **v6.1.0** - Stripe webhook integration for automated order fulfillment and inventory updates
- **v6.0.0** - Complete shopping cart redesign with 15-minute hold timers and real-time updates
- **v5.9.0** - Redemption code system launch with automatic code generation for free products
- **v5.8.0** - Sales dashboard overhaul with comprehensive analytics and bulk operations
- **v5.7.0** - Event-product integration with capacity-based inventory management

## Troubleshooting

### Common Issues
1. **Cart hold timers expiring**: Check system time synchronization and ensure users are warned before expiration. Verify hold extension functionality.
2. **Payment processing failures**: Verify Stripe webhook configuration and endpoint accessibility. Check payment intent creation and confirmation flow.
3. **Inventory discrepancies**: Ensure hold cleanup jobs are running properly. Check for race conditions in concurrent cart operations.
4. **Redemption codes not working**: Verify code is active and associated product has available inventory/capacity. Check for code formatting issues.
5. **Orders stuck in pending status**: Check Stripe webhook delivery and processing. Ensure order fulfillment logic is executing correctly.

### Debug Information
- **Log locations**: Payment processing logs in Stripe dashboard, order processing logs in server console
- **Environment variables**: Stripe keys (publishable/secret), webhook endpoint URLs, hold expiration settings
- **Database queries**: Use Prisma Studio to inspect cart items, holds, and order status
- **Debugging commands**:
  ```bash
  # Check user's cart and holds
  curl -H "Authorization: Bearer <token>" http://localhost:3000/api/cart
  
  # Verify product inventory
  curl http://localhost:3000/api/products/{id}
  
  # Check order status
  curl -H "Authorization: Bearer <token>" http://localhost:3000/api/orders/{id}
  
  # Test redemption code
  curl -X POST -H "Authorization: Bearer <token>" -d '{"code":"TEST123"}' http://localhost:3000/api/shop/redeem-code
  ```

### Performance Considerations
- **Hold Cleanup**: Background jobs automatically clean expired holds every 5 minutes to prevent resource leaks
- **Cart Optimization**: Real-time cart updates use optimistic updates with error rollback
- **Payment Processing**: Stripe webhooks handle order fulfillment asynchronously to prevent timeout issues
- **Database Indexing**: Comprehensive indexing on frequently queried fields (product status, order status, hold expiration)
- **Image Handling**: Product images optimized with caching and CDN delivery

### Security Measures
- **Payment Security**: All payment processing handled by Stripe with PCI compliance
- **Inventory Protection**: 15-minute holds prevent overselling during concurrent checkout attempts
- **Role-based Access**: Sales management functions strictly limited to authorized users
- **Input Validation**: Comprehensive validation on all user inputs and API endpoints
- **Webhook Security**: Stripe webhook signatures validated to ensure authenticity
- **Session Security**: Cart operations tied to authenticated user sessions only

### Integration Patterns
- **Authentication Integration**: Seamless user context for cart persistence and order tracking
- **Banking Integration**: Completed orders automatically create revenue transactions
- **Event Integration**: Product-event relationships enable capacity-based inventory management
- **Notification Integration**: Real-time updates for order status, payment confirmations, and hold warnings
- **Hold System Integration**: Prevents overselling across multiple concurrent shopping sessions

---

**File Locations:**
- Pages: `/src/app/shop/`, `/src/app/sales/`
- Components: `/src/components/shop/`, `/src/components/sales/`
- API: `/src/app/api/products/`, `/src/app/api/cart/`, `/src/app/api/checkout/`, `/src/app/api/orders/`, `/src/app/api/sales/`
- Services: `/src/services/productService.ts`, `/src/services/cartService.ts`, `/src/services/orderService.ts`
- Hooks: `/src/hooks/useCart.ts`, `/src/hooks/useProducts.ts`, `/src/hooks/useOrders.ts`
- Utilities: `/src/lib/stripe.ts`, `/src/lib/redemptionCodes.ts`, `/src/lib/ticket-system.ts`
- Types: `/src/types/shop.ts`, `/src/types/product.ts`