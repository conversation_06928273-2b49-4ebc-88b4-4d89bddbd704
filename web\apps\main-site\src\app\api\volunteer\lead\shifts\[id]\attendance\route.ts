import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser, userHasRole } from "@/lib/auth";

// Helper function to update ship volunteer requirements
async function updateShipVolunteerRequirements(shipId: string, hoursCompleted: number) {
  try {
    // Find all volunteer requirements for this ship
    const requirements = await prisma.shipVolunteerRequirement.findMany({
      where: {
        shipId,
        status: {
          in: ["pending", "in_progress"]
        }
      },
      orderBy: {
        createdAt: "asc" // Process oldest requirements first
      }
    });

    let remainingHours = hoursCompleted;

    // Update each requirement with completed hours
    for (const requirement of requirements) {
      if (remainingHours <= 0) break;

      const neededHours = requirement.requiredHours - requirement.completedHours;
      const hoursToApply = Math.min(remainingHours, neededHours);

      const newCompletedHours = requirement.completedHours + hoursToApply;
      const isCompleted = newCompletedHours >= requirement.requiredHours;

      await prisma.shipVolunteerRequirement.update({
        where: { id: requirement.id },
        data: {
          completedHours: newCompletedHours,
          status: isCompleted ? "completed" : "in_progress",
          updatedAt: new Date(),
        }
      });

      remainingHours -= hoursToApply;
    }
  } catch (error) {
    console.error("Error updating ship volunteer requirements:", error);
    // Don't throw error - volunteer hours should still be recorded even if requirement update fails
  }
}


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// PATCH /api/volunteer/lead/shifts/[id]/attendance - Update attendance for a shift
export async function PATCH(
  req: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const { id: assignmentId } = params;
    const { status, payMultiplier = 1 } = await req.json();

    // Check if user is authenticated and has lead manager role
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 },
      );
    }

    const hasLeadRole = await userHasRole(req, "leadManager");
    if (!hasLeadRole) {
      return NextResponse.json(
        { error: "Unauthorized - Lead Manager role required" },
        { status: 403 },
      );
    }

    // Get the lead manager's category ID
    const leadManagerCategoryId = user.leadManagerCategoryId;
    if (!leadManagerCategoryId) {
      return NextResponse.json(
        { error: "No category assigned to this lead manager" },
        { status: 404 },
      );
    }

    // Verify the assignment exists and belongs to the lead manager's category
    const assignment = await prisma.volunteerAssignment.findUnique({
      where: { id: assignmentId },
      include: {
        shift: {
          include: {
            category: true,
          },
        },
      },
    });

    if (!assignment) {
      return NextResponse.json(
        { error: "Assignment not found" },
        { status: 404 },
      );
    }

    // Check if the assignment belongs to the lead manager's category
    if (assignment.shift.category.id !== leadManagerCategoryId) {
      return NextResponse.json(
        { error: "Unauthorized - This assignment is not in your category" },
        { status: 403 },
      );
    }

    // Update the assignment status
    const updatedAssignment = await prisma.volunteerAssignment.update({
      where: { id: assignmentId },
      data: {
        status,
      },
    });

    // If status is "completed", create or update volunteer hours
    if (status === "completed") {
      // Calculate hours worked based on shift duration
      const startTime = new Date(assignment.shift.startTime);
      const endTime = new Date(assignment.shift.endTime);
      const hoursWorked =
        (endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60);

      // Calculate payment amount based on category pay rate and multiplier
      const payRate = assignment.shift.category.payRate || 0;
      const paymentAmount = payRate * hoursWorked * payMultiplier;

      // Extract ship information from assignment metadata
      const metadata = assignment.metadata as any;
      const creditShipId = metadata?.landGrantCreditShipId || null;
      const isDockHours = metadata?.isDock || false;
      const isLandGrant = metadata?.isLandGrant || false;

      // Create or update volunteer hours
      const hours = await prisma.volunteerHours.upsert({
        where: { assignmentId },
        update: {
          hoursWorked,
          paymentAmount,
          verifiedById: user.id,
          verifiedAt: new Date(),
          paymentStatus: "pending",
          creditShipId,
          isDockHours,
          isLandGrant,
        },
        create: {
          assignmentId,
          userId: assignment.userId,
          hoursWorked,
          paymentAmount,
          verifiedById: user.id,
          verifiedAt: new Date(),
          paymentStatus: "pending",
          creditShipId,
          isDockHours,
          isLandGrant,
        },
      });

      // If this is a land grant or dock hour and credits a ship, update the ship's requirement progress
      if ((isDockHours || isLandGrant) && creditShipId) {
        await updateShipVolunteerRequirements(creditShipId, hoursWorked);
      }

      return NextResponse.json({
        assignment: updatedAssignment,
        hours,
        message: "Attendance updated and payment initiated",
      });
    }

    return NextResponse.json({
      assignment: updatedAssignment,
      message: "Attendance updated",
    });
  } catch (error) {
    console.error("Error updating attendance:", error);
    return NextResponse.json(
      { error: "Failed to update attendance" },
      { status: 500 },
    );
  }
}
