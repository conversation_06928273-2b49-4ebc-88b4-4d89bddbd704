"use client";

import React, { useEffect, useState } from "react";
import fetchClient from "@/lib/fetchClient";

interface DashboardStats {
  totalShifts: number;
  upcomingShifts: number;
  totalVolunteers: number;
  completedShifts: number;
  pendingPayments: number;
}

interface Category {
  id: string;
  name: string;
  description: string | null;
  payRate: number | null;
  event: {
    id: string;
    name: string;
    startDate: string;
    endDate: string;
    status: string;
  };
}

interface DashboardData {
  category: Category;
  stats: DashboardStats;
}

interface LeadDashboardStatsProps {
  categoryId?: string;
  isCoordinatorView?: boolean;
}

export const LeadDashboardStats: React.FC<LeadDashboardStatsProps> = ({
  categoryId,
  isCoordinatorView = false,
}) => {
  const [data, setData] = useState<DashboardData | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        
        // Use coordinator endpoint if in coordinator view, otherwise use regular lead endpoint
        const endpoint = isCoordinatorView && categoryId
          ? `/api/volunteer/management/dashboard?categoryId=${categoryId}`
          : "/api/volunteer/lead/dashboard";
          
        const response = await fetchClient.get<DashboardData>(endpoint);
        setData(response);
        setError(null);
      } catch (err) {
        // Extract more detailed error information if available
        let errorMessage = "Error loading dashboard data";
        if (err instanceof Error) {
          errorMessage = err.message || errorMessage;

          // Check for additional details from the API response
          const details = (err as any).details;
          if (details && typeof details === "object") {
            if (details.error) {
              errorMessage = details.error;
            }
            if (details.message) {
              errorMessage += `: ${details.message}`;
            }
          }
        }

        setError(errorMessage);
        console.error("Error fetching lead dashboard data:", err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [categoryId, isCoordinatorView]);

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6 animate-pulse">
        {[...Array(5)].map((_, i) => (
          <div
            key={i}
            className="bg-secondary p-4 rounded-lg shadow-md border border-gray-600"
          >
            <div className="h-5 bg-gray-600 rounded w-1/3 mb-2"></div>
            <div className="h-8 bg-gray-600 rounded w-1/2"></div>
          </div>
        ))}
      </div>
    );
  }

  if (error || !data) {
    return (
      <div className="bg-red-900/20 border border-red-700 text-red-100 p-4 rounded-lg mb-6">
        <p className="font-medium">Failed to load dashboard statistics.</p>
        {error && <p className="mt-2 text-sm">{error}</p>}
        <p className="mt-3 text-sm">
          If you are a lead manager but don't have a category assigned, please
          contact an administrator.
        </p>
      </div>
    );
  }

  const { stats, category } = data;

  const statItems = [
    {
      label: "Total Shifts",
      value: stats.totalShifts,
      color: "bg-blue-500",
    },
    {
      label: "Upcoming Shifts",
      value: stats.upcomingShifts,
      color: "bg-green-500",
    },
    {
      label: "Total Volunteers",
      value: stats.totalVolunteers,
      color: "bg-purple-500",
    },
    {
      label: "Completed Shifts",
      value: stats.completedShifts,
      color: "bg-yellow-500",
    },
    {
      label: "Pending Payments",
      value: stats.pendingPayments,
      color: "bg-red-500",
    },
  ];

  return (
    <div className="mb-6">
      <div className="mb-4">
        <h2 className="text-xl font-bold text-white">
          {category.name} - {category.event.name}
        </h2>
        <p className="text-gray-400">
          {category.description || "No description available"}
        </p>
        {category.payRate && (
          <p className="text-primary font-semibold mt-1">
            Pay Rate: £{category.payRate.toFixed(2)}/hr
          </p>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
        {statItems.map((item) => (
          <div
            key={item.label}
            className="bg-secondary p-4 rounded-lg shadow-md border border-gray-600"
          >
            <div className="flex items-center mb-2">
              <div className={`w-3 h-3 rounded-full ${item.color} mr-2`}></div>
              <p className="text-gray-400 text-sm">{item.label}</p>
            </div>
            <p className="text-2xl font-bold text-white">{item.value}</p>
          </div>
        ))}
      </div>
    </div>
  );
};
