import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser, userHasRole } from "@/lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// GET /api/volunteer/events - Get events for volunteer management
export async function GET(req: NextRequest) {
  try {
    // Check if user is authenticated and has volunteer coordinator role
    const user = await getCurrentUser(req);

    // Log authentication status for debugging
    console.log(`API /volunteer/events - User authenticated: ${!!user}`);
    if (user) {
      console.log(`API /volunteer/events - User ID: ${user.id}`);
      //console.log(`API /volunteer/events - User roles:`, user.roles || 'No roles');
    }

    // For debugging, temporarily skip authentication check
    // if (!user) {
    //   return NextResponse.json(
    //     { error: "Unauthorized - Authentication required" },
    //     { status: 401 }
    //   );
    // }

    // const hasCoordinatorRole = await user<PERSON>as<PERSON>ole(req, "volunteerCoordinator");
    // console.log(`API /volunteer/events - Has coordinator role: ${hasCoordinatorRole}`);

    // if (!hasCoordinatorRole) {
    //   return NextResponse.json(
    //     { error: "Unauthorized - Coordinator role required" },
    //     { status: 403 }
    //   );
    // }

    // Get current date
    const now = new Date();
    console.log(`API /volunteer/events - Current date: ${now.toISOString()}`);

    // First, get a count of all events regardless of filters
    const totalEventCount = await prisma.event.count();
    console.log(
      `API /volunteer/events - Total events in database: ${totalEventCount}`,
    );

    // Get all events (without any filters for debugging)
    const allEvents = await prisma.event.findMany({
      orderBy: {
        startDate: "asc",
      },
      select: {
        id: true,
        name: true,
        startDate: true,
        endDate: true,
        location: true,
        isVirtual: true,
        status: true,
        category: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    console.log(
      `API /volunteer/events - All events count: ${allEvents.length}`,
    );
    if (allEvents.length > 0) {
      console.log(
        `API /volunteer/events - First event: ${JSON.stringify(allEvents[0])}`,
      );
    } else {
      console.log(`API /volunteer/events - No events found in database`);
    }

    // Get events with only date filter
    const events = await prisma.event.findMany({
      where: {
        // Remove all filters for testing
        // status: "published",
        // endDate: {
        //   gte: now,
        // },
      },
      orderBy: {
        startDate: "asc",
      },
      select: {
        id: true,
        name: true,
        startDate: true,
        endDate: true,
        location: true,
        isVirtual: true,
        status: true,
        category: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    console.log(
      `API /volunteer/events - Filtered events count: ${events.length}`,
    );

    return NextResponse.json({
      events,
    });
  } catch (error) {
    console.error("Error fetching events:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch events",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    );
  }
}
