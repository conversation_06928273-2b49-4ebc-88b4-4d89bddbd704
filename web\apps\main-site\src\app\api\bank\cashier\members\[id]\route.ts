import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { verifyToken } from "@/lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    // Get authorization header
    const authHeader = req.headers.get("authorization");

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Extract token
    const token = authHeader.split(" ")[1];

    // Verify token
    const decoded = verifyToken(token);

    if (!decoded || typeof decoded !== "object" || !decoded.id) {
      return NextResponse.json({ error: "Invalid token" }, { status: 401 });
    }

    // Check if user is a banker or admin
    const currentUser = await prisma.user.findUnique({
      where: { id: decoded.id as string },
      select: { isAdmin: true, isBanker: true },
    });

    if (!currentUser || (!currentUser.isAdmin && !currentUser.isBanker)) {
      return NextResponse.json(
        { error: "You don't have permission to access this resource" },
        { status: 403 },
      );
    }

    // Get member by ID
    const user = await prisma.user.findUnique({
      where: { id: params.id },
    });

    if (!user) {
      return NextResponse.json({ error: "Member not found" }, { status: 404 });
    }

    // Get member's transactions
    const transactions = await prisma.transaction.findMany({
      where: {
        OR: [{ senderId: params.id }, { recipientId: params.id }],
      },
      include: {
        sender: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
        recipient: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
        processedBy: {
          select: {
            id: true,
            username: true,
            displayName: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
    });

    // Format the user data to match BankUser interface
    const formattedUser = {
      id: user.id,
      username: user.username,
      displayName: user.displayName,
      email: user.email,
      avatar: user.avatar,
      balance: user.balance,
      isEmailVerified: user.isEmailVerified,
      preferences: {
        defaultView: user.defaultView,
        notifications: {
          transfers: user.notifyTransfers,
          deposits: user.notifyDeposits,
          withdrawals: user.notifyWithdrawals,
          newsAndEvents: user.notifyNewsEvents,
        },
      },
      connectedAccounts: {
        discord: user.discordConnected,
        discordId: user.discordId,
        facebook: user.facebookConnected,
        facebookId: user.facebookId,
      },
      merchant: {
        status: user.merchantStatus,
        merchantId: user.merchantId,
        slug: user.merchantSlug,
      },
      auctions: {
        hasCreated: user.hasCreatedAuctions,
        auctionCount: user.auctionCount,
      },
      roles: {
        admin: user.isAdmin,
        editor: user.isEditor,
        banker: user.isBanker,
        chatModerator: user.isChatModerator,
        volunteerCoordinator: user.isVolunteerCoordinator,
        leadManager: user.isLeadManager,
      },
    };

    // Format dates in transactions
    const formattedTransactions = transactions.map((transaction) => ({
      ...transaction,
      createdAt: transaction.createdAt.toISOString(),
      updatedAt: transaction.updatedAt.toISOString(),
      processedAt: transaction.processedAt
        ? transaction.processedAt.toISOString()
        : null,
    }));

    return NextResponse.json({
      user: formattedUser,
      transactions: formattedTransactions,
    });
  } catch (error) {
    console.error("Error fetching member details:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
