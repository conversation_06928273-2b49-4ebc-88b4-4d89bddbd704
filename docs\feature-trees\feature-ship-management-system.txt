﻿# Bank of Styx Website - Ship Management System
# Generated on 08/07/2025 12:35:25
# Priority: High
# Ship creation, management, captain dashboard, crew management, and ship-related operations
# Root directory: C:\Users\<USER>\projects\test\web

## Directories and Files

### Files
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\captain\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\captain\dashboard\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\captain\dashboard\route.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\captain\forms\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\captain\forms\[id]\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\captain\forms\available\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\captain\forms\available\route.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\captain\forms\submissions\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\captain\forms\submissions\[id]\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\captain\forms\submissions\route.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\captain\invitations\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\captain\invitations\[id]\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\captain\members\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\captain\members\[userId]\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\captain\members\invite\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\captain\members\invite\route.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\captain\members\route.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\captain\requests\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\captain\requests\[id]\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\captain\roles\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\captain\roles\[id]\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\captain\roles\route.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\captain\ship\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\captain\ship\deletion-request\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\captain\ship\deletion-request\route.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\captain\ship\route.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\captain\volunteer-requirements\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\captain\volunteer-requirements\route.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\ships\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\ships\[id]\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\ships\apply\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\ships\apply\route.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\ships\my-ship\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\ships\my-ship\route.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\ships\route.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\captain\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\captain\dashboard\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\captain\dashboard\forms\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\captain\dashboard\forms\page.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\captain\dashboard\invite\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\captain\dashboard\invite\page.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\captain\dashboard\members\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\captain\dashboard\members\page.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\captain\dashboard\page.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\captain\dashboard\roles\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\captain\dashboard\roles\page.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\captain\dashboard\settings\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\captain\dashboard\settings\page.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\ships\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\ships\[id]\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\ships\apply\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\ships\apply\page.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\ships\page.tsx

