"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { SalesDashboardLayout } from "@/components/sales/SalesDashboardLayout";
import { useSalesOrder, useUpdateOrderStatus } from "@/hooks/useOrders";
import { Card, Button, Spinner } from "@bank-of-styx/ui";
import { Select } from "@/components/shared/Select";
import Link from "next/link";
import { toast } from "react-hot-toast";

export default function OrderDetailPage({
  params,
}: {
  params: { id: string };
}) {
  const { id } = params;
  const { user, isLoading: isLoadingAuth } = useAuth();
  const [isAuthorized, setIsAuthorized] = useState(false);
  const router = useRouter();
  const { data, isLoading, error } = useSalesOrder(id);
  const updateOrderStatus = useUpdateOrderStatus();
  const [status, setStatus] = useState("");

  // Check if user is authorized to access this page
  useEffect(() => {
    if (!isLoadingAuth) {
      if (!user || !user.roles?.salesManager) {
        router.push("/");
      } else {
        setIsAuthorized(true);
      }
    }
  }, [user, isLoadingAuth, router]);

  // Set initial status when order data is loaded
  useEffect(() => {
    if (data?.order) {
      setStatus(data.order.status);
    }
  }, [data]);

  const handleStatusChange = async () => {
    try {
      await updateOrderStatus.mutateAsync({
        orderId: id,
        status,
      });
      toast.success("Order status updated successfully");
    } catch (error) {
      toast.error("Failed to update order status");
    }
  };

  if (isLoadingAuth) {
    return <div className="p-8 text-center">Loading...</div>;
  }

  if (!isAuthorized) {
    return null; // Don't render anything while redirecting
  }

  if (isLoading) {
    return (
      <SalesDashboardLayout>
        <div className="flex justify-center p-8">
          <Spinner size="lg" />
        </div>
      </SalesDashboardLayout>
    );
  }

  if (error || !data?.order) {
    return (
      <SalesDashboardLayout>
        <Card className="p-8 text-center">
          <h2 className="text-2xl font-bold mb-4">Order Not Found</h2>
          <p className="mb-6">
            The order you're looking for doesn't exist or has been removed.
          </p>
          <Link href="/sales/orders">
            <Button variant="primary">Back to Orders</Button>
          </Link>
        </Card>
      </SalesDashboardLayout>
    );
  }

  const { order } = data;

  return (
    <SalesDashboardLayout>
      <div className="mb-6 flex justify-between items-center">
        <div>
          <Link
            href="/sales/orders"
            className="text-primary hover:underline mb-2 inline-block"
          >
            ← Back to Orders
          </Link>
          <h1 className="text-2xl font-bold">Order #{order.orderNumber}</h1>
        </div>
        <div className="flex items-center space-x-4">
          <Select
            value={status}
            onChange={(e) => setStatus(e.target.value)}
            className="w-40"
          >
            <option value="pending">Pending</option>
            <option value="paid">Paid</option>
            <option value="fulfilled">Fulfilled</option>
            <option value="cancelled">Cancelled</option>
            <option value="refunded">Refunded</option>
          </Select>
          <Button
            variant="primary"
            onClick={handleStatusChange}
            disabled={status === order.status || updateOrderStatus.isPending}
            loading={updateOrderStatus.isPending}
          >
            Update Status
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Order Details */}
        <div className="lg:col-span-2">
          <Card title="Order Items">
            <div className="divide-y divide-border-subtle">
              {order.items.map((item) => (
                <div key={item.id} className="p-4 flex justify-between">
                  <div>
                    <p className="font-semibold">{item.name}</p>
                    <p className="text-sm text-text-muted">
                      Quantity: {item.quantity}
                    </p>
                  </div>
                  <div className="text-right">
                    <p>${item.price.toFixed(2)} each</p>
                    <p className="font-semibold">
                      ${(item.price * item.quantity).toFixed(2)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
            <div className="p-4 border-t border-border-subtle flex justify-between font-bold">
              <span>Total</span>
              <span>${order.total.toFixed(2)}</span>
            </div>
          </Card>
        </div>

        {/* Customer and Order Info */}
        <div className="lg:col-span-1">
          <Card title="Order Information" className="mb-6">
            <div className="p-4 space-y-4">
              <div>
                <p className="text-text-muted">Order Date</p>
                <p>{new Date(order.createdAt).toLocaleString()}</p>
              </div>
              <div>
                <p className="text-text-muted">Status</p>
                <p className="capitalize">{order.status}</p>
              </div>
              <div>
                <p className="text-text-muted">Payment Method</p>
                <p>{order.paymentMethod || "Credit Card"}</p>
              </div>
              {order.paymentIntentId && (
                <div>
                  <p className="text-text-muted">Payment ID</p>
                  <p className="text-sm break-all">{order.paymentIntentId}</p>
                </div>
              )}
            </div>
          </Card>

          <Card title="Customer Information">
            <div className="p-4 space-y-4">
              <div>
                <p className="text-text-muted">Customer</p>
                <p>{order.user.displayName || order.user.email}</p>
              </div>
              <div>
                <p className="text-text-muted">Email</p>
                <p>{order.user.email}</p>
              </div>
              <div>
                <p className="text-text-muted">User ID</p>
                <p className="text-sm break-all">{order.userId}</p>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </SalesDashboardLayout>
  );
}
