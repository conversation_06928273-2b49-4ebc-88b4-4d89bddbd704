import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth";
import { generateCSVOnServer } from "@/utils/csv-export";

export const dynamic = 'force-dynamic';

export async function POST(request: Request) {
  try {
    const currentUser = await getCurrentUser(request);

    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Check if user is a land steward or admin
    if (!currentUser.isLandSteward && !currentUser.isAdmin) {
      return NextResponse.json(
        { error: "Land Steward or Admin permissions required" },
        { status: 403 }
      );
    }

    const { fields, filters } = await request.json();

    if (!fields || !Array.isArray(fields) || fields.length === 0) {
      return NextResponse.json(
        { error: "At least one field must be selected for export" },
        { status: 400 }
      );
    }

    // Build where conditions based on filters
    const whereConditions: any = {};
    
    if (filters?.status && filters.status !== 'all') {
      whereConditions.status = filters.status;
    }
    
    if (filters?.search) {
      whereConditions.OR = [
        { name: { contains: filters.search, mode: 'insensitive' } },
        { description: { contains: filters.search, mode: 'insensitive' } },
        { captain: { displayName: { contains: filters.search, mode: 'insensitive' } } },
        { captain: { username: { contains: filters.search, mode: 'insensitive' } } },
      ];
    }

    // Get ships with selected data
    const ships = await prisma.ship.findMany({
      where: whereConditions,
      include: {
        captain: {
          select: {
            id: true,
            username: true,
            displayName: true,
            email: true,
          },
        },
        _count: {
          select: {
            members: {
              where: { status: 'active' },
            },
            deletionRequests: {
              where: { status: 'pending' },
            },
          },
        },
        deletionRequests: {
          where: { status: 'pending' },
          select: {
            id: true,
            reason: true,
            createdAt: true,
          },
          orderBy: { createdAt: 'desc' },
          take: 1,
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    // Transform data based on selected fields
    const exportData = ships.map((ship) => {
      const record: any = {};
      
      fields.forEach((field: string) => {
        switch (field) {
          case 'id':
            record['Ship ID'] = ship.id;
            break;
          case 'name':
            record['Ship Name'] = ship.name;
            break;
          case 'description':
            record['Description'] = ship.description;
            break;
          case 'slogan':
            record['Slogan'] = ship.slogan || '';
            break;
          case 'status':
            record['Status'] = ship.status;
            break;
          case 'captainName':
            record['Captain Name'] = ship.captain.displayName || ship.captain.username;
            break;
          case 'captainUsername':
            record['Captain Username'] = ship.captain.username;
            break;
          case 'captainEmail':
            record['Captain Email'] = ship.captain.email;
            break;
          case 'memberCount':
            record['Active Members'] = ship._count.members;
            break;
          case 'tags':
            record['Tags'] = Array.isArray(ship.tags) ? (ship.tags as string[]).join(', ') : '';
            break;
          case 'createdAt':
            record['Created Date'] = ship.createdAt.toLocaleDateString();
            break;
          case 'updatedAt':
            record['Last Updated'] = ship.updatedAt.toLocaleDateString();
            break;
          case 'hasPendingDeletion':
            record['Has Pending Deletion'] = ship._count.deletionRequests > 0 ? 'Yes' : 'No';
            break;
          case 'deletionReason':
            record['Deletion Reason'] = ship.deletionRequests[0]?.reason || '';
            break;
          default:
            break;
        }
      });
      
      return record;
    });

    // Generate CSV content
    const csvContent = generateCSVOnServer(exportData);

    // Return CSV content with appropriate headers
    const filename = `ships-export-${new Date().toISOString().split('T')[0]}.csv`;

    return new NextResponse(csvContent, {
      status: 200,
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': `attachment; filename="${filename}"`,
      },
    });
  } catch (error) {
    console.error("Error exporting ships:", error);
    return NextResponse.json(
      { error: "Failed to export ships data" },
      { status: 500 }
    );
  }
}