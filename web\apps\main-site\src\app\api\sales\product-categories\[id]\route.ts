import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser, userHasRole } from "@/lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
interface Params {
  params: {
    id: string;
  };
}

// GET /api/sales/product-categories/[id] - Get a specific product category (sales manager)
export async function GET(req: NextRequest, { params }: Params) {
  try {
    const { id } = params;

    // Check if user is authenticated and has sales manager role
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 },
      );
    }

    const hasSalesRole = await userHasRole(req, "salesManager");
    if (!hasSalesRole) {
      return NextResponse.json(
        { error: "Unauthorized - Sales Manager role required" },
        { status: 403 },
      );
    }

    // Get the category
    const category = await prisma.productCategory.findUnique({
      where: { id },
      include: {
        products: true,
        _count: {
          select: {
            products: true,
          },
        },
      },
    });

    if (!category) {
      return NextResponse.json(
        { error: "Category not found" },
        { status: 404 },
      );
    }

    return NextResponse.json({ category });
  } catch (error) {
    console.error("Error fetching product category:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}

// PUT /api/sales/product-categories/[id] - Update a product category (sales manager)
export async function PUT(req: NextRequest, { params }: Params) {
  try {
    const { id } = params;

    // Check if user is authenticated and has sales manager role
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 },
      );
    }

    const hasSalesRole = await userHasRole(req, "salesManager");
    if (!hasSalesRole) {
      return NextResponse.json(
        { error: "Unauthorized - Sales Manager role required" },
        { status: 403 },
      );
    }

    // Get request body
    const { name, description, isActive } = await req.json();

    // Validate required fields
    if (!name) {
      return NextResponse.json({ error: "Name is required" }, { status: 400 });
    }

    // Check if category exists
    const existingCategory = await prisma.productCategory.findUnique({
      where: { id },
    });

    if (!existingCategory) {
      return NextResponse.json(
        { error: "Category not found" },
        { status: 404 },
      );
    }

    // Update the category
    const category = await prisma.productCategory.update({
      where: { id },
      data: {
        name,
        description,
        isActive: isActive !== undefined ? isActive : existingCategory.isActive,
      },
    });

    return NextResponse.json({ category });
  } catch (error) {
    console.error("Error updating product category:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}

// DELETE /api/sales/product-categories/[id] - Delete a product category (sales manager)
export async function DELETE(req: NextRequest, { params }: Params) {
  try {
    const { id } = params;

    // Check if user is authenticated and has sales manager role
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 },
      );
    }

    const hasSalesRole = await userHasRole(req, "salesManager");
    if (!hasSalesRole) {
      return NextResponse.json(
        { error: "Unauthorized - Sales Manager role required" },
        { status: 403 },
      );
    }

    // Check if category exists
    const existingCategory = await prisma.productCategory.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            products: true,
          },
        },
      },
    });

    if (!existingCategory) {
      return NextResponse.json(
        { error: "Category not found" },
        { status: 404 },
      );
    }

    // Check if category has products
    if (existingCategory._count.products > 0) {
      return NextResponse.json(
        { error: "Cannot delete category with associated products" },
        { status: 400 },
      );
    }

    // Delete the category
    await prisma.productCategory.delete({
      where: { id },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting product category:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}
