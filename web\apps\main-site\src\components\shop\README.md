# Shopping Components

E-commerce interface components for the Bank of Styx shopping system.

## Components

- **ProductCard.tsx** - Product display card with image, details, and purchase options
- **ProductSearch.tsx** - Search functionality for finding products
- **CategoryFilter.tsx** - Category filtering component for product browsing
- **CartItemCard.tsx** - Individual cart item display with quantity controls
- **CartHoldTimer.tsx** - Timer component showing remaining hold time for cart items
- **CheckoutForm.tsx** - Checkout form with payment processing integration
- **index.ts** - Component exports for the shopping module

These components provide a complete shopping experience with product browsing, cart management, and secure checkout processing integrated with the ticket hold system.
