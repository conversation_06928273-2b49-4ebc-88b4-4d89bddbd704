// We'll handle server initialization differently
// No need to import a module here that might not exist yet

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  transpilePackages: ["@bank-of-styx/ui"],
  experimental: {
    appDir: true,
  },
  // Configure API routes to be accessible from the correct path
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: '/api/:path*',
      },
    ];
  },
  // Configure allowed image domains with optimized settings to reduce preload warnings
  images: {
    domains: ['cdn.discordapp.com', 'placehold.co'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    formats: ['image/webp'],
    // Reduce aggressive preloading
    unoptimized: false,
    loader: 'default',
    // Disable automatic static optimization for images to reduce preload warnings
    minimumCacheTTL: 60,
  },
  // Reduce resource hints that cause preload warnings
  webpack: (config, { dev, isServer }) => {
    if (!dev && !isServer) {
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          ...config.optimization.splitChunks,
          cacheGroups: {
            ...config.optimization.splitChunks.cacheGroups,
            // Reduce chunk splitting to minimize preload hints
            default: {
              minChunks: 2,
              priority: -20,
              reuseExistingChunk: true,
            },
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendors',
              priority: -10,
              reuseExistingChunk: true,
            },
          },
        },
      };
    }
    return config;
  },
}

module.exports = nextConfig
