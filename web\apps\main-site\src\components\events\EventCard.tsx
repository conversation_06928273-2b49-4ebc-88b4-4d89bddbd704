import React from "react";
import Image from "next/image";
import Link from "next/link";
import { format } from "date-fns";

interface Event {
  id: string;
  name: string;
  shortDescription: string | null;
  startDate: string;
  endDate: string;
  location: string | null;
  isVirtual: boolean;
  image: string | null;
  category: {
    id: string;
    name: string;
    color: string | null;
  };
}

interface EventCardProps {
  event: Event;
  className?: string;
  showFullContent?: boolean;
}

export const EventCard: React.FC<EventCardProps> = ({
  event,
  className = "",
  showFullContent = false,
}) => {
  // Format date range
  const formatDateRange = (startDate: string, endDate: string) => {
    const start = new Date(startDate);
    const end = new Date(endDate);

    // Same day event
    if (start.toDateString() === end.toDateString()) {
      return `${format(start, "MMM d, yyyy")} · ${format(
        start,
        "h:mm a",
      )} - ${format(end, "h:mm a")}`;
    }

    // Multi-day event
    return `${format(start, "MMM d")} - ${format(end, "MMM d, yyyy")}`;
  };

  return (
    <div
      className={`bg-secondary-light rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300 border border-gray-600 max-w-full ${className}`}
    >
      {/* Event image */}
      <div className="relative h-48">
        {event.image ? (
          <Image
            src={event.image}
            alt={event.name}
            fill
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            className="object-cover"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-secondary-light">
            <span className="text-gray-400">No image</span>
          </div>
        )}
        {/* Category badge */}
        <div
          className={`absolute top-4 right-4 px-2 py-1 text-xs font-bold rounded-full bg-primary text-white`}
        >
          {event.category.name}
        </div>
      </div>

      {/* Event content */}
      <div className="p-4 flex-grow">
        <h3 className="text-lg font-bold mb-2 text-white">{event.name}</h3>
        <p className="text-sm mb-4 line-clamp-2 text-gray-400">
          {event.shortDescription || event.name}
        </p>

        {/* Event details */}
        <div className="space-y-2 mb-4">
          {/* Date and time */}
          <div className="flex items-start">
            <svg
              className="h-5 w-5 mt-0.5 mr-2 flex-shrink-0 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
            <div className="text-sm text-gray-400">
              {formatDateRange(event.startDate, event.endDate)}
            </div>
          </div>

          {/* Location */}
          <div className="flex items-start">
            <svg
              className="h-5 w-5 mt-0.5 mr-2 flex-shrink-0 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
              />
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
              />
            </svg>
            <div className="text-sm text-gray-400">
              {event.location || "Virtual Event"}
            </div>
          </div>
        </div>
      </div>

      {/* Event footer */}
      <div className="px-4 py-3 border-t border-gray-600 bg-secondary-light">
        <Link
          href={`/events/${event.id}`}
          className="text-sm font-medium text-primary hover:text-primary-light"
        >
          View Details →
        </Link>
      </div>
    </div>
  );
};
