-- CreateTable
CREATE TABLE `User` (
    `id` VA<PERSON>HA<PERSON>(191) NOT NULL,
    `username` VA<PERSON><PERSON><PERSON>(191) NOT NULL,
    `displayName` VARCHAR(191) NOT NULL,
    `email` VARCHAR(191) NOT NULL,
    `passwordHash` VARCHAR(191) NOT NULL,
    `avatar` VARCHAR(191) NOT NULL DEFAULT '/images/avatars/default.png',
    `balance` DOUBLE NOT NULL DEFAULT 0,
    `isEmailVerified` BOOLEAN NOT NULL DEFAULT false,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `defaultView` VARCHAR(191) NOT NULL DEFAULT 'dashboard',
    `notifyTransfers` BOOLEAN NOT NULL DEFAULT true,
    `notifyDeposits` BOOLEAN NOT NULL DEFAULT true,
    `notifyWithdrawals` BOOLEAN NOT NULL DEFAULT true,
    `notifyNewsEvents` <PERSON><PERSON><PERSON><PERSON>N NOT NULL DEFAULT false,
    `discordConnected` <PERSON><PERSON><PERSON><PERSON>N NOT NULL DEFAULT false,
    `discordId` VARCHAR(191) NULL,
    `facebookConnected` BOOLEAN NOT NULL DEFAULT false,
    `facebookId` VARCHAR(191) NULL,
    `merchantStatus` VARCHAR(191) NULL DEFAULT 'none',
    `merchantId` INTEGER NULL,
    `merchantSlug` VARCHAR(191) NULL,
    `hasCreatedAuctions` BOOLEAN NOT NULL DEFAULT false,
    `auctionCount` INTEGER NOT NULL DEFAULT 0,
    `isAdmin` BOOLEAN NOT NULL DEFAULT false,
    `isEditor` BOOLEAN NOT NULL DEFAULT false,
    `isBanker` BOOLEAN NOT NULL DEFAULT false,
    `isChatModerator` BOOLEAN NOT NULL DEFAULT false,

    UNIQUE INDEX `User_username_key`(`username`),
    UNIQUE INDEX `User_email_key`(`email`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
