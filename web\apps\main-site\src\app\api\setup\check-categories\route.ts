import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";

// This is a temporary endpoint to check if categories exist
export async function GET() {
  try {
    const categories = await prisma.newsCategory.findMany();

    return NextResponse.json({
      success: true,
      count: categories.length,
      categories,
    });
  } catch (error) {
    console.error("Error fetching categories:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch categories",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    );
  }
}
