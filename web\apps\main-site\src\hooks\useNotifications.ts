import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  getUserNotifications,
  markNotificationsAsRead,
  updateNotificationPreferences,
  getNotificationPreferences,
  Notification,
} from "../services/notificationService";

// Notification query keys
export const notificationQueryKeys = {
  all: ["notifications"] as const,
  preferences: ["notificationPreferences"] as const,
};

// Get user notifications hook
export function useUserNotifications(params?: {
  limit?: number;
  unreadOnly?: boolean;
  category?: string;
}) {
  return useQuery({
    queryKey: [...notificationQueryKeys.all, params],
    queryFn: () => getUserNotifications(params),
  });
}

// Mark notifications as read hook
export function useMarkNotificationsAsRead() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: { ids?: string[]; all?: boolean }) =>
      markNotificationsAsRead(data),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: notificationQueryKeys.all,
      });
    },
  });
}

// Get notification preferences hook
export function useNotificationPreferences() {
  return useQuery({
    queryKey: notificationQueryKeys.preferences,
    queryFn: getNotificationPreferences,
  });
}

// Update notification preferences hook
export function useUpdateNotificationPreferences() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updateNotificationPreferences,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: notificationQueryKeys.preferences,
      });
    },
  });
}
