/**
 * @file c:\Users\<USER>\projects\Bank-of-styx-website\web\apps\api\auth\register\route.ts
 * @summary Defines the API route handler for user registration (`/api/auth/register`). It handles POST requests to create new users, validates input, checks for existing users, hashes passwords, stores user data in the database, and returns the new user's details along with a JWT token. Also handles OPTIONS requests for CORS preflight.
 */
import { NextRequest, NextResponse } from "next/server";
import bcrypt from "bcryptjs";
import prisma from "../../../../apps/main-site/src/lib/prisma";
import jwt from "jsonwebtoken";
import { handleOptionRequest } from "../../middleware";

const JWT_SECRET = process.env.JWT_SECRET || "your-development-secret-key";

/**
 * Handles OPTIONS requests for the registration endpoint.
 * @param {NextRequest} req - The incoming request object.
 * @returns {Promise<NextResponse | null>} A response suitable for CORS preflight.
 */
export const OPTIONS = async (req: NextRequest) => {
  return handleOptionRequest(req);
};

/**
 * Handles POST requests for user registration.
 * @param {NextRequest} req - The incoming request object, expected to contain username, email, and password in the JSON body.
 * @returns {Promise<NextResponse>} A JSON response containing the new user object and a JWT token on success, or an error response.
 */
export const POST = async (req: NextRequest) => {
  try {
    const { username, email, password } = await req.json();

    // Validate input
    if (!username || !email || !password) {
      return NextResponse.json(
        { error: "Username, email and password are required" },
        { status: 400 },
      );
    }

    // Check if user already exists
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [{ email }, { username }],
      },
    });

    if (existingUser) {
      return NextResponse.json(
        { error: "User with this email or username already exists" },
        { status: 409 },
      );
    }

    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Create user in database
    const user = await prisma.user.create({
      data: {
        username,
        email,
        displayName: username, // Default to username initially
        passwordHash: hashedPassword,
      },
    });

    // Create JWT token
    const token = jwt.sign({ id: user.id, email: user.email }, JWT_SECRET, {
      expiresIn: "7d",
    });

    // Return user data (excluding password) and token
    return NextResponse.json({
      user: {
        id: user.id,
        username: user.username,
        displayName: user.displayName,
        email: user.email,
        avatar: user.avatar,
        balance: user.balance,
        isEmailVerified: user.isEmailVerified,
        preferences: {
          defaultView: user.defaultView,
          notifications: {
            transfers: user.notifyTransfers,
            deposits: user.notifyDeposits,
            withdrawals: user.notifyWithdrawals,
            newsAndEvents: user.notifyNewsEvents,
          },
        },
        connectedAccounts: {
          discord: user.discordConnected,
          discordId: user.discordId,
          facebook: user.facebookConnected,
          facebookId: user.facebookId,
        },
        merchant: {
          status: user.merchantStatus,
          merchantId: user.merchantId,
          slug: user.merchantSlug,
        },
        auctions: {
          hasCreated: user.hasCreatedAuctions,
          auctionCount: user.auctionCount,
        },
        roles: {
          admin: user.isAdmin,
          editor: user.isEditor,
          banker: user.isBanker,
          chatModerator: user.isChatModerator,
          volunteerCoordinator: user.isVolunteerCoordinator,
          leadManager: user.isLeadManager,
        },
      },
      token,
    });
  } catch (error) {
    console.error("Registration error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
};
