import { NextRequest, NextResponse } from "next/server";
import prisma from "../../../../../../lib/prisma";
import jwt from "jsonwebtoken";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
const JWT_SECRET = process.env.JWT_SECRET || "your-development-secret-key";

// Helper function to verify token
function verifyToken(token: string) {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    return null;
  }
}

export const PATCH = async (
  req: NextRequest,
  { params }: { params: { id: string } },
) => {
  try {
    const id = params.id;

    // Get authorization header
    const authHeader = req.headers.get("authorization");

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Extract token
    const token = authHeader.split(" ")[1];

    // Verify token
    const decoded = verifyToken(token);

    if (!decoded || typeof decoded !== "object" || !decoded.id) {
      return NextResponse.json({ error: "Invalid token" }, { status: 401 });
    }

    const userId = decoded.id as string;

    // Check if user is a banker
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user || !user.isBanker) {
      return NextResponse.json(
        { error: "Unauthorized - Banker role required" },
        { status: 403 },
      );
    }

    // Find the ledger entry
    const ledgerEntry = await prisma.ledger.findUnique({
      where: { id },
    });

    if (!ledgerEntry) {
      return NextResponse.json(
        { error: "Ledger entry not found" },
        { status: 404 },
      );
    }

    // Update the ledger entry to verified status
    const updatedLedgerEntry = await prisma.ledger.update({
      where: { id },
      data: {
        status: "verified",
        verifiedById: userId,
        verifiedAt: new Date(),
      },
      include: {
        verifiedBy: {
          select: {
            id: true,
            username: true,
            displayName: true,
          },
        },
      },
    });

    // Format dates for response
    const formattedLedgerEntry = {
      ...updatedLedgerEntry,
      date: updatedLedgerEntry.date.toISOString(),
      verifiedAt: updatedLedgerEntry.verifiedAt
        ? updatedLedgerEntry.verifiedAt.toISOString()
        : null,
    };

    return NextResponse.json(formattedLedgerEntry);
  } catch (error) {
    console.error("Error verifying ledger entry:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
};
