import React, { useState, useCallback } from "react";
import { <PERSON><PERSON> } from "../button";
import { Modal } from "../modal";
import { Input, Textarea, Checkbox } from "../input";
import type { FormField, FormTemplate, FormBuilderProps } from "./types";

const FIELD_TYPES = [
  {
    type: "text" as const,
    label: "Text Input",
    description: "Single line text input",
    icon: "📝",
  },
  {
    type: "textarea" as const,
    label: "Text Area",
    description: "Multi-line text input",
    icon: "📄",
  },
  {
    type: "select" as const,
    label: "Dropdown",
    description: "Single selection dropdown",
    icon: "📋",
  },
  {
    type: "checkbox" as const,
    label: "Checkbox",
    description: "Yes/No checkbox",
    icon: "☑️",
  },
  {
    type: "multi_select" as const,
    label: "Multi-Select",
    description: "Multiple selection checkboxes",
    icon: "📦",
  },
  {
    type: "file_upload" as const,
    label: "File Upload",
    description: "File attachment field",
    icon: "📎",
  },
  {
    type: "user_select" as const,
    label: "User Selection",
    description: "Select a single user",
    icon: "👤",
  },
  {
    type: "multi_user_select" as const,
    label: "Multi-User Selection",
    description: "Select multiple users",
    icon: "👥",
  },
  {
    type: "volunteer_hours" as const,
    label: "Volunteer Hours Requirement",
    description: "Informational element - shows required volunteer hours (cannot be required)",
    icon: "⏰",
  },
];

export const FormBuilder: React.FC<FormBuilderProps> = ({
  initialTemplate,
  onSave,
  onCancel,
  isEditing = false,
}) => {
  const [template, setTemplate] = useState<FormTemplate>(
    initialTemplate || {
      name: "",
      description: "",
      fields: [],
    }
  );

  const [selectedFieldIndex, setSelectedFieldIndex] = useState<number | null>(null);
  const [showFieldModal, setShowFieldModal] = useState(false);
  const [editingField, setEditingField] = useState<FormField | null>(null);

  // Generate unique field ID
  const generateFieldId = useCallback(() => {
    return `field_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }, []);

  // Add new field
  const addField = useCallback((fieldType: FormField["type"]) => {
    const newField: FormField = {
      id: generateFieldId(),
      type: fieldType,
      label: `New ${FIELD_TYPES.find(t => t.type === fieldType)?.label || "Field"}`,
      required: false,
    };

    if (fieldType === "select" || fieldType === "multi_select") {
      newField.options = ["Option 1", "Option 2"];
    }
    
    if (fieldType === "volunteer_hours") {
      newField.volunteerHours = 1; // Default to 1 hour
      newField.required = false; // Volunteer hours fields cannot be required
    }

    const newFields = [...template.fields];
    if (selectedFieldIndex !== null) {
      // Insert after selected field
      newFields.splice(selectedFieldIndex + 1, 0, newField);
    } else {
      // Add to end
      newFields.push(newField);
    }

    setTemplate(prev => ({ ...prev, fields: newFields }));
    setSelectedFieldIndex(selectedFieldIndex !== null ? selectedFieldIndex + 1 : newFields.length - 1);
  }, [template.fields, selectedFieldIndex, generateFieldId]);

  // Edit field
  const editField = useCallback((index: number) => {
    setEditingField({ ...template.fields[index] });
    setShowFieldModal(true);
  }, [template.fields]);

  // Save field changes
  const saveFieldChanges = useCallback((updatedField: FormField) => {
    const fieldIndex = template.fields.findIndex(f => f.id === updatedField.id);
    if (fieldIndex !== -1) {
      // Ensure volunteer_hours fields cannot be required
      if (updatedField.type === "volunteer_hours") {
        updatedField.required = false;
      }
      
      const newFields = [...template.fields];
      newFields[fieldIndex] = updatedField;
      setTemplate(prev => ({ ...prev, fields: newFields }));
    }
    setEditingField(null);
    setShowFieldModal(false);
  }, [template.fields]);

  // Delete field
  const deleteField = useCallback((index: number) => {
    const newFields = template.fields.filter((_, i) => i !== index);
    setTemplate(prev => ({ ...prev, fields: newFields }));
    setSelectedFieldIndex(null);
  }, [template.fields]);

  // Move field up/down
  const moveField = useCallback((index: number, direction: "up" | "down") => {
    const newFields = [...template.fields];
    const targetIndex = direction === "up" ? index - 1 : index + 1;
    
    if (targetIndex >= 0 && targetIndex < newFields.length) {
      [newFields[index], newFields[targetIndex]] = [newFields[targetIndex], newFields[index]];
      setTemplate(prev => ({ ...prev, fields: newFields }));
      setSelectedFieldIndex(targetIndex);
    }
  }, [template.fields]);

  // Handle save
  const handleSave = useCallback(() => {
    if (!template.name.trim()) {
      alert("Please enter a form name");
      return;
    }
    onSave(template);
  }, [template, onSave]);

  return (
    <div className="h-full flex bg-secondary-dark">
      {/* Form Preview Panel - Left */}
      <div className="flex-1 p-6 overflow-y-auto">
        <div className="bg-secondary-light rounded-lg p-6 max-w-2xl">
          {/* Form Header */}
          <div className="mb-6">
            <Input
              type="text"
              placeholder="Form Name"
              value={template.name}
              onChange={(e) => setTemplate(prev => ({ ...prev, name: e.target.value }))}
              className="text-xl font-bold mb-3"
            />
            <Textarea
              placeholder="Form Description (optional)"
              value={template.description || ""}
              onChange={(e) => setTemplate(prev => ({ ...prev, description: e.target.value }))}
              rows={2}
            />
          </div>

          {/* Form Fields */}
          <div className="space-y-4">
            {template.fields.length === 0 ? (
              <div className="text-center py-12 text-text-secondary">
                <p className="text-lg mb-2">No fields added yet</p>
                <p>Select a field type from the right panel to get started</p>
              </div>
            ) : (
              template.fields.map((field, index) => (
                <div
                  key={field.id}
                  className={`
                    border rounded-lg p-4 cursor-pointer transition-colors
                    ${selectedFieldIndex === index 
                      ? "border-primary bg-primary/10" 
                      : "border-gray-600 hover:border-gray-500"
                    }
                  `}
                  onClick={() => setSelectedFieldIndex(index)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <span className="text-sm bg-secondary px-2 py-1 rounded">
                        {FIELD_TYPES.find(t => t.type === field.type)?.label}
                      </span>
                      {field.required && (
                        <span className="text-accent text-sm">*</span>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={(e) => {
                          e.stopPropagation();
                          moveField(index, "up");
                        }}
                        disabled={index === 0}
                      >
                        ↑
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={(e) => {
                          e.stopPropagation();
                          moveField(index, "down");
                        }}
                        disabled={index === template.fields.length - 1}
                      >
                        ↓
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={(e) => {
                          e.stopPropagation();
                          editField(index);
                        }}
                      >
                        ✏️
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={(e) => {
                          e.stopPropagation();
                          deleteField(index);
                        }}
                      >
                        🗑️
                      </Button>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">
                      {field.label}
                    </label>
                    {field.description && (
                      <p className="text-xs text-text-secondary mb-2">{field.description}</p>
                    )}
                    <FormFieldPreview field={field} />
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>

      {/* Field Selection Panel - Right */}
      <div className="w-80 bg-secondary border-l border-gray-600 p-6 overflow-y-auto">
        <h3 className="text-lg font-bold mb-4">Add Field</h3>
        <div className="space-y-3">
          {FIELD_TYPES.map((fieldType) => (
            <div
              key={fieldType.type}
              className="border border-gray-600 rounded-lg p-3 hover:border-primary cursor-pointer transition-colors"
              onClick={() => addField(fieldType.type)}
            >
              <div className="flex items-center gap-3 mb-2">
                <span className="text-lg">{fieldType.icon}</span>
                <h4 className="font-medium">{fieldType.label}</h4>
              </div>
              <p className="text-sm text-text-secondary">{fieldType.description}</p>
            </div>
          ))}
        </div>

        {selectedFieldIndex !== null && (
          <div className="mt-6 pt-6 border-t border-gray-600">
            <h4 className="font-medium mb-2">Insert Position</h4>
            <p className="text-sm text-text-secondary">
              New fields will be inserted after the selected field
            </p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="mt-8 space-y-3">
          <Button
            variant="primary"
            fullWidth
            onClick={handleSave}
            disabled={!template.name.trim()}
          >
            {isEditing ? "Update Form" : "Save Form"}
          </Button>
          <Button
            variant="outline"
            fullWidth
            onClick={onCancel}
          >
            Cancel
          </Button>
        </div>
      </div>

      {/* Field Edit Modal */}
      {showFieldModal && editingField && (
        <FieldEditModal
          field={editingField}
          onSave={saveFieldChanges}
          onCancel={() => {
            setEditingField(null);
            setShowFieldModal(false);
          }}
        />
      )}
    </div>
  );
};

// Form Field Preview Component
const FormFieldPreview: React.FC<{ field: FormField }> = ({ field }) => {
  switch (field.type) {
    case "text":
      return <Input type="text" placeholder={field.label} disabled />;
    case "textarea":
      return <Textarea placeholder={field.label} rows={3} disabled />;
    case "select":
      return (
        <select className="w-full p-2 border border-gray-600 rounded bg-input text-text-primary" disabled>
          <option>Select an option...</option>
          {field.options?.map((option, i) => (
            <option key={i}>{option}</option>
          ))}
        </select>
      );
    case "checkbox":
      return (
        <label className="flex items-center gap-2">
          <Checkbox disabled />
          <span className="text-sm">{field.label}</span>
        </label>
      );
    case "multi_select":
      return (
        <div className="space-y-2">
          {field.options?.map((option, i) => (
            <label key={i} className="flex items-center gap-2">
              <Checkbox disabled />
              <span className="text-sm">{option}</span>
            </label>
          ))}
        </div>
      );
    case "file_upload":
      return (
        <div className="border-2 border-dashed border-gray-600 rounded-lg p-4 text-center">
          <span className="text-sm text-text-secondary">Click to upload file</span>
        </div>
      );
    case "user_select":
    case "multi_user_select":
      return (
        <div className="border border-gray-600 rounded p-2 bg-input">
          <span className="text-sm text-text-secondary">Search for users...</span>
        </div>
      );
    case "volunteer_hours":
      return (
        <div className="border border-gray-600 rounded p-3 bg-blue-50 dark:bg-blue-900/20">
          <div className="flex items-center gap-2 mb-2">
            <span className="text-lg">⏰</span>
            <span className="font-medium text-sm">Volunteer Hours Required</span>
          </div>
          <span className="text-sm text-text-secondary">
            {field.volunteerHours ? `${field.volunteerHours} hours required` : "Hours not configured"}
          </span>
        </div>
      );
    default:
      return <div className="text-sm text-text-secondary">Preview not available</div>;
  }
};

// Field Edit Modal Component
const FieldEditModal: React.FC<{
  field: FormField;
  onSave: (field: FormField) => void;
  onCancel: () => void;
}> = ({ field, onSave, onCancel }) => {
  const [editedField, setEditedField] = useState<FormField>({ ...field });

  const handleSave = () => {
    if (!editedField.label.trim()) {
      alert("Please enter a field label");
      return;
    }
    onSave(editedField);
  };

  const updateOptions = (options: string[]) => {
    setEditedField(prev => ({ ...prev, options }));
  };

  const addOption = () => {
    const currentOptions = editedField.options || [];
    updateOptions([...currentOptions, `Option ${currentOptions.length + 1}`]);
  };

  const removeOption = (index: number) => {
    const currentOptions = editedField.options || [];
    updateOptions(currentOptions.filter((_, i) => i !== index));
  };

  const updateOption = (index: number, value: string) => {
    const currentOptions = editedField.options || [];
    const newOptions = [...currentOptions];
    newOptions[index] = value;
    updateOptions(newOptions);
  };

  return (
    <Modal
      isOpen={true}
      onClose={onCancel}
      title="Edit Field"
      size="lg"
      footer={
        <div className="flex gap-2">
          <Button variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button variant="primary" onClick={handleSave}>
            Save Changes
          </Button>
        </div>
      }
    >
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-1">Field Label</label>
          <Input
            type="text"
            value={editedField.label}
            onChange={(e) => setEditedField(prev => ({ ...prev, label: e.target.value }))}
            placeholder="Enter field label"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">Description</label>
          <Textarea
            value={editedField.description || ""}
            onChange={(e) => setEditedField(prev => ({ ...prev, description: e.target.value }))}
            placeholder="Optional field description"
            rows={2}
          />
        </div>

        {editedField.type !== "volunteer_hours" && (
          <div>
            <label className="flex items-center gap-2">
              <Checkbox
                checked={editedField.required || false}
                onChange={(e) => setEditedField(prev => ({ ...prev, required: e.target.checked }))}
              />
              <span className="text-sm">Required field</span>
            </label>
          </div>
        )}

        {(editedField.type === "select" || editedField.type === "multi_select") && (
          <div>
            <label className="block text-sm font-medium mb-2">Options</label>
            <div className="space-y-2">
              {(editedField.options || []).map((option, index) => (
                <div key={index} className="flex gap-2">
                  <Input
                    type="text"
                    value={option}
                    onChange={(e) => updateOption(index, e.target.value)}
                    placeholder={`Option ${index + 1}`}
                  />
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => removeOption(index)}
                  >
                    Remove
                  </Button>
                </div>
              ))}
              <Button
                size="sm"
                variant="outline"
                onClick={addOption}
              >
                Add Option
              </Button>
            </div>
          </div>
        )}

        {editedField.type === "text" && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1">Minimum Length</label>
              <Input
                type="number"
                value={editedField.validation?.minLength || ""}
                onChange={(e) => setEditedField(prev => ({
                  ...prev,
                  validation: {
                    ...prev.validation,
                    minLength: e.target.value ? parseInt(e.target.value) : undefined
                  }
                }))}
                placeholder="0"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Maximum Length</label>
              <Input
                type="number"
                value={editedField.validation?.maxLength || ""}
                onChange={(e) => setEditedField(prev => ({
                  ...prev,
                  validation: {
                    ...prev.validation,
                    maxLength: e.target.value ? parseInt(e.target.value) : undefined
                  }
                }))}
                placeholder="No limit"
              />
            </div>
          </div>
        )}

        {editedField.type === "volunteer_hours" && (
          <div>
            <label className="block text-sm font-medium mb-1">Required Volunteer Hours</label>
            <Input
              type="number"
              value={editedField.volunteerHours || ""}
              onChange={(e) => setEditedField(prev => ({
                ...prev,
                volunteerHours: e.target.value ? parseFloat(e.target.value) : undefined
              }))}
              placeholder="Enter number of hours"
              min="0"
              step="0.5"
            />
            <div className="text-xs text-text-secondary mt-1">
              This will require ships to complete volunteer hours when their form is approved
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
};

FormBuilder.displayName = "FormBuilder";