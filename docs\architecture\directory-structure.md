# Bank of Styx - Directory Structure

## Overview
This document provides a comprehensive overview of the Bank of Styx website directory structure, based on the complete file tree. The project uses a monorepo structure with Next.js App Router architecture.

## Root Structure
```
project-root/
├── web/                     # Main monorepo workspace
│   ├── apps/               # Application packages
│   │   ├── api/           # Standalone API modules
│   │   └── main-site/     # Primary Next.js application
│   ├── packages/          # Shared packages
│   │   ├── config/       # Shared configuration
│   │   └── ui/           # Shared UI component library
│   ├── package.json      # Workspace configuration
│   └── pnpm-workspace.yaml # PNPM workspace definition
├── docs/                   # Project documentation
│   ├── features/          # Feature system documentation
│   ├── api/              # API documentation  
│   ├── components/       # UI component documentation
│   └── architecture/     # Architecture documentation
├── context/               # Critical system documentation
├── CLAUDE.md             # Development guidelines
├── PROJECT-STATUS.md     # Current project status
└── pnpm-lock.yaml       # Dependency lock file
```

## Main Application Structure (`apps/main-site/`)

### Configuration & Setup
```
main-site/
├── .env.example           # Environment variables template
├── .env.local.example     # Local development template
├── .env.production        # Production environment
├── next.config.js         # Next.js configuration
├── package.json           # Dependencies and scripts
├── postcss.config.js      # PostCSS configuration
├── prisma/                # Database configuration
└── middleware.ts          # Next.js middleware
```

### Database Layer (`prisma/`)
```
prisma/
├── schema.prisma          # Database schema definition
├── seed.js               # Database seeding script
├── dbsetup.sql           # Initial database setup
└── migrations/           # Database migration history
    ├── 20250426214838_init/
    ├── 20250427154050_news_models/
    ├── 20250428021018_add_bank_models/
    └── [50+ migration files] # Complete migration history
```

### Source Code (`src/`)

#### Application Layer (`src/app/`)
Using Next.js App Router structure:

```
src/app/
├── globals.css           # Global styles
├── layout.tsx           # Root layout
├── page.tsx             # Homepage
├── about/               # About page
├── help/                # Help documentation
└── rules/               # Community rules
```

#### Feature-Based Organization

**Administrative System**
```
src/app/admin/
├── dashboard/           # Admin main dashboard
│   ├── featured/       # Featured content management
│   ├── tickets/        # Support ticket management
│   └── users/          # User management
├── event-categories/    # Event category management
└── events/             # Event management
    ├── [id]/          # Individual event pages
    └── new/           # Event creation
```

**Authentication System**
```
src/app/auth/
├── discord/            # Discord OAuth integration
│   ├── callback/      # OAuth callback handling
│   └── error/         # OAuth error handling
```

**Banking System**
```
src/app/bank/
└── dashboard/          # Main banking interface
    ├── deposit/        # Deposit functionality
    ├── donate/         # Donation system
    ├── pay-code/       # Pay code management
    ├── settings/       # Banking preferences
    ├── transactions/   # Transaction history
    ├── transfer/       # Money transfers
    └── withdraw/       # Withdrawal system
```

**Cashier System**
```
src/app/cashier/
└── dashboard/          # Cashier management interface
    ├── deposits/       # Deposit processing
    ├── ledger/         # Financial ledger
    ├── members/        # Member account management
    │   └── [id]/      # Individual member pages
    ├── statistics/     # Financial statistics
    ├── transactions/   # Transaction processing
    └── withdrawals/    # Withdrawal processing
```

**Ship Management System**
```
src/app/ships/          # Public ship directory
    ├── [id]/          # Individual ship pages
    └── apply/         # Ship application process

src/app/captain/        # Captain-only features
└── dashboard/          # Captain management interface
    ├── forms/         # Form management
    ├── invite/        # Member invitations
    ├── members/       # Crew management
    ├── roles/         # Role management
    └── settings/      # Ship settings

src/app/land-steward/   # Land Steward features
├── applications/       # Ship application review
└── volunteer-requirements/ # Volunteer hour tracking
```

**Volunteer System**
```
src/app/volunteer/      # Public volunteer interface
├── dashboard/          # Volunteer dashboard
│   ├── categories/    # Volunteer categories
│   ├── category-lead-view/ # Lead manager view
│   ├── payments/      # Payment tracking
│   └── shifts/        # Shift management
└── lead/              # Lead manager features
    └── dashboard/     # Lead management interface
```

**Shopping & Sales System**
```
src/app/shop/           # Public shopping interface
├── cart/              # Shopping cart
├── checkout/          # Checkout process
│   └── success/       # Order confirmation
├── orders/            # Order history
│   └── [id]/         # Individual order pages
├── products/          # Product browsing
│   └── [id]/         # Product details
└── search/            # Product search

src/app/sales/          # Sales management
├── categories/         # Category management
│   ├── [id]/         # Category details
│   └── create/       # Category creation
├── dashboard/         # Sales overview
├── orders/           # Order management
│   └── [id]/        # Order details
└── products/         # Product management
    ├── [id]/        # Product details
    └── create/      # Product creation
```

**News & Content System**
```
src/app/news/           # Public news interface
├── [slug]/            # Article pages
└── dashboard/         # Content management
    ├── articles/      # Article management
    │   ├── [id]/     # Article editing
    │   └── new/      # Article creation
    ├── categories/    # Category management
    └── featured/      # Featured content
```

**User Management**
```
src/app/settings/       # User preferences
├── colors/            # Theme settings
└── notifications/     # Notification preferences
```

#### API Layer (`src/app/api/`)

The API follows RESTful conventions with feature-based organization:

**Core APIs**
```
src/app/api/
├── admin/             # Administrative endpoints
│   ├── dashboard/     # Admin dashboard data
│   ├── events/        # Event management
│   ├── featured/      # Featured content
│   └── users/         # User management
├── auth/              # Authentication endpoints
│   ├── discord/       # Discord OAuth
│   ├── login/         # Login endpoint
│   ├── register/      # Registration
│   └── verify-email/  # Email verification
├── bank/              # Banking system APIs
│   ├── cashier/       # Cashier endpoints
│   ├── pay-codes/     # Pay code management
│   ├── transactions/  # Transaction processing
│   └── user/          # User banking data
└── [feature]/         # Other feature APIs
```

#### Component Library (`src/components/`)
```
src/components/
├── admin/             # Admin-specific components
├── auth/              # Authentication components
├── bank/              # Banking components
├── captain/           # Captain dashboard components
├── cashier/           # Cashier interface components
├── common/            # Shared common components
├── events/            # Event-related components
├── layout/            # Layout components
├── news/              # News system components
├── notifications/     # Notification components
├── sales/             # Sales system components
├── settings/          # Settings components
├── shared/            # Cross-feature shared components
├── ships/             # Ship-related components
├── shop/              # Shopping components
├── ui/                # Basic UI components
├── upload/            # File upload components
├── user/              # User profile components
└── volunteer/         # Volunteer system components
    ├── lead/         # Lead manager components
    └── public/       # Public volunteer components
```

#### Supporting Directories
```
src/
├── contexts/          # React contexts
├── hooks/             # Custom React hooks
│   └── captain/      # Captain-specific hooks
├── lib/              # Utility libraries
├── providers/        # React providers
├── scripts/          # Utility scripts
├── services/         # API service layers
├── tests/            # Test files
├── types/            # TypeScript type definitions
└── utils/            # Utility functions
```

### Public Assets (`public/`)
```
public/
├── images/            # Static images
│   ├── avatars/      # Avatar images
│   └── icons/        # Icon assets
└── uploads/           # User-generated content
    ├── avatars/      # User avatars
    ├── deposits/     # Deposit receipts
    ├── general/      # General uploads
    ├── news/         # News article images
    ├── products/     # Product images
    └── ships/        # Ship logos and images
```

### Scripts (`scripts/`)
```
scripts/              # Utility and maintenance scripts
```

## Shared Packages (`packages/`)

### UI Component Library (`packages/ui/`)
```
packages/ui/src/
├── button/           # Button component
├── card/             # Card component
├── content-card/     # Content card component
├── editor/           # Rich text editor
├── featured-content/ # Featured content display
├── form-builder/     # Dynamic form builder
├── hero/             # Hero section component
├── input/            # Input components
├── modal/            # Modal dialog component
├── pagination/       # Pagination component
├── scroll-to-top/    # Scroll to top button
├── search-bar/       # Search interface
├── sidebar/          # Sidebar navigation
└── spinner/          # Loading spinner
```

### Configuration (`packages/config/`)
```
packages/config/      # Shared configuration files
```

## File Naming Conventions

### Pages & API Routes
- **Pages**: `page.tsx` (App Router convention)
- **Layouts**: `layout.tsx`
- **API Routes**: `route.ts`
- **Dynamic Routes**: `[param]/page.tsx`
- **Catch-all Routes**: `[...slug]/page.tsx`

### Components
- **React Components**: PascalCase (e.g., `UserProfile.tsx`)
- **Component Directories**: kebab-case (e.g., `user-profile/`)
- **Index Files**: `index.ts` for re-exports

### Utilities & Services
- **Services**: camelCase with Service suffix (e.g., `userService.ts`)
- **Utilities**: camelCase (e.g., `dateUtils.ts`)
- **Hooks**: camelCase with use prefix (e.g., `useAuth.ts`)
- **Types**: camelCase (e.g., `userTypes.ts`)

## Key Architecture Patterns

### Monorepo Structure
- **Apps**: Independent deployable applications
- **Packages**: Shared code between applications
- **Workspace Management**: PNPM for dependency management

### Next.js App Router
- **File-based Routing**: Directory structure defines routes
- **Server Components**: Default server-side rendering
- **Client Components**: Explicit "use client" directive
- **API Routes**: Co-located with pages

### Feature-Based Organization
- **Domain-Driven Design**: Features organized by business domain
- **Separation of Concerns**: Clear separation between frontend/backend
- **Shared Components**: Reusable across features

### Database-First Approach
- **Prisma ORM**: Type-safe database access
- **Migration-Based**: Version-controlled schema changes
- **Model Relationships**: Explicit foreign key relationships

## Development Workflow

### Adding New Features
1. Create feature directory in `src/app/[feature]/`
2. Add API endpoints in `src/app/api/[feature]/`
3. Create components in `src/components/[feature]/`
4. Add types in `src/types/`
5. Create services in `src/services/`
6. Update documentation

### Database Changes
1. Modify `prisma/schema.prisma`
2. Generate migration: `npx prisma migrate dev`
3. Update types: `npx prisma generate`
4. Update services and components as needed

---

**Last Updated**: 2025-01-07  
**Total Files**: 400+  
**Total Directories**: 200+