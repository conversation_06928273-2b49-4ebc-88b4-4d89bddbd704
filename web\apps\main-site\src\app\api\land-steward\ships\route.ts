import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth";

export const dynamic = 'force-dynamic';

export async function GET(request: Request) {
  try {
    const currentUser = await getCurrentUser(request);

    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Check if user is a land steward or admin
    if (!currentUser.isLandSteward && !currentUser.isAdmin) {
      return NextResponse.json(
        { error: "Land Steward or Admin permissions required" },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const search = searchParams.get('search');
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '20', 10);
    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions: any = {};
    
    if (status && status !== 'all') {
      whereConditions.status = status;
    }
    
    if (search) {
      whereConditions.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { captain: { displayName: { contains: search, mode: 'insensitive' } } },
        { captain: { username: { contains: search, mode: 'insensitive' } } },
      ];
    }

    // Get ships with pagination
    const [ships, totalCount] = await Promise.all([
      prisma.ship.findMany({
        where: whereConditions,
        include: {
          captain: {
            select: {
              id: true,
              username: true,
              displayName: true,
              avatar: true,
              email: true,
            },
          },
          _count: {
            select: {
              members: {
                where: { status: 'active' },
              },
              deletionRequests: {
                where: { status: 'pending' },
              },
            },
          },
          deletionRequests: {
            where: { status: 'pending' },
            select: {
              id: true,
              reason: true,
              createdAt: true,
              requestedBy: {
                select: {
                  id: true,
                  username: true,
                  displayName: true,
                },
              },
            },
            orderBy: { createdAt: 'desc' },
            take: 1,
          },
        },
        orderBy: [
          { status: 'asc' }, // pending_deletion first
          { createdAt: 'desc' },
        ],
        skip: offset,
        take: limit,
      }),
      prisma.ship.count({ where: whereConditions }),
    ]);

    const formattedShips = ships.map((ship) => ({
      id: ship.id,
      name: ship.name,
      description: ship.description,
      slogan: ship.slogan,
      logo: ship.logo,
      tags: ship.tags as string[] || [],
      status: ship.status,
      createdAt: ship.createdAt.toISOString(),
      updatedAt: ship.updatedAt.toISOString(),
      captain: ship.captain,
      memberCount: ship._count.members,
      hasPendingDeletion: ship._count.deletionRequests > 0,
      pendingDeletionRequest: ship.deletionRequests[0] ? {
        id: ship.deletionRequests[0].id,
        reason: ship.deletionRequests[0].reason,
        createdAt: ship.deletionRequests[0].createdAt.toISOString(),
        requestedBy: ship.deletionRequests[0].requestedBy,
      } : null,
    }));

    return NextResponse.json({
      ships: formattedShips,
      pagination: {
        page,
        limit,
        total: totalCount,
        totalPages: Math.ceil(totalCount / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching ships:", error);
    return NextResponse.json(
      { error: "Failed to fetch ships" },
      { status: 500 }
    );
  }
}