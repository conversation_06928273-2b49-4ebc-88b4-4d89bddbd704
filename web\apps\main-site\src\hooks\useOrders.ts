import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import fetchClient from "@/lib/fetchClient";

// Query keys
export const orderQueryKeys = {
  orders: ["orders"],
  order: (id: string) => ["order", id],
};

// Types
export interface OrderItem {
  id: string;
  quantity: number;
  price: number;
  name: string;
  description?: string;
  productId: string;
  product: {
    id: string;
    name: string;
    image?: string;
    category: {
      id: string;
      name: string;
    };
    event?: {
      id: string;
      name: string;
      startDate: string;
      endDate: string;
    };
  };
}

export interface Order {
  id: string;
  orderNumber: string;
  status: string;
  total: number;
  subtotal: number;
  tax?: number;
  discount?: number;
  paymentMethod?: string;
  paymentIntentId?: string;
  notes?: string;
  userId: string;
  user: {
    id: string;
    email: string;
    displayName?: string;
  };
  items: OrderItem[];
  createdAt: string;
  updatedAt: string;
}

/**
 * Hook to fetch user's orders
 */
export function useOrders() {
  return useQuery({
    queryKey: orderQueryKeys.orders,
    queryFn: async () => {
      return fetchClient.get<{ orders: Order[] }>("/api/orders");
    },
  });
}

/**
 * Hook to fetch a specific order
 */
export function useOrder(id: string) {
  return useQuery({
    queryKey: orderQueryKeys.order(id),
    queryFn: async () => {
      return fetchClient.get<{ order: Order }>(`/api/orders/${id}`);
    },
    enabled: !!id,
  });
}

/**
 * Hook to fetch all orders for Sales Manager
 */
export function useSalesOrders() {
  return useQuery({
    queryKey: ["salesOrders"],
    queryFn: async () => {
      return fetchClient.get<{ orders: Order[] }>("/api/sales/orders");
    },
  });
}

/**
 * Hook to fetch a specific order for Sales Manager
 */
export function useSalesOrder(id: string) {
  return useQuery({
    queryKey: ["salesOrder", id],
    queryFn: async () => {
      return fetchClient.get<{ order: Order }>(`/api/sales/orders/${id}`);
    },
    enabled: !!id,
  });
}

/**
 * Hook to update order status (Sales Manager only)
 */
export function useUpdateOrderStatus() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      orderId,
      status,
    }: {
      orderId: string;
      status: string;
    }) => {
      return fetchClient.put<{ order: Order }>(`/api/sales/orders/${orderId}`, {
        status,
      });
    },
    onSuccess: (data, variables) => {
      // Invalidate and refetch orders
      queryClient.invalidateQueries({ queryKey: ["salesOrders"] });
      queryClient.invalidateQueries({
        queryKey: ["salesOrder", variables.orderId],
      });
    },
  });
}
