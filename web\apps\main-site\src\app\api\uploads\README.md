# File Upload API Routes

File upload handling and management for various content types across the platform.

## Endpoints

- **route.ts** - General file upload operations and management
- **avatar/** - User avatar upload and management
- **[type]/** - Type-specific upload handling (news images, deposits, etc.)

## Upload Types

The system handles uploads for:

- User avatars and profile images
- News article featured images
- Deposit verification documents
- Event media and attachments
- Product images and media

## Features

Upload functionality includes:

- File validation and security checks
- Image optimization and resizing
- Secure file storage and serving
- Type-specific handling and organization
- Integration with user permissions and roles

These endpoints ensure secure and organized file management across all platform features.
