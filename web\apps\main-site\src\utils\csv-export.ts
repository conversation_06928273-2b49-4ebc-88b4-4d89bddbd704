/**
 * CSV Export Utility
 *
 * This utility provides functions for exporting data to CSV format and handling downloads.
 * It can be used across the application for admin and cashier features.
 */

/**
 * Escapes a value for CSV format
 * - Wraps strings with quotes if they contain commas, quotes, or newlines
 * - Escapes quotes by doubling them
 * - Handles null/undefined values
 */
export const escapeCSVValue = (value: any): string => {
  if (value === null || value === undefined) {
    return "";
  }

  const stringValue = String(value);

  // If the value contains commas, quotes, or newlines, wrap it in quotes and escape any quotes
  if (
    stringValue.includes(",") ||
    stringValue.includes('"') ||
    stringValue.includes("\n")
  ) {
    return `"${stringValue.replace(/"/g, '""')}"`;
  }

  return stringValue;
};

/**
 * Converts an array of objects to CSV format
 *
 * @param data - Array of objects to convert to CSV
 * @param headers - Optional custom headers (keys and display names)
 * @param options - Additional options for CSV generation
 * @returns CSV formatted string
 */
export const objectsToCSV = <T extends Record<string, any>>(
  data: T[],
  headers?: { key: keyof T; label: string }[],
  options?: {
    includeHeaders?: boolean;
    dateFormat?: Intl.DateTimeFormatOptions;
  },
): string => {
  if (!data || data.length === 0) {
    return "";
  }

  const includeHeaders = options?.includeHeaders !== false;
  const dateFormat = options?.dateFormat || {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  };

  // If headers aren't provided, generate them from the first object's keys
  const csvHeaders =
    headers ||
    Object.keys(data[0]).map((key) => ({
      key: key as keyof T,
      label: key as string,
    }));

  const rows: string[] = [];

  // Add header row if includeHeaders is true
  if (includeHeaders) {
    rows.push(
      csvHeaders.map((header) => escapeCSVValue(header.label)).join(","),
    );
  }

  // Add data rows
  data.forEach((item) => {
    const row = csvHeaders.map((header) => {
      const value = item[header.key];

      // Format dates if the value is a Date object
      if (value && Object.prototype.toString.call(value) === "[object Date]") {
        return escapeCSVValue(
          (value as Date).toLocaleString(undefined, dateFormat),
        );
      }

      return escapeCSVValue(value);
    });

    rows.push(row.join(","));
  });

  return rows.join("\n");
};

/**
 * Triggers a CSV file download in the browser
 *
 * @param csvContent - CSV content as a string
 * @param filename - Name of the file to download (without extension)
 */
export const downloadCSV = (csvContent: string, filename: string): void => {
  // Create a Blob with the CSV content
  const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });

  // Create a download URL
  const url = URL.createObjectURL(blob);

  // Create a temporary link element
  const link = document.createElement("a");
  link.setAttribute("href", url);
  link.setAttribute("download", `${filename}.csv`);
  link.style.visibility = "hidden";

  // Add the link to the DOM, click it, and remove it
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  // Clean up the URL object
  URL.revokeObjectURL(url);
};

/**
 * Complete function to export data to CSV and trigger download
 *
 * @param data - Array of objects to export
 * @param filename - Name of the file to download (without extension)
 * @param headers - Optional custom headers
 * @param options - Additional options for CSV generation
 */
export const exportToCSV = <T extends Record<string, any>>(
  data: T[],
  filename: string,
  headers?: { key: keyof T; label: string }[],
  options?: {
    includeHeaders?: boolean;
    dateFormat?: Intl.DateTimeFormatOptions;
  },
): void => {
  if (!data || data.length === 0) {
    throw new Error("No data to export");
  }

  // Generate CSV content
  const csvContent = objectsToCSV(data, headers, options);

  // Add date to filename if not already present
  const filenameWithDate = filename.includes(
    new Date().toISOString().split("T")[0],
  )
    ? filename
    : `${filename}-${new Date().toISOString().split("T")[0]}`;

  // Trigger download
  downloadCSV(csvContent, filenameWithDate);
};

/**
 * Server-side function to generate CSV content
 * This can be used in API routes for larger datasets
 */
export const generateCSVOnServer = <T extends Record<string, any>>(
  data: T[],
  headers?: { key: keyof T; label: string }[],
  options?: {
    includeHeaders?: boolean;
    dateFormat?: Intl.DateTimeFormatOptions;
  },
): string => {
  return objectsToCSV(data, headers, options);
};
