import { useQuery } from "@tanstack/react-query";
import { getCaptainDashboard, type DashboardData } from "@/services/captainService";

export const useCaptainShip = () => {
  const {
    data,
    isLoading,
    error,
    refetch
  } = useQuery<DashboardData>({
    queryKey: ["captainDashboard"],
    queryFn: getCaptainDashboard,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  return {
    ship: data?.ship,
    statistics: data?.statistics,
    members: data?.members || [],
    pendingRequests: data?.pendingRequests || [],
    pendingInvitations: data?.pendingInvitations || [],
    recentActivity: data?.recentActivity,
    isLoading,
    error,
    refetch
  };
};