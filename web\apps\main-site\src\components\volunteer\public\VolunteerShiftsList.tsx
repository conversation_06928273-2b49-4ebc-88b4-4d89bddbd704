"use client";

import React, { useState } from "react";
import { format } from "date-fns";
import {
  useVolunteerCategory,
  useVolunteerShiftsByCategory,
} from "@/hooks/usePublicVolunteer";
import { VolunteerShiftCard } from "./VolunteerShiftCard";
import { VolunteerSignupModal } from "./VolunteerSignupModal";

interface VolunteerShiftsListProps {
  eventId: string;
  categoryId: string;
  onBack: () => void;
}

export const VolunteerShiftsList: React.FC<VolunteerShiftsListProps> = ({
  eventId,
  categoryId,
  onBack,
}) => {
  const { data: categoryData } = useVolunteerCategory(categoryId);
  const { data, isLoading, error } = useVolunteerShiftsByCategory(categoryId);
  const [selectedShiftId, setSelectedShiftId] = useState<string | null>(null);

  const category = categoryData?.category;
  const shifts = data?.shifts || [];

  // Group shifts by date for better organization
  const groupShiftsByDate = () => {
    return shifts.reduce(
      (groups, shift) => {
        const date = format(new Date(shift.startTime), "EEEE, MMMM d, yyyy");
        if (!groups[date]) {
          groups[date] = [];
        }
        groups[date].push(shift);
        return groups;
      },
      {} as Record<string, typeof shifts>,
    );
  };

  const shiftsByDate = groupShiftsByDate();

  return (
    <div>
      {/* Header with back button */}
      <div className="flex items-center mb-4">
        <button
          onClick={onBack}
          className="mr-3 p-2 rounded-full bg-secondary hover:bg-secondary-dark"
        >
          <svg
            className="w-5 h-5 text-white"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 19l-7-7 7-7"
            />
          </svg>
        </button>
        <h2 className="text-xl font-bold text-white">
          {category?.name || "Loading..."}
        </h2>
      </div>

      {/* Shifts list */}
      {isLoading ? (
        <div className="flex items-center justify-center h-40">
          <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-primary"></div>
        </div>
      ) : error ? (
        <div className="bg-accent bg-opacity-20 border border-accent rounded-md p-4 text-accent">
          <p>{error instanceof Error ? error.message : "An error occurred"}</p>
        </div>
      ) : shifts.length === 0 ? (
        <div className="text-gray-400 p-4 text-center">
          <p className="mb-2">No shifts available for this category.</p>
          <p>Check back later for upcoming volunteer opportunities.</p>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Group shifts by date */}
          {Object.entries(shiftsByDate).map(([date, dateShifts]) => (
            <div key={date}>
              <h3 className="text-lg font-semibold text-white mb-3">{date}</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {dateShifts.map((shift) => (
                  <VolunteerShiftCard
                    key={shift.id}
                    shift={shift}
                    onClick={() => setSelectedShiftId(shift.id)}
                  />
                ))}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Signup Modal */}
      {selectedShiftId && (
        <VolunteerSignupModal
          shiftId={selectedShiftId}
          onClose={() => setSelectedShiftId(null)}
        />
      )}
    </div>
  );
};
