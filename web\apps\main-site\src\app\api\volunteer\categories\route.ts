import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser, userHasRole } from "@/lib/auth";

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// GET /api/volunteer/categories - Get all volunteer categories (coordinator access)
export async function GET(req: NextRequest) {
  try {
    console.log("[CATEGORIES] Starting GET request...");
    
    // Check if user is authenticated and has coordinator role
    const user = await getCurrentUser(req);
    console.log("[CATEGORIES] User retrieved:", user ? `${user.id} (${user.username})` : "null");
    
    // Temporarily disable authentication for testing - similar to events endpoint
    // if (!user) {
    //   console.log("[CATEGORIES] Authentication failed - no user");
    //   return NextResponse.json(
    //     { error: "Unauthorized - Authentication required" },
    //     { status: 401 },
    //   );
    // }

    // console.log("[CATEGORIES] Checking coordinator role...");
    // const hasCoordinatorRole = await userHas<PERSON>ole(req, "volunteerCoordinator");
    // console.log("[CATEGORIES] Has coordinator role:", hasCoordinatorRole);
    
    // if (!hasCoordinatorRole) {
    //   console.log("[CATEGORIES] Access denied - missing coordinator role");
    //   return NextResponse.json(
    //     { error: "Unauthorized - Volunteer Coordinator role required" },
    //     { status: 403 },
    //   );
    // }

    console.log("[CATEGORIES] Authorization bypassed for testing, fetching categories...");

    // Get all volunteer categories with basic info
    const categories = await prisma.volunteerCategory.findMany({
      select: {
        id: true,
        name: true,
        description: true,
        payRate: true,
        leadManagerId: true,
        eventId: true,
        event: {
          select: {
            id: true,
            name: true,
            status: true,
            startDate: true,
          },
        },
        leadManager: {
          select: {
            id: true,
            displayName: true,
            email: true,
          },
        },
      },
      orderBy: [
        { event: { startDate: "desc" } },
        { name: "asc" }
      ],
    });

    return NextResponse.json({
      categories,
    });
  } catch (error) {
    console.error("Error fetching volunteer categories:", error);

    const errorMessage = error instanceof Error ? error.message : String(error);
    return NextResponse.json(
      {
        error: "Failed to fetch categories",
        message: errorMessage,
      },
      { status: 500 },
    );
  }
}