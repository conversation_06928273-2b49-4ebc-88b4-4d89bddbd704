/**
 * Redemption Code Utilities
 * Functions for generating and managing redemption codes
 */

/**
 * Generate a unique redemption code
 * Format: 8-character alphanumeric string (uppercase)
 */
export function generateRedemptionCode(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * Generate multiple unique redemption codes
 * Ensures all codes are unique within the generated batch
 */
export function generateMultipleRedemptionCodes(count: number): string[] {
  const codes = new Set<string>();
  
  while (codes.size < count) {
    codes.add(generateRedemptionCode());
  }
  
  return Array.from(codes);
}