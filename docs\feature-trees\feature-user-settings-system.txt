﻿# Bank of Styx Website - User Settings & Profile System
# Generated on 08/07/2025 12:35:25
# Priority: Low
# User profile management, settings, preferences, and user-related API endpoints
# Root directory: C:\Users\<USER>\projects\test\web

## Directories and Files

### Files
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\user\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\user\README.md
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\user\ship\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\user\ship\leave\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\user\ship\leave\route.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\user\ship-invites\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\user\ship-invites\[id]\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\user\ship-invites\route.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\user\state\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\user\state\route.js
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\users\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\users\[id]\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\users\default-view\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\users\default-view\route.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\users\migrate-notification-preferences\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\users\migrate-notification-preferences\route.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\users\notification-preferences\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\users\notification-preferences\route.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\users\preferences\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\users\preferences\route.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\users\profile\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\users\profile\route.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\users\README.md
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\users\search\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\users\search\route.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\users\security\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\users\security\route.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\users\sync-discord-avatar\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api\users\sync-discord-avatar\route.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\settings\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\settings\colors\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\settings\colors\ColorThemeScript.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\settings\colors\page.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\settings\layout.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\settings\notifications\
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\settings\notifications\page.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\settings\page.tsx

