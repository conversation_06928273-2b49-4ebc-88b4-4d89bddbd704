# React Components

Feature-organized React components providing UI elements and functionality across the Bank of Styx platform.

## Feature Components

- **admin/** - Administrative interface components for system management
- **auth/** - Authentication forms and login/register components
- **bank/** - Banking interface components for accounts and transactions
- **cashier/** - Cashier portal components for staff operations
- **events/** - Event management and calendar components
- **news/** - News article display and management components
- **notifications/** - Notification system components
- **sales/** - Sales management interface components
- **settings/** - User settings and preference components
- **shop/** - Shopping cart, product, and checkout components
- **user/** - User profile and account components
- **volunteer/** - Volunteer management system components

## Shared Components

- **common/** - Common utility components used across features
- **layout/** - Layout components for page structure and navigation
- **shared/** - Shared business logic components
- **ui/** - Basic UI components and design system elements

Components are organized by feature to maintain clear separation of concerns and enable easy maintenance and development.
