import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser, userHasRole } from "@/lib/auth";
import bcrypt from "bcryptjs";
import crypto from "crypto";
import { sendEmail } from "@/lib/email";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
interface Params {
  params: {
    id: string;
  };
}

export async function POST(request: Request, { params }: Params) {
  const { id } = params;

  try {
    // Check if user is authenticated and has admin role
    const currentUser = await getCurrentUser(request);
    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    const isAdmin = await userHasRole(request, "admin");
    if (!isAdmin) {
      return NextResponse.json(
        { error: "Admin privileges required" },
        { status: 403 },
      );
    }

    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { id },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Generate a temporary password
    const tempPassword = crypto.randomBytes(8).toString("hex");

    // Hash the temporary password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(tempPassword, salt);

    // Update user with new password
    await prisma.user.update({
      where: { id },
      data: {
        passwordHash: hashedPassword,
      },
    });

    // Send an email with the temporary password
    const emailText = `
      Hello ${user.displayName},

      Your password for the Bank of Styx has been reset by an administrator.

      Your temporary password is: ${tempPassword}

      Please log in with this temporary password and change it immediately for security reasons.

      If you did not request this password reset, please contact the Bank of Styx administrators immediately.

      Best regards,
      The Bank of Styx Team
    `;

    const emailHtml = `
      <h2>Bank of Styx Password Reset</h2>
      <p>Hello ${user.displayName},</p>
      <p>Your password for the Bank of Styx has been reset by an administrator.</p>
      <p><strong>Your temporary password is:</strong> ${tempPassword}</p>
      <p>Please log in with this temporary password and change it immediately for security reasons.</p>
      <p>If you did not request this password reset, please contact the Bank of Styx administrators immediately.</p>
      <p>Best regards,<br>The Bank of Styx Team</p>
    `;

    // Send the email
    const emailSent = await sendEmail({
      to: user.email,
      subject: "Bank of Styx - Your Password Has Been Reset",
      text: emailText,
      html: emailHtml,
    });

    return NextResponse.json({
      success: true,
      message: emailSent
        ? "Password has been reset and emailed to the user"
        : "Password has been reset but there was an issue sending the email",
      tempPassword: tempPassword, // Keep this for admin reference
      emailSent: emailSent,
    });
  } catch (error) {
    console.error("Error resetting user password:", error);
    return NextResponse.json(
      { error: "Failed to reset user password" },
      { status: 500 },
    );
  }
}
