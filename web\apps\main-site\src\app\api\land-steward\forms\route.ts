import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth";
import prisma from "@/lib/prisma";

export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    
    if (!user || !user.isLandSteward) {
      return NextResponse.json(
        { error: "Unauthorized. Land Steward access required." },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const eventId = searchParams.get("eventId");
    const status = searchParams.get("status");

    const where: any = {};
    
    if (eventId) {
      const eventIdNum = parseInt(eventId);
      if (!isNaN(eventIdNum)) {
        where.eventId = eventIdNum;
      }
    }

    if (status && ["draft", "active", "closed"].includes(status)) {
      where.status = status;
    }

    const forms = await prisma.eventForm.findMany({
      where,
      include: {
        event: {
          select: {
            id: true,
            name: true,
            startDate: true,
            endDate: true,
          },
        },
        template: {
          select: {
            id: true,
            name: true,
          },
        },
        createdBy: {
          select: {
            id: true,
            username: true,
            displayName: true,
          },
        },
        _count: {
          select: {
            submissions: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return NextResponse.json(forms);
  } catch (error) {
    console.error("Error fetching event forms:", error);
    return NextResponse.json(
      { error: "Failed to fetch event forms" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    
    if (!user || !user.isLandSteward) {
      return NextResponse.json(
        { error: "Unauthorized. Land Steward access required." },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { 
      eventId, 
      templateId, 
      name, 
      description, 
      formStructure, 
      status = "draft",
      submissionDeadline 
    } = body;

    // Validate required fields
    if (!eventId || !name) {
      return NextResponse.json(
        { error: "Event ID and name are required" },
        { status: 400 }
      );
    }

    // Check if event exists
    const event = await prisma.event.findUnique({
      where: { id: eventId },
    });

    if (!event) {
      return NextResponse.json(
        { error: "Event not found" },
        { status: 404 }
      );
    }

    let finalFormStructure = formStructure;

    // If using a template, get the structure from it
    if (templateId) {
      const template = await prisma.formTemplate.findUnique({
        where: { id: templateId },
      });

      if (!template) {
        return NextResponse.json(
          { error: "Template not found" },
          { status: 404 }
        );
      }

      finalFormStructure = template.formStructure;
    }

    // Validate form structure
    if (!finalFormStructure || !Array.isArray(finalFormStructure) || finalFormStructure.length === 0) {
      return NextResponse.json(
        { error: "Form structure is required and must be a non-empty array" },
        { status: 400 }
      );
    }

    // Validate each field in the structure
    for (const field of finalFormStructure) {
      if (!field.id || !field.type || !field.label) {
        return NextResponse.json(
          { error: "Each field must have id, type, and label" },
          { status: 400 }
        );
      }

      const validTypes = [
        "text", "textarea", "select", "checkbox", "multi_select", 
        "file_upload", "user_select", "multi_user_select", "volunteer_hours"
      ];
      
      if (!validTypes.includes(field.type)) {
        return NextResponse.json(
          { error: `Invalid field type: ${field.type}` },
          { status: 400 }
        );
      }
    }

    const eventForm = await prisma.eventForm.create({
      data: {
        eventId,
        templateId,
        name,
        description,
        formStructure: finalFormStructure,
        status,
        submissionDeadline: submissionDeadline ? new Date(submissionDeadline) : null,
        createdById: user.id,
      },
      include: {
        event: {
          select: {
            id: true,
            name: true,
            startDate: true,
            endDate: true,
          },
        },
        template: {
          select: {
            id: true,
            name: true,
          },
        },
        createdBy: {
          select: {
            id: true,
            username: true,
            displayName: true,
          },
        },
      },
    });

    return NextResponse.json(eventForm, { status: 201 });
  } catch (error) {
    console.error("Error creating event form:", error);
    return NextResponse.json(
      { error: "Failed to create event form" },
      { status: 500 }
    );
  }
}