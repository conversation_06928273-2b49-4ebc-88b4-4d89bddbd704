/**
 * Service for handling avatar uploads
 */
import fetchClient from "@/lib/fetchClient";

/**
 * Upload an avatar image to the server
 * @param file The image file to upload
 * @returns Response with the uploaded file details
 */
export async function uploadAvatar(file: File): Promise<{
  url: string;
  name: string;
  originalName: string;
  size: number;
  type: string;
}> {
  try {
    // Validate file before sending
    if (!file || !(file instanceof File)) {
      throw new Error("Invalid file object provided");
    }

    // Validate file type
    const allowedTypes = ["image/jpeg", "image/png", "image/gif", "image/webp"];
    if (!allowedTypes.includes(file.type)) {
      throw new Error(
        "File type not supported. Please upload an image (JPEG, PNG, GIF, WEBP)",
      );
    }

    // Validate file size (2MB max for avatars)
    const maxSize = 2 * 1024 * 1024; // 2MB in bytes
    if (file.size > maxSize) {
      throw new Error("File size exceeds 2MB limit");
    }

    // Create FormData
    const formData = new FormData();
    formData.append("file", file);

    // Log for debugging
    console.log("Uploading avatar:", {
      fileName: file.name,
      fileType: file.type,
      fileSize: file.size,
    });

    // Make the request
    const data = await fetchClient.request<{
      file: {
        url: string;
        name: string;
        originalName: string;
        size: number;
        type: string;
      };
    }>("/api/uploads/avatar", {
      method: "POST",
      body: formData,
      // Don't set Content-Type header for FormData as the browser will set it with the boundary
    });

    if (!data || !data.file) {
      throw new Error("Invalid response from server");
    }

    return data.file;
  } catch (error) {
    console.error("Error uploading avatar:", error);
    throw error;
  }
}
