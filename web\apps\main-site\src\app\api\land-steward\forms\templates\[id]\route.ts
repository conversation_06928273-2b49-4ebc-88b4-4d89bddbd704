import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth";
import prisma from "@/lib/prisma";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    
    if (!user || !user.isLandSteward) {
      return NextResponse.json(
        { error: "Unauthorized. Land Steward access required." },
        { status: 403 }
      );
    }

    const templateId = params.id;

    const template = await prisma.formTemplate.findUnique({
      where: { id: templateId },
      include: {
        createdBy: {
          select: {
            id: true,
            username: true,
            displayName: true,
          },
        },
        eventForms: {
          include: {
            event: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    });

    if (!template) {
      return NextResponse.json(
        { error: "Template not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(template);
  } catch (error) {
    console.error("Error fetching form template:", error);
    return NextResponse.json(
      { error: "Failed to fetch form template" },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    
    if (!user || !user.isLandSteward) {
      return NextResponse.json(
        { error: "Unauthorized. Land Steward access required." },
        { status: 403 }
      );
    }

    const templateId = params.id;

    const body = await request.json();
    const { name, description, formStructure, isReusable } = body;

    // Check if template exists
    const existingTemplate = await prisma.formTemplate.findUnique({
      where: { id: templateId },
    });

    if (!existingTemplate) {
      return NextResponse.json(
        { error: "Template not found" },
        { status: 404 }
      );
    }

    // Validate form structure if provided
    if (formStructure) {
      if (!Array.isArray(formStructure) || formStructure.length === 0) {
        return NextResponse.json(
          { error: "Form structure must be a non-empty array of fields" },
          { status: 400 }
        );
      }

      // Validate each field in the structure
      for (const field of formStructure) {
        if (!field.id || !field.type || !field.label) {
          return NextResponse.json(
            { error: "Each field must have id, type, and label" },
            { status: 400 }
          );
        }

        const validTypes = [
          "text", "textarea", "select", "checkbox", "multi_select", 
          "file_upload", "user_select", "multi_user_select", "volunteer_hours"
        ];
        
        if (!validTypes.includes(field.type)) {
          return NextResponse.json(
            { error: `Invalid field type: ${field.type}` },
            { status: 400 }
          );
        }
      }
    }

    const updatedTemplate = await prisma.formTemplate.update({
      where: { id: templateId },
      data: {
        ...(name && { name }),
        ...(description !== undefined && { description }),
        ...(formStructure && { formStructure }),
        ...(isReusable !== undefined && { isReusable }),
      },
      include: {
        createdBy: {
          select: {
            id: true,
            username: true,
            displayName: true,
          },
        },
      },
    });

    return NextResponse.json(updatedTemplate);
  } catch (error) {
    console.error("Error updating form template:", error);
    return NextResponse.json(
      { error: "Failed to update form template" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    
    if (!user || !user.isLandSteward) {
      return NextResponse.json(
        { error: "Unauthorized. Land Steward access required." },
        { status: 403 }
      );
    }

    const templateId = params.id;

    // Check if template exists and is being used
    const template = await prisma.formTemplate.findUnique({
      where: { id: templateId },
      include: {
        _count: {
          select: {
            eventForms: true,
          },
        },
      },
    });

    if (!template) {
      return NextResponse.json(
        { error: "Template not found" },
        { status: 404 }
      );
    }

    if (template._count.eventForms > 0) {
      return NextResponse.json(
        { error: "Cannot delete template that is being used by event forms" },
        { status: 400 }
      );
    }

    await prisma.formTemplate.delete({
      where: { id: templateId },
    });

    return NextResponse.json({ message: "Template deleted successfully" });
  } catch (error) {
    console.error("Error deleting form template:", error);
    return NextResponse.json(
      { error: "Failed to delete form template" },
      { status: 500 }
    );
  }
}