"use client";

import { useState } from "react";
import { useSalesOrders } from "@/hooks/useOrders";
import { <PERSON><PERSON>, Spinner } from "@bank-of-styx/ui";
import { Select } from "@/components/shared/Select";
import Link from "next/link";

export const OrderList: React.FC = () => {
  const [statusFilter, setStatusFilter] = useState("");
  const { data, isLoading, error } = useSalesOrders();

  if (isLoading) {
    return (
      <div className="flex justify-center p-8">
        <Spinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-8 text-center">
        <p className="text-accent">Failed to load orders</p>
      </div>
    );
  }

  const orders = data?.orders || [];

  // Filter orders by status if filter is applied
  const filteredOrders = statusFilter
    ? orders.filter((order) => order.status === statusFilter)
    : orders;

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "text-warning";
      case "paid":
        return "text-success";
      case "fulfilled":
        return "text-primary";
      case "cancelled":
        return "text-accent";
      case "refunded":
        return "text-text-muted";
      default:
        return "text-text-primary";
    }
  };

  return (
    <div className="p-4">
      {/* Filter Controls */}
      <div className="mb-6 flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <label htmlFor="status-filter" className="text-sm font-medium">
            Filter by Status:
          </label>
          <Select
            id="status-filter"
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="w-40"
          >
            <option value="">All Orders</option>
            <option value="pending">Pending</option>
            <option value="paid">Paid</option>
            <option value="fulfilled">Fulfilled</option>
            <option value="cancelled">Cancelled</option>
            <option value="refunded">Refunded</option>
          </Select>
        </div>
        <div className="text-sm text-text-muted">
          {filteredOrders.length} order{filteredOrders.length !== 1 ? "s" : ""}
        </div>
      </div>

      {/* Orders Table */}
      {filteredOrders.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-text-muted">No orders found</p>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-border-subtle">
                <th className="text-left py-3 px-4 font-semibold">Order #</th>
                <th className="text-left py-3 px-4 font-semibold">Customer</th>
                <th className="text-left py-3 px-4 font-semibold">Status</th>
                <th className="text-left py-3 px-4 font-semibold">Total</th>
                <th className="text-left py-3 px-4 font-semibold">Date</th>
                <th className="text-left py-3 px-4 font-semibold">Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredOrders.map((order) => (
                <tr
                  key={order.id}
                  className="border-b border-border-subtle hover:bg-secondary-light"
                >
                  <td className="py-3 px-4">
                    <span className="font-mono text-sm">
                      {order.orderNumber}
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <div>
                      <p className="font-medium">
                        {order.user.displayName || order.user.email}
                      </p>
                      <p className="text-sm text-text-muted">
                        {order.user.email}
                      </p>
                    </div>
                  </td>
                  <td className="py-3 px-4">
                    <span
                      className={`capitalize font-medium ${getStatusColor(
                        order.status,
                      )}`}
                    >
                      {order.status}
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <span className="font-medium">
                      ${order.total.toFixed(2)}
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <span className="text-sm">
                      {new Date(order.createdAt).toLocaleDateString()}
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <Link href={`/sales/orders/${order.id}`}>
                      <Button variant="outline" size="sm">
                        View Details
                      </Button>
                    </Link>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};
