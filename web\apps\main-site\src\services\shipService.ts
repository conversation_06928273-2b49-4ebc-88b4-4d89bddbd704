import fetchClient from "@/lib/fetchClient";

export interface ShipInvitation {
  hasInvitation: boolean;
  invitation?: {
    id: string;
    message: string;
    createdAt: string;
    ship: {
      id: string;
      name: string;
    };
  } | null;
}

// Check if user has a pending invitation for a specific ship
export const checkShipInvitation = async (shipId: string): Promise<ShipInvitation> => {
  return await fetchClient.get(`/api/ships/${shipId}/invitation`);
};

// Accept a ship invitation
export const acceptShipInvitation = async (shipId: string) => {
  return await fetchClient.post(`/api/ships/${shipId}/invitation`, {
    action: 'accept'
  });
};

// Decline a ship invitation
export const declineShipInvitation = async (shipId: string) => {
  return await fetchClient.post(`/api/ships/${shipId}/invitation`, {
    action: 'decline'
  });
};