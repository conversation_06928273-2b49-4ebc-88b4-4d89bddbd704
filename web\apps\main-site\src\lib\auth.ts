import { NextRequest } from "next/server";
import jwt from "jsonwebtoken";
import prisma from "./prisma";

const JWT_SECRET = process.env.JWT_SECRET || "your-development-secret-key";

type JwtPayload = {
  id: string;
  username: string;
  email: string;
  [key: string]: any;
};

/**
 * Verify JWT token and return the decoded payload
 * @param token JWT token string
 * @returns Decoded token payload or null if invalid
 */
export function verifyToken(token: string): JwtPayload | null {
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as JwtPayload;
    return decoded;
  } catch (error) {
    return null;
  }
}

/**
 * Extract JWT token from request Authorization header
 * @param req Next.js request object
 * @returns JWT token string or null if not found
 */
export function extractTokenFromRequest(req: Request): string | null {
  // Try to get token from Authorization header first
  const authHeader = req.headers.get("authorization");

  if (authHeader && authHeader.startsWith("Bearer ")) {
    const token = authHeader.split(" ")[1];
    console.log("Token extracted from Authorization header");
    return token;
  }

  // If no token in header, try to get from cookies (for server-side rendering)
  const cookieHeader = req.headers.get("cookie");
  if (cookieHeader) {
    const cookies = cookieHeader.split(";").reduce(
      (acc, cookie) => {
        const [key, value] = cookie.trim().split("=");
        acc[key] = value;
        return acc;
      },
      {} as Record<string, string>,
    );

    if (cookies["auth_token"]) {
      console.log("Token extracted from cookies");
      return cookies["auth_token"];
    }
  }

  console.log("No token found in request");
  return null;
}

/**
 * Get current authenticated user from request
 * @param req Next.js request object
 * @returns User object or null if not authenticated
 */
export async function getCurrentUser(req: Request) {
  try {
    const token = extractTokenFromRequest(req);

    if (!token) {
      console.log("Authentication failed: No token found");
      return null;
    }

    const decoded = verifyToken(token);

    if (!decoded) {
      console.log("Authentication failed: Token verification failed");
      return null;
    }

    if (!decoded.id) {
      console.log("Authentication failed: Token does not contain user ID");
      return null;
    }

    console.log(`Token verified for user ID: ${decoded.id}`);

    try {
      // Get user from database with all necessary fields including leadManagerCategoryId
      const user = await prisma.user.findUnique({
        where: { id: decoded.id },
        select: {
          id: true,
          username: true,
          displayName: true,
          email: true,
          avatar: true,
          balance: true,
          isAdmin: true,
          isEditor: true,
          isBanker: true,
          isChatModerator: true,
          isVolunteer: true,
          isVolunteerCoordinator: true,
          isLeadManager: true,
          leadManagerCategoryId: true,
          isSalesManager: true,
          isLandSteward: true,
          status: true,
          createdAt: true,
          updatedAt: true,
          // Discord connection fields
          discordConnected: true,
          discordId: true,
          // Facebook connection fields
          facebookConnected: true,
          facebookId: true,
        },
      });

      if (!user) {
        console.log(`User with ID ${decoded.id} not found in database`);
        return null;
      }

      console.log(`User found: ${user.id} (${user.username})`);
      return user;
    } catch (dbError) {
      console.error("Database error when fetching user:", dbError);
      return null;
    }
  } catch (error) {
    console.error("Error getting current user:", error);
    return null;
  }
}

/**
 * Check if the current user has a specific role
 * @param req Next.js request object
 * @param role Role to check (admin, editor, banker, chatModerator)
 * @returns Boolean indicating if user has the role
 */
export async function userHasRole(
  req: Request,
  role:
    | "admin"
    | "editor"
    | "banker"
    | "chatModerator"
    | "volunteerCoordinator"
    | "leadManager"
    | "salesManager"
    | "landSteward",
): Promise<boolean> {
  const user = await getCurrentUser(req);

  if (!user) {
    console.error("User not found when checking role:", role);
    return false;
  }

  let hasRole = false;
  switch (role) {
    case "admin":
      hasRole = Boolean(user.isAdmin);
      break;
    case "editor":
      hasRole = Boolean(user.isEditor);
      break;
    case "banker":
      hasRole = Boolean(user.isBanker);
      break;
    case "chatModerator":
      hasRole = Boolean(user.isChatModerator);
      break;
    case "volunteerCoordinator":
      hasRole = Boolean(user.isVolunteerCoordinator);
      break;
    case "leadManager":
      hasRole = Boolean(user.isLeadManager);
      break;
    case "salesManager":
      hasRole = Boolean(user.isSalesManager);
      break;
    case "landSteward":
      hasRole = Boolean(user.isLandSteward);
      break;
  }

  if (!hasRole) {
    console.error(`User ${user.id} does not have required role: ${role}`);
  }

  return hasRole;
}
