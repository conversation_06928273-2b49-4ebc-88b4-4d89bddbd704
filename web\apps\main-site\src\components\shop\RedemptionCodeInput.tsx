"use client";

import React, { useState } from "react";
import { Card, Input, Button } from "@bank-of-styx/ui";
import { toast } from "react-hot-toast";
import fetchClient from "@/lib/fetchClient";

// Gift icon component
const GiftIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
      d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7" />
  </svg>
);

export const RedemptionCodeInput: React.FC = () => {
  const [code, setCode] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!code.trim()) {
      toast.error("Please enter a redemption code");
      return;
    }

    setIsSubmitting(true);
    try {
      const result = await fetchClient.post("/api/shop/redeem-code", {
        code: code.trim().toUpperCase(),
      }) as { product: { name: string } };

      toast.success(`${result.product.name} added to your cart!`);
      setCode("");
      
      // Optionally redirect to cart or refresh the page
      window.location.href = "/shop/cart";
    } catch (error: any) {
      console.error("Error redeeming code:", error);
      
      if (error.response?.data?.error) {
        toast.error(error.response.data.error);
      } else {
        toast.error("Failed to redeem code. Please try again.");
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="p-6 bg-gradient-to-r from-success-light to-info-light border-success">
      <div className="flex items-center space-x-3 mb-4">
        <GiftIcon className="h-6 w-6 text-success" />
        <h3 className="text-lg font-semibold text-success-dark">
          Have a Redemption Code?
        </h3>
      </div>
      
      <p className="text-sm text-success-dark mb-4">
        Enter your code below to get free access to exclusive products.
      </p>

      <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row gap-3">
        <div className="flex-1">
          <Input
            value={code}
            onChange={(e) => setCode(e.target.value.toUpperCase())}
            placeholder="Enter your code (e.g., ABC123DEF)"
            className="font-mono"
            maxLength={12}
          />
        </div>
        <Button
          type="submit"
          variant="primary"
          loading={isSubmitting}
          className="bg-success hover:bg-success-dark"
        >
          {isSubmitting ? "Redeeming..." : "Redeem Code"}
        </Button>
      </form>

      <div className="mt-3 text-xs text-success-dark">
        Codes are case-insensitive and will be automatically formatted.
      </div>
    </Card>
  );
};