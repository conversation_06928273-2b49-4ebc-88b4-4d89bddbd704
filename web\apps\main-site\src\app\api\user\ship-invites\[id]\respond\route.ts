import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import prisma from '@/lib/prisma';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = user.id;
    const inviteId = params.id;

    const { response } = await request.json();

    if (!response || !['accepted', 'declined'].includes(response)) {
      return NextResponse.json({ error: 'Response must be either "accepted" or "declined"' }, { status: 400 });
    }

    // Find the invitation
    const invite = await prisma.shipJoinRequest.findFirst({
      where: {
        id: inviteId,
        userId,
        type: 'invite',
        status: 'pending'
      },
      include: {
        ship: true
      }
    });

    if (!invite) {
      return NextResponse.json({ error: 'Invitation not found or already responded to' }, { status: 404 });
    }

    // Check if user is already a member of any ship
    const existingMembership = await prisma.shipMember.findFirst({
      where: {
        userId,
        status: 'active'
      }
    });

    if (existingMembership && response === 'accepted') {
      return NextResponse.json({ error: 'You are already a member of another ship' }, { status: 400 });
    }

    // Use transaction to ensure atomicity
    const result = await prisma.$transaction(async (tx) => {
      // Update the invitation
      const updatedInvite = await tx.shipJoinRequest.update({
        where: {
          id: inviteId
        },
        data: {
          status: response,
          respondedAt: new Date()
        }
      });

      let newMember = null;

      // If accepted, create ship membership
      if (response === 'accepted') {
        newMember = await tx.shipMember.create({
          data: {
            userId,
            shipId: invite.shipId,
            role: 'Member',
            status: 'active'
          },
          include: {
            user: {
              select: {
                id: true,
                username: true,
                displayName: true,
                avatar: true
              }
            },
            ship: {
              select: {
                id: true,
                name: true
              }
            }
          }
        });
      }

      return { updatedInvite, newMember };
    });

    const message = response === 'accepted' 
      ? `Successfully joined ${invite.ship.name}!`
      : 'Invitation declined';

    return NextResponse.json({
      success: true,
      message,
      member: result.newMember
    });

  } catch (error) {
    console.error('Error responding to ship invite:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}