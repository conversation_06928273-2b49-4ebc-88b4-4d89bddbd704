import React, { useState, useCallback } from "react";
import { Button } from "../button";
import { Input, Textarea, Checkbox } from "../input";
import type { FormField, FormTemplate, FormSubmissionData, FormRendererProps } from "./types";

export const FormRenderer: React.FC<FormRendererProps> = ({
  form,
  initialData = {},
  onSubmit,
  onCancel,
  readOnly = false,
  showActions = true,
  submitText = "Submit",
  isSubmitting = false,
}) => {
  const [formData, setFormData] = useState<FormSubmissionData>(initialData);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [files, setFiles] = useState<Record<string, File>>({});

  // Update field value
  const updateField = useCallback((fieldId: string, value: any) => {
    setFormData(prev => ({ ...prev, [fieldId]: value }));
    // Clear error when field is updated
    if (errors[fieldId]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[fieldId];
        return newErrors;
      });
    }
  }, [errors]);

  // Handle file upload
  const handleFileUpload = useCallback((fieldId: string, file: File | null) => {
    if (file) {
      setFiles(prev => ({ ...prev, [fieldId]: file }));
      updateField(fieldId, file.name);
    } else {
      setFiles(prev => {
        const newFiles = { ...prev };
        delete newFiles[fieldId];
        return newFiles;
      });
      updateField(fieldId, null);
    }
  }, [updateField]);

  // Validate field
  const validateField = useCallback((field: FormField, value: any): string | null => {
    // Skip validation for volunteer_hours fields as they are informational only
    if (field.type === "volunteer_hours") {
      return null;
    }
    
    // Required validation
    if (field.required) {
      if (!value || (typeof value === "string" && !value.trim())) {
        return `${field.label} is required`;
      }
      if (Array.isArray(value) && value.length === 0) {
        return `${field.label} is required`;
      }
    }

    // Type-specific validation
    if (value && typeof value === "string") {
      const validation = field.validation;
      if (validation) {
        if (validation.minLength && value.length < validation.minLength) {
          return `${field.label} must be at least ${validation.minLength} characters`;
        }
        if (validation.maxLength && value.length > validation.maxLength) {
          return `${field.label} must be no more than ${validation.maxLength} characters`;
        }
        if (validation.pattern) {
          const regex = new RegExp(validation.pattern);
          if (!regex.test(value)) {
            return validation.customMessage || `${field.label} format is invalid`;
          }
        }
      }
    }

    return null;
  }, []);

  // Validate entire form
  const validateForm = useCallback((): boolean => {
    const newErrors: Record<string, string> = {};
    let isValid = true;

    form.fields.forEach(field => {
      const value = formData[field.id];
      const error = validateField(field, value);
      if (error) {
        newErrors[field.id] = error;
        isValid = false;
      }
    });

    setErrors(newErrors);
    return isValid;
  }, [form.fields, formData, validateField]);

  // Handle form submission
  const handleSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    // Combine form data with files
    const submissionData = { ...formData };
    Object.entries(files).forEach(([fieldId, file]) => {
      submissionData[fieldId] = file;
    });

    onSubmit(submissionData);
  }, [formData, files, validateForm, onSubmit]);

  return (
    <div className="bg-secondary-light rounded-lg p-6">
      {/* Form Header */}
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">{form.name}</h2>
        {form.description && (
          <p className="text-text-secondary">{form.description}</p>
        )}
      </div>

      {/* Form Fields */}
      <form onSubmit={handleSubmit} className="space-y-6">
        {form.fields.map((field) => (
          <div key={field.id} className="space-y-2">
            <label className="block text-sm font-medium">
              {field.label}
              {field.required && <span className="text-accent ml-1">*</span>}
            </label>
            
            {field.description && (
              <p className="text-xs text-text-secondary">{field.description}</p>
            )}

            <FormFieldInput
              field={field}
              value={formData[field.id]}
              onChange={(value) => updateField(field.id, value)}
              onFileChange={(file) => handleFileUpload(field.id, file)}
              readOnly={readOnly}
              error={errors[field.id]}
            />

            {errors[field.id] && (
              <p className="text-sm text-accent">{errors[field.id]}</p>
            )}
          </div>
        ))}

        {/* Form Actions */}
        {showActions && !readOnly && (
          <div className="flex gap-4 pt-4 border-t border-gray-600">
            <Button
              type="submit"
              variant="primary"
              loading={isSubmitting}
              disabled={isSubmitting}
            >
              {submitText}
            </Button>
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
            )}
          </div>
        )}
      </form>
    </div>
  );
};

// Individual field input component
const FormFieldInput: React.FC<{
  field: FormField;
  value: any;
  onChange: (value: any) => void;
  onFileChange?: (file: File | null) => void;
  readOnly?: boolean;
  error?: string;
}> = ({ field, value, onChange, onFileChange, readOnly = false, error }) => {
  const hasError = !!error;

  switch (field.type) {
    case "text":
      return (
        <Input
          type="text"
          value={value || ""}
          onChange={(e) => onChange(e.target.value)}
          placeholder={`Enter ${field.label.toLowerCase()}`}
          disabled={readOnly}
          className={hasError ? "border-accent" : ""}
        />
      );

    case "textarea":
      return (
        <Textarea
          value={value || ""}
          onChange={(e) => onChange(e.target.value)}
          placeholder={`Enter ${field.label.toLowerCase()}`}
          disabled={readOnly}
          rows={4}
          className={hasError ? "border-accent" : ""}
        />
      );

    case "select":
      return (
        <select
          value={value || ""}
          onChange={(e) => onChange(e.target.value)}
          disabled={readOnly}
          className={`
            w-full p-2 border rounded bg-input text-text-primary
            ${hasError ? "border-accent" : "border-gray-600"}
            ${readOnly ? "opacity-50 cursor-not-allowed" : ""}
          `}
        >
          <option value="">Select an option...</option>
          {field.options?.map((option, index) => (
            <option key={index} value={option}>
              {option}
            </option>
          ))}
        </select>
      );

    case "checkbox":
      return (
        <label className="flex items-center gap-2">
          <Checkbox
            checked={!!value}
            onChange={(e) => onChange(e.target.checked)}
            disabled={readOnly}
          />
          <span className="text-sm">Yes</span>
        </label>
      );

    case "multi_select":
      const selectedValues = Array.isArray(value) ? value : [];
      return (
        <div className="space-y-2">
          {field.options?.map((option, index) => (
            <label key={index} className="flex items-center gap-2">
              <Checkbox
                checked={selectedValues.includes(option)}
                onChange={(e) => {
                  if (e.target.checked) {
                    onChange([...selectedValues, option]);
                  } else {
                    onChange(selectedValues.filter(v => v !== option));
                  }
                }}
                disabled={readOnly}
              />
              <span className="text-sm">{option}</span>
            </label>
          ))}
        </div>
      );

    case "file_upload":
      return (
        <div>
          <input
            type="file"
            onChange={(e) => {
              const file = e.target.files?.[0] || null;
              onFileChange?.(file);
            }}
            disabled={readOnly}
            className={`
              w-full p-2 border rounded bg-input text-text-primary
              ${hasError ? "border-accent" : "border-gray-600"}
              ${readOnly ? "opacity-50 cursor-not-allowed" : ""}
            `}
          />
          {value && (
            <p className="text-sm text-text-secondary mt-1">
              Selected: {value}
            </p>
          )}
        </div>
      );

    case "user_select":
      return (
        <div className="space-y-2">
          <div className="text-sm text-text-secondary">
            Note: User selection requires API integration for user search
          </div>
          <input
            type="text"
            value={value || ""}
            onChange={(e) => onChange(e.target.value)}
            placeholder={`Enter ${field.label.toLowerCase()}`}
            disabled={readOnly}
            className={`
              w-full p-2 border rounded bg-input text-text-primary
              ${hasError ? "border-accent" : "border-gray-600"}
              ${readOnly ? "opacity-50 cursor-not-allowed" : ""}
            `}
          />
        </div>
      );

    case "multi_user_select":
      const selectedUsers = Array.isArray(value) ? value : [];
      return (
        <div className="space-y-2">
          <div className="text-sm text-text-secondary">
            Note: Multi-user selection requires API integration for user search
          </div>
          <textarea
            value={selectedUsers.join(", ")}
            onChange={(e) => onChange(e.target.value.split(",").map(u => u.trim()).filter(u => u))}
            placeholder={`Enter ${field.label.toLowerCase()} (comma-separated)`}
            disabled={readOnly}
            rows={3}
            className={`
              w-full p-2 border rounded bg-input text-text-primary
              ${hasError ? "border-accent" : "border-gray-600"}
              ${readOnly ? "opacity-50 cursor-not-allowed" : ""}
            `}
          />
          {selectedUsers.length > 0 && (
            <div className="space-y-1">
              <p className="text-sm font-medium">Selected users:</p>
              {selectedUsers.map((username: string, index: number) => (
                <div key={index} className="flex items-center justify-between bg-secondary p-2 rounded">
                  <span className="text-sm">{username}</span>
                  {!readOnly && (
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => onChange(selectedUsers.filter((_, i) => i !== index))}
                    >
                      Remove
                    </Button>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      );

    case "volunteer_hours":
      return (
        <div className="border border-blue-400 rounded p-4 bg-blue-50 dark:bg-blue-900/20">
          <div className="flex items-center gap-2 mb-2">
            <span className="text-lg">⏰</span>
            <span className="font-medium text-blue-700 dark:text-blue-300">Volunteer Hours Requirement</span>
          </div>
          <p className="text-sm text-blue-600 dark:text-blue-400 mb-2">
            This form requires <strong>{field.volunteerHours || 0} volunteer hours</strong> to be completed after approval.
          </p>
          <p className="text-xs text-blue-500 dark:text-blue-400">
            If your ship's form is approved, you will need to complete {field.volunteerHours || 0} hours of volunteer work.
            This requirement will be tracked automatically when you sign up for volunteer shifts.
          </p>
        </div>
      );

    default:
      return (
        <div className="text-sm text-text-secondary">
          Unsupported field type: {field.type}
        </div>
      );
  }
};

FormRenderer.displayName = "FormRenderer";