"use client";

import Link from "next/link";
import { useAuth } from "../../contexts/AuthContext";
import { AuthModal } from "../auth/AuthModal";
import { UserMenu } from "../user/UserMenu";
import { NotificationIcon } from "../notifications/NotificationIcon";
import { useCart } from "../../hooks/useCart";
import { useState, useEffect } from "react";

interface HeaderProps {
  toggleMobileNav: () => void;
}

export default function Header({ toggleMobileNav }: HeaderProps) {
  const { user, isAuthenticated, openAuthModal, logout } = useAuth();
  const [mounted, setMounted] = useState(false);
  const { data: cartData } = useCart();

  // Set mounted to true after component mounts to prevent hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  const cartItemCount = cartData?.cart?.items?.length || 0;

  return (
    <header className="bg-secondary px-4 py-4 shadow-md h-14">
      <div className="w-full flex justify-between items-center">
        <div className="flex items-center">
          <h1 className="text-xl font-bold ">Bank of Styx</h1>
        </div>

        <div className="flex items-center space-x-4">
          {/* Only show notification icon after client-side hydration */}
          {mounted && isAuthenticated && user && <NotificationIcon />}
          <Link
            href="/shop/cart"
            className="p-0 rounded-full hover:bg-secondary-light transition relative"
            aria-label="Shopping Cart"
          >
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="w-6 h-6"
            >
              <circle cx="9" cy="21" r="1"></circle>
              <circle cx="20" cy="21" r="1"></circle>
              <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
            </svg>
            {mounted && isAuthenticated && cartItemCount > 0 && (
              <>
                {cartItemCount <= 99 ? (
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium">
                    {cartItemCount}
                  </span>
                ) : (
                  <span className="absolute -top-1 -right-1 bg-red-500 h-3 w-3 rounded-full"></span>
                )}
              </>
            )}
          </Link>

          {/* Render a consistent UI for the first render, then update after hydration */}
          {!mounted ? (
            <>
              <button
                className="bg-primary hover:bg-primary-dark px-3 py-1 text-sm rounded transition"
                onClick={openAuthModal}
              >
                Login
              </button>
              <Link
                href="/bank"
                className="hidden sm:inline-flex bg-accent hover:bg-accent-dark px-3 py-1 text-sm rounded transition"
              >
                Bank
              </Link>
            </>
          ) : isAuthenticated && user ? (
            <UserMenu />
          ) : (
            <>
              <button
                className="bg-primary hover:bg-primary-dark px-3 py-1 text-sm rounded transition"
                onClick={openAuthModal}
              >
                Login
              </button>
              <Link
                href="/bank"
                className="hidden sm:inline-flex bg-accent hover:bg-accent-dark px-3 py-1 text-sm rounded transition"
              >
                Bank
              </Link>
            </>
          )}
        </div>
      </div>

      {/* Auth Modal */}
      <AuthModal />
    </header>
  );
}
