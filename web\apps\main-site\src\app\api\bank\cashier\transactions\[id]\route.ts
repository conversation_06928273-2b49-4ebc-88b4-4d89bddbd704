import { NextRequest, NextResponse } from "next/server";
import prisma from "../../../../../../lib/prisma";
import jwt from "jsonwebtoken";
import { createNotification } from "../../../../../../lib/notifications";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
const JWT_SECRET = process.env.JWT_SECRET || "your-development-secret-key";

// Helper function to verify token
function verifyToken(token: string) {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    return null;
  }
}

export const PATCH = async (
  req: NextRequest,
  { params }: { params: { id: string } },
) => {
  try {
    const id = params.id;

    // Get authorization header
    const authHeader = req.headers.get("authorization");

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Extract token
    const token = authHeader.split(" ")[1];

    // Verify token
    const decoded = verifyToken(token);

    if (!decoded || typeof decoded !== "object" || !decoded.id) {
      return NextResponse.json({ error: "Invalid token" }, { status: 401 });
    }

    const userId = decoded.id as string;

    // Check if user is a banker
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user || !user.isBanker) {
      return NextResponse.json(
        { error: "Unauthorized - Banker role required" },
        { status: 403 },
      );
    }

    // Get request body
    const { status, note } = await req.json();

    // Validate input
    if (!status || !["approved", "rejected"].includes(status)) {
      return NextResponse.json(
        { error: "Valid status is required (approved, rejected)" },
        { status: 400 },
      );
    }

    // Find the transaction
    const transaction = await prisma.transaction.findUnique({
      where: { id },
      include: {
        sender: true,
        recipient: true,
      },
    });

    if (!transaction) {
      return NextResponse.json(
        { error: "Transaction not found" },
        { status: 404 },
      );
    }

    // Check if the transaction is pending
    if (transaction.status !== "pending") {
      return NextResponse.json(
        { error: "Only pending transactions can be processed" },
        { status: 400 },
      );
    }

    // Process the transaction based on its type and the approval status
    if (status === "approved") {
      if (transaction.type === "deposit") {
        // For deposits, add the amount to the sender's balance
        await prisma.$transaction(async (prisma) => {
          // Update the transaction
          await prisma.transaction.update({
            where: { id },
            data: {
              status: "completed",
              note: note || "Approved by banker",
              processedById: userId,
              processedAt: new Date(),
            },
          });

          // Update the sender's balance
          await prisma.user.update({
            where: { id: transaction.senderId! },
            data: { balance: { increment: transaction.amount } },
          });
        });

        // Check if the user is also a cashier
        const userDetails = await prisma.user.findUnique({
          where: { id: transaction.senderId! },
          select: { isBanker: true },
        });

        // Create notification for the user with instructions in the message instead of a link
        const locationMessage = userDetails?.isBanker
          ? "You can view this transaction in the Cashier Dashboard > Pending Transactions section."
          : "You can view this transaction in the Bank Dashboard > Deposits section.";

        await createNotification(transaction.senderId!, {
          category: "transaction",
          type: "deposit_approved",
          title: "Deposit Approved",
          message: `Your deposit of NS ${transaction.amount.toFixed(
            0,
          )} has been approved and added to your balance. ${locationMessage}`,
          // No link property - clicking will just mark as read
          priority: "high",
          transactionId: transaction.id,
        });
      } else if (transaction.type === "withdrawal") {
        // For withdrawals, check if the sender has enough balance
        if (transaction.sender!.balance < transaction.amount) {
          return NextResponse.json(
            { error: "Insufficient balance for withdrawal" },
            { status: 400 },
          );
        }

        // Process the withdrawal
        await prisma.$transaction(async (prisma) => {
          // Update the transaction
          await prisma.transaction.update({
            where: { id },
            data: {
              status: "completed",
              note: note || "Approved by banker",
              processedById: userId,
              processedAt: new Date(),
            },
          });

          // Update the sender's balance
          await prisma.user.update({
            where: { id: transaction.senderId! },
            data: { balance: { decrement: transaction.amount } },
          });
        });

        // Create notification for the user with instructions in the message
        await createNotification(transaction.senderId!, {
          category: "transaction",
          type: "withdrawal_approved",
          title: "Withdrawal Approved",
          message: `Your withdrawal request of NS ${transaction.amount.toFixed(
            0,
          )} has been approved. You can view this transaction in the Bank Dashboard > Withdraw section.`,
          // No link property - clicking will just mark as read
          priority: "high",
          transactionId: transaction.id,
        });
      } else {
        return NextResponse.json(
          { error: "Unsupported transaction type for approval" },
          { status: 400 },
        );
      }
    } else {
      // For rejected transactions
      if (
        transaction.type === "withdrawal" &&
        transaction.status === "pending"
      ) {
        // For rejected withdrawals, return the funds to the user
        await prisma.$transaction(async (prisma) => {
          // Update the transaction
          await prisma.transaction.update({
            where: { id },
            data: {
              status: "failed",
              note: note || "Rejected by banker",
              processedById: userId,
              processedAt: new Date(),
            },
          });

          // Return the funds to the sender's balance
          await prisma.user.update({
            where: { id: transaction.senderId! },
            data: { balance: { increment: transaction.amount } },
          });
        });

        // Create notification for the user with instructions in the message
        await createNotification(transaction.senderId!, {
          category: "transaction",
          type: "withdrawal_rejected",
          title: "Withdrawal Rejected",
          message: `Your withdrawal request of NS ${transaction.amount.toFixed(
            0,
          )} has been rejected.${
            note ? ` Reason: ${note}` : ""
          } You can view this transaction in the Bank Dashboard > Withdraw section.`,
          // No link property - clicking will just mark as read
          priority: "high",
          transactionId: transaction.id,
        });
      } else {
        // For other rejected transactions, just update the status
        await prisma.transaction.update({
          where: { id },
          data: {
            status: "failed",
            note: note || "Rejected by banker",
            processedById: userId,
            processedAt: new Date(),
          },
        });

        // Create notification for deposit rejection
        if (transaction.type === "deposit") {
          // Check if the user is also a cashier
          const userDetails = await prisma.user.findUnique({
            where: { id: transaction.senderId! },
            select: { isBanker: true },
          });

          // Create notification with instructions in the message instead of a link
          const locationMessage = userDetails?.isBanker
            ? "You can view this transaction in the Cashier Dashboard > Pending Transactions section."
            : "You can view this transaction in the Bank Dashboard > Deposits section.";

          await createNotification(transaction.senderId!, {
            category: "transaction",
            type: "deposit_rejected",
            title: "Deposit Rejected",
            message: `Your deposit request of NS ${transaction.amount.toFixed(
              0,
            )} has been rejected.${
              note ? ` Reason: ${note}` : ""
            } ${locationMessage}`,
            // No link property - clicking will just mark as read
            priority: "high",
            transactionId: transaction.id,
          });
        }
      }
    }

    // Get the updated transaction
    const updatedTransaction = await prisma.transaction.findUnique({
      where: { id },
      include: {
        sender: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
        recipient: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
        processedBy: {
          select: {
            id: true,
            username: true,
            displayName: true,
          },
        },
      },
    });

    // Format dates for response
    const formattedTransaction = {
      ...updatedTransaction,
      createdAt: updatedTransaction!.createdAt.toISOString(),
      updatedAt: updatedTransaction!.updatedAt.toISOString(),
      processedAt: updatedTransaction!.processedAt
        ? updatedTransaction!.processedAt.toISOString()
        : null,
    };

    return NextResponse.json(formattedTransaction);
  } catch (error) {
    console.error("Error processing transaction:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
};
