import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser, userHasRole } from "@/lib/auth";

export const dynamic = 'force-dynamic';

// GET /api/sales/products/[id]/stats - Get product sales statistics
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if user is authenticated and has sales manager role
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 }
      );
    }

    const hasSalesRole = await userHasRole(req, "salesManager");
    if (!hasSalesRole) {
      return NextResponse.json(
        { error: "Unauthorized - Sales Manager role required" },
        { status: 403 }
      );
    }

    const productId = params.id;

    // Verify product exists
    const product = await prisma.product.findUnique({
      where: { id: productId },
      include: {
        category: true,
        event: {
          select: { id: true, name: true }
        }
      }
    });

    if (!product) {
      return NextResponse.json(
        { error: "Product not found" },
        { status: 404 }
      );
    }

    // Get sales statistics
    const [
      orderItems,
      totalStats
    ] = await Promise.all([
      // Get all order items for this product with user info
      prisma.orderItem.findMany({
        where: {
          productId: productId,
          order: {
            status: { in: ['paid', 'fulfilled'] } // Only count successful orders
          }
        },
        include: {
          order: {
            include: {
              user: {
                select: {
                  id: true,
                  username: true,
                  email: true,
                  displayName: true
                }
              }
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      }),
      
      // Get aggregate statistics
      prisma.orderItem.aggregate({
        where: {
          productId: productId,
          order: {
            status: { in: ['paid', 'fulfilled'] }
          }
        },
        _sum: {
          quantity: true,
          price: true
        },
        _count: {
          id: true
        }
      })
    ]);

    // Calculate revenue (quantity * price for each order item)
    const totalRevenue = orderItems.reduce((sum, item) => {
      return sum + (item.quantity * item.price);
    }, 0);

    const totalQuantitySold = totalStats._sum.quantity || 0;
    const totalOrders = totalStats._count.id || 0;

    // Group purchases by user
    const customerPurchases = orderItems.reduce((acc, item) => {
      const userId = item.order.user.id;
      if (!acc[userId]) {
        acc[userId] = {
          user: item.order.user,
          totalQuantity: 0,
          totalSpent: 0,
          orderCount: 0,
          orders: []
        };
      }
      
      acc[userId].totalQuantity += item.quantity;
      acc[userId].totalSpent += (item.quantity * item.price);
      acc[userId].orderCount += 1;
      acc[userId].orders.push({
        orderId: item.order.id,
        orderNumber: item.order.orderNumber,
        quantity: item.quantity,
        price: item.price,
        total: item.quantity * item.price,
        date: item.order.createdAt
      });
      
      return acc;
    }, {} as Record<string, any>);

    // Convert to array and sort by total spent
    const customers = Object.values(customerPurchases).sort((a: any, b: any) => 
      b.totalSpent - a.totalSpent
    );

    // Calculate monthly sales data for the last 12 months
    const now = new Date();
    const twelveMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 11, 1);
    
    const monthlySales = await prisma.orderItem.groupBy({
      by: ['createdAt'],
      where: {
        productId: productId,
        order: {
          status: { in: ['paid', 'fulfilled'] }
        },
        createdAt: {
          gte: twelveMonthsAgo
        }
      },
      _sum: {
        quantity: true,
        price: true
      }
    });

    // Group by month
    const monthlyData = new Map();
    for (let i = 0; i < 12; i++) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      monthlyData.set(key, { quantity: 0, revenue: 0 });
    }

    monthlySales.forEach(sale => {
      const date = new Date(sale.createdAt);
      const key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      const existing = monthlyData.get(key) || { quantity: 0, revenue: 0 };
      monthlyData.set(key, {
        quantity: existing.quantity + (sale._sum.quantity || 0),
        revenue: existing.revenue + (sale._sum.price || 0)
      });
    });

    const monthlyChart = Array.from(monthlyData.entries())
      .map(([month, data]) => ({ month, ...data }))
      .reverse();

    return NextResponse.json({
      product: {
        id: product.id,
        name: product.name,
        price: product.price,
        category: product.category.name,
        event: product.event?.name || null
      },
      stats: {
        totalQuantitySold,
        totalRevenue,
        totalOrders,
        averageOrderValue: totalOrders > 0 ? totalRevenue / totalOrders : 0,
        uniqueCustomers: customers.length
      },
      customers,
      monthlyChart,
      recentSales: orderItems.slice(0, 10).map(item => ({
        id: item.id,
        orderNumber: item.order.orderNumber,
        customer: item.order.user,
        quantity: item.quantity,
        price: item.price,
        total: item.quantity * item.price,
        date: item.order.createdAt
      }))
    });

  } catch (error) {
    console.error("Error fetching product stats:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}