// Create a test user with editor privileges for news article management
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');
const crypto = require('crypto');

const prisma = new PrismaClient();

async function createTestEditor() {
  try {
    // Generate a random password
    const password = crypto.randomBytes(8).toString('hex');
    const saltRounds = 10;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // Create the test editor account
    const user = await prisma.user.create({
      data: {
        username: 'test_editor',
        displayName: 'Test Editor',
        email: '<EMAIL>',
        passwordHash: passwordHash,
        isEditor: true,
        isEmailVerified: true
      }
    });

    console.log('Test editor created successfully:');
    console.log(`Username: ${user.username}`);
    console.log(`Display Name: ${user.displayName}`);
    console.log(`Email: ${user.email}`);
    console.log(`Password: ${password}`);
    console.log(`User ID: ${user.id}`);
    console.log('This user has editor privileges for managing news articles.');

    return user;
  } catch (error) {
    console.error('Error creating test editor:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Execute the function
createTestEditor()
  .catch(console.error);
