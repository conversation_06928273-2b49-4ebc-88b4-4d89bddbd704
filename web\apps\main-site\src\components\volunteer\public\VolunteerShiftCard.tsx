"use client";

import React from "react";
import { format } from "date-fns";
import { VolunteerShift } from "@/hooks/usePublicVolunteer";

interface VolunteerShiftCardProps {
  shift: VolunteerShift;
  onClick: () => void;
}

export const VolunteerShiftCard: React.FC<VolunteerShiftCardProps> = ({
  shift,
  onClick,
}) => {
  // Format time range using date-fns
  const formatTimeRange = (startTime: string, endTime: string) => {
    const start = new Date(startTime);
    const end = new Date(endTime);
    return `${format(start, "h:mm a")} - ${format(end, "h:mm a")}`;
  };

  // Calculate shift duration in hours
  const calculateDuration = (startTime: string, endTime: string) => {
    const start = new Date(startTime);
    const end = new Date(endTime);
    const durationMs = end.getTime() - start.getTime();
    const durationHours = durationMs / (1000 * 60 * 60);
    return durationHours.toFixed(1);
  };

  return (
    <div
      className="bg-secondary-light rounded-lg shadow-md overflow-hidden border border-gray-600 hover:shadow-lg transition-shadow duration-300 cursor-pointer"
      onClick={onClick}
    >
      <div className="p-4">
        {/* Shift Title */}
        <h3 className="text-lg font-bold text-white mb-2">{shift.title}</h3>

        {/* Time */}
        <div className="flex items-center mb-3">
          <svg
            className="h-5 w-5 mr-2 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          <span className="text-gray-300">
            {formatTimeRange(shift.startTime, shift.endTime)}
          </span>
        </div>

        {/* Location */}
        {shift.location && (
          <div className="flex items-center mb-3">
            <svg
              className="h-5 w-5 mr-2 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
              />
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
              />
            </svg>
            <span className="text-gray-300">{shift.location}</span>
          </div>
        )}

        {/* Shift Details */}
        <div className="grid grid-cols-2 gap-2 mb-3">
          <div className="bg-secondary p-2 rounded-md text-center">
            <div className="text-xs text-gray-400">Duration</div>
            <div className="text-white font-medium">
              {calculateDuration(shift.startTime, shift.endTime)} hrs
            </div>
          </div>
          <div className="bg-secondary p-2 rounded-md text-center">
            <div className="text-xs text-gray-400">Payment</div>
            <div className="text-white font-medium">
              £
              {(
                parseFloat(calculateDuration(shift.startTime, shift.endTime)) *
                shift.category.payRate
              ).toFixed(2)}
            </div>
          </div>
        </div>

        {/* Available Slots */}
        <div className="bg-primary bg-opacity-20 p-3 rounded-md flex justify-between items-center">
          <span className="text-gray-300">Available Slots:</span>
          <span className="text-lg font-bold text-primary">
            {shift.vacancies} / {shift.maxVolunteers}
          </span>
        </div>
      </div>
    </div>
  );
};
