# Banking API Routes

Core banking functionality providing account management, transactions, and financial operations.

## Account Management

- **account-summary/** - Account balance and summary information
- **user/** - User-specific banking operations and settings
- **statistics/** - Banking statistics and financial metrics

## Transaction Processing

- **transactions/** - Transaction history, processing, and management
- **pay-codes/** - Pay code generation, validation, and redemption system

## Staff Operations

- **cashier/** - Cashier-specific banking operations and transaction processing

These endpoints provide the complete banking experience including real-time balance updates, secure transaction processing, and the innovative pay code system for digital payments within the Bank of Styx platform.
