"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "../../../../contexts/AuthContext";
import { NewsDashboardLayout } from "../../../../components/news";
import {
  useArticles,
  useToggleArticleFeatured,
} from "../../../../hooks/useNews";
import Link from "next/link";

export default function NewsFeaturedPage() {
  const { user, isLoading: authLoading } = useAuth();
  const router = useRouter();
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [filterStatus, setFilterStatus] = useState<
    "all" | "featured" | "not-featured"
  >("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState<"createdAt" | "title" | "views">(
    "createdAt",
  );
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [dashboardData, setDashboardData] = useState<any>(null);

  // Build API filters
  const filters = {
    featured: filterStatus === "featured" ? true : undefined,
    sortBy,
    order: sortOrder,
    search: searchTerm || undefined,
  };

  // Fetch articles with React Query
  const {
    data: articlesData,
    isLoading: articlesLoading,
    isError: articlesError,
    refetch: refetchArticles,
  } = useArticles(filters);

  // Process article data to build statistics
  useEffect(() => {
    if (articlesData?.data) {
      const articles = articlesData.data;
      const featuredArticles = articles.filter((a) => a.featured).length;
      const totalArticles = articles.length;

      // Set dashboard data
      setDashboardData({
        stats: {
          featuredArticles,
          totalArticles,
        },
      });
    }
  }, [articlesData]);

  // Toggle featured mutation
  const toggleFeaturedMutation = useToggleArticleFeatured();

  // Check authorization after auth is loaded
  useEffect(() => {
    // Only proceed with checks if auth loading is complete
    if (!authLoading) {
      if (!user || !user.roles?.editor) {
        router.push("/");
      } else {
        // User is authorized
        setIsAuthorized(true);
      }
    }
  }, [user, router, authLoading]);

  // Handle featured toggle
  const handleToggleFeatured = (id: string) => {
    try {
      toggleFeaturedMutation.mutate(id, {
        onSuccess: () => {
          refetchArticles();
        },
      });
    } catch (error) {
      console.error("Error toggling article featured status:", error);
    }
  };

  // Show loading state or nothing while checking authorization
  if (authLoading || !isAuthorized) {
    return (
      <div className="min-h-screen bg-secondary-dark flex items-center justify-center">
        <div className="bg-secondary-light p-6 rounded-lg shadow-md border border-gray-600">
          <p className="text-white text-lg">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <NewsDashboardLayout dashboardData={dashboardData}>
      <div className="space-y-6">
        <div className="bg-secondary-light rounded-lg shadow-md p-6 border border-gray-600">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-white">
              Featured Content Management
            </h2>
            <Link
              href="/news/dashboard/articles/new"
              className="bg-primary hover:bg-primary-dark text-white px-4 py-2 rounded-md text-sm font-medium"
            >
              Create New Article
            </Link>
          </div>

          <p className="text-gray-400 mb-6">
            Manage which articles are featured on the news page and homepage.
            Featured articles appear in special sections and receive more
            visibility.
          </p>

          <div className="mb-6 flex flex-col md:flex-row gap-4">
            {/* Search input */}
            <div className="flex-1">
              <div className="relative rounded-md shadow-sm">
                <input
                  type="text"
                  className="bg-secondary border border-gray-600 text-white w-full rounded-md py-2 px-4 focus:outline-none focus:ring-2 focus:ring-primary"
                  placeholder="Search articles..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                  <svg
                    className="h-5 w-5 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>
                </div>
              </div>
            </div>

            {/* Filter buttons */}
            <div className="flex gap-2">
              <button
                onClick={() => setFilterStatus("all")}
                className={`px-4 py-2 rounded-md text-sm ${
                  filterStatus === "all"
                    ? "bg-primary text-white"
                    : "bg-secondary text-gray-400 hover:bg-secondary-dark"
                }`}
              >
                All Articles
              </button>
              <button
                onClick={() => setFilterStatus("featured")}
                className={`px-4 py-2 rounded-md text-sm ${
                  filterStatus === "featured"
                    ? "bg-primary text-white"
                    : "bg-secondary text-gray-400 hover:bg-secondary-dark"
                }`}
              >
                Featured
              </button>
              <button
                onClick={() => setFilterStatus("not-featured")}
                className={`px-4 py-2 rounded-md text-sm ${
                  filterStatus === "not-featured"
                    ? "bg-primary text-white"
                    : "bg-secondary text-gray-400 hover:bg-secondary-dark"
                }`}
              >
                Not Featured
              </button>
            </div>
          </div>

          {articlesLoading ? (
            <div className="text-center py-8">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary mb-2"></div>
              <p className="text-gray-400">Loading articles...</p>
            </div>
          ) : articlesError ? (
            <div className="bg-accent bg-opacity-20 text-accent border border-accent rounded-md p-4 mb-6">
              <p>Error loading articles. Please try again later.</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-600">
                <thead className="bg-secondary">
                  <tr>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"
                    >
                      Article
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"
                    >
                      Author
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"
                    >
                      Status
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"
                    >
                      Featured
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"
                    >
                      Views
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"
                    >
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-secondary-light divide-y divide-gray-600">
                  {articlesData?.data && articlesData.data.length > 0 ? (
                    articlesData.data.map((article) => (
                      <tr key={article.id}>
                        <td className="px-6 py-4">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 h-10 w-10">
                              <img
                                className="h-10 w-10 rounded-md object-cover"
                                src={article.image || "/images/placeholder.jpg"}
                                alt={article.title}
                              />
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-white">
                                {article.title}
                              </div>
                              <div className="text-sm text-gray-400">
                                {article.category.name}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-400">
                            {article.author.displayName}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span
                            className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                              article.status === "published"
                                ? "bg-green-900 text-green-200"
                                : article.status === "draft"
                                ? "bg-gray-700 text-gray-300"
                                : "bg-yellow-900 text-yellow-200"
                            }`}
                          >
                            {article.status === "published"
                              ? "Published"
                              : article.status === "draft"
                              ? "Draft"
                              : "Paused"}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <input
                              type="checkbox"
                              className="h-4 w-4 text-primary focus:ring-primary border-gray-600 rounded"
                              checked={article.featured}
                              onChange={() => handleToggleFeatured(article.id)}
                              disabled={toggleFeaturedMutation.isPending}
                            />
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-400">
                            {article.views}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-3">
                            <Link
                              href={`/news/dashboard/articles/${article.id}`}
                              className="text-primary hover:text-primary-light"
                            >
                              Edit
                            </Link>
                            <button
                              onClick={() => handleToggleFeatured(article.id)}
                              className="text-primary hover:text-primary-light"
                              disabled={toggleFeaturedMutation.isPending}
                            >
                              {article.featured ? "Unfeature" : "Feature"}
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td
                        colSpan={6}
                        className="px-6 py-4 text-center text-gray-400"
                      >
                        No articles found.
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>

              {articlesData && articlesData.meta.totalPages > 1 && (
                <div className="flex justify-center items-center mt-6 space-x-2">
                  <button
                    className="px-3 py-1 rounded-md bg-secondary text-gray-400 hover:bg-secondary-dark disabled:opacity-50"
                    disabled={!articlesData.meta.hasPrev}
                    onClick={() => {
                      // Handle previous page
                    }}
                  >
                    Previous
                  </button>
                  <span className="text-gray-400">
                    Page {articlesData.meta.currentPage} of{" "}
                    {articlesData.meta.totalPages}
                  </span>
                  <button
                    className="px-3 py-1 rounded-md bg-secondary text-gray-400 hover:bg-secondary-dark disabled:opacity-50"
                    disabled={!articlesData.meta.hasNext}
                    onClick={() => {
                      // Handle next page
                    }}
                  >
                    Next
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </NewsDashboardLayout>
  );
}
