/**
 * @file c:\Users\<USER>\projects\Bank-of-styx-website\web\apps\api\auth\test\route.ts
 * @summary Defines API route handlers for testing purposes at `/api/auth/test`. It includes handlers for GET, POST, and OPTIONS requests, primarily for debugging and verifying API connectivity and CORS configuration.
 */
import { NextRequest, NextResponse } from "next/server";
import { handleOptionRequest } from "../../middleware";

/**
 * Handles OPTIONS requests for the test endpoint.
 * @param {NextRequest} req - The incoming request object.
 * @returns {Promise<NextResponse | null>} A response suitable for CORS preflight.
 */
export const OPTIONS = async (req: NextRequest) => {
  return handleOptionRequest(req);
};

/**
 * Handles GET requests for the test endpoint. Returns request information.
 * @param {NextRequest} req - The incoming request object.
 * @returns {Promise<NextResponse>} A JSON response containing request details.
 */
export const GET = async (req: NextRequest) => {
  // Return information about the request to help with debugging
  return NextResponse.json({
    message: "API test endpoint is working!",
    headers: Object.fromEntries(req.headers),
    url: req.url,
    method: req.method,
    time: new Date().toISOString(),
  });
};

/**
 * Handles POST requests for the test endpoint. Returns request information and the received body.
 * @param {NextRequest} req - The incoming request object.
 * @returns {Promise<NextResponse>} A JSON response containing request details and the parsed body, or an error response.
 */
export const POST = async (req: NextRequest) => {
  try {
    // Get the body of the request if any
    const body = await req.json();

    return NextResponse.json({
      message: "POST to test endpoint successful!",
      receivedBody: body,
      headers: Object.fromEntries(req.headers),
      url: req.url,
      method: req.method,
      time: new Date().toISOString(),
    });
  } catch (error) {
    return NextResponse.json(
      {
        message: "Error parsing JSON body",
        error: error.message,
        time: new Date().toISOString(),
      },
      { status: 400 },
    );
  }
};
