# Core Utilities & Configuration

Core utility functions, configurations, and shared libraries used throughout the Bank of Styx application.

## Contents

This directory typically includes:

- Database connection and Prisma client configuration
- Authentication utilities and JWT handling
- Validation schemas and form validators
- Date/time utilities and formatters
- Encryption and security utilities
- API response formatters
- Constants and configuration objects
- Third-party service integrations

These utilities provide the foundational functionality that powers the application's core features and business logic.
