"use client";

import React from "react";
import { Mo<PERSON>, But<PERSON> } from "@bank-of-styx/ui";
import { useQuery } from "@tanstack/react-query";
import fetchClient from "@/lib/fetchClient";

interface ShiftStatusModalProps {
  isOpen: boolean;
  onClose: () => void;
  shiftId: string;
  shiftTitle: string;
}

interface User {
  id: string;
  displayName: string;
  username: string;
  email: string;
  discordId?: string;
}

interface Payment {
  status: string;
  amount: number;
  hoursWorked: number;
  verifiedAt: string;
  verifiedBy?: string;
}

interface Assignment {
  id: string;
  status: string;
  signupDate: string;
  checkedInAt?: string;
  user: User;
  payment?: Payment;
}

interface ShiftStatusData {
  shift: {
    id: string;
    title: string;
    startTime: string;
    endTime: string;
    location?: string;
    maxVolunteers: number;
    event: { name: string };
    category: { name: string; payRate: number };
  };
  statistics: {
    totalSlots: number;
    filledSlots: number;
    availableSlots: number;
    checkedInCount: number;
    completedCount: number;
    pendingPayments: number;
  };
  assignments: Assignment[];
}

export const ShiftStatusModal: React.FC<ShiftStatusModalProps> = ({
  isOpen,
  onClose,
  shiftId,
  shiftTitle,
}) => {
  const {
    data: statusData,
    isLoading,
    error,
  } = useQuery<ShiftStatusData>({
    queryKey: ["shiftStatus", shiftId],
    queryFn: () => fetchClient.get(`/api/volunteer/shifts/${shiftId}/status`),
    enabled: isOpen && !!shiftId,
  });

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString("en-US", {
      weekday: "short",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "text-green-400";
      case "checked_in":
        return "text-blue-400";
      case "assigned":
        return "text-yellow-400";
      case "no_show":
        return "text-red-400";
      default:
        return "text-gray-400";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "completed":
        return "Completed";
      case "checked_in":
        return "Checked In";
      case "assigned":
        return "Assigned";
      case "no_show":
        return "No Show";
      default:
        return status;
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case "paid":
        return "text-green-400";
      case "pending":
        return "text-yellow-400";
      case "failed":
        return "text-red-400";
      default:
        return "text-gray-400";
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={`Shift Status: ${shiftTitle}`}
      size="lg"
    >
      <div className="space-y-6">
        {isLoading && (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
          </div>
        )}

        {error && (
          <div className="bg-red-900/20 border border-red-500 rounded-md p-4 text-red-400">
            <p>Error loading shift status</p>
          </div>
        )}

        {statusData && (
          <>
            {/* Shift Overview */}
            <div className="bg-secondary-light rounded-lg p-4 border border-gray-600">
              <h3 className="text-lg font-semibold text-white mb-3">
                Shift Overview
              </h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-400">Event:</span>
                  <p className="text-white">{statusData.shift.event.name}</p>
                </div>
                <div>
                  <span className="text-gray-400">Category:</span>
                  <p className="text-white">{statusData.shift.category.name}</p>
                </div>
                <div>
                  <span className="text-gray-400">Time:</span>
                  <p className="text-white">
                    {formatTime(statusData.shift.startTime)} -{" "}
                    {formatTime(statusData.shift.endTime)}
                  </p>
                </div>
                <div>
                  <span className="text-gray-400">Location:</span>
                  <p className="text-white">
                    {statusData.shift.location || "Not specified"}
                  </p>
                </div>
              </div>
            </div>

            {/* Statistics */}
            <div className="bg-secondary-light rounded-lg p-4 border border-gray-600">
              <h3 className="text-lg font-semibold text-white mb-3">
                Statistics
              </h3>
              <div className="grid grid-cols-3 md:grid-cols-6 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-primary">
                    {statusData.statistics.filledSlots}
                  </div>
                  <div className="text-xs text-gray-400">Filled</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-gray-400">
                    {statusData.statistics.availableSlots}
                  </div>
                  <div className="text-xs text-gray-400">Available</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-blue-400">
                    {statusData.statistics.checkedInCount}
                  </div>
                  <div className="text-xs text-gray-400">Checked In</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-400">
                    {statusData.statistics.completedCount}
                  </div>
                  <div className="text-xs text-gray-400">Completed</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-yellow-400">
                    {statusData.statistics.pendingPayments}
                  </div>
                  <div className="text-xs text-gray-400">Pending Pay</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-primary">
                    ${statusData.shift.category.payRate}/hr
                  </div>
                  <div className="text-xs text-gray-400">Pay Rate</div>
                </div>
              </div>
            </div>

            {/* Volunteer Assignments */}
            <div className="bg-secondary-light rounded-lg p-4 border border-gray-600">
              <h3 className="text-lg font-semibold text-white mb-3">
                Volunteer Assignments ({statusData.assignments.length})
              </h3>
              
              {statusData.assignments.length === 0 ? (
                <p className="text-gray-400 text-center py-4">
                  No volunteers assigned to this shift yet.
                </p>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b border-gray-600">
                        <th className="text-left py-2 text-gray-400">Volunteer</th>
                        <th className="text-left py-2 text-gray-400">Status</th>
                        <th className="text-left py-2 text-gray-400">Signed Up</th>
                        <th className="text-left py-2 text-gray-400">Checked In</th>
                        <th className="text-left py-2 text-gray-400">Payment</th>
                        <th className="text-left py-2 text-gray-400">Hours</th>
                      </tr>
                    </thead>
                    <tbody>
                      {statusData.assignments.map((assignment) => (
                        <tr
                          key={assignment.id}
                          className="border-b border-gray-700"
                        >
                          <td className="py-3">
                            <div>
                              <div className="text-white font-medium">
                                {assignment.user.displayName || assignment.user.username}
                              </div>
                              <div className="text-gray-400 text-xs">
                                {assignment.user.username && (
                                  <span>@{assignment.user.username}</span>
                                )}
                              </div>
                            </div>
                          </td>
                          <td className="py-3">
                            <span
                              className={`${getStatusColor(
                                assignment.status,
                              )} font-medium`}
                            >
                              {getStatusText(assignment.status)}
                            </span>
                          </td>
                          <td className="py-3 text-gray-300">
                            {formatDate(assignment.signupDate)}
                          </td>
                          <td className="py-3 text-gray-300">
                            {assignment.checkedInAt
                              ? formatDate(assignment.checkedInAt)
                              : "-"}
                          </td>
                          <td className="py-3">
                            {assignment.payment ? (
                              <div>
                                <span
                                  className={`${getPaymentStatusColor(
                                    assignment.payment.status,
                                  )} font-medium capitalize`}
                                >
                                  {assignment.payment.status}
                                </span>
                                <div className="text-gray-400 text-xs">
                                  ${assignment.payment.amount.toFixed(2)}
                                </div>
                              </div>
                            ) : (
                              <span className="text-gray-400">-</span>
                            )}
                          </td>
                          <td className="py-3 text-gray-300">
                            {assignment.payment?.hoursWorked
                              ? `${assignment.payment.hoursWorked.toFixed(1)}h`
                              : "-"}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </>
        )}

        {/* Close Button */}
        <div className="flex justify-end pt-4">
          <Button variant="secondary" onClick={onClose}>
            Close
          </Button>
        </div>
      </div>
    </Modal>
  );
};