// Export all V2 upload components for easy importing
export { default as UniversalImageUploader } from "../common/UniversalImageUploader";
export { default as AvatarUploaderV2 } from "./AvatarUploaderV2";
export { default as NewsImageUploaderV2 } from "./NewsImageUploaderV2";
export { NewsImageUploader } from "./NewsImageUploader";
export { default as ProductImageUploader } from "./ProductImageUploader";
export { default as DepositReceiptUploader } from "./DepositReceiptUploader";
export { default as EventImageUploader } from "./EventImageUploader";
export { default as ShipLogoUploader } from "./ShipLogoUploader";

// Re-export types for convenience
export type {
  UploadResponse,
  UploadType,
  UploadOptions,
  UniversalImageUploaderProps,
  UploadProgress,
} from "../../types/upload";
