import { toast } from "react-hot-toast";

export const copyToClipboard = (text: string) => {
  if (navigator.clipboard) {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        toast.success("Code copied to clipboard!");
      })
      .catch(() => {
        toast.error("Failed to copy code");
      });
  } else {
    // Fallback for browsers that don't support clipboard API
    const textArea = document.createElement("textarea");
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
      const successful = document.execCommand("copy");
      if (successful) {
        toast.success("Code copied to clipboard!");
      } else {
        toast.error("Failed to copy code");
      }
    } catch (err) {
      toast.error("Failed to copy code");
    }

    document.body.removeChild(textArea);
  }
};
