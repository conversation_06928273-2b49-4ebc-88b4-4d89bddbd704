import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser, userHasRole } from "@/lib/auth";

// Force dynamic rendering for this route due to authentication
export const dynamic = 'force-dynamic';

export async function GET(request: Request) {
  try {
    // Check if user is authenticated and has admin role
    const currentUser = await getCurrentUser(request);
    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    const isAdmin = await userHasRole(request, "admin");
    if (!isAdmin) {
      return NextResponse.json(
        { error: "Admin privileges required" },
        { status: 403 },
      );
    }

    // Get total users count
    const totalUsers = await prisma.user.count();

    // Get active merchants count
    const activeMerchants = await prisma.user.count({
      where: {
        merchantStatus: "approved",
      },
    });

    // Get pending applications count
    const pendingApplications = await prisma.user.count({
      where: {
        merchantStatus: "pending",
      },
    });

    // Auction functionality has been removed
    const activeAuctions = 0;

    // Get recent activity
    // This would typically come from an activity log table
    // For now, we'll return placeholder text
    const recentActivity = {
      userManagement: `${pendingApplications} new merchant applications`,
      featuredContent: "5 items pending review",
    };

    return NextResponse.json({
      quickStats: {
        totalUsers,
        activeMerchants,
        pendingApplications,
        activeAuctions,
      },
      recentActivity,
    });
  } catch (error) {
    console.error("Error fetching admin dashboard stats:", error);
    return NextResponse.json(
      { error: "Failed to fetch admin dashboard stats" },
      { status: 500 },
    );
  }
}
