/**
 * Admin API Service
 * Provides functions for interacting with the Admin API endpoints
 */
import fetchClient from "@/lib/fetchClient";

// Types
export interface AdminDashboardStats {
  quickStats: {
    totalUsers: number;
    activeMerchants: number;
    pendingApplications: number;
    activeAuctions: number;
  };
  recentActivity: {
    userManagement: string;
    featuredContent: string;
  };
}

export interface AdminUser {
  id: string;
  username: string;
  displayName: string;
  avatar: string;
  email: string;
  isEmailVerified: boolean;
  status: "active" | "pending" | "inactive" | "suspended" | "frozen";
  roles: {
    admin: boolean;
    editor: boolean;
    banker: boolean;
    chatModerator: boolean;
    volunteerCoordinator: boolean;
    leadManager: boolean;
    volunteer: boolean;
    salesManager: boolean;
    landSteward: boolean;
  };
  createdAt: string;
}

export interface FeaturedContent {
  id: string;
  title: string;
  type: "news" | "hero";
  image: string | null;
  featured: boolean;
  updatedAt: string;
}

// Using the centralized fetch client for all API calls

// Admin dashboard stats
export const getAdminDashboardStats =
  async (): Promise<AdminDashboardStats> => {
    return fetchClient.get<AdminDashboardStats>("/api/admin/dashboard/stats");
  };

// Get all users (with pagination and filtering)
export const getUsers = async (params?: {
  page?: number;
  limit?: number;
  search?: string;
  role?: string;
  status?: string;
}): Promise<{ users: AdminUser[]; total: number; pages: number }> => {
  const queryParams: Record<string, string> = {};

  if (params?.page) queryParams.page = params.page.toString();
  if (params?.limit) queryParams.limit = params.limit.toString();
  if (params?.search) queryParams.search = params.search;
  if (params?.role) queryParams.role = params.role;
  if (params?.status) queryParams.status = params.status;

  return fetchClient.get<{ users: AdminUser[]; total: number; pages: number }>(
    "/api/admin/users",
    { params: queryParams },
  );
};

// Get a single user by ID
export const getUserById = async (id: string): Promise<AdminUser> => {
  return fetchClient.get<AdminUser>(`/api/admin/users/${id}`);
};

// Update user roles
export const updateUserRoles = async (
  userId: string,
  roles: {
    admin?: boolean;
    editor?: boolean;
    banker?: boolean;
    chatModerator?: boolean;
    volunteerCoordinator?: boolean;
    leadManager?: boolean;
    salesManager?: boolean;
    landSteward?: boolean;
  },
): Promise<AdminUser> => {
  return fetchClient.patch<AdminUser>(
    `/api/admin/users/${userId}/roles`,
    roles,
  );
};

// Get featured content
export const getFeaturedContent = async (
  type?: "news" | "hero",
): Promise<FeaturedContent[]> => {
  const params: Record<string, string> = {};

  if (type) {
    params.type = type;
  }

  return fetchClient.get<FeaturedContent[]>("/api/admin/featured", { params });
};

// Toggle featured status
export const toggleFeaturedStatus = async (
  id: string,
  type: "news" | "hero",
): Promise<FeaturedContent> => {
  return fetchClient.put<FeaturedContent>(`/api/admin/featured/${id}`, {
    type,
  });
};

// Update user status
export const updateUserStatus = async (
  userId: string,
  status: "active" | "pending" | "inactive" | "suspended" | "frozen",
): Promise<AdminUser> => {
  return fetchClient.put<AdminUser>(`/api/admin/users/${userId}/status`, {
    status,
  });
};

// Update user email
export const updateUserEmail = async (
  userId: string,
  email: string,
): Promise<AdminUser> => {
  return fetchClient.put<AdminUser>(`/api/admin/users/${userId}/email`, {
    email,
  });
};

// Reset user password
export const resetUserPassword = async (
  userId: string,
): Promise<{
  success: boolean;
  message: string;
  tempPassword: string;
  emailSent: boolean;
}> => {
  return fetchClient.post<{
    success: boolean;
    message: string;
    tempPassword: string;
    emailSent: boolean;
  }>(`/api/admin/users/${userId}/reset-password`, {});
};

// Create a new user
export const createUser = async (userData: {
  username: string;
  displayName: string;
  email: string;
  password: string;
  roles: {
    admin: boolean;
    editor: boolean;
    banker: boolean;
    chatModerator: boolean;
    volunteerCoordinator: boolean;
    leadManager: boolean;
    landSteward: boolean;
  };
  status: string;
}): Promise<AdminUser> => {
  return fetchClient.post<AdminUser>(`/api/admin/users/create`, userData);
};
