"use client";

import { useState } from "react";
import { useNotifications } from "../../../contexts/NotificationContext";
import { useUserState } from "../../../contexts/UserStateContext";
import { createNotification } from "../../../services/notificationService";
import { useAuth } from "../../../contexts/AuthContext";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useSSE } from "../../../hooks/useSSE";

export default function RealtimeTestPage() {
  const { user } = useAuth();
  const { notifications, unreadCount, refreshNotifications } =
    useNotifications();
  const { userState, updateNotificationTimestamp } = useUserState();
  const [message, setMessage] = useState("");
  const queryClient = useQueryClient();

  // Initialize SSE connection
  const {
    isConnected: sseConnected,
    lastEventTime,
    connect: connectSSE,
    disconnect: disconnectSSE,
    error: sseError,
  } = useSSE({
    onMessage: (data) => {
      console.log("[SSE] Received message:", data);

      // Handle notification messages
      if (data.type === "notification") {
        console.log(
          "[SSE Test Page] Received notification:",
          data.notification.title,
        );
        // The notification will be automatically added to the notifications list
        // by the useSSE hook's integration with TanStack Query
      }
    },
    onConnect: () => {
      console.log("[SSE Test Page] Connection established");
    },
    onError: (err) => {
      console.error("[SSE Test Page] Connection error:", err);
    },
    // Don't enable SSE by default - let the user connect manually
    enabled: false,
  });

  // Function to handle manual connection
  const handleConnect = () => {
    console.log("[SSE Test Page] Attempting to connect...");
    connectSSE();
  };

  const createNotificationMutation = useMutation({
    mutationFn: (message: string) =>
      createNotification({
        userId: user?.id || "",
        category: "test",
        type: "test_notification",
        title: "Test Notification",
        message,
        priority: "medium",
      }),
    onMutate: async (message) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ["notifications"] });

      // Snapshot the previous value
      const previousNotifications =
        queryClient.getQueryData<any[]>(["notifications"]) || [];

      // Optimistically update to the new value
      const optimisticNotification = {
        id: `temp-${Date.now()}`,
        userId: user?.id || "",
        category: "test",
        type: "test_notification",
        title: "Test Notification",
        message,
        priority: "medium",
        read: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      queryClient.setQueryData(["notifications"], (old: any[] = []) => {
        return [optimisticNotification, ...old];
      });

      return { previousNotifications };
    },
    onSuccess: (data) => {
      // The response now includes notification and serverTimestamp
      if (data && data.serverTimestamp) {
        updateNotificationTimestamp();
      }
      queryClient.invalidateQueries({ queryKey: ["notifications"] });
      setMessage("");
    },
    onError: (err, message, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousNotifications) {
        queryClient.setQueryData(
          ["notifications"],
          context.previousNotifications,
        );
      }
    },
  });

  const handleCreateNotification = () => {
    if (!message.trim()) return;
    createNotificationMutation.mutate(message);
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Real-Time Notification Test</h1>

      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">Create Test Notification</h2>
        <div className="flex gap-2">
          <input
            type="text"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder="Notification message"
            className="flex-1 p-2 border rounded"
          />
          <button
            onClick={handleCreateNotification}
            disabled={createNotificationMutation.isPending || !message.trim()}
            className="px-4 py-2 bg-primary text-white rounded disabled:opacity-50"
          >
            Send
          </button>
        </div>
      </div>

      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">
          Recent Notifications ({unreadCount} unread)
        </h2>
        <div className="border rounded p-4 max-h-80 overflow-y-auto">
          {notifications.length === 0 ? (
            <p className="text-gray-500">No notifications yet</p>
          ) : (
            <ul className="space-y-2">
              {notifications.map((notification) => (
                <li
                  key={notification.id}
                  className={`p-3 rounded ${
                    notification.read
                      ? "bg-secondary-light"
                      : "bg-primary-light"
                  }`}
                >
                  <p className="font-semibold">{notification.title}</p>
                  <p>{notification.message}</p>
                  <p className="text-xs text-gray-500">
                    {new Date(notification.createdAt).toLocaleString()}
                  </p>
                </li>
              ))}
            </ul>
          )}
        </div>
      </div>

      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">Connection Status</h2>
        <div className="border rounded p-4 bg-secondary-light">
          <div className="flex items-center mb-2">
            <div
              className={`w-3 h-3 rounded-full mr-2 ${
                sseConnected ? "bg-green-500" : "bg-red-500"
              }`}
            ></div>
            <p>
              SSE Connection:
              <span
                className={
                  sseConnected
                    ? "text-green-500 ml-2 font-bold"
                    : "text-red-500 ml-2 font-bold"
                }
              >
                {sseConnected ? "Connected" : "Disconnected"}
              </span>
            </p>
          </div>

          <div className="mb-2">
            <p className="font-semibold">Last Event:</p>
            <p className="ml-2">
              {lastEventTime
                ? new Date(lastEventTime).toLocaleString() +
                  "." +
                  new Date(lastEventTime)
                    .getMilliseconds()
                    .toString()
                    .padStart(3, "0")
                : "None"}
            </p>
          </div>

          {lastEventTime && (
            <div className="mb-2">
              <p className="font-semibold">Time Since Last Event:</p>
              <p className="ml-2">
                {lastEventTime
                  ? `${Math.floor(
                      (new Date().getTime() -
                        new Date(lastEventTime).getTime()) /
                        1000,
                    )} seconds ago`
                  : "N/A"}
              </p>
            </div>
          )}

          {sseError && (
            <div className="mb-2 p-2 bg-red-100 border border-red-300 rounded">
              <p className="font-semibold text-red-700">Error:</p>
              <p className="text-red-700 ml-2">{sseError.message}</p>
            </div>
          )}

          <div className="flex gap-2 mt-4">
            <button
              onClick={handleConnect}
              disabled={sseConnected}
              className="px-4 py-2 bg-green-600 text-white rounded disabled:opacity-50"
            >
              Connect
            </button>
            <button
              onClick={disconnectSSE}
              disabled={!sseConnected}
              className="px-4 py-2 bg-red-600 text-white rounded disabled:opacity-50"
            >
              Disconnect
            </button>
          </div>
        </div>
      </div>

      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">Debug Information</h2>
        <div className="border rounded p-4 bg-secondary-light">
          <p>
            Last Notification Sync:{" "}
            {userState?.lastNotificationSync
              ? new Date(userState.lastNotificationSync).toLocaleString()
              : "Never"}
          </p>
          <p>User ID: {user?.id || "Not logged in"}</p>
          <button
            onClick={() => refreshNotifications()}
            className="mt-2 px-4 py-2 bg-secondary text-white rounded"
          >
            Force Refresh
          </button>
        </div>
      </div>
    </div>
  );
}
