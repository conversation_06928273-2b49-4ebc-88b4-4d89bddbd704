# UI Components

Basic UI utility components for interface elements and interactions.

## Components

- **Tab.tsx** - Tab component for tabbed interfaces and content organization
- **index.ts** - Component exports

These UI components provide fundamental interface elements that complement the shared UI library from `@bank-of-styx/ui` package, offering application-specific implementations and extensions.
