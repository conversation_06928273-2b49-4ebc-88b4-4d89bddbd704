import { NextRequest, NextResponse } from "next/server";
import prisma from "../../../../../lib/prisma";
import jwt from "jsonwebtoken";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
const JWT_SECRET = process.env.JWT_SECRET || "your-development-secret-key";

// Helper function to verify token
function verifyToken(token: string) {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    return null;
  }
}

export const GET = async (req: NextRequest) => {
  try {
    // Get authorization header
    const authHeader = req.headers.get("authorization");

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Extract token
    const token = authHeader.split(" ")[1];

    // Verify token
    const decoded = verifyToken(token);

    if (!decoded || typeof decoded !== "object" || !decoded.id) {
      return NextResponse.json({ error: "Invalid token" }, { status: 401 });
    }

    const userId = decoded.id as string;

    // Get query parameters
    const url = new URL(req.url);
    const limitParam = url.searchParams.get("limit");
    const includeAllParam = url.searchParams.get("includeAll");
    const limit = limitParam ? parseInt(limitParam, 10) : 5;
    const includeAll = includeAllParam === "true";

    // Create the where condition
    const whereCondition: any = {
      senderId: userId,
      type: "withdrawal",
    };

    // Create the query options
    const queryOptions: any = {
      where: whereCondition,
      orderBy: [
        // First order by status to put pending at the top
        { status: "asc" },
        // Then by date descending
        { createdAt: "desc" },
      ],
      include: {
        sender: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
        recipient: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
        processedBy: {
          select: {
            id: true,
            username: true,
            displayName: true,
          },
        },
      },
    };

    // Only apply limit if not including all
    if (!includeAll) {
      queryOptions.take = limit;
    }

    // Query the database for withdrawals
    const withdrawals = await prisma.transaction.findMany(queryOptions);

    // Format dates as ISO strings for JSON serialization
    const formattedWithdrawals = withdrawals.map((withdrawal) => ({
      ...withdrawal,
      createdAt: withdrawal.createdAt.toISOString(),
      updatedAt: withdrawal.updatedAt.toISOString(),
      processedAt: withdrawal.processedAt
        ? withdrawal.processedAt.toISOString()
        : null,
    }));

    return NextResponse.json(formattedWithdrawals);
  } catch (error) {
    console.error("Error fetching withdrawals:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
};
