# Bank of Styx - Development Setup Guide

## Overview
This guide provides step-by-step instructions for setting up a local development environment for the Bank of Styx application. The project is built with Next.js, TypeScript, Prisma, and MySQL in a monorepo structure.

## Prerequisites

### Required Software
- **Node.js**: Version 18.x or 20.x (LTS recommended)
- **PNPM**: Version 8.6.0 or higher
- **MySQL**: Version 8.0 or higher
- **Git**: Latest version for version control

### Development Tools (Recommended)
- **VS Code**: With TypeScript, Prisma, and TailwindCSS extensions
- **MySQL Workbench**: For database management
- **Postman**: For API testing

### Accounts & Services
- **Discord Developer Account**: For OAuth integration
- **Stripe Account**: For payment processing (optional for local dev)
- **SMTP Provider**: For email functionality (optional for local dev)

## Initial Setup

### 1. Clone Repository
```bash
git clone <repository-url>
cd bank-of-styx
```

### 2. Install Dependencies
```bash
# Install all dependencies for the monorepo
pnpm install

# Build shared UI package
pnpm ui:build
```

### 3. Database Setup

#### Create MySQL Database
```sql
CREATE DATABASE bankofstyx_dev;
CREATE USER 'bankofstyx_dev'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON bankofstyx_dev.* TO 'bankofstyx_dev'@'localhost';
FLUSH PRIVILEGES;
```

#### Environment Configuration
Create environment files in `web/apps/main-site/`:

**.env.local** (Development)
```env
# Database
DATABASE_URL="mysql://bankofstyx_dev:your_password@localhost:3306/bankofstyx_dev"

# Next.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret-key"

# JWT
JWT_SECRET="your-jwt-secret-key"

# Discord OAuth (optional)
DISCORD_CLIENT_ID="your-discord-client-id"
DISCORD_CLIENT_SECRET="your-discord-client-secret"
DISCORD_BOT_TOKEN="your-discord-bot-token"
DISCORD_GUILD_ID="your-discord-guild-id"

# Email (optional)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"
ADMIN_EMAIL="<EMAIL>"

# Stripe (optional)
STRIPE_PUBLISHABLE_KEY="pk_test_..."
STRIPE_SECRET_KEY="sk_test_..."
STRIPE_WEBHOOK_SECRET="whsec_..."

# File uploads
MAX_FILE_SIZE="********" # 10MB
ALLOWED_FILE_TYPES="image/jpeg,image/png,image/gif,image/webp"
```

### 4. Database Migration & Setup
```bash
cd web/apps/main-site

# Generate Prisma client
npx prisma generate

# Run database migrations
npx prisma migrate deploy

# Seed the database with initial data
pnpm prisma:seed

# Verify setup with Prisma Studio (optional)
pnpm prisma:studio
```

### 5. Initial Build
```bash
# From project root
pnpm build

# Start development server
pnpm dev
```

The application should now be available at `http://localhost:3000`.

## Development Commands

### Starting Development
```bash
# Start all development servers
pnpm dev

# Start with specific host configuration
pnpm dev:vscode
```

### Building & Testing
```bash
# Build all packages
pnpm build

# Build specific workspace
cd web/apps/main-site && pnpm build

# Lint code
pnpm lint

# Format code
pnpm format

# Type check
cd web/apps/main-site && pnpm type-check
```

### Database Operations
```bash
cd web/apps/main-site

# Create and apply new migration
npx prisma migrate dev --name "descriptive-name"

# Apply existing migrations
npx prisma migrate deploy

# Generate Prisma client after schema changes
npx prisma generate

# Check migration status
npx prisma migrate status

# Reset database (development only)
npx prisma migrate reset

# Seed database
pnpm prisma:seed

# Open Prisma Studio
pnpm prisma:studio
```

### UI Component Development
```bash
# Build UI library
pnpm ui:build

# Watch mode for UI development
pnpm ui:dev
```

## Development Workflow

### 1. Creating New Features
1. **Create Feature Branch**: `git checkout -b feature/feature-name`
2. **Add Database Changes**: Modify `prisma/schema.prisma` if needed
3. **Run Migrations**: `npx prisma migrate dev --name "add-feature-name"`
4. **Create API Routes**: Add endpoints in `src/app/api/[feature]/`
5. **Build Frontend**: Create pages in `src/app/[feature]/`
6. **Add Components**: Create components in `src/components/[feature]/`
7. **Update Documentation**: Document new features
8. **Test & Build**: Ensure `pnpm build` passes
9. **Create Pull Request**: For code review

### 2. Database Schema Changes
```bash
# 1. Modify schema.prisma
# 2. Create migration
npx prisma migrate dev --name "descriptive-change-name"

# 3. Generate new client
npx prisma generate

# 4. Update application code as needed
# 5. Test changes thoroughly
```

### 3. Adding UI Components
```bash
# 1. Create component in packages/ui/src/[component-name]/
# 2. Export from packages/ui/src/index.tsx
# 3. Build UI package
pnpm ui:build

# 4. Use component in main application
import { ComponentName } from "@bank-of-styx/ui";
```

## Authentication Setup

### Discord OAuth Setup
1. **Create Discord App**: Visit Discord Developer Portal
2. **Configure OAuth2**: Add redirect URI `http://localhost:3000/auth/discord/callback`
3. **Bot Permissions**: Configure necessary permissions for guild integration
4. **Environment Variables**: Add Discord credentials to `.env.local`

### Testing Authentication
1. **Register New User**: Visit `/auth/register`
2. **Verify Email**: Check email verification flow (if SMTP configured)
3. **Test Login**: Use email/password or Discord OAuth
4. **Admin Access**: Manually set `isAdmin = true` in database for admin testing

## Email System Setup

### Using Gmail SMTP
1. **Enable 2FA**: On your Gmail account
2. **Generate App Password**: In Google Account settings
3. **Update Environment**: Use app password in `SMTP_PASS`
4. **Test Email**: Create support ticket to test email notifications

### Alternative SMTP Providers
- **SendGrid**: For production email delivery
- **Mailgun**: For email API integration
- **Local Testing**: Use tools like MailHog for development

## File Upload Configuration

### Local Development
```bash
# Create upload directories
mkdir -p web/apps/main-site/public/uploads/{avatars,deposits,news,products,general,ships}

# Set appropriate permissions
chmod 755 web/apps/main-site/public/uploads/
```

### Image Processing
The application uses Sharp for image processing. Supported formats:
- **Images**: JPEG, PNG, GIF, WebP
- **Max Size**: 10MB (configurable)
- **Processing**: Auto-resize, format conversion, optimization

## Troubleshooting

### Common Issues

#### Database Connection Errors
```bash
# Check MySQL service
sudo systemctl status mysql  # Linux
brew services list | grep mysql  # macOS

# Test connection
mysql -h localhost -u bankofstyx_dev -p

# Verify DATABASE_URL format
DATABASE_URL="mysql://username:password@localhost:3306/database_name"
```

#### Prisma Client Issues
```bash
# Delete generated client and regenerate
rm -rf node_modules/.prisma
npx prisma generate

# Clear Prisma cache
rm -rf node_modules/@prisma
pnpm install
```

#### Build Errors
```bash
# Clear Next.js cache
rm -rf web/apps/main-site/.next

# Clear PNPM cache
pnpm store prune

# Reinstall dependencies
rm -rf node_modules pnpm-lock.yaml
pnpm install
```

#### Port Already in Use
```bash
# Find process using port 3000
lsof -ti:3000

# Kill process
kill -9 $(lsof -ti:3000)

# Or use different port
pnpm dev -- -p 3001
```

### Environment Issues

#### Missing Environment Variables
Check `.env.local` file exists and contains required variables:
```bash
cd web/apps/main-site
cat .env.local
```

#### TypeScript Errors
```bash
# Check TypeScript version
npx tsc --version

# Run type checking
pnpm type-check

# Restart TypeScript server in VS Code
# Command Palette > TypeScript: Restart TS Server
```

## Production Deployment

### Build for Production
```bash
# Build all packages
pnpm build

# Test production build locally
cd web/apps/main-site && pnpm start
```

### Environment Variables for Production
- Update all URLs to production domains
- Use production database credentials
- Configure production SMTP settings
- Use production Stripe keys
- Set secure JWT secrets

### Database Migration in Production
```bash
# Apply migrations only (no schema changes)
npx prisma migrate deploy

# Never use migrate dev in production
```

## Performance Optimization

### Development Performance
- Use `pnpm dev` for hot reloading
- Enable TypeScript strict mode for better error catching
- Use React DevTools for component debugging
- Enable Chrome DevTools for performance profiling

### Build Performance
- Monitor bundle size with `pnpm build`
- Use dynamic imports for large components
- Optimize images before uploading
- Enable caching for static assets

---

## Quick Reference

### Key Commands
```bash
pnpm dev                           # Start development
pnpm build                         # Build all packages
npx prisma migrate dev            # Create & apply migration
npx prisma generate               # Generate client
pnpm ui:build                     # Build UI components
```

### Important Files
- `web/apps/main-site/.env.local` - Environment configuration
- `web/apps/main-site/prisma/schema.prisma` - Database schema
- `CLAUDE.md` - Development guidelines and patterns
- `PROJECT-STATUS.md` - Current project status

### Development URLs
- **App**: http://localhost:3000
- **Prisma Studio**: http://localhost:5555 (when running)
- **API**: http://localhost:3000/api/*

---

**Last Updated**: 2025-01-07  
**Compatible Node.js**: 18.x, 20.x  
**PNPM Version**: 8.6.0+