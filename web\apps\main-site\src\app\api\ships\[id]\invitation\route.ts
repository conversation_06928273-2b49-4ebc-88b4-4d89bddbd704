import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import prisma from '@/lib/prisma';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = user.id;
    const shipId = params.id;
    const { action } = await request.json();

    if (!action || !['accept', 'decline'].includes(action)) {
      return NextResponse.json({ error: 'Invalid action. Must be accept or decline' }, { status: 400 });
    }

    // Find the pending invitation for this user and ship
    const invitation = await prisma.shipJoinRequest.findFirst({
      where: {
        userId,
        shipId,
        type: 'invite',
        status: 'pending'
      },
      include: {
        ship: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    if (!invitation) {
      return NextResponse.json({ error: 'No pending invitation found' }, { status: 404 });
    }

    if (action === 'accept') {
      // Use transaction to ensure both operations succeed
      await prisma.$transaction(async (tx) => {
        // Update invitation status
        await tx.shipJoinRequest.update({
          where: { id: invitation.id },
          data: { 
            status: 'accepted',
            respondedAt: new Date()
          }
        });

        // Get the role that was specified when the invitation was created
        // Parse the role from the invitation message 
        let assignedRole = 'Member';
        let assignedRoleId = null;

        // Try to determine the role from the invitation message
        if (invitation.message) {
          // Check for standard roles
          if (invitation.message.includes('Officer')) {
            assignedRole = 'Officer';
          } else {
            // Check for custom roles by finding "as [RoleName]" pattern
            const roleMatch = invitation.message.match(/as\s+([^!]+)/i);
            if (roleMatch) {
              const roleName = roleMatch[1].trim();
              
              // Try to find this custom role in the ship's roles
              const customRole = await tx.shipRole.findFirst({
                where: {
                  shipId,
                  name: roleName
                }
              });
              
              if (customRole) {
                assignedRole = customRole.name;
                assignedRoleId = customRole.id;
              }
            }
          }
        }

        // Add user as ship member
        await tx.shipMember.create({
          data: {
            userId,
            shipId,
            role: assignedRole,
            roleId: assignedRoleId,
            status: 'active',
            joinedAt: new Date()
          }
        });
      });

      return NextResponse.json({
        success: true,
        message: `Successfully joined ${invitation.ship.name}`,
        action: 'accepted'
      });

    } else { // decline
      await prisma.shipJoinRequest.update({
        where: { id: invitation.id },
        data: { 
          status: 'declined',
          respondedAt: new Date()
        }
      });

      return NextResponse.json({
        success: true,
        message: 'Invitation declined',
        action: 'declined'
      });
    }

  } catch (error) {
    console.error('Error processing invitation:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = user.id;
    const shipId = params.id;

    // Check if user has a pending invitation for this ship
    const invitation = await prisma.shipJoinRequest.findFirst({
      where: {
        userId,
        shipId,
        type: 'invite',
        status: 'pending'
      },
      select: {
        id: true,
        message: true,
        createdAt: true,
        ship: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    return NextResponse.json({
      hasInvitation: !!invitation,
      invitation: invitation || null
    });

  } catch (error) {
    console.error('Error checking invitation:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}