/**
 * @file c:\Users\<USER>\projects\Bank-of-styx-website\web\apps\api\auth\me\route.ts
 * @summary Defines the API route handler (`/api/auth/me`) for fetching the currently authenticated user's data. It handles GET requests, verifies the JWT token from the Authorization header, retrieves the user from the database, and returns the user's details (excluding sensitive information). Also handles OPTIONS requests for CORS preflight.
 */
import { NextRequest, NextResponse } from "next/server";
import jwt from "jsonwebtoken";
import prisma from "../../../../apps/main-site/src/lib/prisma";
import { handleOptionRequest } from "../../middleware";

const JWT_SECRET = process.env.JWT_SECRET || "your-development-secret-key";

/**
 * Handles OPTIONS requests for the /me endpoint.
 * @param {NextRequest} req - The incoming request object.
 * @returns {Promise<NextResponse | null>} A response suitable for CORS preflight.
 */
export const OPTIONS = async (req: NextRequest) => {
  return handleOptionRequest(req);
};

/**
 * Verifies a JWT token using the secret key.
 * @param {string} token - The JWT token to verify.
 * @returns {object | null} The decoded token payload if valid, otherwise null.
 */
function verifyToken(token: string) {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    return null;
  }
}

/**
 * Handles GET requests to fetch the authenticated user's data.
 * @param {NextRequest} req - The incoming request object, expected to contain an Authorization header with a Bearer token.
 * @returns {Promise<NextResponse>} A JSON response containing the user object on success, or an error response.
 */
export const GET = async (req: NextRequest) => {
  try {
    // Get authorization header
    const authHeader = req.headers.get("authorization");

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Extract token
    const token = authHeader.split(" ")[1];

    // Verify token
    const decoded = verifyToken(token);

    if (!decoded || typeof decoded !== "object" || !decoded.id) {
      return NextResponse.json({ error: "Invalid token" }, { status: 401 });
    }

    // Get user from database
    const user = await prisma.user.findUnique({
      where: { id: decoded.id as string },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Return user data (excluding password)
    return NextResponse.json({
      user: {
        id: user.id,
        username: user.username,
        displayName: user.displayName,
        email: user.email,
        avatar: user.avatar,
        balance: user.balance,
        isEmailVerified: user.isEmailVerified,
        preferences: {
          defaultView: user.defaultView,
          notifications: {
            transfers: user.notifyTransfers,
            deposits: user.notifyDeposits,
            withdrawals: user.notifyWithdrawals,
            newsAndEvents: user.notifyNewsEvents,
          },
        },
        connectedAccounts: {
          discord: user.discordConnected,
          discordId: user.discordId,
          facebook: user.facebookConnected,
          facebookId: user.facebookId,
        },
        merchant: {
          status: user.merchantStatus,
          merchantId: user.merchantId,
          slug: user.merchantSlug,
        },
        auctions: {
          hasCreated: user.hasCreatedAuctions,
          auctionCount: user.auctionCount,
        },
        roles: {
          admin: user.isAdmin,
          editor: user.isEditor,
          banker: user.isBanker,
          chatModerator: user.isChatModerator,
          volunteerCoordinator: user.isVolunteerCoordinator,
          leadManager: user.isLeadManager,
        },
      },
    });
  } catch (error) {
    console.error("Authentication error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
};
