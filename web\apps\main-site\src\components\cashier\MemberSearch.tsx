import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSearchUsers } from "../../hooks/useBank";
import { MemberCard } from "./MemberCard";

export interface MemberSearchProps {
  onSelectMember?: (memberId: string) => void;
}

export const MemberSearch: React.FC<MemberSearchProps> = ({
  onSelectMember,
}) => {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [debouncedQuery, setDebouncedQuery] = useState<string>("");
  const [viewMode, setViewMode] = useState<"card" | "table">("card");

  // Debounce search query to avoid too many API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(searchQuery);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Use the search hook
  const { data: searchResults, isLoading } = useSearchUsers(debouncedQuery);

  // Handle member selection
  const handleSelectMember = (memberId: string) => {
    if (onSelectMember) {
      onSelectMember(memberId);
    } else {
      router.push(`/cashier/dashboard/members/${memberId}`);
    }
  };

  return (
    <div className="bg-secondary-light rounded-lg shadow-md p-6 border border-gray-600">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4">
        <h2 className="text-xl font-bold text-white mb-2 sm:mb-0">
          Member Lookup
        </h2>

        {/* View toggle buttons */}
        <div className="flex space-x-2">
          <button
            onClick={() => setViewMode("card")}
            className={`flex items-center px-3 py-1 rounded-md ${
              viewMode === "card"
                ? "bg-primary text-white"
                : "bg-secondary text-gray-400 hover:bg-secondary-dark"
            }`}
            aria-label="Card view"
          >
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"
              />
            </svg>
            <span className="ml-1 hidden sm:inline">Cards</span>
          </button>
          <button
            onClick={() => setViewMode("table")}
            className={`flex items-center px-3 py-1 rounded-md ${
              viewMode === "table"
                ? "bg-primary text-white"
                : "bg-secondary text-gray-400 hover:bg-secondary-dark"
            }`}
            aria-label="Table view"
          >
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
              />
            </svg>
            <span className="ml-1 hidden sm:inline">Table</span>
          </button>
        </div>
      </div>

      <div className="mb-6">
        <label
          htmlFor="search"
          className="block text-sm font-medium text-gray-400 mb-2"
        >
          Search by username, display name, or email
        </label>
        <div className="relative">
          <input
            type="text"
            id="search"
            className="w-full bg-secondary border border-gray-600 rounded-md py-2 px-4 text-white focus:outline-none focus:ring-2 focus:ring-primary"
            placeholder="Enter at least 3 characters..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          {searchQuery.length > 0 && (
            <button
              className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
              onClick={() => setSearchQuery("")}
            >
              ✕
            </button>
          )}
        </div>
        <p className="mt-1 text-sm text-gray-400">
          Enter at least 3 characters to search by username, display name, or
          email address
        </p>
      </div>

      {/* Search Results */}
      <div className="mt-4">
        {debouncedQuery.length < 3 ? (
          <div className="text-center py-4 text-gray-400">
            Enter at least 3 characters to search for members by username,
            display name, or email
          </div>
        ) : isLoading ? (
          <div className="text-center py-4 text-gray-400">Searching...</div>
        ) : searchResults && searchResults.length > 0 ? (
          viewMode === "card" ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {searchResults.map((member) => (
                <MemberCard
                  key={member.id}
                  member={member}
                  onSelect={handleSelectMember}
                />
              ))}
            </div>
          ) : (
            <div className="border border-gray-600 rounded-md overflow-hidden">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="bg-secondary-dark">
                    <th className="p-3 text-left text-white">Avatar</th>
                    <th className="p-3 text-left text-white">Username</th>
                    <th className="p-3 text-left text-white">Display Name</th>
                    <th className="p-3 text-left text-white">Email</th>
                    <th className="p-3 text-left text-white">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {searchResults.map((member) => (
                    <tr
                      key={member.id}
                      className="border-t border-gray-600 hover:bg-secondary-dark"
                    >
                      <td className="p-3">
                        <img
                          src={member.avatar || "/images/avatars/default.png"}
                          alt={member.username}
                          className="w-10 h-10 rounded-full object-cover"
                        />
                      </td>
                      <td className="p-3 text-white">@{member.username}</td>
                      <td className="p-3 text-white">{member.displayName}</td>
                      <td className="p-3 text-white">{member.email}</td>
                      <td className="p-3">
                        <button
                          onClick={() => handleSelectMember(member.id)}
                          className="bg-primary hover:bg-primary-dark text-white py-1 px-3 rounded-md text-sm"
                        >
                          View Details
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )
        ) : (
          <div className="text-center py-4 text-gray-400">
            No members found matching "{debouncedQuery}"
          </div>
        )}
      </div>
    </div>
  );
};
