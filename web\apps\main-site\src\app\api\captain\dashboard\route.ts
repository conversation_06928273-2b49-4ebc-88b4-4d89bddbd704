import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth";

export const dynamic = 'force-dynamic';

export async function GET(request: Request) {
  try {
    const currentUser = await getCurrentUser(request);

    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Check if user is a captain
    const captainedShip = await prisma.ship.findFirst({
      where: {
        captainId: currentUser.id,
        status: 'active',
      },
      include: {
        members: {
          where: { status: 'active' },
          include: {
            user: {
              select: {
                id: true,
                username: true,
                displayName: true,
                avatar: true,
              },
            },
          },
          orderBy: { joinedAt: 'asc' },
        },
        joinRequests: {
          where: { status: 'pending' },
          select: {
            id: true,
            type: true,
            message: true,
            createdAt: true,
            user: {
              select: {
                id: true,
                username: true,
                displayName: true,
                avatar: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
        },
        _count: {
          select: {
            members: {
              where: { status: 'active' },
            },
          },
        },
      },
    });

    if (!captainedShip) {
      return NextResponse.json(
        { error: "You are not a captain of any ship" },
        { status: 403 }
      );
    }

    // Get recent activity (you can expand this based on requirements)
    const recentMembers = await prisma.shipMember.findMany({
      where: {
        shipId: captainedShip.id,
        status: 'active',
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
      },
      orderBy: { joinedAt: 'desc' },
      take: 5,
    });

    const dashboardData = {
      ship: {
        id: captainedShip.id,
        name: captainedShip.name,
        description: captainedShip.description,
        slogan: captainedShip.slogan,
        logo: captainedShip.logo,
        tags: captainedShip.tags as string[] || [],
        createdAt: captainedShip.createdAt.toISOString(),
      },
      statistics: {
        totalMembers: captainedShip._count.members,
        pendingRequests: captainedShip.joinRequests.filter(r => r.type === 'request' || !r.type).length,
        pendingInvitations: captainedShip.joinRequests.filter(r => r.type === 'invite').length,
        recentJoins: recentMembers.length,
      },
      members: captainedShip.members.map(member => ({
        id: member.id,
        role: member.role,
        status: member.status,
        joinedAt: member.joinedAt.toISOString(),
        user: member.user,
      })),
      pendingRequests: captainedShip.joinRequests
        .filter(request => request.type === 'request' || !request.type) // Treat null/undefined as 'request' for backwards compatibility
        .map(request => ({
          id: request.id,
          createdAt: request.createdAt.toISOString(),
          message: request.message,
          type: request.type || 'request',
          user: request.user,
        })),
      pendingInvitations: captainedShip.joinRequests
        .filter(request => request.type === 'invite')
        .map(request => ({
          id: request.id,
          createdAt: request.createdAt.toISOString(),
          message: request.message,
          type: request.type,
          user: request.user,
        })),
      recentActivity: recentMembers.map(member => ({
        type: 'member_joined',
        user: member.user,
        timestamp: member.joinedAt.toISOString(),
      })),
    };

    return NextResponse.json(dashboardData);
  } catch (error) {
    console.error("Error fetching captain dashboard:", error);
    return NextResponse.json(
      { error: "Failed to fetch dashboard data" },
      { status: 500 }
    );
  }
}