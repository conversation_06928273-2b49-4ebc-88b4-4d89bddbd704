"use client";

import React from "react";
import Link from "next/link";
import { <PERSON><PERSON>, Spinner } from "@bank-of-styx/ui";
import { useSalesProductCategories } from "@/hooks/useProductCategories";
import { useSalesProducts } from "@/hooks/useProducts";

export const SalesDashboardMain: React.FC = () => {
  // Fetch active categories
  const { data: categoriesData, isLoading: categoriesLoading } =
    useSalesProductCategories(true);

  // Fetch products
  const { data: productsData, isLoading: productsLoading } = useSalesProducts();

  // Calculate stats
  const activeCategories = categoriesData?.categories?.length || 0;
  const totalProducts = productsData?.length || 0;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-text-primary">
          Sales Dashboard
        </h1>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-secondary p-4 rounded-lg shadow-md border border-border-subtle">
          <h3 className="text-lg font-medium text-text-secondary">
            Total Products
          </h3>
          {productsLoading ? (
            <div className="flex justify-center mt-2">
              <Spinner size="sm" />
            </div>
          ) : (
            <p className="text-3xl font-bold text-text-primary mt-2">
              {totalProducts}
            </p>
          )}
        </div>
        <div className="bg-secondary p-4 rounded-lg shadow-md border border-border-subtle">
          <h3 className="text-lg font-medium text-text-secondary">
            Active Categories
          </h3>
          {categoriesLoading ? (
            <div className="flex justify-center mt-2">
              <Spinner size="sm" />
            </div>
          ) : (
            <p className="text-3xl font-bold text-text-primary mt-2">
              {activeCategories}
            </p>
          )}
        </div>
        <div className="bg-secondary p-4 rounded-lg shadow-md border border-border-subtle">
          <h3 className="text-lg font-medium text-text-secondary">
            Total Orders
          </h3>
          <p className="text-3xl font-bold text-text-primary mt-2">0</p>
        </div>
        <div className="bg-secondary p-4 rounded-lg shadow-md border border-border-subtle">
          <h3 className="text-lg font-medium text-text-secondary">Revenue</h3>
          <p className="text-3xl font-bold text-text-primary mt-2">₷0</p>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-secondary p-6 rounded-lg shadow-md border border-border-subtle">
        <h2 className="text-xl font-bold text-text-primary mb-4">
          Quick Actions
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Link href="/sales/products">
            <Button variant="primary" className="w-full">
              Add New Product
            </Button>
          </Link>
          <Link href="/sales/categories">
            <Button variant="secondary" className="w-full">
              Create Category
            </Button>
          </Link>
          <Link href="/sales/products">
            <Button variant="outline" className="w-full">
              Manage Products
            </Button>
          </Link>
          <Link href="/sales/orders">
            <Button variant="outline" className="w-full">
              View Orders
            </Button>
          </Link>
        </div>
      </div>

      {/* Getting Started Guide */}
      <div className="bg-secondary p-6 rounded-lg shadow-md border border-border-subtle">
        <h2 className="text-xl font-bold text-text-primary mb-4">
          Getting Started
        </h2>
        <div className="space-y-4">
          <div className="flex items-start">
            <div className="bg-primary text-white rounded-full w-8 h-8 flex items-center justify-center mr-3 flex-shrink-0">
              1
            </div>
            <div>
              <h3 className="font-medium text-text-primary">
                Create Product Categories
              </h3>
              <p className="text-text-secondary mt-1">
                Start by creating categories to organize your products.
                Categories help customers find what they're looking for.
              </p>
            </div>
          </div>
          <div className="flex items-start">
            <div className="bg-primary text-white rounded-full w-8 h-8 flex items-center justify-center mr-3 flex-shrink-0">
              2
            </div>
            <div>
              <h3 className="font-medium text-text-primary">Add Products</h3>
              <p className="text-text-secondary mt-1">
                Create products with detailed descriptions, images, and pricing.
                You can link products to specific events if needed.
              </p>
            </div>
          </div>
          <div className="flex items-start">
            <div className="bg-primary text-white rounded-full w-8 h-8 flex items-center justify-center mr-3 flex-shrink-0">
              3
            </div>
            <div>
              <h3 className="font-medium text-text-primary">
                Manage Inventory
              </h3>
              <p className="text-text-secondary mt-1">
                Set inventory levels for your products. Leave inventory blank
                for unlimited items like digital products.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
