"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { CaptainDashboardLayout } from "@/components/captain";
import { useCaptainShip } from "@/hooks/captain/useCaptainShip";
import { But<PERSON>, Card, Modal, FormRenderer } from "@bank-of-styx/ui";
import { getCaptainForms, submitForm, type FormsData, type EventForm } from "@/services/captainService";

export default function CaptainFormsPage() {
  const { user, isLoading: authLoading, openAuthModal } = useAuth();
  const router = useRouter();
  
  const { ship, isLoading: shipLoading } = useCaptainShip();
  
  // Local state for forms page
  const [formsData, setFormsData] = useState<FormsData | null>(null);
  const [formsLoading, setFormsLoading] = useState(false);
  const [selectedForm, setSelectedForm] = useState<EventForm | null>(null);
  const [showFormModal, setShowFormModal] = useState(false);

  // Authentication check
  React.useEffect(() => {
    if (!authLoading && !user) {
      openAuthModal();
      router.replace("/");
      return;
    }
  }, [user, authLoading, router, openAuthModal]);

  // Fetch forms
  useEffect(() => {
    if (ship) {
      fetchForms();
    }
  }, [ship]);

  const fetchForms = async () => {
    try {
      setFormsLoading(true);
      const data = await getCaptainForms();
      setFormsData(data);
    } catch (error) {
      console.error("Error fetching forms:", error);
      setFormsData({ ship: null, forms: [] });
    } finally {
      setFormsLoading(false);
    }
  };

  const handleSubmitForm = async (formData: any) => {
    if (!selectedForm) return;

    try {
      await submitForm(selectedForm.id, formData);
      await fetchForms(); // Refresh the forms list
      setShowFormModal(false);
      setSelectedForm(null);
    } catch (error) {
      console.error("Error submitting form:", error);
      alert("Failed to submit form. Please try again.");
    }
  };

  const getStatusColor = (status?: string) => {
    switch (status) {
      case "approved":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
      case "rejected":
        return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";
      case "reviewed":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
      case "submitted":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
    }
  };

  const formatDeadline = (deadline?: string) => {
    if (!deadline) return "No deadline";
    const date = new Date(deadline);
    const now = new Date();
    const isOverdue = date < now;
    
    return (
      <span className={isOverdue ? "text-red-400" : "text-gray-400"}>
        {date.toLocaleDateString()} {isOverdue ? "(Overdue)" : ""}
      </span>
    );
  };

  // Loading state
  if (authLoading || shipLoading) {
    return (
      <div className="min-h-screen bg-secondary-dark flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-400">Loading...</p>
        </div>
      </div>
    );
  }

  if (!ship) {
    return (
      <CaptainDashboardLayout>
        <Card className="p-6 text-center">
          <p className="text-gray-400">Ship not found</p>
        </Card>
      </CaptainDashboardLayout>
    );
  }

  return (
    <CaptainDashboardLayout ship={ship}>
      <div className="space-y-4 sm:space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-xl sm:text-2xl font-bold text-white mb-1">
              Event Forms
            </h1>
            <p className="text-sm text-gray-400">
              View and submit forms for upcoming events
            </p>
          </div>
          <div className="mt-3 sm:mt-0 flex gap-2">
            <Button 
              variant="outline" 
              size="sm"
              onClick={fetchForms}
              disabled={formsLoading}
            >
              {formsLoading ? "Refreshing..." : "Refresh"}
            </Button>
          </div>
        </div>

        {/* Forms List */}
        <div className="space-y-4">
          {formsLoading ? (
            <Card className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-gray-400">Loading available forms...</p>
            </Card>
          ) : (
            <>
              {formsData?.forms && Array.isArray(formsData.forms) && formsData.forms.length > 0 ? (
                formsData.forms.map((form) => (
                  <Card key={form.id} className="p-4 sm:p-6">
                    <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-4">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="font-bold text-lg text-white truncate">{form.name}</h3>
                          {form.hasSubmission && (
                            <span className={`px-2 py-1 text-xs rounded-full whitespace-nowrap ${getStatusColor(form.submissionStatus)}`}>
                              {form.submissionStatus}
                            </span>
                          )}
                        </div>
                        
                        {form.description && (
                          <p className="text-gray-400 mb-3 text-sm">{form.description}</p>
                        )}
                        
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm">
                          <div>
                            <span className="font-medium text-white">Event:</span>{" "}
                            <span className="text-gray-400">{form.event.name}</span>
                          </div>
                          <div>
                            <span className="font-medium text-white">Fields:</span>{" "}
                            <span className="text-gray-400">{form.formStructure.length}</span>
                          </div>
                          <div>
                            <span className="font-medium text-white">Deadline:</span>{" "}
                            {formatDeadline(form.submissionDeadline)}
                          </div>
                          {form.hasSubmission && form.submittedAt && (
                            <div>
                              <span className="font-medium text-white">Submitted:</span>{" "}
                              <span className="text-gray-400">
                                {new Date(form.submittedAt).toLocaleDateString()}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex gap-2 lg:flex-col lg:w-auto">
                        <Button
                          size="sm"
                          variant={form.hasSubmission ? "outline" : "primary"}
                          onClick={() => {
                            setSelectedForm(form);
                            setShowFormModal(true);
                          }}
                          disabled={form.submissionStatus === "approved" || form.submissionStatus === "rejected"}
                          className="flex-1 lg:flex-none"
                        >
                          {form.hasSubmission ? "View/Edit" : "Fill Form"}
                        </Button>
                        
                        {form.hasSubmission && (
                          <div className="text-center lg:mt-2">
                            <div className="text-xs text-gray-400">
                              Status: {form.submissionStatus}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </Card>
                ))
              ) : (
                <Card className="p-8 text-center">
                  <div className="w-16 h-16 bg-secondary-dark rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl">📝</span>
                  </div>
                  <h3 className="text-xl font-bold text-white mb-2">No Available Forms</h3>
                  <p className="text-gray-400">
                    There are currently no forms available for your ship to fill out.
                  </p>
                </Card>
              )}
            </>
          )}
        </div>

        {/* Form Statistics */}
        {formsData?.forms && formsData.forms.length > 0 && (
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
            <Card className="p-4">
              <div className="text-center">
                <p className="text-2xl font-bold text-white">{formsData.forms.length}</p>
                <p className="text-xs text-gray-400">Total Forms</p>
              </div>
            </Card>
            
            <Card className="p-4">
              <div className="text-center">
                <p className="text-2xl font-bold text-white">
                  {formsData.forms.filter(f => f.hasSubmission).length}
                </p>
                <p className="text-xs text-gray-400">Submitted</p>
              </div>
            </Card>
            
            <Card className="p-4">
              <div className="text-center">
                <p className="text-2xl font-bold text-white">
                  {formsData.forms.filter(f => f.submissionStatus === "approved").length}
                </p>
                <p className="text-xs text-gray-400">Approved</p>
              </div>
            </Card>
            
            <Card className="p-4">
              <div className="text-center">
                <p className="text-2xl font-bold text-white">
                  {formsData.forms.filter(f => !f.hasSubmission).length}
                </p>
                <p className="text-xs text-gray-400">Pending</p>
              </div>
            </Card>
          </div>
        )}
      </div>

      {/* Form Modal */}
      {showFormModal && selectedForm && selectedForm.formStructure && Array.isArray(selectedForm.formStructure) && (
        <Modal
          isOpen={showFormModal}
          onClose={() => {
            setShowFormModal(false);
            setSelectedForm(null);
          }}
          title={`${selectedForm.name} - ${selectedForm.event.name}`}
          size="lg"
        >
          <div style={{ maxHeight: "70vh", overflowY: "auto" }}>
            <FormRenderer
              form={{ fields: selectedForm.formStructure }}
              onSubmit={handleSubmitForm}
              onCancel={() => {
                setShowFormModal(false);
                setSelectedForm(null);
              }}
              submitText={selectedForm.hasSubmission ? "Update Submission" : "Submit Form"}
              readOnly={selectedForm.submissionStatus === "approved" || selectedForm.submissionStatus === "rejected"}
            />
          </div>
        </Modal>
      )}
    </CaptainDashboardLayout>
  );
}