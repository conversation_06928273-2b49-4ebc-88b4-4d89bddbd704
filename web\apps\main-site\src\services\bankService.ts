/**
 * Bank API service
 * Handles all bank-related API calls
 */
import fetchClient from "@/lib/fetchClient";

// Types
export interface BankUser {
  id: string;
  username: string;
  displayName: string;
  avatar: string;
  balance: number;
  email: string;
  isEmailVerified?: boolean;
  isAdmin?: boolean;
  landSteward?: boolean;
  preferences: {
    defaultView:
      | "dashboard"
      | "transactions"
      | "transfer"
      | "pay-code"
      | "donate";
    notifications: {
      transfers: boolean;
      deposits: boolean;
      withdrawals: boolean;
      newsAndEvents: boolean;
    };
  };
  connectedAccounts: {
    discord: boolean;
    discordId?: string | null;
    facebook: boolean;
    facebookId?: string | null;
  };
  merchant?: {
    status: "pending" | "approved" | "rejected" | "none";
    merchantId?: number;
    slug?: string;
  };
  auctions?: {
    hasCreated: boolean;
    auctionCount?: number;
  };
  roles?: {
    admin?: boolean;
    editor?: boolean;
    banker?: boolean;
    chatModerator?: boolean;
    volunteer?: boolean;
    volunteerCoordinator?: boolean;
    leadManager?: boolean;
    salesManager?: boolean;
    landSteward?: boolean;
  };
  ship?: {
    id: string;
    name: string;
    isCaptain: boolean;
  } | null;
}

export interface Transaction {
  id: string;
  amount: number;
  type: string; // deposit, withdrawal, transfer, pay-code, donation
  status: "pending" | "completed" | "failed" | "cancelled";
  description?: string;
  note?: string;
  senderId?: string;
  recipientId?: string;
  sender?: {
    id: string;
    username: string;
    displayName: string;
    avatar: string;
  };
  recipient?: {
    id: string;
    username: string;
    displayName: string;
    avatar: string;
  };
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
  processedById?: string;
  processedBy?: {
    id: string;
    username: string;
    displayName: string;
  };
  processedAt?: string; // ISO date string
  paymentMethod?: string;
  receiptImage?: string;
}

export interface AccountSummary {
  accountType: string;
  accountStatus: string;
  lastLogin: string;
  pendingTransactions: number;
}

export interface BankStatistics {
  totalDeposits: {
    count: number;
    amount: number;
  };
  totalWithdrawals: {
    count: number;
    amount: number;
  };
  pendingTransactions: {
    deposits: number;
    withdrawals: number;
  };
  dailyActivity: {
    date: string;
    deposits: number;
    withdrawals: number;
    transfers: number;
  }[];
  userActivity: {
    userId: string;
    username: string;
    transactionCount: number;
    totalAmount: number;
  }[];
}

export interface PayCode {
  id: string;
  code: string;
  amount: number;
  createdAt: string; // ISO date string
  expiresAt: string; // ISO date string
  status: "active" | "redeemed" | "cancelled" | "expired" | "paused";
  createdById: string;
  createdBy: {
    id: string;
    username: string;
    displayName: string;
    avatar: string;
  };
  redeemedById?: string;
  redeemedBy?: {
    id: string;
    username: string;
    displayName: string;
    avatar: string;
  };
  redeemedAt?: string; // ISO date string
  uses: number;
  maxUses?: number;
}

export interface Ledger {
  id: string;
  date: string; // ISO date string
  description: string;
  totalDeposits: number;
  totalWithdrawals: number;
  totalTransfers: number;
  netChange: number;
  status: "pending" | "verified";
  verifiedById?: string;
  verifiedBy?: {
    id: string;
    username: string;
    displayName: string;
  };
  verifiedAt?: string; // ISO date string
}

// Using the centralized fetch client for all API calls

// Bank user data
export const getBankUser = async (): Promise<BankUser> => {
  return fetchClient.get<BankUser>("/api/bank/user");
};

// Account summary
export const getAccountSummary = async (): Promise<AccountSummary> => {
  return fetchClient.get<AccountSummary>("/api/bank/account-summary");
};

// Transactions
export const getTransactions = async (filters?: {
  type?: string;
  startDate?: string;
  endDate?: string;
  search?: string;
}): Promise<Transaction[]> => {
  const params: Record<string, string> = {};
  if (filters?.type) params.type = filters.type;
  if (filters?.startDate) params.startDate = filters.startDate;
  if (filters?.endDate) params.endDate = filters.endDate;
  if (filters?.search) params.search = filters.search;

  return fetchClient.get<Transaction[]>("/api/bank/transactions", { params });
};

// Recent transactions
export const getRecentTransactions = async (
  limit = 5,
): Promise<Transaction[]> => {
  return fetchClient.get<Transaction[]>("/api/bank/transactions/recent", {
    params: { limit: limit.toString() },
  });
};

// Bank statistics
export const getBankStatistics = async (): Promise<BankStatistics> => {
  return fetchClient.get<BankStatistics>("/api/bank/statistics");
};

// Create a transaction (transfer, deposit, withdraw, donate)
export const createTransaction = async (data: {
  type: string;
  amount: number;
  recipient?: string;
  description?: string;
  note?: string;
}): Promise<Transaction> => {
  return fetchClient.post<Transaction>("/api/bank/transactions", data);
};

// Get pending deposits
export const getPendingDeposits = async (): Promise<Transaction[]> => {
  return fetchClient.get<Transaction[]>(
    "/api/bank/transactions/pending-deposits",
  );
};

// Get all deposits (with optional limit and includeAll flag)
export const getDeposits = async (
  limit = 5,
  includeAll = false,
): Promise<Transaction[]> => {
  const params: Record<string, string> = {
    limit: limit.toString(),
  };

  if (includeAll) {
    params.includeAll = "true";
  }

  return fetchClient.get<Transaction[]>("/api/bank/transactions/deposits", {
    params,
  });
};

// Create a deposit with receipt image
export const createDeposit = async (data: FormData): Promise<Transaction> => {
  // For FormData, we need to use the fetch API directly but still leverage our auth token
  // which will be automatically added by the fetchClient
  return fetchClient.request<Transaction>("/api/bank/transactions/deposits", {
    method: "POST",
    body: data,
    // Don't set Content-Type header for FormData as the browser will set it with the boundary
  });
};

// Get recent withdrawals
export const getRecentWithdrawals = async (
  limit = 5,
  includeAll = false,
): Promise<Transaction[]> => {
  const params: Record<string, string> = {
    limit: limit.toString(),
  };

  if (includeAll) {
    params.includeAll = "true";
  }

  return fetchClient.get<Transaction[]>("/api/bank/transactions/withdrawals", {
    params,
  });
};

// Cancel a withdrawal
export const cancelWithdrawal = async (
  id: string,
): Promise<{
  transaction: Transaction;
  message: string;
}> => {
  return fetchClient.post<{
    transaction: Transaction;
    message: string;
  }>("/api/bank/transactions/withdrawals/cancel", { id });
};

// Get recent transfers
export const getRecentTransfers = async (
  limit = 5,
): Promise<Transaction[]> => {
  return fetchClient.get<Transaction[]>("/api/bank/transactions/transfers", {
    params: { limit: limit.toString() },
  });
};

// Get recent donations
export const getRecentDonations = async (
  limit = 5,
): Promise<Transaction[]> => {
  return fetchClient.get<Transaction[]>("/api/bank/transactions/donations", {
    params: { limit: limit.toString() },
  });
};

// Pay Code API functions
export const getPayCodes = async (params?: {
  status?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}): Promise<{
  data: PayCode[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
}> => {
  const queryParams: Record<string, string> = {};

  if (params?.status) queryParams.status = params.status;
  if (params?.page) queryParams.page = params.page.toString();
  if (params?.limit) queryParams.limit = params.limit.toString();
  if (params?.sortBy) queryParams.sortBy = params.sortBy;
  if (params?.sortOrder) queryParams.sortOrder = params.sortOrder;

  return fetchClient.get<{
    data: PayCode[];
    pagination: {
      total: number;
      page: number;
      limit: number;
      pages: number;
    };
  }>("/api/bank/pay-codes", { params: queryParams });
};

export const getActiveCodes = async (): Promise<PayCode[]> => {
  return fetchClient.get<PayCode[]>("/api/bank/pay-codes/active");
};

export const getRedeemedCodes = async (): Promise<PayCode[]> => {
  return fetchClient.get<PayCode[]>("/api/bank/pay-codes/redeemed");
};

export const createPayCode = async (data: {
  amount: number;
  expiresAt: string;
  maxUses?: number;
}): Promise<PayCode> => {
  return fetchClient.post<PayCode>("/api/bank/pay-codes", data);
};

export const validatePayCode = async (
  code: string,
): Promise<{
  id: string;
  code: string;
  amount: number;
  createdBy: {
    id: string;
    username: string;
    displayName: string;
    avatar: string;
  };
  valid: boolean;
}> => {
  return fetchClient.get<{
    id: string;
    code: string;
    amount: number;
    createdBy: {
      id: string;
      username: string;
      displayName: string;
      avatar: string;
    };
    valid: boolean;
  }>(`/api/bank/pay-codes/validate/${code}`);
};

export const redeemPayCode = async (
  code: string,
  trialRun = false,
): Promise<
  | Transaction
  | {
      valid: boolean;
      code: string;
      amount: number;
      createdBy: {
        id: string;
        username: string;
        displayName: string;
        avatar: string;
      };
      userBalance: number;
      trialRun: boolean;
    }
> => {
  return fetchClient.post<
    | Transaction
    | {
        valid: boolean;
        code: string;
        amount: number;
        createdBy: {
          id: string;
          username: string;
          displayName: string;
          avatar: string;
        };
        userBalance: number;
        trialRun: boolean;
      }
  >("/api/bank/pay-codes/redeem", { code, trialRun });
};

export const updatePayCode = async (
  id: string,
  data: { status: "active" | "paused" | "cancelled" },
): Promise<PayCode> => {
  return fetchClient.put<PayCode>(`/api/bank/pay-codes/${id}`, data);
};

export const cancelPayCode = async (
  id: string,
): Promise<{
  payCode: PayCode;
  transaction: Transaction;
  message: string;
}> => {
  return fetchClient.post<{
    payCode: PayCode;
    transaction: Transaction;
    message: string;
  }>(`/api/bank/pay-codes/${id}/cancel`, {});
};

export const updatePayCodeUses = async (
  id: string,
  maxUses: number,
): Promise<PayCode> => {
  return fetchClient.put<PayCode>(`/api/bank/pay-codes/${id}/uses`, {
    maxUses,
  });
};

export const getPayCodeDetails = async (id: string): Promise<PayCode> => {
  return fetchClient.get<PayCode>(`/api/bank/pay-codes/${id}`);
};

// Cashier API functions
export const getPendingTransactions = async (
  type?: string,
): Promise<Transaction[]> => {
  const params: Record<string, string> = {};
  if (type) {
    params.type = type;
  }
  return fetchClient.get<Transaction[]>(
    "/api/bank/cashier/pending-transactions",
    { params },
  );
};

// Get recent transactions for cashier (all users)
export const getCashierRecentTransactions = async (
  limit = 5,
  status?: string,
  type?: string,
): Promise<Transaction[]> => {
  const params: Record<string, string> = {
    limit: limit.toString(),
  };

  if (status) {
    params.status = status;
  }

  if (type) {
    params.type = type;
  }

  return fetchClient.get<Transaction[]>(
    "/api/bank/cashier/transactions/recent",
    { params },
  );
};

// Notification types
export interface Notification {
  id: string;
  type: string;
  message: string;
  read: boolean;
  createdAt: string;
  updatedAt: string;
  transactionId: string | null;
  transaction: Transaction | null;
}

// Get cashier notifications
export const getCashierNotifications = async (
  limit = 20,
  unreadOnly = false,
): Promise<Notification[]> => {
  const params: Record<string, string> = {
    limit: limit.toString(),
  };

  if (unreadOnly) {
    params.unreadOnly = "true";
  }

  return fetchClient.get<Notification[]>("/api/bank/cashier/notifications", {
    params,
  });
};

// Mark notifications as read
export const markNotificationsAsRead = async (data: {
  ids?: string[];
  all?: boolean;
}): Promise<{ message: string }> => {
  return fetchClient.put<{ message: string }>(
    "/api/bank/cashier/notifications",
    data,
  );
};

export const processTransaction = async (
  id: string,
  data: { status: "approved" | "rejected"; note?: string },
): Promise<Transaction> => {
  return fetchClient.put<Transaction>(
    `/api/bank/cashier/transactions/${id}`,
    data,
  );
};

export const getLedgerEntries = async (filters?: {
  startDate?: string;
  endDate?: string;
}): Promise<Ledger[]> => {
  const params: Record<string, string> = {};

  if (filters?.startDate) params.startDate = filters.startDate;
  if (filters?.endDate) params.endDate = filters.endDate;

  return fetchClient.get<Ledger[]>("/api/bank/cashier/ledger", { params });
};

export const createLedgerEntry = async (data: {
  description: string;
  totalDeposits: number;
  totalWithdrawals: number;
  totalTransfers: number;
  netChange: number;
}): Promise<Ledger> => {
  return fetchClient.post<Ledger>("/api/bank/cashier/ledger", data);
};

export const verifyLedgerEntry = async (id: string): Promise<Ledger> => {
  return fetchClient.put<Ledger>(`/api/bank/cashier/ledger/${id}`, {});
};

// User search API function
export interface UserSearchResult {
  id: string;
  username: string;
  displayName: string;
  avatar: string;
  email: string;
}

export const searchUsers = async (
  query: string,
  limit = 10,
): Promise<UserSearchResult[]> => {
  if (!query || query.length < 3) {
    return [];
  }

  return fetchClient.get<UserSearchResult[]>("/api/users/search", {
    params: {
      q: query,
      limit: limit.toString(),
    },
  });
};

// Get member details (for cashier)
export const getMemberDetails = async (
  memberId: string,
): Promise<{ user: BankUser; transactions: Transaction[] }> => {
  return fetchClient.get<{ user: BankUser; transactions: Transaction[] }>(
    `/api/bank/cashier/members/${memberId}`,
  );
};
