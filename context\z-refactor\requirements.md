# Bank of Styx Website Requirements

This document provides a comprehensive overview of the Bank of Styx website codebase, including its architecture, code patterns, UI elements, authentication systems, and other key components. This guide will help you navigate and understand the system effectively.

## Table of Contents

1. [Project Overview](#project-overview)
2. [Technology Stack](#technology-stack)
3. [Architecture](#architecture)
4. [Authentication System](#authentication-system)
5. [Banking System](#banking-system)
6. [UI Components and Patterns](#ui-components-and-patterns)
7. [Database Structure](#database-structure)
8. [API Structure](#api-structure)
9. [State Management](#state-management)
10. [Real-time Updates](#real-time-updates)
11. [Key Features](#key-features)
12. [Current Development Focus](#current-development-focus)
13. [Known Issues](#known-issues)
14. [Development Workflow](#development-workflow)

## Project Overview

The Bank of Styx website is a full-featured banking platform that provides users with account management, transactions, merchant services, auctions, and news. The application is designed to serve approximately 1,000 users with 30-40 concurrent users (with peaks of up to 100).

The platform includes several integrated systems:
- Banking and financial transactions
- News and content management
- Event management
- Volunteer management
- Support ticket system
- Shopping and e-commerce
- Ticket hold system

## Technology Stack

### Development Stack

- **Frontend Framework**: Next.js (v13.4.2) with App Router
- **UI Library**: React (v18.2.0)
- **Programming Language**: TypeScript (v5.8.3)
- **Styling**: TailwindCSS (v3.3.2)
- **State Management**:
  - Server State: TanStack Query (v5.17.19)
  - Client State: React Context
- **Form Handling**: Native React forms
- **Data Visualization**: Recharts (v2.15.3)
- **Rich Text Editing**: React Quill (v2.0.0)
- **Package Management**: PNPM (v8.6.0)
- **Database ORM**: Prisma (v6.6.0)
- **Authentication**: JWT (jsonwebtoken) and OAuth
- **Email**: Nodemailer (v6.10.1)

### Production Stack

- **Node.js Runtime**: >= 18.0.0
- **Database**: MySQL 8.0
- **Deployment Options**:
  - Self-hosted Node.js server with PM2
  - Vercel
  - Docker
- **Web Server**: Nginx (recommended for production)
- **Monitoring**: PM2 (for self-hosted option)

## Architecture

### Monorepo Structure

The project uses a monorepo architecture with PNPM workspaces to manage multiple packages and applications. This structure promotes code reuse, maintains consistency across the platform, and allows for independent versioning and deployment.

#### Applications (`web/apps/`)
- **main-site/**: The primary Next.js application containing the complete banking platform interface, user dashboard, and all core functionality
- **api/**: Standalone API modules and authentication services that can be deployed independently from the main application

#### Shared Packages (`web/packages/`)
- **ui/**: Shared UI components library providing reusable React components, hooks, and design system elements that promote design consistency and code reuse across the entire Bank of Styx platform
- **config/**: Shared configuration files, constants, and settings used across multiple applications, including:
  - ESLint configuration preset for code quality and style enforcement
  - TailwindCSS configuration for consistent styling across applications
  - Base TypeScript configuration extended by individual applications
  - Shared build and development settings

### Code Organization

The codebase follows a well-structured organization pattern with clear separation of concerns:

```
/
├── web/                                # Core application code (monorepo)
│   ├── apps/                           # Applications
│   │   ├── api/                        # Standalone API modules
│   │   └── main-site/                  # Main Next.js application
│   │       ├── prisma/                 # Database schema and migrations
│   │       ├── public/                 # Static assets and uploads
│   │       ├── scripts/                # Utility scripts
│   │       └── src/                    # Source code
│   │           ├── app/                # Next.js App Router pages and API routes
│   │           ├── components/         # React components
│   │           ├── contexts/           # React contexts
│   │           ├── hooks/              # Custom React hooks
│   │           ├── lib/                # Utility functions
│   │           ├── providers/          # React providers
│   │           ├── services/           # API service functions
│   │           ├── scripts/            # Client-side scripts
│   │           ├── tests/              # Testing utilities
│   │           ├── types/              # TypeScript type definitions
│   │           └── utils/              # Utility helpers
│   └── packages/                       # Shared packages
│       ├── config/                     # Shared configuration
│       └── ui/                         # Shared UI components
```

### Key Directories

- **app/**: Contains all Next.js pages and API routes using the App Router pattern following Next.js 13+ App Router conventions
- **components/**: Reusable React components organized by feature and functionality
- **services/**: API service functions for backend communication and data fetching
- **contexts/**: React context providers for global state management
- **hooks/**: Custom React hooks for reusable logic and shared functionality
- **lib/**: Core utility functions and configurations
- **providers/**: React providers for external services (TanStack Query, authentication, etc.)
- **types/**: TypeScript type definitions and interfaces for strong typing
- **utils/**: Helper utilities and shared functions
- **tests/**: Testing utilities, helpers, and shared test configurations
- **scripts/**: Client-side scripts and utilities

## Authentication System

The application implements a comprehensive authentication system with multiple options for users.

### Authentication Methods

1. **Email/Password Authentication**
   - Secure password hashing using bcrypt
   - JWT-based session management
   - Email verification
   - Password reset functionality

2. **Discord OAuth Integration**
   - Login with Discord account
   - Account linking between Discord and Bank of Styx
   - Profile synchronization
   - Dedicated error handling

### Authentication Endpoints

- `/api/auth/login`: Email/password authentication
- `/api/auth/register`: New user registration
- `/api/auth/me`: Get current user information
- `/api/auth/verify-email`: Email verification
- `/api/auth/set-password`: Password reset functionality
- `/api/auth/discord/*`: Discord OAuth integration endpoints

### User Roles and Permissions

The system implements role-based access control with several user roles:
- Regular users
- Admins
- Cashiers
- Volunteer leads
- News editors

## Banking System

### Core Banking Features

- **Account Management**: View account details, balance, and settings
- **Transactions**: Process deposits, withdrawals, transfers, and donations
- **Pay Codes**: Generate and redeem payment codes
- **Transaction History**: View detailed transaction history
- **Real-time Updates**: Instant notifications for account changes

### Cashier Portal

- **Transaction Processing**: Process deposits and withdrawals
- **Member Management**: View and manage user accounts
- **Ledger**: Track all financial transactions
- **Statistics**: View banking system statistics
- **Notifications**: Real-time notifications for pending transactions

### Pay Code System

The digital payment code generation and redemption system provides secure, convenient transactions:

- **Generation**: Create new pay codes with specific amounts
- **QR Code Generation**: Generate QR codes for easy sharing and mobile scanning
- **Validation**: Validate pay codes before redemption with security checks
- **Redemption**: Real-time redemption processing integrated with banking system
- **History**: Track active and redeemed pay codes with detailed transaction history
- **Expiration Management**: Automatic expiration controls and time-based security
- **Security Features**: Fraud prevention and secure transaction processing
- **Mobile Integration**: Mobile-friendly scanning and redemption interface
- **Banking Integration**: Seamless integration with the core banking transaction system

This system revolutionizes digital payments within the Bank of Styx platform, providing a secure and convenient alternative to traditional payment methods.

## UI Components and Patterns

### Component Organization

UI components are organized by feature and functionality:

- **admin/**: Admin-specific components
- **auth/**: Authentication-related components
- **bank/**: Banking interface components
- **cashier/**: Cashier portal components
- **common/**: Shared utility components
- **events/**: Event management components
- **layout/**: Layout and structural components
- **news/**: News system components
- **notifications/**: Notification components
- **sales/**: Sales management components
- **settings/**: User settings components
- **shared/**: Cross-functional shared components
- **shop/**: Shopping system components
- **ui/**: Basic UI building blocks
- **user/**: User profile components
- **volunteer/**: Volunteer management components

### Shared UI Package

The project includes a shared UI package (`web/packages/ui`) with common components:

- Button components
- Card components
- Content card components
- Rich text editor components
- Featured content components
- Hero section components
- Form input components
- Modal dialog components
- Pagination components
- Scroll utility components
- Search components
- Sidebar navigation components
- Loading spinner components

### UI Patterns

The application follows consistent UI patterns:

1. **Layout Structure**:
   - Header with navigation
   - Sidebar for section-specific navigation
   - Main content area
   - Footer with links and information

2. **Dashboard Pattern**:
   - Summary cards at the top
   - Detailed information below
   - Action buttons for common tasks
   - Tabbed interfaces for related content

3. **Form Patterns**:
   - Consistent form layouts
   - Inline validation
   - Clear error messages
   - Progressive disclosure for complex forms

4. **Responsive Design**:
   - Mobile-first approach
   - Responsive breakpoints
   - Adaptive layouts for different screen sizes

## Database Structure

The application uses Prisma ORM with a MySQL database. The database schema and migrations are managed through Prisma and organized in the `web/apps/main-site/prisma/` directory.

### Database Management Files

- **schema.prisma**: Main Prisma schema defining all database models and relationships
- **seed.js**: Database seeding script for initial data and testing
- **dbsetup.sql**: Direct SQL setup commands for database initialization
- **migrations/**: Database migration files organized by feature and chronological order

### Migration Structure

The migrations directory contains feature-specific migrations including:
- Initial database setup
- Banking system models (accounts, transactions, pay codes)
- News and content management models
- Notification systems
- Support ticket system
- Event management models
- Volunteer management system
- Shopping and order system models
- Ticket hold system

### Database Commands

- `pnpm prisma migrate dev`: Apply migrations during development
- `pnpm prisma migrate deploy`: Deploy migrations to production
- `pnpm prisma:seed`: Seed the database with initial data
- `pnpm prisma:studio`: Open Prisma Studio for database management

### Key Database Models

1. **User Models**:
   - User accounts and profiles
   - Authentication credentials
   - User verification
   - User state and status

2. **Banking Models**:
   - Account balances
   - Transactions
   - Pay codes
   - Deposit records

3. **News Models**:
   - Articles
   - Categories
   - Authors
   - Featured content

4. **Notification Models**:
   - User notifications
   - Notification preferences
   - Notification types
   - Notification status

5. **Support Ticket Models**:
   - Tickets
   - Ticket categories
   - Ticket responses
   - Ticket status

6. **Event Management Models**:
   - Events
   - Event categories
   - Event schedules
   - Event registrations

7. **Volunteer Models**:
   - Volunteer categories
   - Volunteer shifts
   - Volunteer registrations
   - Volunteer coordinators

8. **Shopping Models**:
   - Products
   - Categories
   - Carts
   - Orders

9. **Ticket Hold System**:
   - Tickets
   - Holds
   - Ticket status
   - Hold expiration

## API Structure

The application uses Next.js API routes organized by feature:

### API Endpoints

The application includes both integrated API routes within the main Next.js application and standalone API services:

#### Main Application API Routes (`/api/`)
- `/api/admin/*`: Admin-specific APIs for user management and system administration
- `/api/auth/*`: Authentication APIs including login, registration, and OAuth integration
- `/api/bank/*`: Banking transaction APIs for deposits, withdrawals, transfers, and balance management
- `/api/cart/*`: Shopping cart APIs for cart management and item operations
- `/api/cashier/*`: Cashier functionality APIs for transaction processing and member management
- `/api/checkout/*`: Checkout and payment processing APIs
- `/api/cron/*`: Scheduled tasks and automated processes
- `/api/events/*`: Events management APIs for event creation, registration, and scheduling
- `/api/news/*`: News content APIs for article management and publishing
- `/api/notifications/*`: Notification APIs for real-time updates and messaging
- `/api/orders/*`: Order management APIs for purchase tracking and fulfillment
- `/api/products/*`: Product management APIs for inventory and catalog management
- `/api/sales/*`: Sales management APIs for transaction reporting and analytics
- `/api/support/*`: Support ticket system APIs for customer service management
- `/api/uploads/*`: File upload APIs for image and document management
- `/api/users/*`: User management APIs for profile and account operations
- `/api/volunteer/*`: Volunteer management APIs for shift scheduling and coordination

#### Standalone API Services (`web/apps/api/`)
- **Authentication Services**: Independent authentication endpoints that can be deployed separately
  - Login endpoint with JWT token generation
  - User registration with email verification
  - Current user information retrieval
  - Authentication testing and validation utilities
- **Shared Middleware**: CORS handling, authentication middleware, and request processing

### API Patterns

The API follows consistent patterns:

1. **RESTful Design**:
   - Resource-based URLs
   - Standard HTTP methods (GET, POST, PUT, DELETE)
   - Consistent response formats

2. **Error Handling**:
   - Standardized error responses
   - HTTP status codes
   - Detailed error messages

3. **Authentication**:
   - JWT-based authentication
   - Role-based access control
   - API route protection

## State Management

The application uses a combination of state management approaches:

### Server State

- **TanStack Query** (v5.17.19) for server state management
  - Data fetching and caching
  - Automatic refetching
  - Optimistic updates
  - Mutation handling

### Client State



- **React Context** for feature-specific state
  - Authentication context
  - Theme context
  - Notification context
  - Shopping cart context

### State Patterns

1. **Data Fetching Pattern**:
   - Use TanStack Query hooks for data fetching
   - Handle loading, error, and success states
   - Implement optimistic updates for mutations

2. **Global State Pattern**:
 
   - Create separate stores for different domains
   - Use selectors for performance optimization

3. **Context Pattern**:
   - Create context providers for feature-specific state
   - Use context consumers or hooks to access state
   - Implement memoization for performance

## Real-time Updates

The application uses Server-Sent Events (SSE) to provide real-time updates to users.

### Server-Sent Events (SSE)

- One-way communication channel from server to client
- Efficient for broadcasting updates to multiple clients
- Integrated with TanStack Query for state management

### Real-time Features

- **Balance Updates**: Instant balance updates when transactions occur
- **Transaction Notifications**: Real-time transaction history updates
- **Notifications**: System notifications for various events
- **Cashier Dashboard**: Real-time updates for cashiers processing transactions

## Key Features

### Core Administrative Features

#### News System
- Article creation, editing, and deletion
- Category organization
- Featured content highlighting
- Rich text editing

#### Admin Dashboard
- User account management
- Content management
- Support ticket handling
- Site usage statistics

### Ticket Hold System

- **Automatic Generation**: System auto-generates tickets for products and slots for volunteer shifts
- **Hold Management**: 15-minute automatic expiration with user-triggered extensions
- **Status Tracking**: Complete lifecycle tracking (Available, Held, Sold, Cancelled)
- **Race Condition Prevention**: Transaction-based system prevents double-booking
- **Real-time Updates**: Countdown timers show users how long their holds last

### Event Management System

- **Event Creation**: Admins can create and manage events with rich details
- **Event Categories**: Events can be organized by categories
- **Featured Events**: Highlight important upcoming events
- **Calendar Integration**: View events in calendar format with filters

### Volunteer Management System

- **Volunteer Categories**: Create categories with specific roles and pay rates
- **Shift Creation**: Set up shifts with time slots and volunteer limits
- **Volunteer Sign-up**: Users can browse and sign up for volunteer opportunities
- **Hour Tracking**: Track volunteer hours and process payments
- **Admin Dashboard**: Comprehensive management interface for organizers

### Support Ticket System

- **Ticket Categories**: Organize tickets by type (general, account, banking, etc.)
- **Priority Levels**: Assign low, medium, high, or urgent priority to tickets
- **Assignment**: Admins can assign tickets to specific staff members
- **Communication**: Public and internal notes for ticket management
- **Email Notifications**: Automatic notifications for ticket updates

### Shop Items and Shopping System

- **Product Management**: Create and manage products with inventory tracking
- **Shopping Cart**: Add items to cart with automatic hold management
- **Checkout Process**: Secure payment processing integration
- **Order History**: View past purchases and order status
- **Inventory Control**: Automatic inventory management with ticket-based system

## Current Development Focus

Based on the codebase analysis, the current development focus includes:

### Image Upload System Refactorization

A comprehensive plan is in place to refactor the image upload system:

1. **Unified API Structure**:
   - Single upload endpoint: `/api/uploads`
   - Consistent response format
   - Organized file structure

2. **Configuration-Driven Upload Rules**:
   - Type-specific configurations
   - Validation rules
   - Directory organization

3. **Unified Frontend Service**:
   - Single upload service
   - Progress tracking
   - Error handling

4. **Standardized Components**:
   - Universal image uploader
   - Specialized wrapper components
   - Consistent UI

### Event Capacity Management

There's ongoing work to improve event capacity management:

- Tying event capacity to product tickets
- Showing available capacity when creating product tickets
- Preventing overselling through the ticket hold system

### Admin Dashboard Navigation

Fixing navigation issues in the admin dashboard:
- Ensuring the "Admin" breadcrumb redirects back to the admin dashboard
- Currently experiencing a 404 error at http://192.168.1.87:3000/admin

## Known Issues

Based on the Z-TODO.TXT file, there are a few known issues:

1. Admin breadcrumb navigation issue:
   - When in the admin dashboard, the "Admin" breadcrumb leads to a 404 error
   - URL: http://192.168.1.87:3000/admin

2. Event capacity management:
   - Need to tie event capacity to product tickets
   - Show available capacity when creating product tickets

## Development Workflow

### Local Development Setup

1. **Clone the repository**:
   ```bash
   git clone https://github.com/your-organization/bank-of-styx.git
   cd bank-of-styx
   ```

2. **Install dependencies**:
   ```bash
   pnpm install
   ```

3. **Set up environment variables**:
   - Copy `.env.example` to `.env.local` in `web/apps/main-site`
   - Update the variables with your local configuration

4. **Set up the database**:
   ```bash
   cd web/apps/main-site
   pnpm prisma migrate dev
   pnpm prisma:seed
   ```

5. **Start the development server**:
   ```bash
   cd ../..
   pnpm dev
   ```

### Key Development Commands

- `pnpm dev`: Start the development server
- `pnpm build`: Build the application for production
- `pnpm lint`: Run linting checks
- `pnpm format`: Format code with Prettier
- `pnpm prisma:seed`: Seed the database with initial data
- `pnpm prisma:studio`: Open Prisma Studio to manage the database
- `pnpm ui:build`: Build the shared UI package
- `pnpm ui:dev`: Start the UI package development server
- `pnpm clean`: Clean up node_modules and build artifacts

### Environment Variables

Key environment variables required for the application:

```
# Database Connection
DATABASE_URL="mysql://username:password@localhost:3306/bank_of_styx"

# Authentication
JWT_SECRET="your_secret_key"

# Discord OAuth (if using)
DISCORD_CLIENT_ID=your_discord_client_id
DISCORD_CLIENT_SECRET=your_discord_client_secret
DISCORD_REDIRECT_URI=http://localhost:3000/api/auth/discord/callback

# Email Configuration
EMAIL_HOST=smtp.your-email-provider.com
EMAIL_PORT=465
EMAIL_SECURE=true
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_email_password
ADMIN_EMAIL=<EMAIL>
```

## Static Assets and File Management

### Static Assets (`web/apps/main-site/public/`)

The application manages static files and user-generated content in an organized structure:

#### Static Images
- Platform icons and branding assets
- Social media icons and external service logos
- UI assets and graphics for the interface
- Default avatars and placeholder images

#### User Uploads
- **User Avatars**: Profile pictures and avatar images
- **Deposit Verification**: Documents and receipts for deposit verification
- **News Article Images**: Featured images and media for news articles
- **Event Media**: Images and promotional materials for events

All files in the public directory are served statically by Next.js and should be optimized for web delivery.

## Testing Infrastructure

### Testing Utilities (`web/apps/main-site/src/tests/`)

The application includes comprehensive testing infrastructure:

- **Test Utility Functions**: Helper functions for common testing scenarios
- **Mock Data Generators**: Automated generation of test data for various models
- **Custom Testing Hooks**: React hooks specifically designed for testing components
- **Shared Test Configurations**: Common test setup and configuration files
- **API Mocking Utilities**: Mock implementations for API endpoints during testing
- **Database Testing Helpers**: Utilities for database setup and teardown in tests
- **Authentication Test Utilities**: Mock authentication states and user sessions

These utilities support comprehensive testing across the application by providing reusable testing infrastructure and mock implementations.

## TypeScript Type System

### Type Definitions (`web/apps/main-site/src/types/`)

The application maintains strong typing through comprehensive TypeScript definitions:

#### Type Categories

- **API Types**: Request/response interfaces for backend communication
- **Database Models**: Types matching Prisma schema models for type safety
- **Component Props**: Interface definitions for React components and their properties
- **Form Types**: Form validation and input type definitions for user interfaces
- **State Types**: Application state and context type definitions for state management
- **Utility Types**: Generic helper types and transformations for code reuse
- **Third-party Types**: Custom type definitions for external libraries and integrations

Strong typing ensures code reliability, enables better IDE support with autocomplete and error detection, and helps catch errors during development rather than runtime.

## Configuration Management

### Shared Configuration (`web/packages/config/`)

The configuration package ensures consistency across all applications:

#### Configuration Files

- **ESLint Preset**: Code quality and style enforcement rules
- **TailwindCSS Configuration**: Consistent styling system across applications
- **Base TypeScript Configuration**: Shared TypeScript settings extended by individual applications
- **Build and Development Settings**: Standardized build processes and development tools

#### Benefits

- Consistent code formatting and linting rules across the entire platform
- Unified styling system that maintains design consistency
- Standardized TypeScript configuration with flexibility for application-specific overrides
- Shared build and development settings that reduce configuration duplication

Applications extend these base configurations while maintaining the flexibility to override specific settings when needed for their particular requirements.

## Conclusion

The Bank of Styx website is a comprehensive banking platform with multiple integrated systems built using modern web technologies and best practices. This document provides an overview of the codebase structure, patterns, and key components to help you navigate and understand the system effectively.

### Key Takeaways

1. **Monorepo Architecture**: Well-organized structure with shared packages and independent applications
2. **Comprehensive Feature Set**: Banking, news, events, volunteer management, shopping, and support systems
3. **Modern Technology Stack**: Next.js 13+ with App Router, TypeScript, TailwindCSS, and Prisma
4. **Strong Type Safety**: Comprehensive TypeScript definitions throughout the application
5. **Real-time Capabilities**: Server-Sent Events for live updates and notifications
6. **Scalable Design**: Modular architecture that supports independent deployment and scaling
7. **Testing Infrastructure**: Comprehensive testing utilities and mock implementations
8. **Security Focus**: JWT authentication, role-based access control, and secure payment processing

For more detailed information on specific features, refer to the relevant sections of the codebase and the individual README files in each directory.