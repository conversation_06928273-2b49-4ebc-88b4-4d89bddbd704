import { NextRequest, NextResponse } from "next/server";
import { prisma } from "../../../../../../lib/prisma";
import { verifyToken } from "../../../../../../lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
export const GET = async (
  req: NextRequest,
  { params }: { params: { code: string } },
) => {
  try {
    const code = params.code;

    // Get authorization header
    const authHeader = req.headers.get("authorization");

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Extract token
    const token = authHeader.split(" ")[1];

    // Verify token
    const decoded = verifyToken(token);

    if (!decoded || typeof decoded !== "object" || !decoded.id) {
      return NextResponse.json({ error: "Invalid token" }, { status: 401 });
    }

    const userId = decoded.id as string;

    // Find the pay code
    const payCode = await prisma.payCode.findUnique({
      where: { code },
      include: {
        createdBy: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
      },
    });

    if (!payCode) {
      return NextResponse.json(
        { error: "Pay code not found" },
        { status: 404 },
      );
    }

    // Check if the code is active
    if (payCode.status !== "active") {
      return NextResponse.json(
        { error: `Pay code is ${payCode.status}` },
        { status: 400 },
      );
    }

    // Check if the code has expired
    if (new Date() > payCode.expiresAt) {
      // Update the code status to expired
      await prisma.payCode.update({
        where: { id: payCode.id },
        data: { status: "expired" },
      });

      return NextResponse.json(
        { error: "Pay code has expired" },
        { status: 400 },
      );
    }

    // Check if the code has reached its maximum uses
    if (payCode.maxUses !== null && payCode.uses >= payCode.maxUses) {
      // Update the code status to expired
      await prisma.payCode.update({
        where: { id: payCode.id },
        data: { status: "expired" },
      });

      return NextResponse.json(
        { error: "Pay code has reached its maximum uses" },
        { status: 400 },
      );
    }

    // Check if the user is trying to redeem their own code
    if (payCode.createdById === userId) {
      return NextResponse.json(
        { error: "You cannot redeem your own pay code" },
        { status: 400 },
      );
    }

    // Return the pay code details for validation
    const validationDetails = {
      id: payCode.id,
      code: payCode.code,
      amount: payCode.amount,
      createdBy: payCode.createdBy,
      valid: true,
    };

    return NextResponse.json(validationDetails);
  } catch (error) {
    console.error("Error validating pay code:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
};
