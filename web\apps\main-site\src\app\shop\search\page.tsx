"use client";

import { useSearchParams } from "next/navigation";
import { useSearchProducts } from "@/hooks/useProducts";
import { ProductCard } from "@/components/shop/ProductCard";
import { ProductSearch } from "@/components/shop/ProductSearch";
import { Spin<PERSON>, Card } from "@bank-of-styx/ui";
import Link from "next/link";

export default function SearchResultsPage() {
  const searchParams = useSearchParams();
  const query = searchParams.get("q") || "";

  const { data, isLoading, error } = useSearchProducts(query);
  const products = data?.products || [];

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-4">Search Results</h1>
        <ProductSearch />
      </div>

      {isLoading ? (
        <div className="flex justify-center p-8">
          <Spinner size="lg" />
        </div>
      ) : error ? (
        <Card className="p-8 text-center">
          <h2 className="text-2xl font-bold mb-4">Error</h2>
          <p className="mb-6">
            Failed to load search results. Please try again.
          </p>
        </Card>
      ) : products.length === 0 ? (
        <Card className="p-8 text-center">
          <h2 className="text-2xl font-bold mb-4">No results found</h2>
          <p className="mb-6">
            We couldn't find any products matching "{query}".
          </p>
          <Link href="/shop">
            <button className="bg-primary text-white px-4 py-2 rounded-md">
              Browse All Products
            </button>
          </Link>
        </Card>
      ) : (
        <>
          <p className="mb-6">
            Showing {products.length} results for "{query}"
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {products.map((product) => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>
        </>
      )}
    </div>
  );
}
