"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import fetchClient from "@/lib/fetchClient";

// Types
export interface VolunteerPayment {
  id: string;
  assignmentId: string;
  userId: string;
  user: {
    id: string;
    username: string;
    displayName: string;
    avatar: string | null;
  };
  hoursWorked: number;
  paymentAmount: number;
  paymentStatus: string; // pending, processing, paid, cancelled
  verifiedById: string | null;
  verifiedBy: {
    id: string;
    username: string;
    displayName: string;
  } | null;
  verifiedAt: string | null;
  transactionId: string | null;
  transaction: {
    id: string;
    amount: number;
    status: string;
    createdAt: string;
  } | null;
  createdAt: string;
  updatedAt: string;
  assignment: {
    id: string;
    status: string;
    shiftId: string;
    shift: {
      id: string;
      name: string;
      startTime: string;
      endTime: string;
      eventId: string;
      categoryId: string;
      event: {
        id: string;
        name: string;
      };
      category: {
        id: string;
        name: string;
        payRate: number | null;
      };
    };
  };
}

export interface PaymentFilters {
  eventId?: string;
  categoryId?: string;
  startDate?: string;
  endDate?: string;
  userId?: string;
  status?: string;
}

export interface ProcessPaymentData {
  paymentIds: string[];
  note?: string;
}

// Query keys
export const volunteerPaymentQueryKeys = {
  payments: "volunteerPayments",
  filteredPayments: (filters: PaymentFilters) => [
    "volunteerPayments",
    "filtered",
    filters,
  ],
  paymentHistory: "volunteerPaymentHistory",
  filteredPaymentHistory: (filters: PaymentFilters) => [
    "volunteerPaymentHistory",
    "filtered",
    filters,
  ],
};

/**
 * Hook to fetch pending volunteer payments with optional filters
 */
export function useVolunteerPayments(filters: PaymentFilters = {}) {
  const queryKey =
    Object.keys(filters).length > 0
      ? volunteerPaymentQueryKeys.filteredPayments(filters)
      : ["volunteerPayments"];

  return useQuery({
    queryKey,
    queryFn: async () => {
      const params = new URLSearchParams();

      if (filters.eventId) params.append("eventId", filters.eventId);
      if (filters.categoryId) params.append("categoryId", filters.categoryId);
      if (filters.startDate) params.append("startDate", filters.startDate);
      if (filters.endDate) params.append("endDate", filters.endDate);
      if (filters.userId) params.append("userId", filters.userId);
      if (filters.status) params.append("status", filters.status);

      const url = `/api/volunteer/management/payments${
        params.toString() ? `?${params.toString()}` : ""
      }`;
      return fetchClient.get<VolunteerPayment[]>(url);
    },
  });
}

/**
 * Hook to fetch volunteer payment history with optional filters
 */
export function useVolunteerPaymentHistory(filters: PaymentFilters = {}) {
  const queryKey =
    Object.keys(filters).length > 0
      ? volunteerPaymentQueryKeys.filteredPaymentHistory(filters)
      : ["volunteerPaymentHistory"];

  return useQuery({
    queryKey,
    queryFn: async () => {
      const params = new URLSearchParams();

      if (filters.eventId) params.append("eventId", filters.eventId);
      if (filters.categoryId) params.append("categoryId", filters.categoryId);
      if (filters.startDate) params.append("startDate", filters.startDate);
      if (filters.endDate) params.append("endDate", filters.endDate);
      if (filters.userId) params.append("userId", filters.userId);
      if (filters.status) params.append("status", filters.status);

      const url = `/api/volunteer/management/payments/history${
        params.toString() ? `?${params.toString()}` : ""
      }`;
      return fetchClient.get<VolunteerPayment[]>(url);
    },
  });
}

/**
 * Hook to process a single volunteer payment
 */
export function useProcessVolunteerPayment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      paymentId,
      note,
    }: {
      paymentId: string;
      note?: string;
    }) => {
      return fetchClient.post(`/api/volunteer/management/payments/process`, {
        paymentIds: [paymentId],
        note,
      });
    },
    onSuccess: () => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ["volunteerPayments"] });
      queryClient.invalidateQueries({ queryKey: ["volunteerPaymentHistory"] });
    },
  });
}

/**
 * Hook to process multiple volunteer payments at once
 */
export function useBulkProcessVolunteerPayments() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: ProcessPaymentData) => {
      return fetchClient.post(
        `/api/volunteer/management/payments/process`,
        data,
      );
    },
    onSuccess: () => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ["volunteerPayments"] });
      queryClient.invalidateQueries({ queryKey: ["volunteerPaymentHistory"] });
    },
  });
}

export default {
  useVolunteerPayments,
  useVolunteerPaymentHistory,
  useProcessVolunteerPayment,
  useBulkProcessVolunteerPayments,
};
