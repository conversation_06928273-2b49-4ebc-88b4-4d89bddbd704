"use client";

import { useQuery } from "@tanstack/react-query";
import { useUserState } from "@/contexts/UserStateContext";
import fetchClient from "@/lib/fetchClient";

/**
 * Interface for news query options
 */
interface NewsQueryOptions {
  featured?: boolean;
  category?: string;
  limit?: number;
  sortBy?: string;
  order?: string;
  maxAgeMinutes?: number;
}

/**
 * Custom hook for fetching news articles with state-based caching
 * @param {Object} options - Query options
 * @param {boolean} options.featured - Filter by featured status
 * @param {string} options.category - Filter by category slug
 * @param {number} options.limit - Limit the number of results
 * @param {string} options.sortBy - Sort field
 * @param {string} options.order - Sort order (asc/desc)
 * @param {number} options.maxAgeMinutes - Maximum age of cached data in minutes
 * @returns {Object} Query result with data, isLoading, and error
 */
export function useStateBasedNews({
  featured,
  category,
  limit = 10,
  sortBy = "publishedAt",
  order = "desc",
  maxAgeMinutes = 60,
}: NewsQueryOptions = {}) {
  const { userState, updateUserState, needsFreshData } = useUserState();

  // Build query parameters
  const params = new URLSearchParams();
  if (featured !== undefined) params.append("featured", featured.toString());
  if (category) params.append("category", category);
  if (limit) params.append("limit", limit.toString());
  if (sortBy) params.append("sortBy", sortBy);
  if (order) params.append("order", order);

  // Create query key
  const queryKey = ["news", featured, category, limit, sortBy, order];

  // Check if we need fresh data
  const needsFresh = userState ? needsFreshData("News", maxAgeMinutes) : true;

  // Fetch news articles
  const query = useQuery({
    queryKey,
    queryFn: async () => {
      // Use public news endpoint for non-authenticated users
      const endpoint = userState ? `/api/news/articles` : `/api/news/public`;

      // Only log in development or when debugging is needed
      if (process.env.NODE_ENV === "development") {
        console.log("useStateBasedNews - Fetching from:", endpoint);
        console.log("useStateBasedNews - Params:", {
          featured,
          category,
          limit,
          sortBy,
          order,
          userState: !!userState,
        });
      }

      // Convert params to query params object
      const queryParams: Record<string, string> = {};
      if (featured !== undefined) queryParams.featured = featured.toString();
      if (category) queryParams.category = category;
      if (limit) queryParams.limit = limit.toString();
      if (sortBy) queryParams.sortBy = sortBy;
      if (order) queryParams.order = order;

      const data = await fetchClient.get(endpoint, { params: queryParams });

      // Update last sync time if user state is available
      if (userState) {
        updateUserState({ lastNewsSync: new Date().toISOString() });
      }

      return data;
    },
    staleTime: needsFresh ? 0 : 1000 * 60 * maxAgeMinutes, // Use stale data if within maxAgeMinutes
    enabled: true, // Always run the query, even for non-authenticated users
  });

  return query;
}

/**
 * Interface for category query options
 */
interface CategoryQueryOptions {
  maxAgeMinutes?: number;
}

/**
 * Custom hook for fetching news categories with state-based caching
 * @param {Object} options - Query options
 * @param {number} options.maxAgeMinutes - Maximum age of cached data in minutes
 * @returns {Object} Query result with data, isLoading, and error
 */
export function useStateBasedCategories({
  maxAgeMinutes = 1440,
}: CategoryQueryOptions = {}) {
  const { userState, updateUserState, needsFreshData } = useUserState();

  // Check if we need fresh data
  const needsFresh = userState
    ? needsFreshData("Category", maxAgeMinutes)
    : true;

  // Fetch categories
  const query = useQuery({
    queryKey: ["categories"],
    queryFn: async () => {
      const data = await fetchClient.get("/api/news/categories");

      // Update last sync time if user state is available
      if (userState) {
        updateUserState({ lastCategorySync: new Date().toISOString() });
      }

      return data;
    },
    staleTime: needsFresh ? 0 : 1000 * 60 * maxAgeMinutes, // Use stale data if within maxAgeMinutes
    enabled: true, // Always run the query, even for non-authenticated users
  });

  return query;
}
