"use client";

import React, { useState, useEffect, useRef } from "react";
import { useShipSearch, Ship } from "@/hooks/usePublicVolunteer";
import { useDebounce } from "@/hooks/useDebounce";

interface ShipSearchProps {
  value?: Ship | null;
  onChange: (ship: Ship | null) => void;
  placeholder?: string;
  allowNone?: boolean;
  disabled?: boolean;
  error?: string;
}

export const ShipSearch: React.FC<ShipSearchProps> = ({
  value,
  onChange,
  placeholder = "Search for ship...",
  allowNone = false,
  disabled = false,
  error,
}) => {
  const [query, setQuery] = useState("");
  const [showDropdown, setShowDropdown] = useState(false);
  const [inputValue, setInputValue] = useState(value?.name || "");
  const debouncedQuery = useDebounce(query, 300);
  const containerRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const { data: searchResults, isLoading } = useShipSearch(debouncedQuery);
  const ships = searchResults?.ships || [];

  // Handle clicks outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setShowDropdown(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Update input value when value prop changes
  useEffect(() => {
    setInputValue(value?.name || "");
  }, [value]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    setQuery(newValue);
    setShowDropdown(true);
    
    // If input is cleared, clear the selection
    if (!newValue) {
      onChange(null);
    }
  };

  const handleShipSelect = (ship: Ship | null) => {
    if (ship) {
      setInputValue(ship.name);
      setQuery("");
    } else {
      setInputValue("");
      setQuery("");
    }
    onChange(ship);
    setShowDropdown(false);
  };

  const handleInputFocus = () => {
    setShowDropdown(true);
    // If there's a current value, search for similar ships
    if (value) {
      setQuery(value.name);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Escape") {
      setShowDropdown(false);
      inputRef.current?.blur();
    }
  };

  return (
    <div className="relative" ref={containerRef}>
      <input
        ref={inputRef}
        type="text"
        value={inputValue}
        onChange={handleInputChange}
        onFocus={handleInputFocus}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        disabled={disabled}
        className={`w-full px-3 py-2 bg-secondary border ${
          error ? "border-accent" : "border-gray-600"
        } rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary ${
          disabled ? "opacity-50 cursor-not-allowed" : ""
        }`}
      />
      
      {error && (
        <p className="text-accent text-sm mt-1">{error}</p>
      )}

      {showDropdown && (query.length >= 2 || allowNone) && (
        <div className="absolute z-10 w-full mt-1 bg-secondary border border-gray-600 rounded-md shadow-lg max-h-60 overflow-y-auto">
          {allowNone && (
            <button
              type="button"
              onClick={() => handleShipSelect(null)}
              className="w-full text-left px-3 py-2 hover:bg-secondary-light text-gray-400 border-b border-gray-700"
            >
              None / Open Camping
            </button>
          )}
          
          {isLoading && query.length >= 2 && (
            <div className="px-3 py-2 text-gray-400">
              Searching ships...
            </div>
          )}
          
          {!isLoading && query.length >= 2 && ships.length === 0 && (
            <div className="px-3 py-2 text-gray-400">
              No ships found matching "{query}"
            </div>
          )}
          
          {ships.map((ship) => (
            <button
              key={ship.id}
              type="button"
              onClick={() => handleShipSelect(ship)}
              className="w-full text-left px-3 py-2 hover:bg-secondary-light text-white border-b border-gray-700 last:border-b-0"
            >
              <div className="font-medium">{ship.name}</div>
              <div className="text-sm text-gray-400">
                Captain: {ship.captain.displayName}
              </div>
            </button>
          ))}
          
          {query.length < 2 && !allowNone && (
            <div className="px-3 py-2 text-gray-400">
              Type at least 2 characters to search
            </div>
          )}
        </div>
      )}
    </div>
  );
};