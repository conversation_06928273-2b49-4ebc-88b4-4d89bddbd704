# Banking System

## Overview
The Banking System is the core financial infrastructure of the Bank of Styx application, providing comprehensive transaction management, pay code functionality, cashier operations, and real-time balance tracking. It supports user-to-user transfers, deposits, withdrawals, donations, and a sophisticated pay code system, all with role-based access control and real-time notifications.

## Directory Structure
```
banking/
├── src/app/bank/                      # User banking interface
│   ├── page.tsx                       # Banking landing page
│   └── dashboard/                     # Main banking dashboard
│       ├── page.tsx                   # Dashboard home
│       ├── transfer/                  # Transfer operations
│       ├── withdraw/                  # Withdrawal requests
│       ├── deposit/                   # Deposit submissions
│       ├── donate/                    # Donation system
│       ├── transactions/              # Transaction history
│       └── pay-code/                  # Pay code management
│           ├── create/                # Pay code creation
│           ├── manage/                # Pay code management
│           └── redeem/                # Pay code redemption
├── src/app/cashier/                   # Cashier operations
│   └── dashboard/                     # Cashier dashboard
│       ├── page.tsx                   # Cashier home
│       ├── members/                   # Member management
│       ├── deposits/                  # Deposit processing
│       ├── withdrawals/               # Withdrawal processing
│       ├── transactions/              # Transaction search
│       ├── ledger/                    # Ledger management
│       └── statistics/                # Banking statistics
├── src/app/api/bank/                  # Banking API endpoints
│   ├── transactions/                  # Transaction operations
│   ├── pay-codes/                     # Pay code system
│   ├── statistics/                    # Banking analytics
│   └── cashier/                       # Cashier operations
├── src/components/bank/               # Banking UI components
│   ├── DashboardLayout.tsx           # Main layout wrapper
│   ├── QuickActions.tsx              # Action buttons
│   ├── AccountSummary.tsx            # Account information
│   └── RecentTransactions.tsx        # Transaction display
├── src/components/cashier/            # Cashier UI components
├── src/services/bankService.ts        # Banking business logic
└── src/hooks/useBank.ts               # Banking React hooks
```

## Key Files & Components

### Frontend Components
- **`DashboardLayout.tsx`** - Main banking layout with responsive navigation and account summary
- **`QuickActions.tsx`** - Responsive action buttons for transfer, deposit, withdraw, and pay code operations
- **`AccountSummary.tsx`** - Real-time balance display with transaction summary cards
- **`RecentTransactions.tsx`** - Paginated transaction history with filtering capabilities
- **`TransactionItem.tsx`** - Individual transaction display with status indicators and details
- **`CashierDashboardLayout.tsx`** - Cashier interface layout with specialized sidebar navigation

### API Endpoints
- **`GET /api/bank/transactions`** - Filtered transaction history with search, date range, and type filtering
- **`POST /api/bank/transactions`** - Create transactions (transfer, withdrawal, donation) with balance validation
- **`GET /api/bank/transactions/deposits`** - Deposit history and status tracking
- **`POST /api/bank/transactions/deposits`** - Submit deposit requests with receipt upload
- **`GET /api/bank/transactions/withdrawals`** - Withdrawal history and pending requests
- **`POST /api/bank/transactions/withdrawals`** - Submit withdrawal requests with escrow system
- **`GET /api/bank/transactions/recent`** - Recent transaction summaries for dashboard display
- **`GET /api/bank/pay-codes`** - Pay code listing with pagination and filtering
- **`POST /api/bank/pay-codes`** - Create pay codes with unique BOS-XXXX-XXXX format
- **`POST /api/bank/pay-codes/redeem`** - Redeem pay codes with trial run capability
- **`GET /api/bank/pay-codes/validate/[code]`** - Validate pay code existence and status
- **`POST /api/bank/pay-codes/[id]/cancel`** - Cancel active pay codes
- **`GET /api/bank/statistics`** - Banking statistics including deposits, withdrawals, and user activity
- **`GET /api/bank/cashier/pending-transactions`** - Pending transactions requiring cashier approval
- **`PUT /api/bank/cashier/transactions/[id]`** - Approve or reject pending transactions

### Database Models
- **`Transaction`** - Core transaction model with atomic balance updates and comprehensive tracking
- **`PayCode`** - Pay code system with unique code generation and usage tracking
- **`Ledger`** - Daily reconciliation and verification system for audit trails
- **`User`** - Banking-related fields including balance, role flags, and notification preferences

### Services
- **`bankService.ts`** - Comprehensive banking service with 613 lines covering all transaction types, pay codes, and cashier operations
- **`uploadService.ts`** - File upload handling for deposit receipts with validation and processing

### Hooks
- **`useBank()`** - TanStack Query-based banking hook with optimistic updates and error handling
- **`useBankStatistics()`** - Real-time banking statistics and analytics
- **`useCashierOperations()`** - Cashier-specific operations and member management

## Common Tasks

### Task 1: How to Process a Transfer
1. User navigates to `/bank/dashboard/transfer`
2. Selects recipient from user search or dropdown
3. Enters amount with real-time balance validation
4. Adds optional description/note
5. Confirms transfer via modal
6. System creates transaction record and updates both user balances atomically
7. Real-time notifications sent to both parties
8. Balance updates reflected immediately in UI via SSE

### Task 2: How to Create a Pay Code
1. User navigates to `/bank/dashboard/pay-code/create`
2. Specifies amount and optional expiration date
3. Sets usage limits (single-use or multiple-use)
4. System generates unique BOS-XXXX-XXXX format code
5. Pay code stored with active status
6. Amount deducted from creator's balance immediately
7. QR code and sharing options provided
8. Code becomes available for redemption

### Task 3: How to Process Cashier Deposits
1. Cashier logs into `/cashier/dashboard`
2. Reviews pending deposits in `/cashier/dashboard/deposits`
3. Validates deposit receipt images and amounts
4. Approves or rejects deposits with notes
5. Approved deposits increment user balance immediately
6. Rejected deposits notify user with rejection reason
7. All actions logged with cashier identification
8. Real-time balance updates via SSE

## API Integration

### Authentication Requirements
- **Public endpoints**: None (all banking endpoints require authentication)
- **User endpoints**: Valid JWT token for personal banking operations
- **Cashier endpoints**: JWT token + `isBanker` role flag
- **Admin endpoints**: JWT token + `isAdmin` role flag
- **Token format**: `Bearer <jwt-token>` in Authorization header

### Request/Response Examples
```typescript
// Transfer Request
interface TransferRequest {
  recipientId: string;
  amount: number;
  description?: string;
  note?: string;
}

// Transaction Response
interface TransactionResponse {
  id: string;
  amount: number;
  type: 'transfer' | 'withdrawal' | 'deposit' | 'donation' | 'paycode_create' | 'paycode_redeem';
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  description?: string;
  note?: string;
  sender?: {
    id: string;
    username: string;
    displayName: string;
  };
  recipient?: {
    id: string;
    username: string;
    displayName: string;
  };
  processedBy?: {
    id: string;
    username: string;
    displayName: string;
  };
  createdAt: string;
  updatedAt: string;
  processedAt?: string;
}

// Pay Code Creation Request
interface PayCodeCreateRequest {
  amount: number;
  expiresAt?: string;
  maxUses?: number;
}

// Pay Code Response
interface PayCodeResponse {
  id: string;
  code: string; // Format: BOS-XXXX-XXXX
  amount: number;
  status: 'active' | 'redeemed' | 'cancelled' | 'expired' | 'paused';
  expiresAt?: string;
  maxUses?: number;
  uses: number;
  createdBy: {
    id: string;
    username: string;
    displayName: string;
  };
  redeemedBy?: {
    id: string;
    username: string;
    displayName: string;
  };
  createdAt: string;
  redeemedAt?: string;
}

// Banking Statistics Response
interface BankingStatisticsResponse {
  totalBalance: number;
  totalDeposits: number;
  totalWithdrawals: number;
  totalTransfers: number;
  pendingDeposits: number;
  pendingWithdrawals: number;
  dailyActivity: Array<{
    date: string;
    deposits: number;
    withdrawals: number;
    transfers: number;
  }>;
  topUsers: Array<{
    user: {
      id: string;
      username: string;
      displayName: string;
    };
    transactionCount: number;
    totalVolume: number;
  }>;
}
```

## Database Schema

### Primary Models
```sql
-- Transaction table (core financial records)
CREATE TABLE Transaction (
  id VARCHAR(191) PRIMARY KEY,
  amount DOUBLE NOT NULL,
  type ENUM('transfer', 'withdrawal', 'deposit', 'donation', 'paycode_create', 'paycode_redeem') NOT NULL,
  status ENUM('pending', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
  description TEXT,
  note TEXT,
  senderId VARCHAR(191),
  recipientId VARCHAR(191),
  processedById VARCHAR(191), -- Cashier who processed transaction
  payCodeId VARCHAR(191), -- Linked pay code if applicable
  paymentMethod VARCHAR(255),
  receiptImage VARCHAR(255), -- Upload path for deposit receipts
  ledgerId VARCHAR(191),
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  processedAt DATETIME,
  FOREIGN KEY (senderId) REFERENCES User(id),
  FOREIGN KEY (recipientId) REFERENCES User(id),
  FOREIGN KEY (processedById) REFERENCES User(id),
  FOREIGN KEY (payCodeId) REFERENCES PayCode(id),
  FOREIGN KEY (ledgerId) REFERENCES Ledger(id),
  INDEX idx_transaction_sender (senderId),
  INDEX idx_transaction_recipient (recipientId),
  INDEX idx_transaction_status (status),
  INDEX idx_transaction_created (createdAt),
  INDEX idx_transaction_type (type)
);

-- PayCode table (pay code system)
CREATE TABLE PayCode (
  id VARCHAR(191) PRIMARY KEY,
  code VARCHAR(20) UNIQUE NOT NULL, -- Format: BOS-XXXX-XXXX
  amount DOUBLE NOT NULL,
  status ENUM('active', 'redeemed', 'cancelled', 'expired', 'paused') DEFAULT 'active',
  expiresAt DATETIME,
  maxUses INT DEFAULT 1,
  uses INT DEFAULT 0,
  createdById VARCHAR(191) NOT NULL,
  redeemedById VARCHAR(191),
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  redeemedAt DATETIME,
  FOREIGN KEY (createdById) REFERENCES User(id),
  FOREIGN KEY (redeemedById) REFERENCES User(id),
  INDEX idx_paycode_code (code),
  INDEX idx_paycode_creator (createdById),
  INDEX idx_paycode_status (status),
  INDEX idx_paycode_expires (expiresAt)
);

-- Ledger table (daily reconciliation)
CREATE TABLE Ledger (
  id VARCHAR(191) PRIMARY KEY,
  date DATE NOT NULL,
  description TEXT,
  totalDeposits DOUBLE DEFAULT 0,
  totalWithdrawals DOUBLE DEFAULT 0,
  totalTransfers DOUBLE DEFAULT 0,
  netChange DOUBLE DEFAULT 0,
  status ENUM('pending', 'verified') DEFAULT 'pending',
  verifiedById VARCHAR(191),
  verifiedAt DATETIME,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (verifiedById) REFERENCES User(id),
  INDEX idx_ledger_date (date),
  INDEX idx_ledger_status (status)
);

-- User banking fields
ALTER TABLE User ADD COLUMN balance DOUBLE DEFAULT 0;
ALTER TABLE User ADD COLUMN isBanker BOOLEAN DEFAULT false;
ALTER TABLE User ADD COLUMN notifyTransfers BOOLEAN DEFAULT true;
ALTER TABLE User ADD COLUMN notifyDeposits BOOLEAN DEFAULT true;
ALTER TABLE User ADD COLUMN notifyWithdrawals BOOLEAN DEFAULT true;
```

### Relationships
- **User ↔ Transaction**: One-to-many as sender, recipient, or processor
- **PayCode ↔ User**: Many-to-one for creator and redeemer relationships
- **PayCode ↔ Transaction**: One-to-one for pay code creation and redemption transactions
- **Ledger ↔ Transaction**: One-to-many for daily transaction grouping
- **Transaction ↔ Upload**: One-to-one for deposit receipt storage

## Related Features
- **[Authentication System](./authentication-system.md)** - JWT authentication and role-based access control for banking operations
- **[Notification System](./notification-system.md)** - Real-time transaction notifications and balance updates
- **[Real-time SSE](./real-time-sse-system.md)** - Server-Sent Events for immediate balance updates and transaction status
- **[Upload System](./upload-system.md)** - Deposit receipt handling with image validation and processing
- **[User Management System](./user-management-system.md)** - User search and selection for transfers and cashier operations

## User Roles & Permissions
- **Users**: Personal banking operations (transfer, deposit request, withdraw request, pay code creation/redemption)
- **Bankers/Cashiers**: Transaction approval/rejection, member account access, deposit/withdrawal processing, ledger management
- **Admins**: Full banking system access, user balance management, system statistics access
- **Super Admins**: All banking operations plus system configuration and critical financial operations

## Recent Changes
- **v4.2.0** - Enhanced pay code system with usage limits and expiration management
- **v4.1.0** - Implemented real-time balance updates via Server-Sent Events
- **v4.0.0** - Complete cashier dashboard overhaul with improved transaction processing
- **v3.5.0** - Added comprehensive banking statistics and reporting system
- **v3.4.0** - Escrow system implementation for withdrawal processing
- **v3.3.0** - Pay code system launch with BOS-XXXX-XXXX format
- **v3.2.0** - Deposit receipt upload system with image validation

## Troubleshooting

### Common Issues
1. **Balance discrepancies**: Check Prisma transaction isolation and atomic updates. Verify all balance changes use database transactions.
2. **Pay code collisions**: System generates unique codes with collision detection. If collisions occur, check code generation algorithm.
3. **Deposit receipt uploads failing**: Verify file size limits (20MB), supported formats (JPG, PNG, PDF), and upload directory permissions.
4. **Real-time updates not working**: Check SSE connection status and JWT token validity for real-time endpoints.
5. **Withdrawal escrow issues**: Ensure balance deduction happens before approval process, with proper rollback on rejection.

### Debug Information
- **Log locations**: Transaction logs in server console, file upload logs in upload service
- **Environment variables**: Database connection, file upload paths, notification settings
- **Database queries**: Use Prisma Studio to inspect transaction records and balance consistency
- **Debugging commands**:
  ```bash
  # Check user balance consistency
  curl -H "Authorization: Bearer <token>" http://localhost:3000/api/auth/me
  
  # Verify transaction history
  curl -H "Authorization: Bearer <token>" http://localhost:3000/api/bank/transactions
  
  # Check banking statistics
  curl -H "Authorization: Bearer <token>" http://localhost:3000/api/bank/statistics
  ```

### Performance Considerations
- **Transaction Processing**: All financial operations use Prisma transactions for ACID compliance
- **Balance Updates**: Atomic operations prevent race conditions during concurrent transactions
- **Real-time Updates**: SSE connections limited to prevent server overload
- **Database Indexing**: Comprehensive indexing on transaction tables for fast queries
- **File Upload Optimization**: Image compression and validation for deposit receipts
- **Pagination**: Large transaction lists use cursor-based pagination for performance

### Security Measures
- **Balance Validation**: Server-side validation prevents negative balances and overdrafts
- **Transaction Integrity**: Double-entry bookkeeping principles with sender/recipient tracking
- **Role-based Access**: Strict permission checking for cashier and admin operations  
- **Audit Trails**: Complete transaction history with processor identification
- **File Security**: Upload validation and secure file storage for deposit receipts
- **Rate Limiting**: Transaction frequency limits to prevent abuse

### Currency System
- **Currency**: "NS" (Nexus Styx)
- **Display Format**: `NS ${amount.toFixed(0)}` (whole numbers only in UI)
- **Storage**: Double precision for calculation accuracy
- **Minimum Amount**: 1 NS (no decimal transactions in UI)

---

**File Locations:**
- Pages: `/src/app/bank/`, `/src/app/cashier/`
- Components: `/src/components/bank/`, `/src/components/cashier/`
- API: `/src/app/api/bank/`
- Services: `/src/services/bankService.ts`
- Hooks: `/src/hooks/useBank.ts`
- Types: `/src/types/bank.ts`