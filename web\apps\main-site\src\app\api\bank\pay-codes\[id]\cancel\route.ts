import { NextRequest, NextResponse } from "next/server";
import prisma from "../../../../../../lib/prisma";
import { verifyToken } from "../../../../../../lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
export const POST = async (
  req: NextRequest,
  { params }: { params: { id: string } },
) => {
  try {
    const id = params.id;

    // Get authorization header
    const authHeader = req.headers.get("authorization");

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Extract token
    const token = authHeader.split(" ")[1];

    // Verify token
    const decoded = verifyToken(token);

    if (!decoded || !decoded.id) {
      return NextResponse.json({ error: "Invalid token" }, { status: 401 });
    }

    const userId = decoded.id as string;

    // Find the pay code with creator details
    const payCode = await prisma.payCode.findUnique({
      where: { id },
      include: {
        createdBy: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
      },
    });

    if (!payCode) {
      return NextResponse.json(
        { error: "Pay code not found" },
        { status: 404 },
      );
    }

    // Check if the user is the creator of the pay code
    if (payCode.createdById !== userId) {
      return NextResponse.json(
        { error: "You can only cancel your own pay codes" },
        { status: 403 },
      );
    }

    // Check if the pay code is in a state that can be cancelled
    if (payCode.status !== "active" && payCode.status !== "paused") {
      return NextResponse.json(
        { error: `Cannot cancel a pay code that is ${payCode.status}` },
        { status: 400 },
      );
    }

    // Process the cancellation in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Update the pay code status to cancelled
      const updatedPayCode = await tx.payCode.update({
        where: { id },
        data: { status: "cancelled" },
        include: {
          createdBy: {
            select: {
              id: true,
              username: true,
              displayName: true,
              avatar: true,
            },
          },
        },
      });

      // Create a transaction record for audit purposes only (zero amount)
      const transaction = await tx.transaction.create({
        data: {
          amount: 0, // Zero amount since no money is moving
          type: "paycode_cancel",
          status: "completed",
          description: `Pay code cancelled: ${payCode.code}`,
          senderId: userId, // The creator is cancelling their own code
          payCodeId: payCode.id,
        },
        include: {
          sender: {
            select: {
              id: true,
              username: true,
              displayName: true,
              avatar: true,
            },
          },
        },
      });

      // No balance update needed since no money was taken when creating the code

      return { payCode: updatedPayCode, transaction };
    });

    // Format dates for response
    const formattedResponse = {
      payCode: {
        ...result.payCode,
        createdAt: result.payCode.createdAt.toISOString(),
        expiresAt: result.payCode.expiresAt.toISOString(),
        redeemedAt: result.payCode.redeemedAt
          ? result.payCode.redeemedAt.toISOString()
          : null,
      },
      transaction: {
        ...result.transaction,
        createdAt: result.transaction.createdAt.toISOString(),
        updatedAt: result.transaction.updatedAt.toISOString(),
        processedAt: result.transaction.processedAt
          ? result.transaction.processedAt.toISOString()
          : null,
      },
      message: "Pay code cancelled successfully",
    };

    return NextResponse.json(formattedResponse);
  } catch (error) {
    console.error("Error cancelling pay code:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
};
