"use client";

import React from "react";

export interface SidebarProps {
  title?: string;
  className?: string;
  children: React.ReactNode;
}

export const Sidebar: React.FC<SidebarProps> = ({
  title,
  className = "",
  children,
}) => {
  return (
    <div className={`bg-secondary-light rounded-lg shadow-md p-5 ${className}`}>
      {title && (
        <h3 className="text-xl font-bold mb-4 pb-3 border-b border-gray-600 text-white">
          {title}
        </h3>
      )}
      <div>{children}</div>
    </div>
  );
};

export interface SidebarItemProps {
  title: string;
  subtitle?: string;
  image?: string;
  date?: string;
  onClick?: () => void;
  className?: string;
}

export const SidebarItem: React.FC<SidebarItemProps> = ({
  title,
  subtitle,
  image,
  date,
  onClick,
  className = "",
}) => {
  return (
    <div
      className={`flex items-start py-3 border-b border-gray-600 last:border-0 ${
        onClick ? "cursor-pointer hover:bg-secondary" : ""
      } ${className}`}
      onClick={onClick}
    >
      {image && (
        <div className="flex-shrink-0 mr-3">
          <img
            src={image}
            alt={title}
            className="w-16 h-16 object-cover rounded"
          />
        </div>
      )}
      <div className="flex-grow">
        <h4 className="text-sm font-semibold text-white mb-1">{title}</h4>
        {subtitle && (
          <p className="text-xs text-gray-400 mb-1 line-clamp-2">{subtitle}</p>
        )}
        {date && <span className="text-xs text-gray-500">{date}</span>}
      </div>
    </div>
  );
};
