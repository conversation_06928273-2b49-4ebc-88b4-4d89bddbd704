import { NextRequest, NextResponse } from "next/server";
import { getEventCapacityProducts } from "@/lib/event-capacity-system";

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// GET /api/events/[id]/products - Get products associated with an event
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const eventId = params.id;

    if (!eventId) {
      return NextResponse.json(
        { error: "Event ID is required" },
        { status: 400 }
      );
    }

    const products = await getEventCapacityProducts(eventId);

    return NextResponse.json({
      eventId,
      products,
      count: products.length
    });
  } catch (error) {
    console.error("Error fetching event products:", error);
    return NextResponse.json(
      { error: "Failed to fetch event products" },
      { status: 500 }
    );
  }
}