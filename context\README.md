# Context Documentation

This folder contains important code snippets and patterns that differ from standard practices in this codebase. Use this as a reference when working on the project.

## Contents

- `authentication.md` - JWT authentication patterns and dual credential system
- `real-time-sse.md` - Server-Sent Events implementation for real-time updates
- `upload-system.md` - Custom image upload and processing patterns
- `banking-system.md` - Banking transaction and payment code system
- `event-volunteer-system.md` - Event management, volunteer shifts, and payment processing
- `product-shopping-system.md` - E-commerce with ticket holds and Stripe integration
- `color-theme-ui-elements.md` - Custom color themes, mobile-first design, and UI patterns

## Key Architecture Notes

### Monorepo Structure
- PNPM workspace with multiple apps (`main-site`, `api`)
- Shared UI package with custom components
- Next.js 13.4+ with App Router pattern

### Authentication Flow
- Dual credential system (new + legacy)
- JWT tokens with 7-day expiration
- Discord OAuth integration
- Query parameter auth for SSE connections

### Real-time Features
- SSE endpoint at `/api/notifications/sse`
- Connection store for managing active connections
- Heartbeat service for connection health
- Real-time balance updates and notifications

### Database
- MySQL 8.0 with Prisma ORM
- Complex banking transaction system
- Role-based access control
- Extensive notification preferences

### Custom Systems
- Image upload with Sharp processing
- Ticket hold system (15-minute auto-expiration)
- Pay code system for transactions
- Volunteer management with categories and shifts