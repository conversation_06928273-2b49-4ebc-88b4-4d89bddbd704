import fs from "fs";
import path from "path";
import { v4 as uuidv4 } from "uuid";
import { prisma } from "@/lib/prisma";

interface SaveImageOptions {
  entityType?: string | null;
  entityId?: string | null;
  directory?: string;
}

export async function saveImage(file: File, options: SaveImageOptions = {}) {
  try {
    const {
      entityType = null,
      entityId = null,
      directory = "general",
    } = options;

    // Create unique filename
    const timestamp = Date.now();
    const safeFilename = file.name.replace(/[^a-zA-Z0-9.-]/g, "-");
    const filename = `${timestamp}-${safeFilename}`;

    // Set paths
    const uploadDir = path.join(process.cwd(), "public", "uploads", directory);
    const relativePath = path.join("public", "uploads", directory, filename);
    const fullPath = path.join(process.cwd(), relativePath);

    // Ensure directory exists with proper error handling
    try {
      await fs.promises.mkdir(uploadDir, { recursive: true });
    } catch (error) {
      console.error(`Error creating directory ${uploadDir}:`, error);
      throw new Error(
        `Failed to create upload directory: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
      );
    }

    // Write file with proper error handling
    try {
      const arrayBuffer = await file.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);
      await fs.promises.writeFile(fullPath, uint8Array);
    } catch (error) {
      console.error(`Error writing file to ${fullPath}:`, error);
      throw new Error(
        `Failed to write file: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
      );
    }

    // Check if an image with the same entityType, entityId, and filename already exists
    let image;
    try {
      if (entityType && entityId) {
        const existingImage = await prisma.uploadedImage.findFirst({
          where: {
            entityType,
            entityId,
            filename: safeFilename,
          },
        });

        if (existingImage) {
          console.log(
            `Found existing image record for ${entityType}/${entityId}/${safeFilename}, updating it`,
          );

          // Update the existing record with the new file info
          image = await prisma.uploadedImage.update({
            where: { id: existingImage.id },
            data: {
              path: relativePath,
              url: `/api/images/${directory}/${filename}`, // Will be updated after
              mimeType: file.type || "application/octet-stream",
              size: file.size,
              updatedAt: new Date(),
            },
          });
        } else {
          // Create a new record
          image = await prisma.uploadedImage.create({
            data: {
              filename: safeFilename,
              path: relativePath,
              url: `/api/images/${directory}/${filename}`, // Will be updated after we have the ID
              mimeType: file.type || "application/octet-stream",
              size: file.size,
              entityType,
              entityId,
            },
          });
        }
      } else {
        // If no entityType or entityId, just create a new record
        image = await prisma.uploadedImage.create({
          data: {
            filename: safeFilename,
            path: relativePath,
            url: `/api/images/${directory}/${filename}`, // Will be updated after we have the ID
            mimeType: file.type || "application/octet-stream",
            size: file.size,
            entityType,
            entityId,
          },
        });
      }
    } catch (error) {
      console.error(
        "Error handling database record for uploaded image:",
        error,
      );
      throw new Error(
        `Failed to handle database record: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
      );
    }

    // Update the URL with the actual ID
    try {
      await prisma.uploadedImage.update({
        where: { id: image.id },
        data: { url: `/api/images/${image.id}` },
      });
    } catch (error) {
      console.error("Error updating image URL in database:", error);
      // Don't throw here, as the image is already saved
      console.warn("Image saved but URL not updated in database");
    }

    return {
      id: image.id,
      filename: image.filename,
      url: `/api/images/${image.id}`,
      originalUrl: `/uploads/${directory}/${filename}`, // For debugging only
    };
  } catch (error) {
    console.error("Error in saveImage function:", error);
    throw error;
  }
}
