﻿# Bank of Styx Website - Core Infrastructure
# Generated on 08/07/2025 12:35:25
# Priority: High
# Shared components, UI library, configuration, utilities, hooks, and core infrastructure
# Root directory: C:\Users\<USER>\projects\test\web

## Directories and Files

### Files
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\admin\
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\admin\AddUserModal.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\admin\AdminDashboardLayout.backup.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\admin\AdminDashboardLayout.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\admin\CSVExportButton.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\admin\EventStatsModal.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\admin\index.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\admin\README.md
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\admin\UserActionButtons.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\admin\UserProfileCard.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\admin\UserProfileModal.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\admin\UserRoleBadges.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\admin\UserStatusBadge.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\auth\
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\auth\AuthModal.css
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\auth\AuthModal.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\auth\EmailVerificationForm.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\auth\GeneratedPasswordDisplay.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\auth\index.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\auth\PasswordCreationModal.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\auth\README.md
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\bank\
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\bank\AccountSummary.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\bank\DashboardLayout.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\bank\index.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\bank\QuickActions.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\bank\README.md
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\bank\RecentTransactions.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\bank\TransactionItem.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\captain\
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\captain\CaptainDashboardLayout.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\captain\index.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\captain\VolunteerHoursStatus.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\cashier\
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\cashier\CashierDashboardLayout.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\cashier\CashierQuickActions.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\cashier\DepositCard.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\cashier\DepositsManager.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\cashier\index.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\cashier\LedgerDisplay.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\cashier\MemberCard.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\cashier\MemberProfile.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\cashier\MemberSearch.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\cashier\MemberTransactionHistory.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\cashier\MemberTransactionStats.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\cashier\README.md
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\cashier\StatisticsDisplay.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\cashier\TransactionCard.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\cashier\TransactionHistoryCard.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\cashier\TransactionsManager.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\cashier\WithdrawalCard.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\cashier\WithdrawalsManager.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\common\
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\common\DatabaseImage.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\common\ImageUploader.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\common\PageViewTracker.js
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\common\README.md
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\common\UniversalImageUploader.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\ErrorBoundary.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\events\
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\events\EventCard.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\events\README.md
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\layout\
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\layout\Breadcrumbs.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\layout\Footer.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\layout\Header.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\layout\MainLayout.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\layout\Navigation.backup.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\layout\Navigation.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\layout\README.md
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\news\
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\news\ArticlePreviewModal.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\news\ArticleViewTracker.js
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\news\CategorySelector.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\news\FeaturedImageUploader.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\news\index.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\news\NewsArticleCard.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\news\NewsDashboardLayout.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\news\NewsEditor.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\news\README.md
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\notifications\
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\notifications\index.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\notifications\NotificationIcon.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\notifications\NotificationItem.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\notifications\NotificationPanel.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\notifications\README.md
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\README.md
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\sales\
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\sales\CategoryForm.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\sales\CategoryList.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\sales\index.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\sales\OrderList.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\sales\ProductForm.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\sales\ProductList.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\sales\README.md
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\sales\RedemptionCodesSection.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\sales\SalesDashboardLayout.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\sales\SalesDashboardMain.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\settings\
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\settings\ColorPicker.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\settings\README.md
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\settings\ThemePresets.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\shared\
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\shared\Badge.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\shared\ConfirmationModal.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\shared\index.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\shared\README.md
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\shared\Select.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\ships\
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\ships\InvitationBar.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\ships\JoinRequestNotification.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\ships\MemberInviteSearch.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\ships\MemberManagementTable.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\ships\RoleCreationForm.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\ships\ShipInviteCard.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\shop\
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\shop\CartHoldTimer.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\shop\CartItemCard.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\shop\CategoryFilter.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\shop\CheckoutForm.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\shop\index.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\shop\ProductCard.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\shop\ProductSearch.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\shop\README.md
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\shop\RedemptionCodeInput.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\ui\
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\ui\index.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\ui\README.md
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\ui\Tab.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\upload\
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\upload\AvatarUploaderV2.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\upload\DepositReceiptUploader.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\upload\EventImageUploader.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\upload\index.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\upload\NewsImageUploader.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\upload\NewsImageUploaderV2.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\upload\ProductImageUploader.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\upload\ShipLogoUploader.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\user\
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\user\AvatarUploadModal.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\user\README.md
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\user\SimpleAvatarUpload.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\user\SyncDiscordAvatarButton.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\user\UserMenu.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\volunteer\
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\volunteer\CategoryCard.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\volunteer\CategoryForm.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\volunteer\CategoryList.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\volunteer\CategorySelector.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\volunteer\ConfirmationModal.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\volunteer\DashboardMain.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\volunteer\DashboardStats.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\volunteer\EventSelector.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\volunteer\index.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\volunteer\lead\
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\volunteer\lead\index.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\volunteer\lead\LeadDashboardMain.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\volunteer\lead\LeadDashboardStats.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\volunteer\lead\README.md
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\volunteer\lead\ShiftCard.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\volunteer\lead\VolunteerLeadDashboardLayout.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\volunteer\PaymentConfirmationModal.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\volunteer\PaymentFilters.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\volunteer\PaymentHistory.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\volunteer\PaymentList.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\volunteer\public\
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\volunteer\public\index.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\volunteer\public\README.md
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\volunteer\public\ShipSearch.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\volunteer\public\UserVolunteerQuickActions.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\volunteer\public\VolunteerCategoriesList.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\volunteer\public\VolunteerCategoryCard.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\volunteer\public\VolunteerEventCard.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\volunteer\public\VolunteerEventsList.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\volunteer\public\VolunteerShiftCard.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\volunteer\public\VolunteerShiftsList.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\volunteer\public\VolunteerSignupModal.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\volunteer\README.md
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\volunteer\ShiftCard.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\volunteer\ShiftForm.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\volunteer\ShiftList.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\volunteer\ShiftStatusModal.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\volunteer\Tabs.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\volunteer\UserSearchInput.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\volunteer\VolunteerDashboardLayout.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\components\volunteer\VolunteerQuickActions.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\hooks\
C:\Users\<USER>\projects\test\web\apps\main-site\src\hooks\captain\
C:\Users\<USER>\projects\test\web\apps\main-site\src\hooks\captain\useCaptainShip.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\hooks\captain\useVolunteerRequirements.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\hooks\README.md
C:\Users\<USER>\projects\test\web\apps\main-site\src\hooks\useBank.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\hooks\useCart.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\hooks\useConfirm.tsx
C:\Users\<USER>\projects\test\web\apps\main-site\src\hooks\useDebounce.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\hooks\useNews.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\hooks\useNotificationPreferences.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\hooks\useNotifications.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\hooks\useOrders.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\hooks\useProductCategories.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\hooks\useProducts.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\hooks\usePublicNews.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\hooks\usePublicVolunteer.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\hooks\useSSE.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\hooks\useStateBasedNews.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\hooks\useVolunteerCategories.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\hooks\useVolunteerPayments.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\hooks\useVolunteerShifts.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\hooks\useVolunteerUsers.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\lib\
C:\Users\<USER>\projects\test\web\apps\main-site\src\lib\auth.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\lib\clientImageProcessing.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\lib\connectionStore.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\lib\dev-config.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\lib\discord.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\lib\discordAvatarUtils.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\lib\email.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\lib\event-capacity-system.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\lib\fetchClient.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\lib\heartbeatService.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\lib\imageDisplayUtils.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\lib\imageProcessing.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\lib\imageProcessingV2.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\lib\imageUpload.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\lib\notifications.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\lib\performance.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\lib\prisma.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\lib\README.md
C:\Users\<USER>\projects\test\web\apps\main-site\src\lib\README-image-processing.md
C:\Users\<USER>\projects\test\web\apps\main-site\src\lib\redemptionCodes.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\lib\sanitize.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\lib\shipLogoUpload.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\lib\stripe.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\lib\stripe-server.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\lib\ticket-system.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\lib\uploadConfig.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\lib\upload-service.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\lib\utils.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\utils\
C:\Users\<USER>\projects\test\web\apps\main-site\src\utils\csv-export.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\utils\loremIpsum.ts
C:\Users\<USER>\projects\test\web\apps\main-site\src\utils\README.md
C:\Users\<USER>\projects\test\web\apps\main-site\src\utils\shiftIntervalHelper.ts
C:\Users\<USER>\projects\test\web\packages\config\
C:\Users\<USER>\projects\test\web\packages\config\eslint-preset.js
C:\Users\<USER>\projects\test\web\packages\config\package.json
C:\Users\<USER>\projects\test\web\packages\config\README.md
C:\Users\<USER>\projects\test\web\packages\config\tailwind.config.js
C:\Users\<USER>\projects\test\web\packages\config\tsconfig.base.json
C:\Users\<USER>\projects\test\web\packages\ui\
C:\Users\<USER>\projects\test\web\packages\ui\.eslintrc.js
C:\Users\<USER>\projects\test\web\packages\ui\package.json
C:\Users\<USER>\projects\test\web\packages\ui\README.md
C:\Users\<USER>\projects\test\web\packages\ui\src\
C:\Users\<USER>\projects\test\web\packages\ui\src\button\
C:\Users\<USER>\projects\test\web\packages\ui\src\button\Button.tsx
C:\Users\<USER>\projects\test\web\packages\ui\src\button\index.tsx
C:\Users\<USER>\projects\test\web\packages\ui\src\card\
C:\Users\<USER>\projects\test\web\packages\ui\src\card\Card.tsx
C:\Users\<USER>\projects\test\web\packages\ui\src\card\index.tsx
C:\Users\<USER>\projects\test\web\packages\ui\src\content-card\
C:\Users\<USER>\projects\test\web\packages\ui\src\content-card\ContentCard.tsx
C:\Users\<USER>\projects\test\web\packages\ui\src\content-card\index.ts
C:\Users\<USER>\projects\test\web\packages\ui\src\editor\
C:\Users\<USER>\projects\test\web\packages\ui\src\editor\index.tsx
C:\Users\<USER>\projects\test\web\packages\ui\src\editor\rich-text-editor.tsx
C:\Users\<USER>\projects\test\web\packages\ui\src\featured-content\
C:\Users\<USER>\projects\test\web\packages\ui\src\featured-content\FeaturedContent.tsx
C:\Users\<USER>\projects\test\web\packages\ui\src\featured-content\index.ts
C:\Users\<USER>\projects\test\web\packages\ui\src\form-builder\
C:\Users\<USER>\projects\test\web\packages\ui\src\form-builder\FormBuilder.tsx
C:\Users\<USER>\projects\test\web\packages\ui\src\form-builder\FormRenderer.tsx
C:\Users\<USER>\projects\test\web\packages\ui\src\form-builder\index.tsx
C:\Users\<USER>\projects\test\web\packages\ui\src\form-builder\types.ts
C:\Users\<USER>\projects\test\web\packages\ui\src\hero\
C:\Users\<USER>\projects\test\web\packages\ui\src\hero\Hero.tsx
C:\Users\<USER>\projects\test\web\packages\ui\src\hero\index.ts
C:\Users\<USER>\projects\test\web\packages\ui\src\index.tsx
C:\Users\<USER>\projects\test\web\packages\ui\src\input\
C:\Users\<USER>\projects\test\web\packages\ui\src\input\Checkbox.tsx
C:\Users\<USER>\projects\test\web\packages\ui\src\input\index.tsx
C:\Users\<USER>\projects\test\web\packages\ui\src\input\Input.tsx
C:\Users\<USER>\projects\test\web\packages\ui\src\input\Textarea.tsx
C:\Users\<USER>\projects\test\web\packages\ui\src\input\UserSearchInput.tsx
C:\Users\<USER>\projects\test\web\packages\ui\src\modal\
C:\Users\<USER>\projects\test\web\packages\ui\src\modal\index.tsx
C:\Users\<USER>\projects\test\web\packages\ui\src\modal\Modal.css
C:\Users\<USER>\projects\test\web\packages\ui\src\modal\Modal.tsx
C:\Users\<USER>\projects\test\web\packages\ui\src\pagination\
C:\Users\<USER>\projects\test\web\packages\ui\src\pagination\index.ts
C:\Users\<USER>\projects\test\web\packages\ui\src\pagination\Pagination.tsx
C:\Users\<USER>\projects\test\web\packages\ui\src\README.md
C:\Users\<USER>\projects\test\web\packages\ui\src\scroll-to-top\
C:\Users\<USER>\projects\test\web\packages\ui\src\scroll-to-top\index.ts
C:\Users\<USER>\projects\test\web\packages\ui\src\scroll-to-top\ScrollToTop.tsx
C:\Users\<USER>\projects\test\web\packages\ui\src\search-bar\
C:\Users\<USER>\projects\test\web\packages\ui\src\search-bar\index.ts
C:\Users\<USER>\projects\test\web\packages\ui\src\search-bar\SearchBar.tsx
C:\Users\<USER>\projects\test\web\packages\ui\src\sidebar\
C:\Users\<USER>\projects\test\web\packages\ui\src\sidebar\index.ts
C:\Users\<USER>\projects\test\web\packages\ui\src\sidebar\Sidebar.tsx
C:\Users\<USER>\projects\test\web\packages\ui\src\spinner\
C:\Users\<USER>\projects\test\web\packages\ui\src\spinner\index.tsx
C:\Users\<USER>\projects\test\web\packages\ui\tsconfig.json

