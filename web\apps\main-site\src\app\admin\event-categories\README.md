# Event Categories Management

Administrative interface for managing event categories and organizational structure.

## Features

- **page.tsx** - Event category management dashboard

## Category Management

This interface provides administrators with tools for:

- Creating new event categories
- Editing existing category information
- Managing category hierarchy and organization
- Setting category descriptions and metadata
- Controlling category visibility and display order
- Organizing events by category
- Managing category-specific settings and properties

Event categories help organize and structure events for better navigation and discovery, making it easier for users to find relevant events and for administrators to manage event organization.
