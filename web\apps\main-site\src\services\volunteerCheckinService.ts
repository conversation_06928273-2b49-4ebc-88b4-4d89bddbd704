/**
 * Volunteer Check-In Service
 * Handles time-based check-in logic and window calculations
 */

export interface CheckInStatus {
  canCheckIn: boolean;
  isCheckedIn: boolean;
  timeUntilCheckIn?: number; // minutes until check-in opens
  timeUntilExpiry?: number;  // minutes until check-in closes
  windowStart: Date;         // 15 minutes before shift
  windowEnd: Date;           // shift start time
  status: 'not_ready' | 'available' | 'expired' | 'checked_in';
}

export interface VolunteerAssignmentStatus {
  id: string;
  status: string;
  shift: {
    startTime: string;
    endTime: string;
  };
}

/**
 * Calculate check-in status for a volunteer shift
 * Check-in window opens 15 minutes before shift start and closes at shift start
 */
export const getCheckInStatus = (
  shiftStartTime: Date,
  assignmentStatus: string,
  currentTime: Date = new Date()
): CheckInStatus => {
  const CHECKIN_WINDOW_MINUTES = 15;
  
  // Calculate check-in window
  const windowStart = new Date(shiftStartTime.getTime() - (CHECKIN_WINDOW_MINUTES * 60 * 1000));
  const windowEnd = new Date(shiftStartTime.getTime());
  
  // Check if already checked in
  const isCheckedIn = assignmentStatus === 'checked_in';
  
  // If already checked in, return checked in status
  if (isCheckedIn) {
    return {
      canCheckIn: false,
      isCheckedIn: true,
      windowStart,
      windowEnd,
      status: 'checked_in'
    };
  }
  
  // Check if current time is before check-in window
  if (currentTime < windowStart) {
    const minutesUntilCheckIn = Math.ceil((windowStart.getTime() - currentTime.getTime()) / (1000 * 60));
    return {
      canCheckIn: false,
      isCheckedIn: false,
      timeUntilCheckIn: minutesUntilCheckIn,
      windowStart,
      windowEnd,
      status: 'not_ready'
    };
  }
  
  // Check if current time is after check-in window (shift has started)
  if (currentTime > windowEnd) {
    return {
      canCheckIn: false,
      isCheckedIn: false,
      windowStart,
      windowEnd,
      status: 'expired'
    };
  }
  
  // Check-in window is currently open
  const minutesUntilExpiry = Math.ceil((windowEnd.getTime() - currentTime.getTime()) / (1000 * 60));
  
  return {
    canCheckIn: true,
    isCheckedIn: false,
    timeUntilExpiry: minutesUntilExpiry,
    windowStart,
    windowEnd,
    status: 'available'
  };
};

/**
 * Batch check-in status calculation for multiple assignments
 */
export const getBatchCheckInStatus = (
  assignments: VolunteerAssignmentStatus[],
  currentTime: Date = new Date()
): Record<string, CheckInStatus> => {
  const statusMap: Record<string, CheckInStatus> = {};
  
  assignments.forEach(assignment => {
    const shiftStartTime = new Date(assignment.shift.startTime);
    statusMap[assignment.id] = getCheckInStatus(shiftStartTime, assignment.status, currentTime);
  });
  
  return statusMap;
};

/**
 * Check if volunteer is eligible to check in
 * Validates assignment status and time window
 */
export const canVolunteerCheckIn = (
  assignment: VolunteerAssignmentStatus,
  currentTime: Date = new Date()
): boolean => {
  // Only volunteers with 'pending', 'assigned' or 'confirmed' status can check in
  if (!['pending', 'assigned', 'confirmed'].includes(assignment.status)) {
    return false;
  }
  
  const shiftStartTime = new Date(assignment.shift.startTime);
  const checkInStatus = getCheckInStatus(shiftStartTime, assignment.status, currentTime);
  
  return checkInStatus.canCheckIn;
};

/**
 * Format time remaining for display
 */
export const formatTimeRemaining = (minutes: number): string => {
  if (minutes <= 0) {
    return 'Now';
  }
  
  if (minutes < 60) {
    return `${minutes}m`;
  }
  
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  
  if (remainingMinutes === 0) {
    return `${hours}h`;
  }
  
  return `${hours}h ${remainingMinutes}m`;
};