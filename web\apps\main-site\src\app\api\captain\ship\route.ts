import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth";

export const dynamic = 'force-dynamic';

export async function GET(request: Request) {
  try {
    const currentUser = await getCurrentUser(request);

    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Get the ship this user captains
    const ship = await prisma.ship.findFirst({
      where: {
        captainId: currentUser.id,
        status: 'active',
      },
      include: {
        members: {
          where: { status: 'active' },
          include: {
            user: {
              select: {
                id: true,
                username: true,
                displayName: true,
                avatar: true,
              },
            },
          },
          orderBy: { joinedAt: 'asc' },
        },
        _count: {
          select: {
            members: {
              where: { status: 'active' },
            },
          },
        },
      },
    });

    if (!ship) {
      return NextResponse.json(
        { error: "You are not a captain of any ship" },
        { status: 403 }
      );
    }

    const transformedShip = {
      id: ship.id,
      name: ship.name,
      description: ship.description,
      slogan: ship.slogan,
      logo: ship.logo,
      tags: ship.tags as string[] || [],
      members: ship.members.map(member => ({
        id: member.id,
        role: member.role,
        joinedAt: member.joinedAt.toISOString(),
        user: member.user,
      })),
      memberCount: ship._count.members,
      createdAt: ship.createdAt.toISOString(),
      updatedAt: ship.updatedAt.toISOString(),
    };

    return NextResponse.json(transformedShip);
  } catch (error) {
    console.error("Error fetching captain ship:", error);
    return NextResponse.json(
      { error: "Failed to fetch ship data" },
      { status: 500 }
    );
  }
}

export async function PUT(request: Request) {
  try {
    const currentUser = await getCurrentUser(request);

    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Get the ship this user captains
    const existingShip = await prisma.ship.findFirst({
      where: {
        captainId: currentUser.id,
        status: 'active',
      },
    });

    if (!existingShip) {
      return NextResponse.json(
        { error: "You are not a captain of any ship" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { name, description, slogan, tags, logo } = body;

    // Validate required fields
    if (!name || typeof name !== 'string' || name.trim().length === 0) {
      return NextResponse.json(
        { error: "Ship name is required" },
        { status: 400 }
      );
    }

    if (!description || typeof description !== 'string' || description.trim().length === 0) {
      return NextResponse.json(
        { error: "Ship description is required" },
        { status: 400 }
      );
    }

    // Check if name is taken by another ship
    if (name.trim() !== existingShip.name) {
      const nameExists = await prisma.ship.findFirst({
        where: {
          name: name.trim(),
          id: { not: existingShip.id },
          status: { not: 'deleted' },
        },
      });

      if (nameExists) {
        return NextResponse.json(
          { error: "Ship name is already taken" },
          { status: 400 }
        );
      }
    }

    // Validate tags if provided
    let processedTags = [];
    if (tags) {
      if (Array.isArray(tags)) {
        processedTags = tags
          .filter(tag => typeof tag === 'string' && tag.trim().length > 0)
          .map(tag => tag.trim().toLowerCase())
          .slice(0, 10); // Limit to 10 tags
      }
    }

    // Update the ship
    const updatedShip = await prisma.ship.update({
      where: { id: existingShip.id },
      data: {
        name: name.trim(),
        description: description.trim(),
        slogan: slogan ? slogan.trim() : null,
        tags: processedTags.length > 0 ? processedTags : undefined,
        logo: logo || existingShip.logo, // Update logo if provided, otherwise keep existing
      },
      include: {
        captain: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
        _count: {
          select: {
            members: {
              where: { status: 'active' },
            },
          },
        },
      },
    });

    const transformedShip = {
      id: updatedShip.id,
      name: updatedShip.name,
      description: updatedShip.description,
      slogan: updatedShip.slogan,
      logo: updatedShip.logo,
      tags: updatedShip.tags as string[] || [],
      captain: (updatedShip as any).captain,
      memberCount: (updatedShip as any)._count.members,
      createdAt: updatedShip.createdAt.toISOString(),
      updatedAt: updatedShip.updatedAt.toISOString(),
    };

    return NextResponse.json({
      success: true,
      message: "Ship updated successfully",
      ship: transformedShip,
    });
  } catch (error) {
    console.error("Error updating ship:", error);
    return NextResponse.json(
      { error: "Failed to update ship" },
      { status: 500 }
    );
  }
}

export async function DELETE(request: Request) {
  try {
    const currentUser = await getCurrentUser(request);

    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Get the ship this user captains
    const ship = await prisma.ship.findFirst({
      where: {
        captainId: currentUser.id,
        status: 'active',
      },
      include: {
        members: {
          where: { status: 'active' },
        },
      },
    });

    if (!ship) {
      return NextResponse.json(
        { error: "You are not a captain of any ship" },
        { status: 403 }
      );
    }

    // For now, we'll mark the ship as pending deletion rather than actually deleting
    // This allows for administrative review and potential recovery
    await prisma.ship.update({
      where: { id: ship.id },
      data: {
        status: 'pending_deletion',
      },
    });

    // Optionally, you could also deactivate all members
    // await prisma.shipMember.updateMany({
    //   where: { shipId: ship.id },
    //   data: { status: 'removed' },
    // });

    return NextResponse.json({
      success: true,
      message: "Ship deletion requested. Your ship has been marked for administrative review.",
    });
  } catch (error) {
    console.error("Error requesting ship deletion:", error);
    return NextResponse.json(
      { error: "Failed to request ship deletion" },
      { status: 500 }
    );
  }
}