"use client";

import React, { useEffect, useState, useMemo, useCallback } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "../../../../../contexts/AuthContext";
import {
  NewsDashboardLayout,
  NewsEditor,
  NewsImageUploader,
  CategorySelector,
  ArticlePreviewModal,
} from "../../../../../components/news";
import { sanitizeHtml } from "../../../../../lib/sanitize";
import Link from "next/link";
import { useCreateArticle, useCategories } from "../../../../../hooks/useNews";

export default function CreateArticlePage() {
  const { user, isLoading: authLoading } = useAuth();
  const router = useRouter();
  const [isAuthorized, setIsAuthorized] = useState(false);

  // Form state
  const [title, setTitle] = useState("");
  const [content, setContent] = useState("");
  const [excerpt, setExcerpt] = useState("");
  const [categoryId, setCategoryId] = useState("");
  const [featured, setFeatured] = useState(false);
  const [status, setStatus] = useState<"published" | "draft">("draft");
  const [imageUrl, setImageUrl] = useState<string>("");
  const [errors, setErrors] = useState<{
    title?: string;
    content?: string;
    excerpt?: string;
    categoryId?: string;
    image?: string;
  }>({});

  // Preview state
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>("");

  // Fetch categories
  const { data: categories, isLoading: categoriesLoading } = useCategories();

  // Create article mutation
  const createArticleMutation = useCreateArticle();

  // Handle image upload - memoized to prevent unnecessary re-renders
  const handleImageChange = useCallback(
    (imageUrl: string) => {
      setImageUrl(imageUrl);
      // Clear image error when an image is selected
      if (imageUrl && errors.image) {
        setErrors((prev) => ({ ...prev, image: undefined }));
      }
    },
    [errors.image],
  );

  // Handle category change - memoized to prevent unnecessary re-renders
  const handleCategoryChange = useCallback(
    (category: string) => {
      setCategoryId(category);
      // Clear category error when a category is selected
      if (category && errors.categoryId) {
        setErrors((prev) => ({ ...prev, categoryId: undefined }));
      }

      // Update selected category name for preview
      if (category && categories) {
        const selectedCat = categories.find((cat) => cat.id === category);
        if (selectedCat) {
          setSelectedCategory(selectedCat.name);
        }
      }
    },
    [errors.categoryId, categories],
  );

  // Memoize categories to prevent unnecessary re-renders of CategorySelector
  const memoizedCategories = useMemo(() => categories || [], [categories]);

  // Check authorization after auth is loaded
  useEffect(() => {
    // Only proceed with checks if auth loading is complete
    if (!authLoading) {
      if (!user || !user.roles?.editor) {
        router.push("/");
      } else {
        // User is authorized
        setIsAuthorized(true);
      }
    }
  }, [user, router, authLoading]);

  // Show loading state or nothing while checking authorization
  if (authLoading || !isAuthorized) {
    return (
      <div className="min-h-screen bg-secondary-dark flex items-center justify-center">
        <div className="bg-secondary-light p-6 rounded-lg shadow-md border border-gray-600">
          <p className="text-white text-lg">Loading...</p>
        </div>
      </div>
    );
  }

  // Validate form
  const validateForm = () => {
    const newErrors: typeof errors = {};

    if (!title.trim()) {
      newErrors.title = "Title is required";
    }

    if (!content.trim()) {
      newErrors.content = "Content is required";
    }

    if (!excerpt.trim()) {
      newErrors.excerpt = "Excerpt is required";
    }

    if (!categoryId) {
      newErrors.categoryId = "Category is required";
    }

    if (!imageUrl) {
      newErrors.image = "Featured image is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      try {
        // Sanitize content before submission
        const sanitizedContent = sanitizeHtml(content);

        // Submit the form data using mutation
        await createArticleMutation.mutateAsync({
          title,
          content: sanitizedContent,
          excerpt,
          categoryId,
          image: imageUrl,
          status,
          featured,
        });

        // Navigate to articles list on success
        router.push("/news/dashboard/articles");
      } catch (error) {
        console.error("Error creating article:", error);
        // You could set an overall form error state here if needed
      }
    }
  };

  return (
    <NewsDashboardLayout>
      <div className="space-y-5">
        <div className="bg-secondary-light rounded-lg shadow-md pb-2 border border-gray-600">
          <div className="flex justify-between items-center p-2">
            <h2 className="text-xl sm:text-2xl font-bold text-white">
              Create New Article
            </h2>
          </div>

          {createArticleMutation.isError && (
            <div className="bg-accent bg-opacity-20 text-accent border border-accent rounded-md p-3 mb-4">
              <p>Error creating article. Please try again.</p>
            </div>
          )}

          <form onSubmit={handleSubmit} className="p-2">
            <div className="space-y-3">
              <div className="bg-secondary-dark rounded-lg p-3 mb-4 border border-gray-600">
                <label
                  htmlFor="title"
                  className="block text-base font-medium text-gray-400 mb-1"
                >
                  Title
                </label>
                <input
                  type="text"
                  id="title"
                  className={`w-full px-4 py-2 bg-secondary border ${
                    errors.title ? "border-accent" : "border-gray-600"
                  } rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary`}
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  disabled={createArticleMutation.isPending}
                />
                {errors.title && (
                  <p className="mt-2 text-sm text-accent">{errors.title}</p>
                )}
              </div>

              <div className="bg-secondary-dark rounded-lg p-3 mb-4 border border-gray-600">
                <label
                  htmlFor="excerpt"
                  className="block text-base font-medium text-gray-400 mb-2"
                >
                  Excerpt
                </label>
                <input
                  type="text"
                  id="excerpt"
                  className={`w-full px-4 py-2 bg-secondary border ${
                    errors.excerpt ? "border-accent" : "border-gray-600"
                  } rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary`}
                  value={excerpt}
                  onChange={(e) => setExcerpt(e.target.value)}
                  disabled={createArticleMutation.isPending}
                />
                {errors.excerpt && (
                  <p className="mt-2 text-sm text-accent">{errors.excerpt}</p>
                )}
              </div>

              <div className="bg-secondary-dark rounded-lg p-3 mb-4 border border-gray-600">
                <label
                  htmlFor="category"
                  className="block text-base font-medium text-gray-400 mb-2"
                >
                  Category
                </label>
                <CategorySelector
                  selectedCategory={categoryId}
                  onChange={handleCategoryChange}
                  error={!!errors.categoryId}
                  errorMessage={errors.categoryId}
                  disabled={
                    createArticleMutation.isPending || categoriesLoading
                  }
                  categories={memoizedCategories}
                />
              </div>

              <div className="bg-secondary-dark rounded-lg p-3 mb-4 border border-gray-600">
                <label
                  htmlFor="content"
                  className="block text-base font-medium text-gray-400 mb-2"
                >
                  Content
                </label>
                <NewsEditor
                  value={content}
                  onChange={setContent}
                  error={!!errors.content}
                  errorMessage={errors.content}
                  disabled={createArticleMutation.isPending}
                />
              </div>

              <div className="bg-secondary-dark rounded-lg p-3 mb-4 border border-gray-600">
                <label
                  htmlFor="image"
                  className="block text-base font-medium text-gray-400 mb-2"
                >
                  Featured Image
                </label>
                <div className="flex items-center gap-2">
                  <div className="flex-1">
                    <NewsImageUploader
                      imageUrl={imageUrl}
                      onChange={handleImageChange}
                      error={!!errors.image}
                      errorMessage={errors.image}
                      disabled={createArticleMutation.isPending}
                    />
                  </div>
                </div>
              </div>

              <div className="bg-secondary-dark rounded-lg p-3 mb-4 border border-gray-600">
                <div className="flex items-center">
                  <input
                    id="featured"
                    type="checkbox"
                    className="h-4 w-4 text-primary focus:ring-primary border-gray-600 rounded"
                    checked={featured}
                    onChange={(e) => setFeatured(e.target.checked)}
                    disabled={createArticleMutation.isPending}
                  />
                  <label
                    htmlFor="featured"
                    className="ml-2 text-base text-gray-400"
                  >
                    Featured Article
                  </label>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row sm:justify-end gap-2 pt-4 border-t border-gray-600">
                <Link
                  href="/news/dashboard/articles"
                  className="px-4 py-2 bg-secondary text-white rounded-md hover:bg-secondary-dark w-full sm:w-auto text-center"
                >
                  Cancel
                </Link>
                <button
                  type="button"
                  className="px-4 py-2 bg-secondary text-white rounded-md hover:bg-secondary-dark w-full sm:w-auto"
                  onClick={() => {
                    setStatus("draft");
                    handleSubmit(new Event("submit") as any);
                  }}
                  disabled={createArticleMutation.isPending}
                >
                  Save as Draft
                </button>
                <button
                  type="submit"
                  className={`px-4 py-2 ${
                    createArticleMutation.isPending
                      ? "bg-gray-600 cursor-not-allowed"
                      : "bg-primary hover:bg-primary-dark"
                  } text-white rounded-md flex items-center justify-center w-full sm:w-auto`}
                  onClick={() => setStatus("published")}
                  disabled={createArticleMutation.isPending}
                >
                  {createArticleMutation.isPending && (
                    <svg
                      className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                  )}
                  Publish
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </NewsDashboardLayout>
  );
}
