import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser, userHasRole } from "@/lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// GET /api/volunteer/shifts/[id] - Get a specific shift
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const { id } = params;

    // Check if user is authenticated and has volunteer coordinator role
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 },
      );
    }

    const hasCoordinatorRole = await userHasRole(req, "volunteerCoordinator");
    if (!hasCoordinatorRole) {
      return NextResponse.json(
        { error: "Unauthorized - Coordinator role required" },
        { status: 403 },
      );
    }

    // Get the shift
    const shift = await prisma.volunteerShift.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            assignments: true,
          },
        },
        assignments: {
          select: {
            status: true,
          },
        },
      },
    });

    if (!shift) {
      return NextResponse.json({ error: "Shift not found" }, { status: 404 });
    }

    // Calculate statistics
    const totalAssignments = shift._count.assignments;
    const completedAssignments = shift.assignments.filter(
      (assignment) => assignment.status === "completed",
    ).length;

    // Return shift with statistics
    const shiftWithStats = {
      id: shift.id,
      title: shift.title,
      description: shift.description,
      startTime: shift.startTime,
      endTime: shift.endTime,
      location: shift.location,
      maxVolunteers: shift.maxVolunteers,
      eventId: shift.eventId,
      categoryId: shift.categoryId,
      isAutomated: shift.isAutomated,
      createdAt: shift.createdAt,
      updatedAt: shift.updatedAt,
      stats: {
        totalAssignments,
        completedAssignments,
      },
    };

    return NextResponse.json(shiftWithStats);
  } catch (error) {
    console.error("Error fetching shift:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch shift",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    );
  }
}

// PUT /api/volunteer/shifts/[id] - Update a shift
export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const { id } = params;
    const { title, description, startTime, endTime, location, maxVolunteers } =
      await req.json();

    // Check if user is authenticated and has volunteer coordinator role
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 },
      );
    }

    const hasCoordinatorRole = await userHasRole(req, "volunteerCoordinator");
    if (!hasCoordinatorRole) {
      return NextResponse.json(
        { error: "Unauthorized - Coordinator role required" },
        { status: 403 },
      );
    }

    // Validate required fields
    if (!title) {
      return NextResponse.json({ error: "Title is required" }, { status: 400 });
    }

    if (!startTime || !endTime) {
      return NextResponse.json(
        { error: "Start time and end time are required" },
        { status: 400 },
      );
    }

    // Parse dates
    const parsedStartTime = new Date(startTime);
    const parsedEndTime = new Date(endTime);

    // Validate dates
    if (isNaN(parsedStartTime.getTime()) || isNaN(parsedEndTime.getTime())) {
      return NextResponse.json(
        { error: "Invalid date format" },
        { status: 400 },
      );
    }

    if (parsedStartTime >= parsedEndTime) {
      return NextResponse.json(
        { error: "End time must be after start time" },
        { status: 400 },
      );
    }

    // Check if shift exists
    const existingShift = await prisma.volunteerShift.findUnique({
      where: { id },
    });

    if (!existingShift) {
      return NextResponse.json({ error: "Shift not found" }, { status: 404 });
    }

    // Update the shift
    const updatedShift = await prisma.volunteerShift.update({
      where: { id },
      data: {
        title,
        description,
        startTime: parsedStartTime,
        endTime: parsedEndTime,
        location,
        maxVolunteers: maxVolunteers ? parseInt(maxVolunteers.toString()) : 1,
      },
    });

    return NextResponse.json(updatedShift);
  } catch (error) {
    console.error("Error updating shift:", error);
    return NextResponse.json(
      {
        error: "Failed to update shift",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    );
  }
}

// DELETE /api/volunteer/shifts/[id] - Delete a shift
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const { id } = params;

    // Check if user is authenticated and has volunteer coordinator role
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 },
      );
    }

    const hasCoordinatorRole = await userHasRole(req, "volunteerCoordinator");
    if (!hasCoordinatorRole) {
      return NextResponse.json(
        { error: "Unauthorized - Coordinator role required" },
        { status: 403 },
      );
    }

    // Check if shift exists
    const existingShift = await prisma.volunteerShift.findUnique({
      where: { id },
    });

    if (!existingShift) {
      return NextResponse.json({ error: "Shift not found" }, { status: 404 });
    }

    // Delete the shift
    await prisma.volunteerShift.delete({
      where: { id },
    });

    return NextResponse.json({ message: "Shift deleted successfully" });
  } catch (error) {
    console.error("Error deleting shift:", error);
    return NextResponse.json(
      {
        error: "Failed to delete shift",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    );
  }
}
