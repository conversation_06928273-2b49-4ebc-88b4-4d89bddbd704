"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useAuth } from "../../contexts/AuthContext";
import { SimpleAvatarUpload } from "../../components/user/SimpleAvatarUpload";
import { SyncDiscordAvatarButton } from "../../components/user/SyncDiscordAvatarButton";
import { PasswordCreationModal } from "../../components/auth/PasswordCreationModal";
import { EmailVerificationForm } from "../../components/auth/EmailVerificationForm";
import { GeneratedPasswordDisplay } from "../../components/auth/GeneratedPasswordDisplay";
import toast from "react-hot-toast";
import {
  updateProfile,
  updatePassword,
  updateDefaultView,
} from "../../services/userService";
import { useNotificationPreferences } from "../../hooks/useNotificationPreferences";
import fetchClient from "@/lib/fetchClient";

// Default empty user data to use before real data is loaded
const emptyUserData = {
  displayName: "",
  email: "",
  avatar: "/images/avatars/default.png",
  username: "",
  isEmailVerified: false,
  preferences: {
    defaultView: "dashboard" as const,
    notifications: {
      transfers: false,
      deposits: false,
      withdrawals: false,
      newsAndEvents: false,
      auctions: true,
      chat: true,
      admin: true,
    },
  },
  connectedAccounts: {
    discord: false,
    facebook: false,
  },
};

export default function SettingsPage() {
  const {
    user,
    isAuthenticated,
    isLoading,
    connectDiscord,
    disconnectDiscord,
    setPassword,
    isPasswordModalOpen,
    closePasswordModal,
    refreshUserData,
  } = useAuth();
  const router = useRouter();

  // Handle token from Discord connection and redirect to home if not authenticated
  useEffect(() => {
    const searchParams = new URLSearchParams(window.location.search);
    const token = searchParams.get("token");
    const connected = searchParams.get("connected");

    // If we have a token from Discord connection, store it and refresh user data
    if (token && connected === "true") {
      localStorage.setItem("auth_token", token);
      refreshUserData().then(() => {
        // Remove the token from the URL to prevent it from being shared
        const newUrl = window.location.pathname;
        window.history.replaceState({}, document.title, newUrl);
        toast.success("Discord account connected successfully!");
      });
    } else if (!isLoading && !isAuthenticated) {
      router.push("/");
    }
  }, [isAuthenticated, isLoading, router, refreshUserData]);

  // State for profile settings - initialize with empty values
  const [displayName, setDisplayName] = useState("");
  const [email, setEmail] = useState("");
  const [avatar, setAvatar] = useState("/images/avatars/default.png");
  const [profileLoading, setProfileLoading] = useState(false);
  const [profileError, setProfileError] = useState<string | null>(null);

  // State for preferences - initialize with default values
  const [defaultView, setDefaultView] = useState<
    "dashboard" | "transactions" | "transfer" | "pay-code" | "donate"
  >("dashboard");
  const {
    preferences: notificationPreferences,
    isLoading: notificationsLoading,
    updatePreferences,
  } = useNotificationPreferences();
  const [preferencesLoading, setPreferencesLoading] = useState(false);
  const [preferencesError, setPreferencesError] = useState<string | null>(null);

  // Update form values when user data changes
  useEffect(() => {
    if (user) {
      setDisplayName(user.displayName);
      setEmail(user.email);
      setAvatar(user.avatar);
      setDefaultView(user.preferences.defaultView);
    }
  }, [user]);

  // State for security settings
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [securityLoading, setSecurityLoading] = useState(false);
  const [securityError, setSecurityError] = useState<string | null>(null);

  // State for email verification flow
  const [isVerifyingEmail, setIsVerifyingEmail] = useState(false);
  const [generatedPassword, setGeneratedPassword] = useState("");
  const [showPasswordDisplay, setShowPasswordDisplay] = useState(false);

  // State to track if user has a password set
  const [hasPassword, setHasPassword] = useState(true);

  // Check if user has a password set
  useEffect(() => {
    const checkPasswordStatus = async () => {
      if (user && user.connectedAccounts.discord) {
        try {
          const data = await fetchClient.get<{ hasPassword: boolean }>(
            "/api/auth/has-password",
          );
          setHasPassword(data.hasPassword);
        } catch (error) {
          console.error("Failed to check password status:", error);
        }
      }
    };

    checkPasswordStatus();
  }, [user]);

  // Check if user needs to verify email (Discord-only user without password)
  const needsEmailVerification =
    user &&
    user.connectedAccounts.discord &&
    !hasPassword &&
    !user.isEmailVerified;

  const handleProfileSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setProfileLoading(true);
    setProfileError(null);

    try {
      // Only proceed if user is logged in
      if (!user) {
        throw new Error("You must be logged in to update your profile");
      }

      // Check if we're using mock data or empty values
      if (!isAuthenticated) {
        throw new Error("Authentication required to update profile");
      }

      await updateProfile({
        displayName,
        email,
        avatar,
      });

      // Refresh user data in the AuthContext to update the UI everywhere
      await refreshUserData();

      toast.success("Profile updated successfully!");
    } catch (error: any) {
      setProfileError(error.message || "Failed to update profile");
      console.error("Profile update error:", error);
      toast.error(error.message || "Failed to update profile");
    } finally {
      setProfileLoading(false);
    }
  };

  const handlePreferencesSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setPreferencesLoading(true);
    setPreferencesError(null);

    try {
      // Only proceed if user is logged in
      if (!user) {
        throw new Error("You must be logged in to update your preferences");
      }

      // Check if we're using mock data or empty values
      if (!isAuthenticated) {
        throw new Error("Authentication required to update preferences");
      }

      // Update default view through the user API
      await updateDefaultView(defaultView);

      // Refresh user data in the AuthContext to update the UI everywhere
      await refreshUserData();

      toast.success("Preferences updated successfully!");
    } catch (error: any) {
      setPreferencesError(error.message || "Failed to update preferences");
      console.error("Preferences update error:", error);
      toast.error(error.message || "Failed to update preferences");
    } finally {
      setPreferencesLoading(false);
    }
  };

  const handleSecuritySubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate passwords match
    if (newPassword !== confirmPassword) {
      setSecurityError("New passwords do not match!");
      toast.error("New passwords do not match!");
      return;
    }

    setSecurityLoading(true);
    setSecurityError(null);

    try {
      // Only proceed if user is logged in
      if (!user) {
        throw new Error("You must be logged in to change your password");
      }

      // Check if we're using mock data or empty values
      if (!isAuthenticated) {
        throw new Error("Authentication required to change password");
      }

      await updatePassword({
        currentPassword,
        newPassword,
      });

      toast.success("Password changed successfully!");
      setCurrentPassword("");
      setNewPassword("");
      setConfirmPassword("");
    } catch (error: any) {
      setSecurityError(error.message || "Failed to update password");
      console.error("Password update error:", error);
      toast.error(error.message || "Failed to update password");
    } finally {
      setSecurityLoading(false);
    }
  };

  // Handle starting the email verification process
  const handleStartEmailVerification = () => {
    setIsVerifyingEmail(true);
  };

  // Handle successful email verification
  const handleVerificationSuccess = (password: string) => {
    setGeneratedPassword(password);
    setShowPasswordDisplay(true);
    setIsVerifyingEmail(false);

    // Refresh user data to update UI
    refreshUserData();
  };

  // Handle completion of the password display step
  const handlePasswordDisplayDone = () => {
    setShowPasswordDisplay(false);
    setGeneratedPassword("");

    // Refresh user data to update UI
    refreshUserData();
  };

  const handleNotificationChange = (key: string) => {
    if (!notificationPreferences) return;

    // Map the settings page keys to notification service keys
    const keyMap: Record<string, string> = {
      transfers: "notifyTransfers",
      deposits: "notifyDeposits",
      withdrawals: "notifyWithdrawals",
      newsAndEvents: "notifyNewsEvents",
      auctions: "notifyAuctions",
      chat: "notifyChat",
      admin: "notifyAdmin",
    };

    // Toggle the notification preference using the mapped key
    const mappedKey = keyMap[key];
    if (mappedKey) {
      updatePreferences({
        ...notificationPreferences,
        [mappedKey]:
          !notificationPreferences[
            mappedKey as keyof typeof notificationPreferences
          ],
      });
    }
  };

  const handleAvatarSave = async (newAvatarUrl: string) => {
    setAvatar(newAvatarUrl);

    // Only update on server if user is logged in and authenticated
    if (user && isAuthenticated) {
      setProfileLoading(true);
      setProfileError(null);

      try {
        await updateProfile({
          displayName: user.displayName, // Use user data directly, not userData
          avatar: newAvatarUrl,
        });

        // Refresh user data in the AuthContext to update the UI everywhere
        await refreshUserData();

        // Show success toast notification
        toast.success("Avatar updated successfully!");

        console.log("Avatar updated successfully:", newAvatarUrl);
      } catch (error: any) {
        setProfileError(error.message || "Failed to update avatar");
        toast.error(error.message || "Failed to update avatar");
        console.error("Avatar update error:", error);
      } finally {
        setProfileLoading(false);
      }
    } else if (!isAuthenticated) {
      // If not authenticated, just show a message that this is preview only
      toast.error("You must be logged in to save avatar changes");
      console.log("Avatar preview only (not authenticated):", newAvatarUrl);
    } else {
      // Local update only
      console.log("Avatar updated (local only):", newAvatarUrl);
      toast.success("Avatar updated successfully (local only)");
    }
  };

  return (
    <div className="container mx-auto px-2 py-2 max-w-md">
      {/* Password Creation Modal */}
      <PasswordCreationModal
        isOpen={isPasswordModalOpen}
        onClose={closePasswordModal}
        onConfirm={setPassword}
        isLoading={isLoading}
      />

      <div className="mb-4">
        <h1 className="text-2xl font-bold mb-1">Account Settings</h1>
        <p className="text-text-secondary text-sm">
          Manage your account preferences and security settings
        </p>
      </div>

      <div className="bg-secondary-light rounded-lg shadow-md overflow-hidden mb-6">
        {/* Profile Section */}
        <div className="p-4 border-b border-gray-600">
          <h2 className="text-xl font-bold text-white mb-4">
            Profile Settings
          </h2>

          <form onSubmit={handleProfileSubmit}>
            <div className="space-y-3">
              <div className="flex items-center mb-4">
                <div className="flex-grow">
                  <div className="flex items-center mb-2">
                    {isLoading ? (
                      <div className="h-5 w-24 bg-gray-700 animate-pulse rounded"></div>
                    ) : (
                      <h3 className="text-base font-medium text-white">
                        {user ? `@${user.username}` : ""}
                      </h3>
                    )}
                    {user && user.isEmailVerified && (
                      <span className="ml-2 bg-success text-white text-xs px-2 py-0.5 rounded-full flex items-center">
                        <svg
                          className="w-3 h-3 mr-1"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M5 13l4 4L19 7"
                          ></path>
                        </svg>
                        Verified
                      </span>
                    )}
                  </div>
                  {isLoading ? (
                    <div className="h-20 w-20 bg-gray-700 animate-pulse rounded-full"></div>
                  ) : (
                    <SimpleAvatarUpload
                      currentAvatar={avatar}
                      onSave={handleAvatarSave}
                    />
                  )}
                </div>
              </div>

              <div>
                <label
                  htmlFor="displayName"
                  className="block text-white font-medium mb-1 text-sm"
                >
                  Display Name
                </label>
                <input
                  type="text"
                  id="displayName"
                  value={displayName}
                  onChange={(e) => setDisplayName(e.target.value)}
                  className="w-full px-3 py-2 bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary text-sm"
                  required
                />
              </div>

              <div>
                <label
                  htmlFor="email"
                  className="block text-white font-medium mb-1 text-sm"
                >
                  Email
                </label>
                <input
                  type="email"
                  id="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full px-3 py-2 bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary text-sm"
                  required
                />
              </div>

              <div className="pt-3">
                <h3 className="text-base font-medium text-white mb-2">
                  Connected Accounts
                </h3>
                <div className="space-y-2">
                  <div className="flex justify-between items-center p-2 border border-gray-600 rounded-md">
                    <div className="flex items-center">
                      <svg
                        className="w-5 h-5 text-primary mr-2"
                        fill="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path d="M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3847-.4058-.874-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.946 2.4189-2.1568 2.4189Z" />
                      </svg>
                      <span className="text-white text-sm">Discord</span>
                    </div>
                    <div className="flex items-center">
                      {isLoading ? (
                        <div className="h-6 w-16 bg-gray-700 animate-pulse rounded"></div>
                      ) : user && user.connectedAccounts.discord ? (
                        <div className="flex flex-col space-y-2">
                          <div className="flex items-center space-x-2">
                            <button
                              type="button"
                              className="px-2 py-1 bg-accent hover:bg-accent-dark text-white rounded-md text-xs transition-colors"
                              onClick={async () => {
                                try {
                                  await disconnectDiscord();
                                  // Note: We don't need to manually update the UI here as refreshUserData()
                                  // is called in the disconnectDiscord function, which will update the UI
                                } catch (error) {
                                  console.error(
                                    "Failed to disconnect Discord:",
                                    error,
                                  );
                                }
                              }}
                            >
                              Disconnect
                            </button>
                            <SyncDiscordAvatarButton
                              variant="secondary"
                              size="sm"
                              className="text-xs px-2 py-1"
                            />
                          </div>
                        </div>
                      ) : (
                        <button
                          type="button"
                          className="px-2 py-1 bg-success hover:bg-success-dark text-white rounded-md text-xs transition-colors"
                          onClick={async () => {
                            try {
                              await connectDiscord();
                            } catch (error) {
                              console.error(
                                "Failed to connect Discord:",
                                error,
                              );
                            }
                          }}
                        >
                          Connect
                        </button>
                      )}
                    </div>
                  </div>

                  {/* Facebook connect element hidden as requested */}
                  <div className="hidden justify-between items-center p-2 border border-gray-600 rounded-md">
                    <div className="flex items-center">
                      <svg
                        className="w-5 h-5 text-primary mr-2"
                        fill="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
                      </svg>
                      <span className="text-white text-sm">Facebook</span>
                    </div>
                    <div className="flex items-center">
                      {isLoading ? (
                        <div className="h-6 w-16 bg-gray-700 animate-pulse rounded"></div>
                      ) : user && user.connectedAccounts.facebook ? (
                        <button
                          type="button"
                          className="px-2 py-1 bg-accent hover:bg-accent-dark text-white rounded-md text-xs transition-colors"
                          disabled={!isAuthenticated}
                        >
                          Disconnect
                        </button>
                      ) : (
                        <button
                          type="button"
                          className="px-2 py-1 bg-primary hover:bg-primary-dark text-white rounded-md text-xs transition-colors"
                          disabled={!isAuthenticated}
                        >
                          Connect
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {profileError && (
                <div className="mt-3 text-accent text-sm">{profileError}</div>
              )}

              <div className="flex justify-end space-x-2 pt-3">
                <button
                  type="button"
                  onClick={() => {
                    if (user) {
                      setDisplayName(user.displayName);
                      setEmail(user.email);
                    } else {
                      setDisplayName("");
                      setEmail("");
                    }
                    setProfileError(null);
                  }}
                  className="px-3 py-1 border border-gray-600 rounded-md text-white hover:bg-secondary text-sm"
                  disabled={profileLoading}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-3 py-1 bg-primary text-white rounded-md hover:bg-primary-dark text-sm flex items-center"
                  disabled={profileLoading}
                >
                  {profileLoading ? (
                    <>
                      <svg
                        className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      Saving...
                    </>
                  ) : (
                    "Save Changes"
                  )}
                </button>
              </div>
            </div>
          </form>
        </div>

        {/* Preferences Section */}
        <div className="p-4 border-b border-gray-600">
          <h2 className="text-xl font-bold text-white mb-4">Preferences</h2>

          <form onSubmit={handlePreferencesSubmit}>
            <div className="space-y-3">
              <div>
                <label
                  htmlFor="defaultView"
                  className="block text-white font-medium mb-1 text-sm"
                >
                  Default Dashboard View
                </label>
                <select
                  id="defaultView"
                  value={defaultView}
                  onChange={(e) =>
                    setDefaultView(
                      e.target.value as
                        | "dashboard"
                        | "transactions"
                        | "transfer"
                        | "pay-code"
                        | "donate",
                    )
                  }
                  className="w-full px-3 py-2 bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary text-sm"
                >
                  <option value="dashboard" className="bg-secondary text-white">
                    Dashboard
                  </option>
                  <option
                    value="transactions"
                    className="bg-secondary text-white"
                  >
                    Transactions
                  </option>
                  <option value="transfer" className="bg-secondary text-white">
                    Transfer
                  </option>
                  <option value="pay-code" className="bg-secondary text-white">
                    Pay-code
                  </option>
                  <option value="donate" className="bg-secondary text-white">
                    Donate
                  </option>
                </select>
              </div>

              <div className="pt-3">
                <h3 className="text-base font-medium text-white mb-2">
                  Notifications
                </h3>
                <div className="space-y-2">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="notifyTransfers"
                      checked={
                        notificationPreferences?.notifyTransfers || false
                      }
                      onChange={() => handleNotificationChange("transfers")}
                      className="h-4 w-4 text-primary focus:ring-primary border-gray-600 rounded"
                    />
                    <label
                      htmlFor="notifyTransfers"
                      className="ml-2 block text-white text-sm"
                    >
                      Transfers
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="notifyDeposits"
                      checked={notificationPreferences?.notifyDeposits || false}
                      onChange={() => handleNotificationChange("deposits")}
                      className="h-4 w-4 text-primary focus:ring-primary border-gray-600 rounded"
                    />
                    <label
                      htmlFor="notifyDeposits"
                      className="ml-2 block text-white text-sm"
                    >
                      Deposits
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="notifyWithdrawals"
                      checked={
                        notificationPreferences?.notifyWithdrawals || false
                      }
                      onChange={() => handleNotificationChange("withdrawals")}
                      className="h-4 w-4 text-primary focus:ring-primary border-gray-600 rounded"
                    />
                    <label
                      htmlFor="notifyWithdrawals"
                      className="ml-2 block text-white text-sm"
                    >
                      Withdrawals
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="notifyNewsEvents"
                      checked={
                        notificationPreferences?.notifyNewsEvents || false
                      }
                      onChange={() => handleNotificationChange("newsAndEvents")}
                      className="h-4 w-4 text-primary focus:ring-primary border-gray-600 rounded"
                    />
                    <label
                      htmlFor="notifyNewsEvents"
                      className="ml-2 block text-white text-sm"
                    >
                      News and Events
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="notifyAuctions"
                      checked={notificationPreferences?.notifyAuctions || false}
                      onChange={() => handleNotificationChange("auctions")}
                      className="h-4 w-4 text-primary focus:ring-primary border-gray-600 rounded"
                    />
                    <label
                      htmlFor="notifyAuctions"
                      className="ml-2 block text-white text-sm"
                    >
                      Auctions
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="notifyChat"
                      checked={notificationPreferences?.notifyChat || false}
                      onChange={() => handleNotificationChange("chat")}
                      className="h-4 w-4 text-primary focus:ring-primary border-gray-600 rounded"
                    />
                    <label
                      htmlFor="notifyChat"
                      className="ml-2 block text-white text-sm"
                    >
                      Chat Messages
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="notifyAdmin"
                      checked={notificationPreferences?.notifyAdmin || false}
                      onChange={() => handleNotificationChange("admin")}
                      className="h-4 w-4 text-primary focus:ring-primary border-gray-600 rounded"
                    />
                    <label
                      htmlFor="notifyAdmin"
                      className="ml-2 block text-white text-sm"
                    >
                      Admin Notifications
                    </label>
                  </div>
                </div>
              </div>

              {preferencesError && (
                <div className="mt-3 text-accent text-sm">
                  {preferencesError}
                </div>
              )}

              <div className="flex justify-end space-x-2 pt-3">
                <button
                  type="button"
                  onClick={() => {
                    if (user) {
                      setDefaultView(user.preferences.defaultView);
                    } else {
                      setDefaultView("dashboard");
                    }
                    setPreferencesError(null);
                  }}
                  className="px-3 py-1 border border-gray-600 rounded-md text-white hover:bg-secondary text-sm"
                  disabled={preferencesLoading}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-3 py-1 bg-primary text-white rounded-md hover:bg-primary-dark text-sm flex items-center"
                  disabled={preferencesLoading}
                >
                  {preferencesLoading ? (
                    <>
                      <svg
                        className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      Saving...
                    </>
                  ) : (
                    "Save Changes"
                  )}
                </button>
              </div>
            </div>
          </form>
        </div>

        {/* Security Section */}
        <div className="p-4">
          <h2 className="text-xl font-bold text-white mb-4">Security</h2>

          {needsEmailVerification &&
          !isVerifyingEmail &&
          !showPasswordDisplay ? (
            <div className="bg-secondary p-4 rounded-md mb-4 border border-gray-600">
              <div className="mb-3">
                <h3 className="text-lg font-medium text-white mb-2">
                  Account Security Notice
                </h3>
                <p className="text-sm text-gray-300">
                  Your account is currently linked to Discord but doesn't have a
                  password set up. To enhance your account security, we
                  recommend verifying your email and setting a password.
                </p>
              </div>
              <button
                type="button"
                className="px-3 py-2 bg-primary text-white rounded-md hover:bg-primary-dark text-sm"
                onClick={handleStartEmailVerification}
              >
                Verify Email & Set Password
              </button>
            </div>
          ) : needsEmailVerification && isVerifyingEmail ? (
            <div className="bg-secondary p-4 rounded-md mb-4 border border-gray-600">
              <EmailVerificationForm
                onVerificationSuccess={handleVerificationSuccess}
              />
            </div>
          ) : needsEmailVerification && showPasswordDisplay ? (
            <div className="bg-secondary p-4 rounded-md mb-4 border border-gray-600">
              <GeneratedPasswordDisplay
                password={generatedPassword}
                onDone={handlePasswordDisplayDone}
              />
            </div>
          ) : (
            <form onSubmit={handleSecuritySubmit}>
              <div className="space-y-3">
                <div>
                  <label
                    htmlFor="currentPassword"
                    className="block text-white font-medium mb-1 text-sm"
                  >
                    Current Password
                  </label>
                  <input
                    type="password"
                    id="currentPassword"
                    value={currentPassword}
                    onChange={(e) => setCurrentPassword(e.target.value)}
                    className="w-full px-3 py-2 bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary text-sm"
                    required
                  />
                </div>

                <div>
                  <label
                    htmlFor="newPassword"
                    className="block text-white font-medium mb-1 text-sm"
                  >
                    New Password
                  </label>
                  <input
                    type="password"
                    id="newPassword"
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.target.value)}
                    className="w-full px-3 py-2 bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary text-sm"
                    required
                  />
                </div>

                <div>
                  <label
                    htmlFor="confirmPassword"
                    className="block text-white font-medium mb-1 text-sm"
                  >
                    Confirm New Password
                  </label>
                  <input
                    type="password"
                    id="confirmPassword"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    className="w-full px-3 py-2 bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary text-sm"
                    required
                  />
                </div>

                {securityError && (
                  <div className="mt-3 text-accent text-sm">
                    {securityError}
                  </div>
                )}

                <div className="flex justify-end space-x-2 pt-3">
                  <button
                    type="button"
                    onClick={() => {
                      setCurrentPassword("");
                      setNewPassword("");
                      setConfirmPassword("");
                      setSecurityError(null);
                    }}
                    className="px-3 py-1 border border-gray-600 rounded-md text-white hover:bg-secondary text-sm"
                    disabled={securityLoading}
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-3 py-1 bg-primary text-white rounded-md hover:bg-primary-dark text-sm flex items-center"
                    disabled={securityLoading}
                  >
                    {securityLoading ? (
                      <>
                        <svg
                          className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                        >
                          <circle
                            className="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            strokeWidth="4"
                          ></circle>
                          <path
                            className="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                          ></path>
                        </svg>
                        Updating...
                      </>
                    ) : (
                      "Change Password"
                    )}
                  </button>
                </div>
              </div>
            </form>
          )}

          <div className="mt-6 pt-4 border-t border-gray-600">
            <h3 className="text-base font-medium text-white mb-3">
              Account Actions
            </h3>
            <button
              type="button"
              className="text-accent hover:text-accent-light font-medium text-sm"
              onClick={() => {
                if (
                  confirm(
                    "Are you sure you want to deactivate your account? This action cannot be undone.",
                  )
                ) {
                  alert(
                    "This is a placeholder - no actual deactivation occurred.",
                  );
                }
              }}
            >
              Deactivate Account
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
