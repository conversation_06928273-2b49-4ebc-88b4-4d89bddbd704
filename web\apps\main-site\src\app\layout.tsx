import "./globals.css";
import { Inter, Poppins } from "next/font/google";
import MainLayout from "@/components/layout/MainLayout";
import { AuthProvider } from "@/contexts/AuthContext";
import { ColorThemeProvider } from "@/contexts/ColorThemeContext";
import { UserStateProvider } from "@/contexts/UserStateContext";
import { QueryProvider } from "@/providers/QueryProvider";
import { NotificationProvider } from "@/providers/NotificationProvider";

// Import development configuration to reduce console noise
import "@/lib/dev-config";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
});

const poppins = Poppins({
  weight: ["400", "500", "600", "700"],
  subsets: ["latin"],
  variable: "--font-poppins",
  display: "swap",
});

export const metadata = {
  title: "Bank of Styx",
  description:
    "Bank of Styx hosted by La Maga Demonio's - Do as you will, yet harm none",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className={`${inter.variable} ${poppins.variable}`}>
      <body>
        <QueryProvider>
          <ColorThemeProvider>
            <AuthProvider>
              <UserStateProvider>
                <NotificationProvider>
                  <MainLayout>{children}</MainLayout>
                </NotificationProvider>
              </UserStateProvider>
            </AuthProvider>
          </ColorThemeProvider>
        </QueryProvider>
      </body>
    </html>
  );
}
