"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { CaptainDashboardLayout } from "@/components/captain";
import { useCaptainShip } from "@/hooks/captain/useCaptainShip";
import { <PERSON><PERSON>, <PERSON> } from "@bank-of-styx/ui";
import MemberInviteSearch from "@/components/ships/MemberInviteSearch";
import { getCaptainRoles, inviteUser, cancelInvitation, acceptJoinRequest, declineJoinRequest, type ShipRole } from "@/services/captainService";

export default function CaptainInvitePage() {
  const { user, isLoading: authLoading, openAuthModal } = useAuth();
  const router = useRouter();
  
  const { ship, statistics, pendingInvitations, pendingRequests, isLoading: shipLoading, refetch } = useCaptainShip();
  
  // Local state for invite page
  const [roles, setRoles] = useState<ShipRole[]>([]);
  const [rolesLoading, setRolesLoading] = useState(false);

  // Authentication check
  React.useEffect(() => {
    if (!authLoading && !user) {
      openAuthModal();
      router.replace("/");
      return;
    }
  }, [user, authLoading, router, openAuthModal]);

  // Fetch roles
  useEffect(() => {
    if (ship) {
      fetchRoles();
    }
  }, [ship]);

  const fetchRoles = async () => {
    try {
      setRolesLoading(true);
      const data = await getCaptainRoles();
      setRoles(data);
    } catch (error) {
      console.error("Error fetching roles:", error);
    } finally {
      setRolesLoading(false);
    }
  };

  const handleInviteUser = async (userId: string, message?: string, roleId?: string) => {
    try {
      // Find the role name from the roleId if provided
      const selectedRole = roles.find(role => role.id === roleId);
      const roleName = selectedRole?.name;
      
      await inviteUser(userId, roleId, roleName, message);
      await refetch(); // Refresh dashboard data to show new invitation
    } catch (error) {
      console.error("Error inviting user:", error);
      throw error;
    }
  };

  const handleCancelInvitation = async (invitationId: string) => {
    try {
      await cancelInvitation(invitationId);
      await refetch(); // Refresh dashboard data to remove cancelled invitation
    } catch (error) {
      console.error("Error cancelling invitation:", error);
      alert("Failed to cancel invitation. Please try again.");
    }
  };

  const handleAcceptRequest = async (requestId: string, roleId?: string, roleName?: string) => {
    try {
      await acceptJoinRequest(requestId, roleId, roleName);
      await refetch(); // Refresh dashboard data
    } catch (error) {
      console.error("Error accepting join request:", error);
      alert("Failed to accept join request. Please try again.");
    }
  };

  const handleDeclineRequest = async (requestId: string) => {
    try {
      await declineJoinRequest(requestId);
      await refetch(); // Refresh dashboard data
    } catch (error) {
      console.error("Error declining join request:", error);
      alert("Failed to decline join request. Please try again.");
    }
  };

  // Loading state
  if (authLoading || shipLoading) {
    return (
      <div className="min-h-screen bg-secondary-dark flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-400">Loading...</p>
        </div>
      </div>
    );
  }

  if (!ship) {
    return (
      <CaptainDashboardLayout>
        <Card className="p-6 text-center">
          <p className="text-gray-400">Ship not found</p>
        </Card>
      </CaptainDashboardLayout>
    );
  }

  return (
    <CaptainDashboardLayout ship={ship}>
      <div className="space-y-4 sm:space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-xl sm:text-2xl font-bold text-white mb-1">
              Invite Members
            </h1>
            <p className="text-sm text-gray-400">
              Search and invite users to join your ship
            </p>
          </div>
        </div>

        {/* Ship Stats */}
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          <Card className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-white">
                {statistics?.totalMembers || 0}
              </p>
              <p className="text-sm text-gray-400">Current Members</p>
            </div>
          </Card>
          
          <Card className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-white">
                {statistics?.pendingRequests || 0}
              </p>
              <p className="text-sm text-gray-400">Pending Requests</p>
            </div>
          </Card>
          
          <Card className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-white">
                {statistics?.recentJoins || 0}
              </p>
              <p className="text-sm text-gray-400">Recent Joins</p>
            </div>
          </Card>
        </div>

        {/* Join Requests */}
        <Card className="p-4 sm:p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-white">Join Requests</h2>
            {statistics && (
              <span className="bg-blue-500/20 text-blue-400 px-2 py-1 rounded text-sm">
                {statistics.pendingRequests || 0}
              </span>
            )}
          </div>
          
          {shipLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-gray-400">Loading requests...</p>
            </div>
          ) : pendingRequests && pendingRequests.length > 0 ? (
            <div className="space-y-3">
              <p className="text-sm text-gray-400 mb-3">
                Users requesting to join your ship
              </p>
              <div className="space-y-3">
                {pendingRequests.map((request) => (
                  <div key={request.id} className="flex items-center justify-between p-3 bg-secondary-dark/50 rounded-lg border border-gray-600">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 rounded-full bg-blue-500/20 flex items-center justify-center flex-shrink-0">
                        {request.user.avatar ? (
                          <img 
                            src={request.user.avatar} 
                            alt={request.user.displayName}
                            className="w-full h-full rounded-full object-cover"
                          />
                        ) : (
                          <span className="text-sm font-medium text-white">
                            {request.user.displayName.charAt(0)}
                          </span>
                        )}
                      </div>
                      <div className="min-w-0 flex-1">
                        <p className="text-sm font-medium text-white">
                          {request.user.displayName}
                        </p>
                        <p className="text-xs text-gray-400">
                          @{request.user.username}
                        </p>
                        {request.message && (
                          <p className="text-xs text-gray-300 mt-1 italic">
                            "{request.message}"
                          </p>
                        )}
                        <p className="text-xs text-gray-500 mt-1">
                          Requested {new Date(request.createdAt).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleAcceptRequest(request.id, undefined, 'Member')}
                        className="text-green-400 border-green-400 hover:bg-green-400 hover:text-white"
                      >
                        Accept
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeclineRequest(request.id)}
                        className="text-red-400 border-red-400 hover:bg-red-400 hover:text-white"
                      >
                        Decline
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="w-16 h-16 bg-secondary-dark rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">📋</span>
              </div>
              <p className="text-white font-medium mb-1">No Join Requests</p>
              <p className="text-gray-400 text-sm">
                No one has requested to join your ship yet
              </p>
            </div>
          )}
        </Card>

        {/* Pending Invitations */}
        <Card className="p-4 sm:p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-white">Pending Invitations</h2>
            {statistics && (
              <span className="bg-primary/20 text-primary px-2 py-1 rounded text-sm">
                {statistics.pendingInvitations || 0}
              </span>
            )}
          </div>
          
          {shipLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-gray-400">Loading invitations...</p>
            </div>
          ) : pendingInvitations && pendingInvitations.length > 0 ? (
            <div className="space-y-3">
              <p className="text-sm text-gray-400 mb-3">
                Invitations you've sent that are waiting for a response
              </p>
              <div className="space-y-3">
                {pendingInvitations.map((invitation) => (
                  <div key={invitation.id} className="flex items-center justify-between p-3 bg-secondary-dark/50 rounded-lg border border-gray-600">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 rounded-full bg-primary/20 flex items-center justify-center flex-shrink-0">
                        {invitation.user.avatar ? (
                          <img 
                            src={invitation.user.avatar} 
                            alt={invitation.user.displayName}
                            className="w-full h-full rounded-full object-cover"
                          />
                        ) : (
                          <span className="text-sm font-medium text-white">
                            {invitation.user.displayName.charAt(0)}
                          </span>
                        )}
                      </div>
                      <div className="min-w-0 flex-1">
                        <p className="text-sm font-medium text-white">
                          {invitation.user.displayName}
                        </p>
                        <p className="text-xs text-gray-400">
                          @{invitation.user.username}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          Invited {new Date(invitation.createdAt).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleCancelInvitation(invitation.id)}
                      className="text-red-400 border-red-400 hover:bg-red-400 hover:text-white"
                    >
                      Cancel
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="w-16 h-16 bg-secondary-dark rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">📭</span>
              </div>
              <p className="text-white font-medium mb-1">No Pending Invitations</p>
              <p className="text-gray-400 text-sm">
                All your invitations have been responded to
              </p>
            </div>
          )}
        </Card>

        {/* Invite Search */}
        <Card className="p-4 sm:p-6">
          <h2 className="text-lg font-semibold text-white mb-4">Search & Invite Users</h2>
          
          {rolesLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-gray-400">Loading roles...</p>
            </div>
          ) : (
            <MemberInviteSearch
              roles={roles}
              onInviteUser={handleInviteUser}
              loading={false}
            />
          )}
        </Card>

        {/* Invitation Tips */}
        <Card className="p-4 sm:p-6 bg-blue-900/10 border-blue-400/30">
          <h2 className="text-lg font-semibold text-white mb-3">💡 Invitation Tips</h2>
          <div className="space-y-3 text-sm">
            <div className="flex items-start gap-3">
              <span className="text-blue-400 mt-0.5">•</span>
              <div>
                <p className="text-white font-medium">Search by username or display name</p>
                <p className="text-gray-400">Use the search box to find specific users</p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <span className="text-blue-400 mt-0.5">•</span>
              <div>
                <p className="text-white font-medium">Assign roles during invitation</p>
                <p className="text-gray-400">Choose Officer, Member, or custom roles when inviting</p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <span className="text-blue-400 mt-0.5">•</span>
              <div>
                <p className="text-white font-medium">Users can also request to join</p>
                <p className="text-gray-400">Check pending requests on the Members page</p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <span className="text-blue-400 mt-0.5">•</span>
              <div>
                <p className="text-white font-medium">Manage roles after joining</p>
                <p className="text-gray-400">You can change member roles anytime on the Members page</p>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </CaptainDashboardLayout>
  );
}