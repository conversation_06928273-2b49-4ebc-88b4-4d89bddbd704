/*
  Warnings:

  - You are about to drop the column `lastNotificationSync` on the `user_state` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE `user_state` DROP COLUMN `lastNotificationSync`;

-- CreateTable
CREATE TABLE `uploaded_image` (
    `id` VARCHAR(191) NOT NULL,
    `filename` VARCHAR(191) NOT NULL,
    `path` VARCHAR(191) NOT NULL,
    `url` VARCHAR(191) NOT NULL,
    `mimeType` VARCHAR(191) NOT NULL,
    `size` INTEGER NOT NULL,
    `entityId` VARCHAR(191) NULL,
    `entityType` VARCHAR(191) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    INDEX `uploaded_image_entityType_entityId_idx`(`entityType`, `entityId`),
    UNIQUE INDEX `uploaded_image_entityType_entityId_filename_key`(`entityType`, `entityId`, `filename`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
