import { UploadConfigMap } from "@/types/upload";

export const uploadConfig: UploadConfigMap = {
  avatar: {
    maxSize: 1024 * 1024 * 2, // 2MB
    allowedTypes: ["image/jpeg", "image/png"],
    processImage: true,
    generateThumbnail: true,
    quality: 90,
  },
  news: {
    maxSize: 1024 * 1024 * 5, // 5MB
    allowedTypes: ["image/jpeg", "image/png", "image/gif", "image/webp"],
    processImage: true,
    generateThumbnail: false,
    quality: 85,
  },
  event: {
    maxSize: 1024 * 1024 * 5, // 5MB
    allowedTypes: ["image/jpeg", "image/png", "image/gif", "image/webp"],
    processImage: true,
    generateThumbnail: false,
    quality: 85,
  },
  product: {
    maxSize: 1024 * 1024 * 5, // 5MB
    allowedTypes: ["image/jpeg", "image/png"],
    processImage: true,
    generateThumbnail: true,
    quality: 90,
  },
  deposit: {
    maxSize: 1024 * 1024 * 10, // 10MB
    allowedTypes: ["image/jpeg", "image/png", "application/pdf"],
    processImage: true,
    generateThumbnail: false,
    quality: 90,
  },
  "ship-logo": {
    maxSize: 1024 * 1024 * 5, // 5MB
    allowedTypes: ["image/jpeg", "image/png", "image/gif"],
    processImage: true,
    generateThumbnail: true,
    quality: 90,
  },
};
