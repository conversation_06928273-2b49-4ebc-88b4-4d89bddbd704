"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { VolunteerLeadDashboardLayout } from "@/components/volunteer/lead/VolunteerLeadDashboardLayout";
import { LeadDashboardMain } from "@/components/volunteer/lead/LeadDashboardMain";

interface VolunteerCategory {
  id: string;
  name: string;
  description?: string;
  event: {
    id: string;
    name: string;
    status: string;
  };
}

export default function CategoryLeadViewPage() {
  const { user, isLoading } = useAuth();
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [categories, setCategories] = useState<VolunteerCategory[]>([]);
  const [selectedCategoryId, setSelectedCategoryId] = useState<string>("");
  const [loadingCategories, setLoadingCategories] = useState(true);
  const router = useRouter();

  // Check if user is authorized to access this page (volunteer coordinator only)
  useEffect(() => {
    if (!isLoading) {
      if (!user || !user.roles?.volunteerCoordinator) {
        router.push("/");
      } else {
        setIsAuthorized(true);
      }
    }
  }, [user, isLoading, router]);

  // Fetch available categories
  useEffect(() => {
    if (isAuthorized) {
      fetchCategories();
    }
  }, [isAuthorized]);

  const fetchCategories = async () => {
    try {
      const response = await fetch("/api/volunteer/categories");
      if (response.ok) {
        const data = await response.json();
        setCategories(data.categories || []);
        // Auto-select first category if available
        if (data.categories && data.categories.length > 0) {
          setSelectedCategoryId(data.categories[0].id);
        }
      } else {
        console.error("Failed to fetch categories");
      }
    } catch (error) {
      console.error("Error fetching categories:", error);
    } finally {
      setLoadingCategories(false);
    }
  };

  // Show loading state while checking authorization
  if (isLoading || !isAuthorized || loadingCategories) {
    return (
      <div className="min-h-screen bg-secondary-dark flex items-center justify-center">
        <div className="bg-secondary-light p-6 rounded-lg shadow-md border border-gray-600">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary mx-auto"></div>
          <p className="text-white text-lg mt-4">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-secondary-dark">
      {/* Category Selector Header */}
      <div className="bg-secondary-light border-b border-gray-600 p-4">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-white">Category Lead View</h1>
              <p className="text-gray-300 mt-1">
                Coordinator view of category lead dashboard
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <label htmlFor="category-select" className="text-white font-medium">
                Select Category:
              </label>
              <select
                id="category-select"
                value={selectedCategoryId}
                onChange={(e) => setSelectedCategoryId(e.target.value)}
                className="w-64 p-2 bg-secondary-dark border border-gray-600 text-white rounded-md focus:outline-none focus:border-primary"
              >
                <option value="">Choose a category...</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.event.name} - {category.name}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Lead Dashboard Content */}
      {selectedCategoryId ? (
        <VolunteerLeadDashboardLayout>
          <LeadDashboardMain categoryId={selectedCategoryId} isCoordinatorView={true} />
        </VolunteerLeadDashboardLayout>
      ) : (
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <p className="text-gray-400 text-lg">
              {categories.length === 0 
                ? "No categories available" 
                : "Select a category to view its lead dashboard"
              }
            </p>
          </div>
        </div>
      )}
    </div>
  );
}