import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser, userHasRole } from "@/lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// GET /api/volunteer/dashboard/stats - Get volunteer dashboard statistics (public stats endpoint)
export async function GET(req: NextRequest) {
  try {
    // Stats are now publicly accessible as they don't contain sensitive data

    // Get current date for upcoming shifts calculation
    const now = new Date();

    // Get total events count
    const totalEvents = await prisma.event.count({
      where: {
        status: "published",
        endDate: {
          gte: now,
        },
      },
    });

    // Get active categories count
    const activeCategories = await prisma.volunteerCategory.count({
      where: {
        event: {
          status: "published",
          endDate: {
            gte: now,
          },
        },
      },
    });

    // Get upcoming shifts count
    const upcomingShifts = await prisma.volunteerShift.count({
      where: {
        startTime: {
          gte: now,
        },
        event: {
          status: "published",
        },
      },
    });

    // Get registered volunteers count (users with volunteer assignments)
    const registeredVolunteers = await prisma.user.count({
      where: {
        volunteerShifts: {
          some: {},
        },
      },
    });

    return NextResponse.json({
      totalEvents,
      activeCategories,
      upcomingShifts,
      registeredVolunteers,
    });
  } catch (error) {
    console.error("Error fetching dashboard stats:", error);
    return NextResponse.json(
      { error: "Failed to fetch dashboard statistics" },
      { status: 500 },
    );
  }
}
