import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth";

export const dynamic = 'force-dynamic';

interface Params {
  params: {
    id: string;
  };
}

export async function GET(request: Request, { params }: Params) {
  try {
    const { id } = params;
    const currentUser = await getCurrentUser(request);

    const ship = await prisma.ship.findUnique({
      where: { 
        id,
        status: 'active',
      },
      include: {
        captain: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
        members: {
          where: { status: 'active' },
          include: {
            user: {
              select: {
                id: true,
                username: true,
                displayName: true,
                avatar: true,
              },
            },
          },
          orderBy: { joinedAt: 'asc' },
        },
        joinRequests: currentUser ? {
          where: {
            userId: currentUser.id,
            status: 'pending',
          },
        } : false,
      },
    });

    if (!ship) {
      return NextResponse.json({ error: "Ship not found" }, { status: 404 });
    }

    // Check if current user is already a member of any ship
    let userShipStatus = null;
    if (currentUser) {
      const existingMembership = await prisma.shipMember.findFirst({
        where: {
          userId: currentUser.id,
          status: 'active',
        },
        include: {
          ship: {
            select: { id: true, name: true },
          },
        },
      });

      if (existingMembership) {
        userShipStatus = {
          isMember: existingMembership.ship.id === ship.id,
          currentShip: existingMembership.ship,
        };
      }
    }

    // Transform ship data
    const transformedShip = {
      id: ship.id,
      name: ship.name,
      description: ship.description,
      slogan: ship.slogan,
      logo: ship.logo,
      tags: ship.tags as string[] || [],
      captain: ship.captain,
      members: ship.members.map(member => ({
        id: member.id,
        role: member.role,
        joinedAt: member.joinedAt.toISOString(),
        user: member.user,
      })),
      memberCount: ship.members.length,
      createdAt: ship.createdAt.toISOString(),
      canJoin: currentUser && !userShipStatus?.isMember && !userShipStatus?.currentShip,
      hasPendingRequest: ship.joinRequests && ship.joinRequests.length > 0,
      userShipStatus,
    };

    return NextResponse.json(transformedShip);
  } catch (error) {
    console.error("Error fetching ship:", error);
    return NextResponse.json(
      { error: "Failed to fetch ship" },
      { status: 500 }
    );
  }
}