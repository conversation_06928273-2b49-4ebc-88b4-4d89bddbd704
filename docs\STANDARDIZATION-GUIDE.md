# Documentation Standardization Guide

## Overview
This guide ensures all documentation follows the exact template structure and formatting standards.

## Required Section Order
All feature documentation must follow this exact order:

1. **Title**: `# [Feature Name] System`
2. **Overview**: `## Overview`
3. **Directory Structure**: `## Directory Structure`
4. **Key Files & Components**: `## Key Files & Components`
5. **Common Tasks**: `## Common Tasks`
6. **API Integration**: `## API Integration`
7. **Database Schema**: `## Database Schema`
8. **Related Features**: `## Related Features`
9. **User Roles & Permissions**: `## User Roles & Permissions`
10. **Recent Changes**: `## Recent Changes`
11. **Troubleshooting**: `## Troubleshooting`
12. **File Locations**: `---` followed by file locations

## Formatting Standards

### Headers
- **H1**: `# [Feature Name] System` (title only)
- **H2**: `## Section Name` (main sections)
- **H3**: `### Subsection Name` (subsections)
- **H4**: `#### Sub-subsection` (if needed)

### API Endpoints
Format: `- **\`METHOD /api/endpoint\`** - Description`
Example: `- **\`GET /api/auth/me\`** - Current user session validation`

### Code Blocks
Use triple backticks with language specification:
```typescript
// TypeScript example
interface Example {
  id: string;
  name: string;
}
```

### File Locations Section
Must end with this exact format:
```markdown
---

**File Locations:**
- Pages: `/src/app/feature-name/`
- Components: `/src/components/feature-name/`
- API: `/src/app/api/feature-name/`
- Types: `/src/types/feature.ts`
```

### Directory Structure
Use consistent tree format:
```
feature-name/
├── components/           # Description
│   ├── ComponentName/    # Individual component folders
│   └── shared/          # Shared components
├── api/                 # API endpoints
└── utils/               # Utility functions
```

### Common Tasks
Format as:
```markdown
### Task 1: How to [Action Name]
1. Step-by-step instructions
2. Code examples if needed
3. Expected outcomes
```

### Related Features
Format as:
```markdown
- **[Feature Name](./feature-name.md)** - How it integrates
```

### User Roles & Permissions
Format as:
```markdown
- **Role Name**: Description of permissions
```

### Troubleshooting
Must include these subsections:
```markdown
### Common Issues
1. **Issue Description**: Solution steps

### Debug Information
- Log locations
- Environment variables
- Debugging commands
```

## Quality Checklist
For each documentation file, verify:

- [ ] Title follows `# [Feature Name] System` format
- [ ] All sections present in correct order
- [ ] Headers use correct levels (H2 for main sections)
- [ ] API endpoints formatted consistently
- [ ] Code blocks have language specification
- [ ] File locations section present and formatted correctly
- [ ] Directory structure uses tree format
- [ ] Common tasks numbered and descriptive
- [ ] Related features have proper links
- [ ] Troubleshooting has required subsections

## Non-Feature Documentation
For technical documentation (architecture, development, etc.):
- Follow similar structure but adapt sections as appropriate
- Maintain consistent formatting standards
- Include relevant troubleshooting and reference sections

## Review Process
1. Check against template structure
2. Verify formatting consistency
3. Ensure all required sections present
4. Test any code examples provided
5. Validate internal links work correctly
