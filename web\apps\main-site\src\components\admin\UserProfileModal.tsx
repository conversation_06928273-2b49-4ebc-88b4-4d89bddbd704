"use client";

import React, { useState } from "react";
import { AdminUser } from "../../services/adminService";
import { UserStatusBadge } from "./UserStatusBadge";
import { UserRoleBadges } from "./UserRoleBadges";

interface UserProfileModalProps {
  user: AdminUser;
  isOpen: boolean;
  onClose: () => void;
  onStatusChange?: (userId: string, newStatus: string) => void;
  onEmailChange?: (userId: string, newEmail: string) => void;
  onPasswordReset?: (userId: string) => void;
  onRoleChange?: (userId: string, roles: { [key: string]: boolean }) => void;
}

export const UserProfileModal: React.FC<UserProfileModalProps> = ({
  user,
  isOpen,
  onClose,
  onStatusChange,
  onEmailChange,
  onPasswordReset,
  onRoleChange,
}) => {
  const [activeTab, setActiveTab] = useState<
    "profile" | "roles" | "security" | "activity"
  >("profile");
  const [newEmail, setNewEmail] = useState(user.email);
  const [emailError, setEmailError] = useState("");

  // State for role toggles
  const [roles, setRoles] = useState({
    admin: user.roles.admin || false,
    editor: user.roles.editor || false,
    banker: user.roles.banker || false,
    chatModerator: user.roles.chatModerator || false,
    volunteerCoordinator: user.roles.volunteerCoordinator || false,
    leadManager: user.roles.leadManager || false,
    salesManager: user.roles.salesManager || false,
    landSteward: user.roles.landSteward || false,
  });

  if (!isOpen) return null;

  const handleEmailChange = () => {
    // Basic email validation
    if (!newEmail || !newEmail.includes("@")) {
      setEmailError("Please enter a valid email address");
      return;
    }

    if (onEmailChange) {
      onEmailChange(user.id, newEmail);
      setEmailError("");
    }
  };

  const handleStatusChange = (newStatus: string) => {
    if (onStatusChange) {
      onStatusChange(user.id, newStatus);
    }
  };

  const handlePasswordReset = () => {
    if (onPasswordReset) {
      onPasswordReset(user.id);
    }
  };

  // Handle role toggle
  const handleRoleToggle = (
    role:
      | "admin"
      | "editor"
      | "banker"
      | "chatModerator"
      | "volunteerCoordinator"
      | "leadManager"
      | "salesManager"
      | "landSteward",
  ) => {
    // Create updated roles object
    const updatedRoles = {
      ...roles,
      [role]: !roles[role],
    };

    // Update local state
    setRoles(updatedRoles);

    // Call API to update roles
    if (onRoleChange) {
      onRoleChange(user.id, { [role]: updatedRoles[role] });
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-secondary-light rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="bg-secondary p-4 border-b border-gray-600 flex justify-between items-center">
          <h2 className="text-xl font-bold text-white">User Profile</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        {/* User Header */}
        <div className="bg-secondary-dark p-6 flex items-center border-b border-gray-600">
          <div className="flex-shrink-0 h-20 w-20">
            <img
              className="h-20 w-20 rounded-full object-cover"
              src={user.avatar || "/images/avatars/default.png"}
              alt={user.displayName}
            />
          </div>
          <div className="ml-6 flex-grow">
            <h3 className="text-2xl font-bold text-white">
              {user.displayName}
            </h3>
            <p className="text-gray-400">@{user.username}</p>
            <div className="mt-2 flex items-center space-x-3">
              <UserStatusBadge status={user.status as any} size="md" />
              <span className="text-gray-400 text-sm">
                Joined: {new Date(user.createdAt).toLocaleDateString()}
              </span>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-secondary px-2 py-2 border-b border-gray-600">
          <div className="flex space-x-4">
            <button
              className={`py-2 px-1 text-sm font-medium border-b-2 ${
                activeTab === "profile"
                  ? "border-primary text-primary"
                  : "border-transparent text-gray-400 hover:text-white"
              }`}
              onClick={() => setActiveTab("profile")}
            >
              Profile
            </button>
            <button
              className={`py-2 px-1 text-sm font-medium border-b-2 ${
                activeTab === "roles"
                  ? "border-primary text-primary"
                  : "border-transparent text-gray-400 hover:text-white"
              }`}
              onClick={() => setActiveTab("roles")}
            >
              Roles
            </button>
            <button
              className={`py-2 px-1 text-sm font-medium border-b-2 ${
                activeTab === "security"
                  ? "border-primary text-primary"
                  : "border-transparent text-gray-400 hover:text-white"
              }`}
              onClick={() => setActiveTab("security")}
            >
              Security
            </button>
            <button
              className={`py-2 px-1 text-sm font-medium border-b-2 ${
                activeTab === "activity"
                  ? "border-primary text-primary"
                  : "border-transparent text-gray-400 hover:text-white"
              }`}
              onClick={() => setActiveTab("activity")}
            >
              Activity
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-grow overflow-y-auto p-6">
          {activeTab === "profile" && (
            <div className="space-y-6">
              <div>
                <h4 className="text-lg font-medium text-white mb-3">
                  Basic Information
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-1">
                      Display Name
                    </label>
                    <div className="bg-secondary p-3 rounded-md text-white">
                      {user.displayName}
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-1">
                      Username
                    </label>
                    <div className="bg-secondary p-3 rounded-md text-white">
                      {user.username}
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-1">
                      Email
                    </label>
                    <div className="bg-secondary p-3 rounded-md text-white">
                      {user.email}
                      {user.isEmailVerified && (
                        <span className="ml-2 bg-green-900 text-green-100 text-xs px-2 py-0.5 rounded-full">
                          Verified
                        </span>
                      )}
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-1">
                      User ID
                    </label>
                    <div className="bg-secondary p-3 rounded-md text-white text-sm">
                      {user.id}
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="text-lg font-medium text-white mb-3">
                  Account Status
                </h4>
                <div className="bg-secondary p-4 rounded-md">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-white mb-1">Current Status</p>
                      <UserStatusBadge status={user.status as any} size="lg" />
                    </div>
                    <div className="flex space-x-2">
                      {user.status === "active" && (
                        <>
                          <button
                            onClick={() => handleStatusChange("frozen")}
                            className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded-md text-sm"
                          >
                            Freeze Account
                          </button>
                          <button
                            onClick={() => handleStatusChange("suspended")}
                            className="px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded-md text-sm"
                          >
                            Suspend Account
                          </button>
                        </>
                      )}
                      {user.status === "frozen" && (
                        <>
                          <button
                            onClick={() => handleStatusChange("active")}
                            className="px-3 py-1 bg-green-600 hover:bg-green-700 text-white rounded-md text-sm"
                          >
                            Unfreeze Account
                          </button>
                          <button
                            onClick={() => handleStatusChange("suspended")}
                            className="px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded-md text-sm"
                          >
                            Suspend Account
                          </button>
                        </>
                      )}
                      {user.status === "suspended" && (
                        <button
                          onClick={() => handleStatusChange("active")}
                          className="px-3 py-1 bg-green-600 hover:bg-green-700 text-white rounded-md text-sm"
                        >
                          Reactivate Account
                        </button>
                      )}
                      {user.status === "inactive" && (
                        <button
                          onClick={() => handleStatusChange("active")}
                          className="px-3 py-1 bg-green-600 hover:bg-green-700 text-white rounded-md text-sm"
                        >
                          Reactivate Account
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === "roles" && (
            <div className="space-y-6">
              <div>
                <h4 className="text-lg font-medium text-white mb-3">
                  User Roles
                </h4>
                <div className="bg-secondary p-4 rounded-md">
                  <div className="mb-4">
                    <p className="text-white mb-2">Current Roles</p>
                    <UserRoleBadges roles={user.roles} size="lg" />
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center justify-between bg-secondary-dark p-3 rounded-md">
                      <span className="text-white">Admin</span>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          className="sr-only peer"
                          checked={roles.admin}
                          onChange={() => handleRoleToggle("admin")}
                        />
                        <div className="w-11 h-6 bg-gray-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                      </label>
                    </div>
                    <div className="flex items-center justify-between bg-secondary-dark p-3 rounded-md">
                      <span className="text-white">Editor</span>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          className="sr-only peer"
                          checked={roles.editor}
                          onChange={() => handleRoleToggle("editor")}
                        />
                        <div className="w-11 h-6 bg-gray-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                      </label>
                    </div>
                    <div className="flex items-center justify-between bg-secondary-dark p-3 rounded-md">
                      <span className="text-white">Banker</span>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          className="sr-only peer"
                          checked={roles.banker}
                          onChange={() => handleRoleToggle("banker")}
                        />
                        <div className="w-11 h-6 bg-gray-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                      </label>
                    </div>
                    <div className="flex items-center justify-between bg-secondary-dark p-3 rounded-md">
                      <span className="text-white">Chat Moderator</span>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          className="sr-only peer"
                          checked={roles.chatModerator}
                          onChange={() => handleRoleToggle("chatModerator")}
                        />
                        <div className="w-11 h-6 bg-gray-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                      </label>
                    </div>
                    <div className="flex items-center justify-between bg-secondary-dark p-3 rounded-md">
                      <span className="text-white">Volunteer Coordinator</span>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          className="sr-only peer"
                          checked={roles.volunteerCoordinator}
                          onChange={() =>
                            handleRoleToggle("volunteerCoordinator")
                          }
                        />
                        <div className="w-11 h-6 bg-gray-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                      </label>
                    </div>
                    <div className="flex items-center justify-between bg-secondary-dark p-3 rounded-md">
                      <span className="text-white">Category Lead</span>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          className="sr-only peer"
                          checked={roles.leadManager}
                          onChange={() => handleRoleToggle("leadManager")}
                        />
                        <div className="w-11 h-6 bg-gray-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                      </label>
                    </div>
                    <div className="flex items-center justify-between bg-secondary-dark p-3 rounded-md">
                      <span className="text-white">Sales Manager</span>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          className="sr-only peer"
                          checked={roles.salesManager}
                          onChange={() => handleRoleToggle("salesManager")}
                        />
                        <div className="w-11 h-6 bg-gray-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                      </label>
                    </div>
                    <div className="flex items-center justify-between bg-secondary-dark p-3 rounded-md">
                      <span className="text-white">Land Steward</span>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          className="sr-only peer"
                          checked={roles.landSteward}
                          onChange={() => handleRoleToggle("landSteward")}
                        />
                        <div className="w-11 h-6 bg-gray-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === "security" && (
            <div className="space-y-6">
              <div>
                <h4 className="text-lg font-medium text-white mb-3">
                  Email Management
                </h4>
                <div className="bg-secondary p-4 rounded-md">
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-400 mb-1">
                      Current Email
                    </label>
                    <div className="bg-secondary-dark p-3 rounded-md text-white">
                      {user.email}
                      {user.isEmailVerified && (
                        <span className="ml-2 bg-green-900 text-green-100 text-xs px-2 py-0.5 rounded-full">
                          Verified
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-400 mb-1">
                      New Email
                    </label>
                    <input
                      type="email"
                      value={newEmail}
                      onChange={(e) => setNewEmail(e.target.value)}
                      className="w-full px-4 py-2 bg-secondary-dark border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary"
                    />
                    {emailError && (
                      <p className="mt-1 text-red-400 text-sm">{emailError}</p>
                    )}
                  </div>
                  <button
                    onClick={handleEmailChange}
                    className="px-4 py-2 bg-primary hover:bg-primary-dark text-white rounded-md text-sm"
                  >
                    Update Email
                  </button>
                </div>
              </div>

              <div>
                <h4 className="text-lg font-medium text-white mb-3">
                  Password Management
                </h4>
                <div className="bg-secondary p-4 rounded-md">
                  <p className="text-gray-400 mb-4">
                    Reset the user's password. A temporary password will be
                    generated and sent to their email.
                  </p>
                  <button
                    onClick={handlePasswordReset}
                    className="px-4 py-2 bg-primary hover:bg-primary-dark text-white rounded-md text-sm"
                  >
                    Reset Password
                  </button>
                </div>
              </div>
            </div>
          )}

          {activeTab === "activity" && (
            <div className="space-y-6">
              <div>
                <h4 className="text-lg font-medium text-white mb-3">
                  Account Activity
                </h4>
                <div className="bg-secondary p-4 rounded-md">
                  <p className="text-gray-400 mb-4">
                    Recent account activity will be displayed here.
                  </p>
                  <div className="text-center py-8">
                    <p className="text-gray-400">
                      Activity tracking is not yet implemented.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="bg-secondary p-4 border-t border-gray-600 flex justify-between">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-md text-sm"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};
