import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import prisma from '@/lib/prisma';

export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = user.id;

    // Find the user's current ship membership
    const membership = await prisma.shipMember.findFirst({
      where: {
        userId,
        status: 'active'
      },
      include: {
        ship: {
          select: {
            id: true,
            name: true,
            captainId: true
          }
        }
      }
    });

    if (!membership) {
      return NextResponse.json({ error: 'You are not currently a member of any ship' }, { status: 404 });
    }

    // Check if user is the captain
    if (membership.ship.captainId === userId) {
      return NextResponse.json({ 
        error: 'Captains cannot leave their ship. You must transfer captaincy or delete the ship first.' 
      }, { status: 400 });
    }

    // Update membership status to left
    await prisma.shipMember.update({
      where: {
        id: membership.id
      },
      data: {
        status: 'left',
        leftAt: new Date()
      }
    });

    return NextResponse.json({
      success: true,
      message: `Successfully left ${membership.ship.name}`
    });

  } catch (error) {
    console.error('Error leaving ship:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}