import prisma from "./prisma";
import { connectionStore } from "./connectionStore";

/**
 * Create a notification for a user
 * @param userId User ID to create notification for
 * @param data Notification data
 * @returns Created notification
 */
export async function createNotification(
  userId: string,
  data: {
    category: string;
    type: string;
    title: string;
    message: string;
    link?: string;
    icon?: string;
    priority?: "low" | "medium" | "high";
    transactionId?: string;
  },
) {
  try {
    // Check if user has notification preferences enabled for this category
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        notifyTransfers: true,
        notifyDeposits: true,
        notifyWithdrawals: true,
        notifyNewsEvents: true,
        notifyAuctions: true,
        notifyChat: true,
        notifyAdmin: true,
      },
    });

    if (!user) {
      throw new Error("User not found");
    }

    // Check if user has notifications enabled for this category
    let shouldNotify = true;

    switch (data.category) {
      case "transaction":
        if (
          data.type === "transfer" ||
          data.type === "transfer_sent" ||
          data.type === "transfer_received"
        ) {
          shouldNotify = user.notifyTransfers;
        } else if (
          data.type === "deposit" ||
          data.type === "deposit_approved" ||
          data.type === "deposit_rejected"
        ) {
          shouldNotify = user.notifyDeposits;
        } else if (
          data.type === "withdrawal" ||
          data.type === "withdrawal_approved" ||
          data.type === "withdrawal_rejected"
        ) {
          shouldNotify = user.notifyWithdrawals;
        }
        break;
      case "news":
        shouldNotify = user.notifyNewsEvents;
        break;
      case "auction":
        shouldNotify = user.notifyAuctions;
        break;
      case "chat":
        shouldNotify = user.notifyChat;
        break;
      case "admin":
        shouldNotify = user.notifyAdmin;
        break;
    }

    // If user has disabled notifications for this category, don't create notification
    if (!shouldNotify) {
      return null;
    }

    // Create notification
    const notification = await prisma.notification.create({
      data: {
        userId,
        category: data.category,
        type: data.type,
        title: data.title,
        message: data.message,
        link: data.link,
        icon: data.icon,
        priority: data.priority || "medium",
        transactionId: data.transactionId,
      },
    });

    // Format notification for SSE
    const formattedNotification = {
      ...notification,
      createdAt: notification.createdAt.toISOString(),
      updatedAt: notification.updatedAt.toISOString(),
    };

    // Broadcast notification via SSE if user has active connections
    if (connectionStore.getUserConnectionCount(userId) > 0) {
      console.log(
        `[Notifications] Broadcasting notification to user ${userId} via SSE`,
      );
      await connectionStore.sendToUser(userId, {
        type: "notification",
        notification: formattedNotification,
      });
    } else {
      console.log(
        `[Notifications] User ${userId} has no active SSE connections, skipping broadcast`,
      );
    }

    return notification;
  } catch (error) {
    console.error("Error creating notification:", error);
    return null;
  }
}

/**
 * Create notifications for multiple users
 * @param userIds Array of user IDs to create notifications for
 * @param data Notification data
 * @returns Array of created notifications
 */
export async function createNotificationForMultipleUsers(
  userIds: string[],
  data: {
    category: string;
    type: string;
    title: string;
    message: string;
    link?: string;
    icon?: string;
    priority?: "low" | "medium" | "high";
    transactionId?: string;
  },
) {
  try {
    const notifications = [];

    for (const userId of userIds) {
      const notification = await createNotification(userId, data);
      if (notification) {
        notifications.push(notification);
      }
    }

    return notifications;
  } catch (error) {
    console.error("Error creating notifications for multiple users:", error);
    return [];
  }
}

/**
 * Create a system-wide notification for all users
 * @param data Notification data
 * @returns Array of created notifications
 */
export async function createSystemNotification(data: {
  category: string;
  type: string;
  title: string;
  message: string;
  link?: string;
  icon?: string;
  priority?: "low" | "medium" | "high";
  transactionId?: string;
}) {
  try {
    // Get all active users
    const users = await prisma.user.findMany({
      where: {
        status: "active",
      },
      select: {
        id: true,
      },
    });

    const userIds = users.map((user) => user.id);
    return createNotificationForMultipleUsers(userIds, data);
  } catch (error) {
    console.error("Error creating system notification:", error);
    return [];
  }
}

/**
 * Create a notification for all admin users
 * @param data Notification data
 * @returns Array of created notifications
 */
export async function createAdminNotification(data: {
  type: string;
  title: string;
  message: string;
  link?: string;
  icon?: string;
  priority?: "low" | "medium" | "high";
  transactionId?: string;
}) {
  try {
    // Get all active admin users
    const admins = await prisma.user.findMany({
      where: {
        isAdmin: true,
        status: "active",
      },
      select: {
        id: true,
      },
    });

    const adminIds = admins.map((admin) => admin.id);

    // Set category to admin for these notifications
    const notificationData = {
      ...data,
      category: "admin",
    };

    return createNotificationForMultipleUsers(adminIds, notificationData);
  } catch (error) {
    console.error("Error creating admin notification:", error);
    return [];
  }
}
