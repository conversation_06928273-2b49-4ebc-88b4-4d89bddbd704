import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import prisma from '@/lib/prisma';

export async function DELETE(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const captainId = user.id;
    const memberUserId = params.userId;

    // Verify captain owns a ship
    const ship = await prisma.ship.findFirst({
      where: {
        captainId,
        status: 'active'
      }
    });

    if (!ship) {
      return NextResponse.json({ error: 'Ship not found or you are not the captain' }, { status: 404 });
    }

    // Cannot remove the captain themselves
    if (memberUserId === captainId) {
      return NextResponse.json({ error: 'Captain cannot remove themselves from the ship' }, { status: 400 });
    }

    // Find the member
    const member = await prisma.shipMember.findUnique({
      where: {
        userId_shipId: {
          userId: memberUserId,
          shipId: ship.id
        }
      },
      include: {
        user: {
          select: {
            username: true,
            displayName: true
          }
        }
      }
    });

    if (!member) {
      return NextResponse.json({ error: 'Member not found' }, { status: 404 });
    }

    // Update member status to removed and set leftAt timestamp
    await prisma.shipMember.update({
      where: {
        userId_shipId: {
          userId: memberUserId,
          shipId: ship.id
        }
      },
      data: {
        status: 'removed',
        leftAt: new Date()
      }
    });

    return NextResponse.json({
      success: true,
      message: `${member.user.displayName} has been removed from the ship`
    });

  } catch (error) {
    console.error('Error removing ship member:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}