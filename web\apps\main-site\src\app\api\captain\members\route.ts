import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import prisma from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = user.id;

    // Get the user's ship where they are captain
    const ship = await prisma.ship.findFirst({
      where: {
        captainId: userId,
        status: 'active'
      },
      include: {
        members: {
          include: {
            user: {
              select: {
                id: true,
                username: true,
                displayName: true,
                avatar: true,
                email: true
              }
            },
            customRole: true
          },
          orderBy: {
            joinedAt: 'asc'
          }
        }
      }
    });

    if (!ship) {
      return NextResponse.json({ error: 'Ship not found or you are not the captain' }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      members: ship.members
    });

  } catch (error) {
    console.error('Error fetching ship members:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}