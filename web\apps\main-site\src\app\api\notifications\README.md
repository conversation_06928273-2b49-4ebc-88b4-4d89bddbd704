# Notifications API Routes

Real-time notification system using Server-Sent Events (SSE) for instant updates.

## Core Operations

- **route.ts** - Main notification operations (list, mark as read)
- **create/** - Notification creation and distribution
- **[id]/** - Individual notification operations (view, update, delete)

## Real-time Features

- **sse/** - Server-Sent Events endpoint for real-time notification streaming
- **broadcast/** - Broadcast notifications to multiple users or groups

## Development

- **test/** - Notification testing and validation utilities

## Notification Types

The system handles notifications for:

- Banking transactions and balance updates
- Volunteer shift assignments and updates
- Order confirmations and status changes
- System announcements and alerts
- Support ticket updates

These endpoints provide instant communication to users about important platform activities and updates.
