import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// DELETE /api/volunteer/public/user/shifts/[id] - Cancel a shift signup
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const { id: shiftId } = params;

    // Check if user is authenticated
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 },
      );
    }

    // Find the assignment
    const assignment = await prisma.volunteerAssignment.findFirst({
      where: {
        shiftId,
        userId: user.id,
        status: {
          in: ["pending", "confirmed"],
        },
      },
      include: {
        shift: {
          select: {
            startTime: true,
          },
        },
      },
    });

    if (!assignment) {
      return NextResponse.json(
        { error: "Assignment not found" },
        { status: 404 },
      );
    }

    // Check if shift has already started
    const now = new Date();
    if (new Date(assignment.shift.startTime) < now) {
      return NextResponse.json(
        { error: "Cannot cancel a shift that has already started" },
        { status: 400 },
      );
    }

    // Update assignment status to cancelled
    await prisma.volunteerAssignment.update({
      where: { id: assignment.id },
      data: {
        status: "cancelled",
      },
    });

    return NextResponse.json({
      success: true,
      message: "Shift signup cancelled successfully",
    });
  } catch (error) {
    console.error("Error cancelling shift signup:", error);
    return NextResponse.json(
      { error: "Failed to cancel shift signup" },
      { status: 500 },
    );
  }
}
