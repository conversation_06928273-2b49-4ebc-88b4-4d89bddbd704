import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import prisma from '@/lib/prisma';

export async function PUT(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const captainId = user.id;
    const memberUserId = params.userId;

    const { roleId, role } = await request.json();

    // Verify captain owns a ship
    const ship = await prisma.ship.findFirst({
      where: {
        captainId,
        status: 'active'
      }
    });

    if (!ship) {
      return NextResponse.json({ error: 'Ship not found or you are not the captain' }, { status: 404 });
    }

    // Cannot change the captain's role
    if (memberUserId === captainId) {
      return NextResponse.json({ error: 'Cannot change captain role' }, { status: 400 });
    }

    // Find the member
    const member = await prisma.shipMember.findUnique({
      where: {
        userId_shipId: {
          userId: memberUserId,
          shipId: ship.id
        }
      }
    });

    if (!member) {
      return NextResponse.json({ error: 'Member not found' }, { status: 404 });
    }

    if (member.status !== 'active') {
      return NextResponse.json({ error: 'Cannot update role for inactive member' }, { status: 400 });
    }

    // Verify custom role exists if provided
    if (roleId) {
      const customRole = await prisma.shipRole.findFirst({
        where: {
          id: roleId,
          shipId: ship.id
        }
      });

      if (!customRole) {
        return NextResponse.json({ error: 'Custom role not found' }, { status: 404 });
      }
    }

    // Update member role
    const updatedMember = await prisma.shipMember.update({
      where: {
        userId_shipId: {
          userId: memberUserId,
          shipId: ship.id
        }
      },
      data: {
        role: role || member.role, // Fallback role for backward compatibility
        roleId: roleId || null
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true
          }
        },
        customRole: true
      }
    });

    return NextResponse.json({
      success: true,
      member: updatedMember
    });

  } catch (error) {
    console.error('Error updating member role:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}