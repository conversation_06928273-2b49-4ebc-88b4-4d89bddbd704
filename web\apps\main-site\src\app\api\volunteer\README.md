# Volunteer Management API Routes

Comprehensive volunteer management system with shift scheduling, categories, and payment tracking.

## Core Management

- **categories/** - Volunteer category management with roles and pay rates
- **shifts/** - Shift creation, scheduling, and assignment management
- **events/** - Event-based volunteer coordination
- **dashboard/** - Volunteer dashboard data and statistics

## User Interface

- **public/** - Public-facing volunteer opportunities and signup
- **user-search/** - User search functionality for volunteer assignment

## Administrative

- **management/** - Administrative volunteer management operations
- **lead/** - Volunteer lead management and oversight functionality

## Features

The volunteer system provides:

- Category-based volunteer organization
- Shift scheduling with time slots and limits
- Hour tracking and payment processing
- Public signup and private management interfaces
- Lead management for volunteer coordination
- Integration with event management system

These endpoints support a complete volunteer management ecosystem for organizing community involvement and tracking volunteer contributions.
