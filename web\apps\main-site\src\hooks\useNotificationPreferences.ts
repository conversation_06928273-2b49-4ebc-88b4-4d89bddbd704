import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  getNotificationPreferences,
  updateNotificationPreferences,
} from "../services/notificationService";
import toast from "react-hot-toast";

// Query key for notification preferences
export const notificationPreferencesKey = ["notificationPreferences"];

/**
 * Hook for managing notification preferences
 * This provides a consistent way to access and update notification preferences across the app
 */
export function useNotificationPreferences() {
  const queryClient = useQueryClient();

  // Fetch notification preferences
  const { data: preferences, isLoading } = useQuery({
    queryKey: notificationPreferencesKey,
    queryFn: getNotificationPreferences,
  });

  // Update notification preferences
  const updatePreferencesMutation = useMutation({
    mutationFn: updateNotificationPreferences,
    onSuccess: () => {
      // Invalidate the query to refetch the data
      queryClient.invalidateQueries({ queryKey: notificationPreferencesKey });
      toast.success("Notification preferences updated");
    },
    onError: () => {
      toast.error("Failed to update notification preferences");
    },
  });

  // Function to toggle a notification preference
  const toggleNotificationPreference = (key: string, value?: boolean) => {
    if (!preferences) return;

    const updatedPreferences = {
      ...preferences,
      [key]:
        value !== undefined
          ? value
          : !preferences[key as keyof typeof preferences],
    };

    updatePreferencesMutation.mutate(updatedPreferences);

    return updatedPreferences;
  };

  return {
    preferences,
    isLoading,
    toggleNotificationPreference,
    updatePreferences: updatePreferencesMutation.mutate,
  };
}
