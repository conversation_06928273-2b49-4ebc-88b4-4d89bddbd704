import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth";
import { canVolunteerCheckIn, getCheckInStatus } from "@/services/volunteerCheckinService";

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// GET /api/volunteer/public/shifts/[id]/checkin - Get check-in status for a shift
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id: shiftId } = params;
    
    // Check if user is authenticated
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 }
      );
    }

    // Find the user's assignment for this shift
    const assignment = await prisma.volunteerAssignment.findFirst({
      where: {
        userId: user.id,
        shiftId: shiftId,
        status: {
          in: ["pending", "assigned", "confirmed", "checked_in"]
        }
      },
      include: {
        shift: {
          select: {
            id: true,
            title: true,
            startTime: true,
            endTime: true,
          }
        }
      }
    });

    if (!assignment) {
      return NextResponse.json(
        { error: "No assignment found for this shift" },
        { status: 404 }
      );
    }

    // Calculate check-in status
    const shiftStartTime = new Date(assignment.shift.startTime);
    const checkInStatus = getCheckInStatus(shiftStartTime, assignment.status);

    return NextResponse.json({
      assignmentId: assignment.id,
      shiftId: assignment.shift.id,
      shiftTitle: assignment.shift.title,
      shiftStartTime: assignment.shift.startTime,
      currentStatus: assignment.status,
      checkInStatus
    });

  } catch (error) {
    console.error("Error fetching check-in status:", error);
    return NextResponse.json(
      { error: "Failed to fetch check-in status" },
      { status: 500 }
    );
  }
}

// POST /api/volunteer/public/shifts/[id]/checkin - Check in to a shift
export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id: shiftId } = params;
    
    // Check if user is authenticated
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 }
      );
    }

    // Find the user's assignment for this shift
    const assignment = await prisma.volunteerAssignment.findFirst({
      where: {
        userId: user.id,
        shiftId: shiftId,
        status: {
          in: ["pending", "assigned", "confirmed"]
        }
      },
      include: {
        shift: {
          select: {
            id: true,
            title: true,
            startTime: true,
            endTime: true,
            category: {
              select: {
                name: true
              }
            },
            event: {
              select: {
                name: true
              }
            }
          }
        }
      }
    });

    if (!assignment) {
      return NextResponse.json(
        { error: "No eligible assignment found for this shift" },
        { status: 404 }
      );
    }

    // Check if volunteer can check in (time window validation)
    const assignmentStatus = {
      id: assignment.id,
      status: assignment.status,
      shift: {
        startTime: assignment.shift.startTime.toISOString(),
        endTime: assignment.shift.endTime.toISOString()
      }
    };

    if (!canVolunteerCheckIn(assignmentStatus)) {
      const shiftStartTime = new Date(assignment.shift.startTime);
      const checkInStatus = getCheckInStatus(shiftStartTime, assignment.status);
      
      let errorMessage = "Check-in not available";
      if (checkInStatus.status === 'not_ready') {
        errorMessage = `Check-in opens ${checkInStatus.timeUntilCheckIn} minutes before your shift`;
      } else if (checkInStatus.status === 'expired') {
        errorMessage = "Check-in window has expired - your shift has already started";
      }

      return NextResponse.json(
        { error: errorMessage, checkInStatus },
        { status: 400 }
      );
    }

    // Update assignment status to checked_in and set check-in timestamp
    const updatedAssignment = await prisma.volunteerAssignment.update({
      where: { id: assignment.id },
      data: {
        status: "checked_in",
        checkedInAt: new Date(),
        updatedAt: new Date()
      },
      include: {
        shift: {
          select: {
            title: true,
            startTime: true,
            category: {
              select: {
                name: true
              }
            },
            event: {
              select: {
                name: true
              }
            }
          }
        }
      }
    });

    // TODO: Send notification to Category Lead about volunteer check-in
    // This would integrate with the existing notification system

    return NextResponse.json({
      message: "Successfully checked in",
      assignment: {
        id: updatedAssignment.id,
        status: updatedAssignment.status,
        shift: {
          title: updatedAssignment.shift.title,
          startTime: updatedAssignment.shift.startTime,
          category: updatedAssignment.shift.category.name,
          event: updatedAssignment.shift.event.name
        }
      }
    });

  } catch (error) {
    console.error("Error checking in to shift:", error);
    
    // Handle specific database errors
    if (error instanceof Error && error.message.includes('unique constraint')) {
      return NextResponse.json(
        { error: "You are already checked in to this shift" },
        { status: 409 }
      );
    }

    return NextResponse.json(
      { error: "Failed to check in to shift" },
      { status: 500 }
    );
  }
}