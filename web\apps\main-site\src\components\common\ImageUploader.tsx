"use client";

import React, { useRef, useState } from "react";
import { Button } from "@bank-of-styx/ui";
import { uploadImage } from "@/lib/upload-service";

export interface ImageUploaderProps {
  onImageUploaded: (imageUrl: string) => void;
  onError?: (error: string) => void;
  className?: string;
  buttonText?: string;
  acceptedFormats?: string;
  maxSizeMB?: number;
}

export const ImageUploader: React.FC<ImageUploaderProps> = ({
  onImageUploaded,
  onError,
  className = "",
  buttonText = "Upload Image",
  acceptedFormats = "image/jpeg, image/png, image/gif, image/webp",
  maxSizeMB = 10,
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleButtonClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!acceptedFormats.includes(file.type)) {
      onError?.("Unsupported file format. Please upload a valid image.");
      return;
    }

    // Validate file size
    if (file.size > maxSizeMB * 1024 * 1024) {
      onError?.(`File size exceeds ${maxSizeMB}MB limit.`);
      return;
    }

    setIsLoading(true);

    try {
      const uploadedFile = await uploadImage(file);
      onImageUploaded(uploadedFile.url);
    } catch (error) {
      console.error("Error uploading image:", error);
      onError?.(
        error instanceof Error ? error.message : "Failed to upload image",
      );
    } finally {
      setIsLoading(false);

      // Clear the file input so the same file can be selected again
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  return (
    <div className={`${className}`}>
      <input
        type="file"
        accept={acceptedFormats}
        ref={fileInputRef}
        className="hidden"
        onChange={handleFileChange}
      />
      <Button
        type="button"
        variant="secondary"
        size="sm"
        onClick={handleButtonClick}
        disabled={isLoading}
        className="flex items-center"
      >
        {isLoading ? (
          <>
            <svg
              className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              ></circle>
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
            Uploading...
          </>
        ) : (
          <>
            <svg
              className="-ml-1 mr-2 h-4 w-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
              />
            </svg>
            {buttonText}
          </>
        )}
      </Button>
    </div>
  );
};
