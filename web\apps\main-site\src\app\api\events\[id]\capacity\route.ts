import { NextRequest, NextResponse } from "next/server";
import { getEventCapacityStats } from "@/lib/event-capacity-system";

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// GET /api/events/[id]/capacity - Get event capacity information
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const eventId = params.id;

    if (!eventId) {
      return NextResponse.json(
        { error: "Event ID is required" },
        { status: 400 }
      );
    }

    const capacityStats = await getEventCapacityStats(eventId);

    return NextResponse.json({
      eventId,
      capacity: capacityStats,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error("Error fetching event capacity:", error);
    return NextResponse.json(
      { error: "Failed to fetch event capacity" },
      { status: 500 }
    );
  }
}