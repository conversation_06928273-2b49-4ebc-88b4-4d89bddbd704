import React, { useState, useRef, ChangeEvent } from "react";
import { UploadResponse, UniversalImageUploaderProps } from "@/types/upload";

const UniversalImageUploader: React.FC<UniversalImageUploaderProps> = ({
  uploadType,
  onUploadComplete,
  onUploadStart,
  entityId,
  options = {},
  inputRef: externalInputRef,
  className = "",
}) => {
  const [file, setFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const internalInputRef = useRef<HTMLInputElement>(null);
  const inputRef = externalInputRef || internalInputRef;

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFile(e.target.files[0]);
      setError(null); // Clear any previous errors when a new file is selected
    }
  };

  const handleUpload = async () => {
    if (!file) return;

    // Validate file type
    if (options?.allowedTypes && !options.allowedTypes.includes(file.type)) {
      setError(
        `Invalid file type. Allowed types: ${options.allowedTypes.join(", ")}`,
      );
      return;
    }

    // Validate file size
    if (options?.maxSize && file.size > options.maxSize) {
      const maxSizeMB = options.maxSize / (1024 * 1024);
      setError(`File too large. Maximum size: ${maxSizeMB}MB`);
      return;
    }

    setUploading(true);
    setError(null);

    // Call onUploadStart callback if provided
    if (onUploadStart) {
      onUploadStart();
    }

    const formData = new FormData();
    formData.append("file", file);
    formData.append("type", uploadType);

    if (entityId) {
      formData.append("entityId", entityId);
    }

    // Add options if provided
    if (options && Object.keys(options).length > 0) {
      formData.append("options", JSON.stringify(options));
    }

    try {
      const res = await fetch("/api/uploads/v2", {
        method: "POST",
        body: formData,
      });

      const data: UploadResponse = await res.json();

      if (!res.ok) {
        throw new Error(data.error || "Something went wrong");
      }

      // Remove progress polling since uploads are fast
      // if (data.file?.id) {
      //   pollProgress(data.file.id);
      // }

      onUploadComplete(data);
      setFile(null); // Reset file after successful upload
      if (inputRef?.current) {
        inputRef.current.value = ""; // Reset file input
      }
    } catch (err) {
      const error = err as Error;
      setError(error.message);
      onUploadComplete({
        success: false,
        message: error.message,
        error: error.message,
      });
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className={className}>
      <div className="space-y-2">
        <input
          ref={inputRef}
          type="file"
          accept={options?.allowedTypes?.join(",")}
          onChange={handleFileChange}
          disabled={uploading}
          className="hidden"
        />
        <button
          type="button"
          onClick={() => inputRef?.current?.click()}
          disabled={uploading}
          className={`w-full px-4 py-2 text-sm font-medium rounded-md ${
            uploading
              ? "bg-gray-200 text-gray-500 cursor-not-allowed"
              : "bg-indigo-600 text-white hover:bg-indigo-700"
          }`}
        >
          {file ? file.name : "Choose File"}
        </button>
        <button
          type="button"
          onClick={handleUpload}
          disabled={uploading || !file}
          className={`w-full px-4 py-2 text-sm font-medium rounded-md ${
            uploading || !file
              ? "bg-gray-200 text-gray-500 cursor-not-allowed"
              : "bg-green-600 text-white hover:bg-green-700"
          }`}
        >
          {uploading ? "Uploading..." : "Upload"}
        </button>

        {/* Simple upload indicator */}
        {uploading && (
          <div className="space-y-2">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300 animate-pulse"
                style={{ width: "100%" }}
              ></div>
            </div>
            <div className="text-xs text-gray-600 text-center">
              Uploading...
            </div>
          </div>
        )}

        {error && <div className="text-red-500 text-sm mt-2">{error}</div>}
      </div>
    </div>
  );
};

export default UniversalImageUploader;
