import React from "react";

export interface TextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  /**
   * Textarea label
   */
  label?: string;
  /**
   * Textarea error message
   */
  error?: boolean;
  /**
   * Textarea helper text
   */
  helperText?: string;
  /**
   * Textarea full width
   */
  fullWidth?: boolean;
}

export const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  (
    {
      label,
      error,
      helperText,
      fullWidth = false,
      className = "",
      id,
      ...props
    },
    ref,
  ) => {
    // Generate a random ID if not provided
    const textareaId =
      id || `textarea-${Math.random().toString(36).substring(2, 9)}`;

    // Width classes
    const widthClasses = fullWidth ? "w-full" : "";

    // Error classes
    const errorClasses = error
      ? "border-accent focus:border-accent focus:ring-accent"
      : "border-gray-600 focus:border-primary focus:ring-primary";

    return (
      <div className={`${widthClasses}`}>
        {label && (
          <label
            htmlFor={textareaId}
            className="block text-sm font-medium text-white mb-1"
          >
            {label}
          </label>
        )}
        <textarea
          ref={ref}
          id={textareaId}
          className={`
            block rounded-md shadow-sm text-white border p-3
            ${errorClasses}
            ${widthClasses}
            ${className}
            focus:outline-none focus:ring-2 focus:ring-offset-0
            disabled:bg-secondary-dark disabled:text-disabled disabled:cursor-not-allowed
            bg-[#2C2F33] border border-gray-600
          `}
          aria-invalid={error ? "true" : "false"}
          aria-describedby={
            error
              ? `${textareaId}-error`
              : helperText
              ? `${textareaId}-helper-text`
              : undefined
          }
          {...props}
        />
        {helperText && (
          <p
            id={`${textareaId}-helper-text`}
            className={`mt-1 text-sm ${
              error ? "text-accent" : "text-gray-400"
            }`}
          >
            {helperText}
          </p>
        )}
      </div>
    );
  },
);

Textarea.displayName = "Textarea";
