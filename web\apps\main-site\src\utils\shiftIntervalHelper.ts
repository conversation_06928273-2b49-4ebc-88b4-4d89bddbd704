/**
 * Utility functions for generating interval-based shifts
 */

export interface ShiftData {
  title: string;
  description?: string;
  startTime: string; // ISO date string
  endTime: string; // ISO date string
  location?: string;
  maxVolunteers?: number;
}

/**
 * Generate shifts based on a specified interval
 *
 * @param baseShift The base shift data to use as a template
 * @param intervalHours The interval length in hours (1, 2, 4, or 6)
 * @returns An array of shift objects with calculated start and end times
 */
export function generateIntervalShifts(
  baseShift: ShiftData,
  intervalHours: number,
): ShiftData[] {
  // Parse start and end times
  const startTime = new Date(baseShift.startTime);
  const endTime = new Date(baseShift.endTime);

  // Check if start and end times are on the same day
  const sameDay =
    startTime.getDate() === endTime.getDate() &&
    startTime.getMonth() === endTime.getMonth() &&
    startTime.getFullYear() === endTime.getFullYear();

  // Calculate total duration in milliseconds
  const totalDuration = endTime.getTime() - startTime.getTime();

  // Convert interval to milliseconds
  const intervalMs = intervalHours * 60 * 60 * 1000;

  // Calculate how many complete intervals fit in the total duration
  const intervalCount = Math.floor(totalDuration / intervalMs);

  // If no complete intervals fit, return the original shift
  if (intervalCount <= 0) {
    return [baseShift];
  }

  // Generate shifts for each interval
  const shifts: ShiftData[] = [];

  for (let i = 0; i < intervalCount; i++) {
    // Calculate start time for this interval
    const intervalStartTime = new Date(startTime.getTime() + i * intervalMs);

    // Calculate end time for this interval
    const intervalEndTime = new Date(intervalStartTime.getTime() + intervalMs);

    // Ensure the last interval doesn't exceed the original end time
    const adjustedEndTime =
      intervalEndTime > endTime ? endTime : intervalEndTime;

    // If we're supposed to stay on the same day and this interval would cross to the next day, stop here
    if (sameDay && intervalStartTime.getDate() !== startTime.getDate()) {
      console.log(
        `Stopping interval creation at ${i} intervals because next interval would cross to next day`,
      );
      break;
    }

    // Format the time part for the title (e.g., "9:00 AM - 11:00 AM")
    const startTimeStr = intervalStartTime.toLocaleTimeString("en-US", {
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    });

    const endTimeStr = adjustedEndTime.toLocaleTimeString("en-US", {
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    });

    // Create a title that includes the time range
    const intervalTitle = `${baseShift.title} (${startTimeStr} - ${endTimeStr})`;

    // Create the shift object
    shifts.push({
      ...baseShift,
      title: intervalTitle,
      startTime: intervalStartTime.toISOString(),
      endTime: adjustedEndTime.toISOString(),
    });
  }

  return shifts;
}

/**
 * Validate if a shift can be split into intervals
 *
 * @param startTime The start time of the shift
 * @param endTime The end time of the shift
 * @param intervalHours The interval length in hours
 * @returns An object with validation result and message
 */
export function validateIntervalShift(
  startTime: string,
  endTime: string,
  intervalHours: number,
): { isValid: boolean; message?: string } {
  if (!intervalHours) {
    return { isValid: true };
  }

  // Parse start and end times
  const start = new Date(startTime);
  const end = new Date(endTime);

  // Check if start and end times are on the same day
  const sameDay =
    start.getDate() === end.getDate() &&
    start.getMonth() === end.getMonth() &&
    start.getFullYear() === end.getFullYear();

  // Calculate total duration in hours
  const totalDurationHours =
    (end.getTime() - start.getTime()) / (60 * 60 * 1000);

  // Check if duration is less than the interval
  if (totalDurationHours < intervalHours) {
    return {
      isValid: false,
      message: `Total duration (${totalDurationHours.toFixed(
        1,
      )} hours) is less than the selected interval (${intervalHours} hours).`,
    };
  }

  // Calculate how many complete intervals fit in the total duration
  const intervalCount = Math.floor(totalDurationHours / intervalHours);

  if (intervalCount <= 0) {
    return {
      isValid: false,
      message: `No complete intervals of ${intervalHours} hours fit within the selected time range.`,
    };
  }

  // Calculate the maximum number of intervals that can fit in the same day
  let maxIntervalsInSameDay = intervalCount;

  if (sameDay) {
    // Calculate how many intervals would fit before midnight
    const msUntilMidnight =
      new Date(
        start.getFullYear(),
        start.getMonth(),
        start.getDate() + 1,
        0,
        0,
        0,
      ).getTime() - start.getTime();

    const hoursUntilMidnight = msUntilMidnight / (60 * 60 * 1000);
    maxIntervalsInSameDay = Math.floor(hoursUntilMidnight / intervalHours);

    // If some intervals would cross midnight, adjust the count and add a note
    if (maxIntervalsInSameDay < intervalCount) {
      return {
        isValid: true,
        message: `This will create ${maxIntervalsInSameDay} shifts of ${intervalHours} hour${
          intervalHours > 1 ? "s" : ""
        } each (stopping at midnight).`,
      };
    }
  }

  return {
    isValid: true,
    message: `This will create ${intervalCount} shifts of ${intervalHours} hour${
      intervalHours > 1 ? "s" : ""
    } each.`,
  };
}
