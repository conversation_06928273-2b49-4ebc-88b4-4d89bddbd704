"use client";

import React, { useState, useEffect } from "react";
import { format } from "date-fns";
import { toast } from "react-hot-toast";
import { useAuth } from "@/contexts/AuthContext";
import { Button } from "@bank-of-styx/ui";
import {
  useVolunteerShift,
  useVolunteerSignup,
  useUserShip,
  VolunteerSignupFormData,
  Ship,
} from "@/hooks/usePublicVolunteer";
import { ShipSearch } from "./ShipSearch";

interface VolunteerSignupModalProps {
  shiftId: string;
  onClose: () => void;
}

export const VolunteerSignupModal: React.FC<VolunteerSignupModalProps> = ({
  shiftId,
  onClose,
}) => {
  // Use the useAuth hook to get the current user
  const { user } = useAuth();
  const { data: shiftData, isLoading: isLoadingShift } =
    useVolunteerShift(shiftId);
  const { data: userShipData } = useUserShip();

  // Use the TanStack Query mutation pattern
  const signupMutation = useVolunteerSignup();

  const [formData, setFormData] = useState<VolunteerSignupFormData>({
    firstName: user?.displayName?.split(" ")[0] || "",
    lastName: user?.displayName?.split(" ").slice(1).join(" ") || "",
    email: user?.email || "",
    pirateName: "",
    stayingWith: userShipData?.ship?.name || "",
    stayingWithShipId: userShipData?.ship?.id || undefined,
    isDock: false,
    isLandGrant: false,
    landGrantCredit: "",
    landGrantCreditShipId: undefined,
    checkedAutofill: false,
    pronouns: "",
    addToDeedsLottery: false,
    emailNotification: true,
    websiteNotification: true,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [stayingWithShip, setStayingWithShip] = useState<Ship | null>(userShipData?.ship || null);
  const [landGrantCreditShip, setLandGrantCreditShip] = useState<Ship | null>(null);

  // Format date for display
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "EEEE, MMMM d, yyyy");
  };

  // Pre-fill form with user data if available
  useEffect(() => {
    if (user) {
      setFormData((prev) => ({
        ...prev,
        firstName: user.displayName?.split(" ")[0] || prev.firstName,
        lastName:
          user.displayName?.split(" ").slice(1).join(" ") || prev.lastName,
        email: user.email || prev.email,
      }));
    }
  }, [user]);

  // Update form data when ship selections change
  useEffect(() => {
    if (userShipData?.ship && !stayingWithShip) {
      setStayingWithShip(userShipData.ship);
    }
  }, [userShipData, stayingWithShip]);

  useEffect(() => {
    setFormData((prev) => ({
      ...prev,
      stayingWith: stayingWithShip?.name || "",
      stayingWithShipId: stayingWithShip?.id,
    }));
  }, [stayingWithShip]);

  useEffect(() => {
    setFormData((prev) => ({
      ...prev,
      landGrantCredit: landGrantCreditShip?.name || "",
      landGrantCreditShipId: landGrantCreditShip?.id,
    }));
  }, [landGrantCreditShip]);

  // Handle form field changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));

    // Clear error for this field when changed
    if (errors[name]) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // Validate form fields
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Required fields
    if (!formData.firstName.trim())
      newErrors.firstName = "First name is required";
    if (!formData.lastName.trim()) newErrors.lastName = "Last name is required";
    if (!formData.email.trim()) newErrors.email = "Email is required";
    if (!formData.pirateName.trim())
      newErrors.pirateName = "Pirate name is required (or N/A)";
    if (!formData.stayingWith.trim() && !stayingWithShip)
      newErrors.stayingWith = "This field is required (or N/A)";

    // Email validation
    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    // Land Grant credit validation
    if ((formData.isDock || formData.isLandGrant)) {
      if (!formData.landGrantCredit.trim() && !landGrantCreditShip) {
        newErrors.landGrantCredit =
          "Please specify who gets credit for these hours";
      }
    }

    // Autofill check validation
    if (!formData.checkedAutofill) {
      newErrors.checkedAutofill =
        "Please confirm you've checked your information";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      await signupMutation.mutateAsync({
        shiftId,
        formData,
      });

      // Use the toast notification pattern for success
      toast.success("Successfully signed up for volunteer shift!");
      onClose();
    } catch (error) {
      // Use the error handling pattern
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to sign up for shift. Please try again.";

      toast.error(errorMessage);
    }
  };

  if (isLoadingShift) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
        <div className="bg-secondary-light rounded-lg shadow-xl p-6 max-w-md w-full">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
            <p className="ml-2 text-white">Loading shift details...</p>
          </div>
        </div>
      </div>
    );
  }

  const shift = shiftData?.shift;
  if (!shift) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
      <div className="bg-secondary-light rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <h2 className="text-xl font-bold text-white mb-4">
            You are signing up for... {shift.title} on{" "}
            {formatDate(shift.startTime)}
          </h2>
          <p className="text-gray-400 mb-4">
            For NS £{shift.category.payRate.toFixed(2)}/hr
          </p>

          <form onSubmit={handleSubmit} className="space-y-4">
            {/* First Name */}
            <div>
              <label className="block text-white mb-1">First Name *</label>
              <input
                type="text"
                name="firstName"
                value={formData.firstName}
                onChange={handleChange}
                className={`w-full px-3 py-2 bg-secondary border ${
                  errors.firstName ? "border-accent" : "border-gray-600"
                } rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary`}
              />
              {errors.firstName && (
                <p className="text-accent text-sm mt-1">{errors.firstName}</p>
              )}
            </div>

            {/* Last Name */}
            <div>
              <label className="block text-white mb-1">Last Name *</label>
              <input
                type="text"
                name="lastName"
                value={formData.lastName}
                onChange={handleChange}
                className={`w-full px-3 py-2 bg-secondary border ${
                  errors.lastName ? "border-accent" : "border-gray-600"
                } rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary`}
              />
              {errors.lastName && (
                <p className="text-accent text-sm mt-1">{errors.lastName}</p>
              )}
            </div>

            {/* Email */}
            <div>
              <label className="block text-white mb-1">E-mail *</label>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                className={`w-full px-3 py-2 bg-secondary border ${
                  errors.email ? "border-accent" : "border-gray-600"
                } rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary`}
              />
              {errors.email && (
                <p className="text-accent text-sm mt-1">{errors.email}</p>
              )}
            </div>

            {/* Pronouns */}
            <div>
              <label className="block text-white mb-1">
                Pronouns/Preferred Name
              </label>
              <input
                type="text"
                name="pronouns"
                value={formData.pronouns}
                onChange={handleChange}
                className="w-full px-3 py-2 bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary"
              />
            </div>

            {/* Pirate Name */}
            <div>
              <label className="block text-white mb-1">
                Pirate Name (Don't have one yet? Say "N/A") *
              </label>
              <input
                type="text"
                name="pirateName"
                value={formData.pirateName}
                onChange={handleChange}
                className={`w-full px-3 py-2 bg-secondary border ${
                  errors.pirateName ? "border-accent" : "border-gray-600"
                } rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary`}
              />
              {errors.pirateName && (
                <p className="text-accent text-sm mt-1">{errors.pirateName}</p>
              )}
            </div>

            {/* Staying With - Ship Search */}
            <div>
              <label className="block text-white mb-1">
                Who you are staying with, If Open Camping say "N/A" *
              </label>
              <ShipSearch
                value={stayingWithShip}
                onChange={setStayingWithShip}
                placeholder="Search for ship or type N/A"
                allowNone={true}
                error={errors.stayingWith}
              />
              {errors.stayingWith && (
                <p className="text-accent text-sm mt-1">{errors.stayingWith}</p>
              )}
            </div>

            {/* Land Grant/Dock */}
            <div>
              <label className="block text-white mb-2">
                Land Grant/Dock - Are these Land Grant/Dock hours? (click yes or
                no. Unsure? Click "no")
              </label>
              <div className="flex space-x-4">
                <div className="flex items-center">
                  <label className="flex items-center text-white">
                    <span className="mr-2">Dock:</span>
                    <input
                      type="checkbox"
                      name="isDock"
                      checked={formData.isDock}
                      onChange={handleChange}
                      className="h-4 w-4 rounded border-gray-600 text-primary focus:ring-primary"
                    />
                  </label>
                </div>
                <div className="flex items-center">
                  <label className="flex items-center text-white">
                    <span className="mr-2">Land Grant:</span>
                    <input
                      type="checkbox"
                      name="isLandGrant"
                      checked={formData.isLandGrant}
                      onChange={handleChange}
                      className="h-4 w-4 rounded border-gray-600 text-primary focus:ring-primary"
                    />
                  </label>
                </div>
              </div>
            </div>

            {/* Land Grant Credit */}
            <div
              className={
                !formData.isDock && !formData.isLandGrant ? "opacity-50" : ""
              }
            >
              <label className="block text-white mb-1">
                Who gets credit for your Land Grant/Dock hours? (N/A if not Land
                Grant Hours)
              </label>
              <ShipSearch
                value={landGrantCreditShip}
                onChange={setLandGrantCreditShip}
                placeholder="Search for ship or type N/A"
                allowNone={true}
                disabled={!formData.isDock && !formData.isLandGrant}
                error={errors.landGrantCredit}
              />
              {errors.landGrantCredit && (
                <p className="text-accent text-sm mt-1">
                  {errors.landGrantCredit}
                </p>
              )}
            </div>

            {/* Deeds Lottery */}
            <div>
              <label className="flex items-center text-white">
                <input
                  type="checkbox"
                  name="addToDeedsLottery"
                  checked={formData.addToDeedsLottery}
                  onChange={handleChange}
                  className="mr-2 h-4 w-4 rounded border-gray-600 text-primary focus:ring-primary"
                />
                Add these hours to my Deeds Card Lottery
              </label>
            </div>

            {/* Notifications */}
            <div className="space-y-2">
              <label className="block text-white">Notifications</label>
              <div className="flex flex-col space-y-2">
                <label className="flex items-center text-white">
                  <input
                    type="checkbox"
                    name="emailNotification"
                    checked={formData.emailNotification}
                    onChange={handleChange}
                    className="mr-2 h-4 w-4 rounded border-gray-600 text-primary focus:ring-primary"
                  />
                  Get notified by email 24 hours before
                </label>
                <label className="flex items-center text-white">
                  <input
                    type="checkbox"
                    name="websiteNotification"
                    checked={formData.websiteNotification}
                    onChange={handleChange}
                    className="mr-2 h-4 w-4 rounded border-gray-600 text-primary focus:ring-primary"
                  />
                  Get a notification on the website 24 hours before
                </label>
              </div>
            </div>

            {/* Autofill Check */}
            <div>
              <label className="flex items-center text-white">
                <input
                  type="checkbox"
                  name="checkedAutofill"
                  checked={formData.checkedAutofill}
                  onChange={handleChange}
                  className={`mr-2 h-4 w-4 rounded border-gray-600 text-primary focus:ring-primary ${
                    errors.checkedAutofill ? "border-accent" : ""
                  }`}
                />
                Did you check to make sure Autofill hasn't entered garbage? *
              </label>
              {errors.checkedAutofill && (
                <p className="text-accent text-sm mt-1">
                  {errors.checkedAutofill}
                </p>
              )}
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <Button type="button" onClick={onClose} variant="secondary">
                Cancel
              </Button>
              <Button
                type="submit"
                variant="primary"
                disabled={signupMutation.isPending}
              >
                {signupMutation.isPending ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                    Signing Up...
                  </>
                ) : (
                  "Sign Up"
                )}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};
