"use client";

import React, { useState } from "react";
import { Button } from "@bank-of-styx/ui";
import toast from "react-hot-toast";

interface GeneratedPasswordDisplayProps {
  password: string;
  onDone: () => void;
}

export const GeneratedPasswordDisplay = ({
  password,
  onDone,
}: GeneratedPasswordDisplayProps) => {
  const [copied, setCopied] = useState(false);

  const handleCopyPassword = () => {
    navigator.clipboard
      .writeText(password)
      .then(() => {
        setCopied(true);
        toast.success("Password copied to clipboard");
      })
      .catch(() => {
        toast.error("Failed to copy password");
      });
  };

  return (
    <div className="space-y-4">
      <div className="mb-4">
        <h3 className="text-lg font-medium text-white mb-2">
          Your Generated Password
        </h3>
        <p className="text-sm text-gray-300">
          We've generated a secure password for your account. This password will
          expire in 24 hours. Please copy it now and change it after logging in.
        </p>
      </div>

      <div className="p-4 bg-secondary border border-gray-600 rounded-md">
        <div className="flex justify-between items-center">
          <div className="font-mono text-lg text-primary-light">{password}</div>
          <Button
            type="button"
            variant={copied ? "outline" : "primary"}
            size="sm"
            onClick={handleCopyPassword}
          >
            {copied ? "Copied!" : "Copy"}
          </Button>
        </div>
      </div>

      <div className="mt-2 text-sm text-gray-400">
        <p>A copy of this password has been sent to your email.</p>
        <p className="mt-1">
          For security reasons, we recommend changing this password as soon as
          possible.
        </p>
      </div>

      <div className="flex justify-end mt-4">
        <Button type="button" variant="primary" onClick={onDone}>
          Done
        </Button>
      </div>
    </div>
  );
};
