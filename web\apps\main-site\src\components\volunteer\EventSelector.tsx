"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import fetchClient from "@/lib/fetchClient";

interface Event {
  id: string;
  name: string;
  startDate: string;
  endDate: string;
  location: string | null;
}

interface EventSelectorProps {
  onEventSelect?: (eventId: string) => void;
  className?: string;
  initialEventId?: string;
}

export const EventSelector: React.FC<EventSelectorProps> = ({
  onEventSelect,
  className = "",
  initialEventId = "",
}) => {
  const [events, setEvents] = useState<Event[]>([]);
  const [selectedEventId, setSelectedEventId] =
    useState<string>(initialEventId);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  // Fetch events when component mounts
  useEffect(() => {
    const fetchEvents = async () => {
      try {
        setIsLoading(true);
        console.log("EventSelector: Fetching events...");

        // Add a timestamp to prevent caching
        const timestamp = new Date().getTime();
        const data = await fetchClient.get<{ events: Event[] }>(
          `/api/volunteer/events`,
          {
            params: { t: timestamp.toString() },
          },
        );
        console.log("EventSelector: Received data:", data);

        if (data.events && Array.isArray(data.events)) {
          console.log(`EventSelector: Found ${data.events.length} events`);
          if (data.events.length > 0) {
            console.log("EventSelector: First event:", data.events[0]);
          } else {
            console.log("EventSelector: No events returned from API");
          }
          setEvents(data.events);
        } else {
          console.warn("EventSelector: No events array in response:", data);
          setEvents([]);
        }

        setError(null);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : String(err);
        setError(`Error loading events: ${errorMessage}`);
        console.error("EventSelector: Error fetching events:", err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchEvents();
  }, []);

  // Update selectedEventId when initialEventId changes
  useEffect(() => {
    if (initialEventId && initialEventId !== selectedEventId) {
      console.log(
        "EventSelector: Updating selected event ID from prop:",
        initialEventId,
      );
      setSelectedEventId(initialEventId);
    }
  }, [initialEventId, selectedEventId]);

  // Handle event selection
  const handleEventChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const eventId = e.target.value;
    setSelectedEventId(eventId);

    if (onEventSelect) {
      onEventSelect(eventId);
    } else if (eventId) {
      // If no custom handler is provided, navigate to the categories page for this event
      router.push(`/volunteer/dashboard/categories?eventId=${eventId}`);
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  return (
    <div className={`${className}`}>
      <div className="bg-secondary-light rounded-lg shadow-md p-4 border border-gray-600">
        <h2 className="text-lg font-semibold mb-4 text-white">Select Event</h2>

        {isLoading ? (
          <div className="flex items-center justify-center h-12">
            <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-primary"></div>
          </div>
        ) : error ? (
          <div className="text-accent p-2 rounded-md bg-secondary border border-accent">
            {error}
          </div>
        ) : events.length === 0 ? (
          <div className="text-gray-400 p-2">
            No events available. Please create an event first.
          </div>
        ) : (
          <div className="relative">
            <select
              className="w-full px-4 py-2 bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary appearance-none"
              value={selectedEventId}
              onChange={handleEventChange}
            >
              <option value="">Select an event</option>
              {events.map((event) => (
                <option key={event.id} value={event.id}>
                  {event.name} ({formatDate(event.startDate)})
                </option>
              ))}
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-white">
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                ></path>
              </svg>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
