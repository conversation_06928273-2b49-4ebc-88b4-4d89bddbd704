import fetchClient from "@/lib/fetchClient";
import { EventStatsResponse } from "@/types/eventStats";

export class EventStatsService {
  /**
   * Fetch comprehensive event statistics
   */
  static async fetchEventStats(eventId: string): Promise<EventStatsResponse> {
    try {
      const response = await fetchClient.get<EventStatsResponse>(
        `/api/admin/events/${eventId}/stats`
      );
      return response;
    } catch (error) {
      console.error("Error fetching event stats:", error);
      throw new Error("Failed to fetch event statistics");
    }
  }

  /**
   * Format currency for display
   */
  static formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  }

  /**
   * Format percentage for display
   */
  static formatPercentage(value: number): string {
    return `${(value * 100).toFixed(1)}%`;
  }

  /**
   * Format date for display
   */
  static formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  }

  /**
   * Format date and time for display
   */
  static formatDateTime(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  }

  /**
   * Calculate ticket utilization percentage
   */
  static calculateTicketUtilization(soldTickets: number, totalTickets: number): number {
    if (totalTickets === 0) return 0;
    return (soldTickets / totalTickets) * 100;
  }

  /**
   * Get status color for ticket utilization
   */
  static getUtilizationColor(percentage: number): string {
    if (percentage >= 90) return "text-green-600";
    if (percentage >= 70) return "text-yellow-600";
    if (percentage >= 40) return "text-orange-600";
    return "text-red-600";
  }

  /**
   * Get display name for customer
   */
  static getCustomerDisplayName(customer: {
    displayName: string | null;
    username: string;
    email: string;
  }): string {
    return customer.displayName || customer.username || customer.email;
  }
}