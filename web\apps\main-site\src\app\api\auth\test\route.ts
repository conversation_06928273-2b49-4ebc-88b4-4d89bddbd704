import { NextRequest, NextResponse } from "next/server";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
export const OPTIONS = async (req: NextRequest) => {
  // Handle OPTIONS request for CORS preflight
  const response = new NextResponse(null, { status: 204 });

  response.headers.set("Access-Control-Allow-Origin", "*");
  response.headers.set(
    "Access-Control-Allow-Methods",
    "GET, POST, PUT, DELETE, OPTIONS",
  );
  response.headers.set(
    "Access-Control-Allow-Headers",
    "Content-Type, Authorization",
  );

  return response;
};

export const GET = async (req: NextRequest) => {
  // Return information about the request to help with debugging
  return NextResponse.json({
    message: "API test endpoint is working from main-site/app/api!",
    headers: Object.fromEntries(req.headers),
    url: req.url,
    method: req.method,
    time: new Date().toISOString(),
  });
};

export const POST = async (req: NextRequest) => {
  try {
    // Get the body of the request if any
    const body = await req.json();

    return NextResponse.json({
      message: "POST to test endpoint successful from main-site/app/api!",
      receivedBody: body,
      headers: Object.fromEntries(req.headers),
      url: req.url,
      method: req.method,
      time: new Date().toISOString(),
    });
  } catch (error: any) {
    return NextResponse.json(
      {
        message: "Error parsing JSON body",
        error: error?.message || String(error),
        time: new Date().toISOString(),
      },
      { status: 400 },
    );
  }
};
