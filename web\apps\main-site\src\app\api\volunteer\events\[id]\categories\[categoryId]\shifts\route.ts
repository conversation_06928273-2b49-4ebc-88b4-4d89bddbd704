import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser, userHasRole } from "@/lib/auth";
import { TicketStatus } from "@prisma/client";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// GET /api/volunteer/events/[id]/categories/[categoryId]/shifts - Get shifts for a specific category in an event
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string; categoryId: string } },
) {
  try {
    const { id: eventId, categoryId } = params;

    // Check if user is authenticated and has volunteer coordinator role
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 },
      );
    }

    const hasCoordinatorRole = await userHasRole(req, "volunteerCoordinator");
    if (!hasCoordinatorRole) {
      return NextResponse.json(
        { error: "Unauthorized - Coordinator role required" },
        { status: 403 },
      );
    }

    // Check if event exists
    const event = await prisma.event.findUnique({
      where: { id: eventId },
    });

    if (!event) {
      return NextResponse.json({ error: "Event not found" }, { status: 404 });
    }

    // Check if category exists and belongs to the event
    const category = await prisma.volunteerCategory.findFirst({
      where: {
        id: categoryId,
        eventId: eventId,
      },
    });

    if (!category) {
      return NextResponse.json(
        { error: "Category not found or does not belong to the event" },
        { status: 404 },
      );
    }

    // Get shifts for the category
    const shifts = await prisma.volunteerShift.findMany({
      where: {
        categoryId: categoryId,
        eventId: eventId,
      },
      include: {
        _count: {
          select: {
            assignments: true,
          },
        },
        assignments: {
          select: {
            status: true,
          },
        },
      },
      orderBy: {
        startTime: "asc",
      },
    });

    // Transform shifts to include statistics
    const shiftsWithStats = shifts.map((shift) => {
      const totalAssignments = shift._count.assignments;
      const completedAssignments = shift.assignments.filter(
        (assignment) => assignment.status === "completed",
      ).length;

      return {
        id: shift.id,
        title: shift.title,
        description: shift.description,
        startTime: shift.startTime,
        endTime: shift.endTime,
        location: shift.location,
        maxVolunteers: shift.maxVolunteers,
        eventId: shift.eventId,
        categoryId: shift.categoryId,
        isAutomated: shift.isAutomated,
        createdAt: shift.createdAt,
        updatedAt: shift.updatedAt,
        stats: {
          totalAssignments,
          completedAssignments,
        },
      };
    });

    return NextResponse.json(shiftsWithStats);
  } catch (error) {
    console.error("Error fetching shifts:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch shifts",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    );
  }
}

// POST /api/volunteer/events/[id]/categories/[categoryId]/shifts - Create new shift(s) for a specific category in an event
export async function POST(
  req: NextRequest,
  { params }: { params: { id: string; categoryId: string } },
) {
  try {
    const { id: eventId, categoryId } = params;
    const requestData = await req.json();

    // Check if we're receiving a batch of shifts or a single shift
    const isBatchRequest = Array.isArray(requestData);
    const shiftsToCreate = isBatchRequest ? requestData : [requestData];

    // Check if user is authenticated and has volunteer coordinator role
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 },
      );
    }

    const hasCoordinatorRole = await userHasRole(req, "volunteerCoordinator");
    if (!hasCoordinatorRole) {
      return NextResponse.json(
        { error: "Unauthorized - Coordinator role required" },
        { status: 403 },
      );
    }

    // Check if event exists
    const event = await prisma.event.findUnique({
      where: { id: eventId },
    });

    if (!event) {
      return NextResponse.json({ error: "Event not found" }, { status: 404 });
    }

    // Check if category exists and belongs to the event
    const category = await prisma.volunteerCategory.findFirst({
      where: {
        id: categoryId,
        eventId: eventId,
      },
    });

    if (!category) {
      return NextResponse.json(
        { error: "Category not found or does not belong to the event" },
        { status: 404 },
      );
    }

    // Validate and process each shift
    const validatedShifts: any[] = [];
    const errors: any[] = [];

    for (let i = 0; i < shiftsToCreate.length; i++) {
      const shift = shiftsToCreate[i];
      const {
        title,
        description,
        startTime,
        endTime,
        location,
        maxVolunteers,
      } = shift;

      // Validate required fields
      if (!title) {
        errors.push({ index: i, error: "Title is required" });
        continue;
      }

      if (!startTime || !endTime) {
        errors.push({
          index: i,
          error: "Start time and end time are required",
        });
        continue;
      }

      // Parse dates
      const parsedStartTime = new Date(startTime);
      const parsedEndTime = new Date(endTime);

      // Validate dates
      if (isNaN(parsedStartTime.getTime()) || isNaN(parsedEndTime.getTime())) {
        errors.push({ index: i, error: "Invalid date format" });
        continue;
      }

      if (parsedStartTime >= parsedEndTime) {
        errors.push({ index: i, error: "End time must be after start time" });
        continue;
      }

      // Add validated shift to the list
      validatedShifts.push({
        title,
        description,
        startTime: parsedStartTime,
        endTime: parsedEndTime,
        location,
        maxVolunteers: maxVolunteers ? parseInt(maxVolunteers.toString()) : 1,
        eventId,
        categoryId,
      });
    }

    // If there are validation errors, return them
    if (errors.length > 0) {
      return NextResponse.json(
        { error: "Validation failed", validationErrors: errors },
        { status: 400 },
      );
    }

    // Create all shifts in a transaction with automatic slot generation
    const createdShifts = await prisma.$transaction(async (tx) => {
      const shifts = [];

      for (const shiftData of validatedShifts) {
        // Create the shift
        const shift = await tx.volunteerShift.create({ data: shiftData });

        // Auto-generate volunteer slots
        if (shiftData.maxVolunteers > 0) {
          const slots = Array(shiftData.maxVolunteers)
            .fill(null)
            .map(() => ({
              shiftId: shift.id,
              status: TicketStatus.AVAILABLE,
            }));

          await tx.volunteerSlot.createMany({
            data: slots,
          });
        }

        shifts.push(shift);
      }

      return shifts;
    });

    return NextResponse.json(isBatchRequest ? createdShifts : createdShifts[0]);
  } catch (error) {
    console.error("Error creating shift(s):", error);
    return NextResponse.json(
      {
        error: "Failed to create shift(s)",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    );
  }
}
