# MySQL Setup Script for Bank of Styx

# Create the database if it doesn't exist
CREATE DATABASE IF NOT EXISTS bank_of_styx;

# Create a user with proper privileges
CREATE USER IF NOT EXISTS 'bankofstyx_user'@'localhost' IDENTIFIED BY 'choose_a_strong_password';
GRANT ALL PRIVILEGES ON bank_of_styx.* TO 'bankofstyx_user'@'localhost';
FLUSH PRIVILEGES;

# Switch to the database
USE bank_of_styx;

# Create a test user for development
# This adds a user with credentials: <EMAIL> / admin123
INSERT IGNORE INTO User (id, username, displayName, email, passwordHash, isAdmin) 
VALUES (
    'test-admin-id', 
    'admin', 
    'Admin User', 
    '<EMAIL>', 
    '$2a$10$JKkDhdhigZoh6sjO.CerM.Ho5Z5wI3F5Z0QcnO8amVLBYuKIw51Mu', 
    1
);
