import { NextRequest, NextResponse } from "next/server";
import prisma from "../../../../../lib/prisma";
import jwt from "jsonwebtoken";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
const JWT_SECRET = process.env.JWT_SECRET || "your-development-secret-key";

// Helper function to verify token
function verifyToken(token: string) {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    return null;
  }
}

export const GET = async (req: NextRequest) => {
  try {
    // Get authorization header
    const authHeader = req.headers.get("authorization");

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Extract token
    const token = authHeader.split(" ")[1];

    // Verify token
    const decoded = verifyToken(token);

    if (!decoded || typeof decoded !== "object" || !decoded.id) {
      return NextResponse.json({ error: "Invalid token" }, { status: 401 });
    }

    const userId = decoded.id as string;

    // Query the database for redeemed pay codes
    const redeemedCodes = await prisma.payCode.findMany({
      where: {
        OR: [
          // Codes created by the user that have been redeemed
          {
            createdById: userId,
            status: "redeemed",
          },
          // Codes redeemed by the user
          {
            redeemedById: userId,
          },
        ],
      },
      include: {
        createdBy: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
        redeemedBy: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Format dates as ISO strings for JSON serialization
    const formattedCodes = redeemedCodes.map((code) => ({
      ...code,
      createdAt: code.createdAt.toISOString(),
      expiresAt: code.expiresAt.toISOString(),
      redeemedAt: code.redeemedAt ? code.redeemedAt.toISOString() : null,
    }));

    return NextResponse.json(formattedCodes);
  } catch (error) {
    console.error("Error fetching redeemed pay codes:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
};
