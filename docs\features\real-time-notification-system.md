# Real-Time Notification System

## Overview
The Bank of Styx real-time notification system provides instant, persistent messaging to users across the platform using Server-Sent Events (SSE). The system manages connection lifecycle, delivers targeted notifications, handles offline scenarios, and provides comprehensive notification management with database persistence and user preferences.

## System Architecture

### Core Components
```
Notification System
├── SSE Connection Management (/api/notifications/sse)
├── Notification API (/api/notifications/)
├── Connection Store (singleton service)
├── Heartbeat Service (connection monitoring)
├── Notification Components (UI layer)
└── Database Integration (persistence)
```

### Technology Stack
- **Server-Sent Events (SSE)**: Real-time communication protocol
- **Connection Store**: In-memory connection management
- **MySQL Database**: Notification persistence via Prisma
- **React Components**: Frontend notification interface
- **JWT Authentication**: Secure connection establishment

## SSE Connection Management

### Connection Endpoint
**Location**: `/api/notifications/sse`

#### Connection Flow
```typescript
1. Authentication (JWT token via query param or header)
   ↓
2. User verification and authorization
   ↓
3. Existing connection detection and replacement
   ↓
4. New connection registration in store
   ↓
5. Initial connection message broadcast
   ↓
6. Heartbeat service integration
   ↓
7. Connection monitoring and cleanup
```

#### Authentication Methods
```typescript
// Query parameter (recommended for EventSource)
const eventSource = new EventSource('/api/notifications/sse?auth_token=' + jwt);

// Authorization header (alternative)
fetch('/api/notifications/sse', {
  headers: { 'Authorization': 'Bearer ' + jwt }
});
```

#### Connection Security
- **JWT Token Validation**: All connections require valid authentication
- **User-Connection Mapping**: One active connection per user (replaces duplicates)
- **Connection Transition**: Graceful replacement with notification to old connection
- **Automatic Cleanup**: Orphaned connection detection and removal

## Connection Store Management

### Connection Architecture
**Location**: `/lib/connectionStore.ts`

#### Connection Data Structure
```typescript
type Connection = {
  userId: string;                          // User identifier
  writer: WritableStreamDefaultWriter;     // SSE stream writer
  lastActivity: Date;                      // Last communication timestamp
  messagesSent: number;                    // Sent message counter
  messagesReceived: number;                // Received message counter
  bytesTransferred: number;                // Data transfer tracking
  errors: number;                          // Error counter
};
```

#### Key Features
- **Single Connection Per User**: Prevents duplicate connections and resource waste
- **Connection Statistics**: Comprehensive metrics tracking
- **Automatic Cleanup**: Inactive connection detection and removal
- **Error Recovery**: Failed connection handling and removal
- **Activity Monitoring**: User activity-based connection management

### Connection Management Methods

#### Adding Connections
```typescript
await connectionStore.addConnection(connectionId, userId, writer);
// Handles existing connection replacement automatically
```

#### Message Delivery
```typescript
// Send to specific user
await connectionStore.sendToUser(userId, {
  type: "transaction_update",
  title: "Payment Received",
  message: "Your account has been credited $50.00"
});

// Broadcast to all users
await connectionStore.broadcast({
  type: "system_announcement", 
  title: "Scheduled Maintenance",
  message: "System will be down for maintenance at 2 AM EST"
});
```

## Heartbeat Service

### Connection Health Monitoring
**Location**: `/lib/heartbeatService.ts`

#### Heartbeat Features
- **30-Second Intervals**: Regular connection health checks
- **Batch Processing**: Efficient message delivery to multiple users
- **Duplicate Prevention**: Service instance conflict detection
- **Inactive User Skipping**: Performance optimization
- **Connection Validation**: Automatic cleanup of broken connections

#### Heartbeat Message Format
```typescript
{
  type: "heartbeat",
  timestamp: "2025-01-07T10:30:00.000Z",
  batchId: "abc12345",
  serviceId: "service-uuid"
}
```

## Notification API System

### Core Endpoints
**Base Path**: `/api/notifications/`

#### Notification Retrieval (`GET /api/notifications`)
```typescript
// Query parameters
?limit=20           // Number of notifications to retrieve
&unreadOnly=true    // Only unread notifications
&category=transaction // Filter by notification category

// Response format
{
  id: string,
  category: string,
  type: string,
  title: string,
  message: string,
  read: boolean,
  link?: string,
  icon?: string,
  priority: "low" | "medium" | "high",
  userId: string,
  transactionId?: string,
  createdAt: string,
  updatedAt: string
}
```

#### Notification Creation (`POST /api/notifications/create`)
```typescript
// Request body
{
  userId?: string,           // Target user (admin-only for other users)
  category: string,          // notification category
  type: string,             // specific notification type
  title: string,            // notification title
  message: string,          // notification content
  link?: string,            // optional link to relevant content
  icon?: string,            // optional icon identifier
  priority?: string,        // "low", "medium", "high"
  transactionId?: string    // optional transaction reference
}
```

#### Notification Management (`PATCH /api/notifications`)
```typescript
// Mark specific notifications as read
{ ids: ["notification-id-1", "notification-id-2"] }

// Mark all notifications as read
{ all: true }
```

### Broadcast System (`POST /api/notifications/broadcast`)
- **Admin-Only Access**: Requires administrative privileges
- **System-Wide Messaging**: Broadcast to all connected users
- **Announcement Distribution**: Critical system messages

## Database Integration

### Notification Model
```sql
CREATE TABLE Notification (
  id VARCHAR(191) PRIMARY KEY,
  category VARCHAR(255) NOT NULL,        -- transaction, system, volunteer, etc.
  type VARCHAR(255) NOT NULL,            -- deposit_approved, shift_assigned, etc.
  title VARCHAR(255) NOT NULL,           -- Display title
  message TEXT NOT NULL,                 -- Notification content
  read BOOLEAN DEFAULT FALSE,            -- Read status
  link VARCHAR(255),                     -- Optional link to content
  icon VARCHAR(255),                     -- Optional icon identifier
  priority VARCHAR(50) DEFAULT 'medium', -- Priority level
  userId VARCHAR(191) NOT NULL,          -- Target user
  transactionId VARCHAR(191),            -- Optional transaction reference
  createdAt DATETIME DEFAULT NOW(),      -- Creation timestamp
  updatedAt DATETIME DEFAULT NOW() ON UPDATE NOW()
);
```

### Database Relationships
- **User → Notifications**: One-to-many relationship for user notifications
- **Transaction → Notifications**: Optional linking for financial notifications
- **Indexes**: Optimized for userId, read status, and creation timestamp queries

## Frontend Components

### Component Architecture
**Location**: `/components/notifications/`

#### NotificationPanel Component
- **Real-time Updates**: SSE connection integration
- **Notification List**: Scrollable notification history
- **Read Status Management**: Mark as read functionality
- **Category Filtering**: Filter notifications by type
- **Auto-refresh**: Real-time notification updates

#### NotificationIcon Component
- **Unread Badge**: Visual indicator for pending notifications
- **Real-time Counter**: Live unread notification count
- **Click Integration**: Opens notification panel
- **Visual States**: Different states for notification presence

#### NotificationItem Component
- **Rich Content**: Supports links, icons, and formatted messages
- **Actions**: Mark as read, delete, navigate to linked content
- **Timestamp Display**: Human-readable creation times
- **Priority Styling**: Visual differentiation by priority level

### SSE Frontend Integration
```typescript
// Establish SSE connection
const eventSource = new EventSource(`/api/notifications/sse?auth_token=${token}`);

// Handle incoming notifications
eventSource.onmessage = (event) => {
  const data = JSON.parse(event.data);
  
  switch(data.type) {
    case 'connected':
      console.log('SSE connection established');
      break;
    case 'notification':
      // Add new notification to UI
      addNotificationToUI(data);
      break;
    case 'heartbeat':
      // Update connection status
      updateConnectionStatus();
      break;
  }
};
```

## Notification Categories & Types

### Banking System Notifications
- **Category**: `transaction`
- **Types**: 
  - `deposit_approved` - Deposit request approved
  - `deposit_rejected` - Deposit request rejected  
  - `transfer_received` - Money transfer received
  - `withdrawal_processed` - Withdrawal completed
  - `balance_low` - Account balance warning

### Volunteer System Notifications
- **Category**: `volunteer`
- **Types**:
  - `shift_assigned` - Volunteer shift assignment
  - `shift_cancelled` - Shift cancellation notice
  - `payment_processed` - Volunteer payment completed
  - `hours_updated` - Volunteer hours verification

### Shopping System Notifications  
- **Category**: `shopping`
- **Types**:
  - `order_confirmed` - Order confirmation
  - `order_shipped` - Shipping notification
  - `payment_failed` - Payment processing failure
  - `hold_expired` - Cart hold expiration warning

### System Notifications
- **Category**: `system`
- **Types**:
  - `maintenance_scheduled` - Planned maintenance notice
  - `feature_update` - New feature announcements
  - `security_alert` - Security-related notifications
  - `policy_update` - Terms or policy changes

## Real-Time Features

### Instant Delivery
- **SSE Protocol**: True push notifications without polling
- **Connection Persistence**: Maintains open connections for instant delivery
- **Offline Handling**: Notifications stored in database for offline users
- **Reconnection Logic**: Automatic reconnection on connection loss

### Performance Optimization
- **Batch Heartbeats**: Efficient connection monitoring
- **Inactive User Detection**: Skip processing for inactive users
- **Connection Limits**: One connection per user to prevent resource exhaustion
- **Message Compression**: Efficient message encoding for bandwidth optimization

### Scalability Features
- **Connection Statistics**: Monitor system load and performance
- **Error Tracking**: Comprehensive error logging and recovery
- **Resource Management**: Automatic cleanup of stale connections
- **Load Balancing Ready**: Architecture supports multiple server instances

## User Preferences

### Notification Settings
**Location**: `/settings/notifications`

#### Preference Categories
- **Banking Notifications**: Transaction alerts, balance updates
- **Volunteer Notifications**: Shift assignments, payment updates  
- **Shopping Notifications**: Order updates, shipping alerts
- **System Notifications**: Maintenance notices, feature updates
- **Chat Notifications**: Communication and messaging alerts

#### User Preference Model
```typescript
interface NotificationPreferences {
  notifyTransfers: boolean;      // Money transfer notifications
  notifyDeposits: boolean;       // Deposit status notifications
  notifyWithdrawals: boolean;    // Withdrawal confirmations
  notifyNewsEvents: boolean;     // News and event announcements
  notifyAuctions: boolean;       // Auction-related notifications
  notifyChat: boolean;           // Chat and messaging notifications
  notifyAdmin: boolean;          // Administrative notifications
}
```

## Development & Testing

### Testing Endpoints
**Location**: `/api/notifications/test`

#### Test Features
- **Connection Testing**: Validate SSE connection establishment
- **Message Delivery Testing**: Verify notification delivery
- **Performance Testing**: Load testing for multiple connections
- **Error Simulation**: Test error handling and recovery

### Development Utilities
```typescript
// Test notification creation
await fetch('/api/notifications/create', {
  method: 'POST',
  headers: { 
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    category: 'system',
    type: 'test',
    title: 'Test Notification',
    message: 'This is a test notification'
  })
});

// Test broadcast functionality
await fetch('/api/notifications/broadcast', {
  method: 'POST',
  headers: { 
    'Authorization': `Bearer ${adminToken}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    category: 'system',
    type: 'announcement',
    title: 'System Test',
    message: 'Broadcasting to all users'
  })
});
```

## Operational Monitoring

### Connection Statistics
```typescript
interface ConnectionStats {
  totalConnections: number;       // Active SSE connections
  activeUsers: number;           // Unique connected users
  messagesSent: number;          // Total messages delivered
  messagesReceived: number;      // Total messages received
  bytesTransferred: number;      // Data transfer volume
  errors: number;                // Error count
  connectionsPerUser: Record<string, number>; // Connection distribution
}
```

### Performance Metrics
- **Connection Duration**: Average connection lifetime
- **Message Latency**: Time from creation to delivery
- **Heartbeat Success Rate**: Connection health indicator
- **Error Rate**: Connection failure percentage
- **Resource Usage**: Memory and CPU utilization

### System Maintenance
- **Connection Cleanup**: Automated removal of stale connections
- **Database Cleanup**: Archive old notifications periodically
- **Performance Monitoring**: Track system performance metrics
- **Error Logging**: Comprehensive error tracking and alerting

## Troubleshooting

### Common Issues

#### SSE Connection Failures
```javascript
// Check authentication token
const token = localStorage.getItem('authToken');
if (!token) {
  console.error('No authentication token available');
}

// Verify connection endpoint
const eventSource = new EventSource('/api/notifications/sse?auth_token=' + token);
eventSource.onerror = (error) => {
  console.error('SSE connection error:', error);
};
```

#### Missing Notifications
```typescript
// Check user notification preferences
const preferences = await fetch('/api/users/notification-preferences');

// Verify notification creation
await fetch('/api/notifications', { 
  headers: { 'Authorization': `Bearer ${token}` }
});
```

#### Connection Duplicates
```bash
# Check for multiple service instances
ps aux | grep node
# Look for multiple server processes

# Restart server to clear duplicate services
npm run dev
```

---

**File Locations:**
- **SSE Endpoint**: `/src/app/api/notifications/sse/route.ts`
- **Notification API**: `/src/app/api/notifications/route.ts`
- **Connection Store**: `/src/lib/connectionStore.ts`
- **Heartbeat Service**: `/src/lib/heartbeatService.ts`
- **UI Components**: `/src/components/notifications/`
- **Database Model**: `Notification` in `prisma/schema.prisma`

**Dependencies:**
- **Server-Sent Events**: Native web standard
- **Prisma ORM**: Database operations
- **JWT**: Authentication and authorization
- **React**: Frontend notification components
- **Next.js**: API endpoints and SSE handling