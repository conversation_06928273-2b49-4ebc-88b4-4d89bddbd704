import React from "react";

export interface CheckboxProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, "type"> {
  /**
   * Checkbox label
   */
  label?: string;
  /**
   * Checkbox error message
   */
  error?: boolean;
  /**
   * Checkbox helper text
   */
  helperText?: string;
}

export const Checkbox = React.forwardRef<HTMLInputElement, CheckboxProps>(
  ({ label, error, helperText, className = "", id, ...props }, ref) => {
    // Generate a random ID if not provided
    const checkboxId =
      id || `checkbox-${Math.random().toString(36).substring(2, 9)}`;

    // Error classes
    const errorClasses = error
      ? "border-accent focus:border-accent focus:ring-accent"
      : "border-gray-600 focus:border-primary focus:ring-primary";

    return (
      <div className="flex items-center">
        <input
          ref={ref}
          id={checkboxId}
          type="checkbox"
          className={`
            h-4 w-4 rounded transition-colors
            ${errorClasses}
            ${className}
            focus:outline-none focus:ring-2 focus:ring-offset-0
            disabled:bg-secondary-dark disabled:text-disabled disabled:cursor-not-allowed
            bg-[#2C2F33] border border-gray-600
          `}
          aria-invalid={error ? "true" : "false"}
          aria-describedby={
            error
              ? `${checkboxId}-error`
              : helperText
              ? `${checkboxId}-helper-text`
              : undefined
          }
          {...props}
        />
        {label && (
          <label
            htmlFor={checkboxId}
            className="ml-2 block text-sm font-medium text-white"
          >
            {label}
          </label>
        )}
        {helperText && (
          <p
            id={`${checkboxId}-helper-text`}
            className="mt-1 text-sm text-gray-400"
          >
            {helperText}
          </p>
        )}
      </div>
    );
  },
);

Checkbox.displayName = "Checkbox";
