import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser, userHasRole } from "@/lib/auth";

export const dynamic = 'force-dynamic';

// GET /api/land-steward/volunteer-requirements - Get all ship volunteer requirements
export async function GET(req: NextRequest) {
  try {
    // Check if user is authenticated and has land steward role
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 },
      );
    }

    const hasLandStewardRole = await userHasRole(req, "landSteward");
    if (!hasLandStewardRole) {
      return NextResponse.json(
        { error: "Unauthorized - Land Steward role required" },
        { status: 403 },
      );
    }

    const { searchParams } = new URL(req.url);
    const status = searchParams.get('status');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const skip = (page - 1) * limit;

    // Build where clause
    const whereClause: any = {};
    if (status && status !== 'all') {
      whereClause.status = status;
    }

    // Get ship volunteer requirements with ship and form submission details
    const [requirements, total] = await Promise.all([
      prisma.shipVolunteerRequirement.findMany({
        where: whereClause,
        include: {
          ship: {
            select: {
              id: true,
              name: true,
              captain: {
                select: {
                  id: true,
                  displayName: true,
                  email: true,
                },
              },
              _count: {
                select: {
                  members: {
                    where: { status: 'active' }
                  }
                }
              }
            },
          },
          formSubmission: {
            select: {
              id: true,
              formId: true,
              status: true,
              submittedAt: true,
              submissionData: true,
            },
          },
        },
        orderBy: [
          { status: 'asc' }, // pending first, then in_progress, then completed
          { createdAt: 'desc' }
        ],
        skip,
        take: limit,
      }),
      prisma.shipVolunteerRequirement.count({
        where: whereClause,
      }),
    ]);

    // Transform the data to include progress calculations
    const transformedRequirements = requirements.map((req: any) => {
      const completionPercentage = req.requiredHours > 0 
        ? Math.round((req.completedHours / req.requiredHours) * 100)
        : 0;
      
      const remainingHours = Math.max(0, req.requiredHours - req.completedHours);
      
      const isOverdue = req.status === 'pending' && req.createdAt < new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 30 days old

      return {
        ...req,
        completionPercentage,
        remainingHours,
        isOverdue,
        ship: {
          ...req.ship,
          memberCount: req.ship._count.members,
        },
      };
    });

    // Calculate summary stats
    const summaryStats = {
      total: total,
      pending: requirements.filter((r: any) => r.status === 'pending').length,
      inProgress: requirements.filter((r: any) => r.status === 'in_progress').length,
      completed: requirements.filter((r: any) => r.status === 'completed').length,
      overdue: transformedRequirements.filter((r: any) => r.isOverdue).length,
    };

    return NextResponse.json({
      requirements: transformedRequirements,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
      summaryStats,
    });
  } catch (error) {
    console.error("Error fetching volunteer requirements:", error);
    return NextResponse.json(
      { error: "Failed to fetch volunteer requirements" },
      { status: 500 },
    );
  }
}