"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  getPublicArticles,
  getPublicArticleBySlug,
  getPublicCategories,
  PublicNewsFilters,
} from "../services/publicNewsService";

// Query keys for public news
export const publicNewsQueryKeys = {
  articles: "publicArticles",
  article: (slug: string) => ["publicArticle", slug],
  categories: "publicCategories",
};

/**
 * Hook to fetch public articles with filtering and pagination
 */
export function usePublicArticles(filters: PublicNewsFilters = {}) {
  return useQuery({
    queryKey: [publicNewsQueryKeys.articles, filters],
    queryFn: () => getPublicArticles(filters),
  });
}

/**
 * Hook to fetch a single public article by slug
 */
export function usePublicArticle(slug: string) {
  return useQuery({
    queryKey: publicNewsQueryKeys.article(slug),
    queryFn: () => getPublicArticleBySlug(slug),
    enabled: !!slug, // Only run query if slug is provided
  });
}

/**
 * Hook to fetch public categories
 */
export function usePublicCategories() {
  return useQuery({
    queryKey: [publicNewsQueryKeys.categories],
    queryFn: () => getPublicCategories(),
  });
}
