import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser, userHasRole } from "@/lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
interface Params {
  params: {
    id: string;
  };
}

export async function GET(request: Request, { params }: Params) {
  const { id } = params;

  try {
    // Check if user is authenticated and has admin role
    const currentUser = await getCurrentUser(request);
    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    const isAdmin = await userHasRole(request, "admin");
    if (!isAdmin) {
      return NextResponse.json(
        { error: "Admin privileges required" },
        { status: 403 },
      );
    }

    // Get user by ID
    const user = await prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        username: true,
        displayName: true,
        avatar: true,
        email: true,
        isEmailVerified: true,
        isAdmin: true,
        isEditor: true,
        isBanker: true,
        isChatModerator: true,
        isVolunteerCoordinator: true,
        isLeadManager: true,
        isSalesManager: true,
        isLandSteward: true,
        createdAt: true,
      },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Transform user to match the AdminUser interface
    const transformedUser = {
      id: user.id,
      username: user.username,
      displayName: user.displayName,
      avatar: user.avatar,
      email: user.email,
      isEmailVerified: user.isEmailVerified,
      status: "active", // This would need to be mapped from your actual schema
      roles: {
        admin: user.isAdmin,
        editor: user.isEditor,
        banker: user.isBanker,
        chatModerator: user.isChatModerator,
        volunteerCoordinator: user.isVolunteerCoordinator,
        leadManager: user.isLeadManager,
        salesManager: user.isSalesManager,
        landSteward: user.isLandSteward,
      },
      createdAt: user.createdAt.toISOString(),
    };

    return NextResponse.json(transformedUser);
  } catch (error) {
    console.error("Error fetching user:", error);
    return NextResponse.json(
      { error: "Failed to fetch user" },
      { status: 500 },
    );
  }
}
