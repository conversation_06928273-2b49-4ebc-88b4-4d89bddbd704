"use client";

import React, { useRef, useState } from "react";
import { Button } from "@bank-of-styx/ui";
import { uploadAvatar } from "@/services/avatarService";

interface SimpleAvatarUploadProps {
  currentAvatar?: string;
  onSave: (avatarUrl: string) => void;
  className?: string;
}

export const SimpleAvatarUpload: React.FC<SimpleAvatarUploadProps> = ({
  currentAvatar,
  onSave,
  className = "",
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];

      // Validate file type
      if (!file.type.match("image.*")) {
        setError("Please select an image file (JPEG, PNG, etc.)");
        return;
      }

      // Validate file size (max 2MB)
      if (file.size > 2 * 1024 * 1024) {
        setError("Image size should be less than 2MB");
        return;
      }

      setError(null);
      setIsUploading(true);

      try {
        // Process and crop the image
        const croppedFile = await cropImageToSquare(file);

        // Upload the cropped image
        const uploadedImage = await uploadAvatar(croppedFile);

        // Pass the URL to the parent component
        onSave(uploadedImage.url);
      } catch (error) {
        console.error("Error processing avatar:", error);
        setError(
          error instanceof Error ? error.message : "Failed to process avatar",
        );
      } finally {
        setIsUploading(false);
      }
    }
  };

  // Function to crop image to a square
  const cropImageToSquare = (file: File): Promise<File> => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        // Create canvas for cropping
        const canvas = document.createElement("canvas");
        const ctx = canvas.getContext("2d");

        if (!ctx) {
          reject(new Error("Could not get canvas context"));
          return;
        }

        // Determine the size of the square crop (use the smaller dimension)
        const size = Math.min(img.width, img.height);

        // Set canvas size to 100x100 for a small avatar
        const outputSize = 100;
        canvas.width = outputSize;
        canvas.height = outputSize;

        // Calculate crop position (center of the image)
        const startX = (img.width - size) / 2;
        const startY = (img.height - size) / 2;

        // Draw the cropped and resized image on the canvas
        ctx.drawImage(
          img,
          startX,
          startY,
          size,
          size, // Source rectangle
          0,
          0,
          outputSize,
          outputSize, // Destination rectangle
        );

        // Convert canvas to blob
        canvas.toBlob(
          (blob) => {
            if (!blob) {
              reject(new Error("Failed to create image blob"));
              return;
            }

            // Create a File object from the blob
            const croppedFile = new File([blob], `avatar-${Date.now()}.jpg`, {
              type: "image/jpeg",
            });
            resolve(croppedFile);
          },
          "image/jpeg",
          0.9, // 90% quality
        );
      };

      img.onerror = () => {
        reject(new Error("Failed to load image"));
      };

      // Load the image from the file
      img.src = URL.createObjectURL(file);
    });
  };

  const handleButtonClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <div className={`flex flex-col items-center ${className}`}>
      {currentAvatar && (
        <div className="mb-3">
          <img
            src={currentAvatar}
            alt="Current avatar"
            className="w-16 h-16 rounded-full object-cover"
          />
        </div>
      )}

      <div className="flex items-center">
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileChange}
          className="hidden"
          accept="image/*"
        />

        <Button
          type="button"
          variant="secondary"
          size="sm"
          onClick={handleButtonClick}
          disabled={isUploading}
        >
          {isUploading ? (
            <span className="flex items-center">
              <svg
                className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              Uploading...
            </span>
          ) : (
            "Change Avatar"
          )}
        </Button>
      </div>

      {error && <div className="mt-2 text-accent text-xs">{error}</div>}

      <p className="mt-2 text-xs text-gray-400">
        Images will be automatically cropped to a square.
      </p>
    </div>
  );
};
