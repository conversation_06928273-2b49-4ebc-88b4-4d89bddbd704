"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, SearchBar } from "@bank-of-styx/ui"; 
import Link from "next/link";
import { useAuth } from "@/contexts/AuthContext";

interface Ship {
  id: string;
  name: string;
  description: string;
  slogan: string | null;
  logo: string | null;
  tags: string[];
  captain: {
    id: string;
    username: string;
    displayName: string;
    avatar: string;
  };
  memberCount: number;
  createdAt: string;
}

interface ShipsResponse {
  ships: Ship[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

const ShipCard: React.FC<{ ship: Ship; onTagClick: (tag: string) => void }> = ({ ship, onTagClick }) => {
  return (
    <div className="bg-secondary rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
      {/* Ship Logo */}
      <div className="h-48 bg-secondary-dark flex items-center justify-center">
        {ship.logo ? (
          <img
            src={ship.logo}
            alt={`${ship.name} logo`}
            className="max-h-32 max-w-32 object-contain"
          />
        ) : (
          <div className="w-24 h-24 bg-primary rounded-full flex items-center justify-center">
            <svg
              className="w-12 h-12 text-white"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        )}
      </div>

      {/* Ship Info */}
      <div className="p-4">
        <div className="flex items-start justify-between mb-2">
          <h3 className="text-xl font-bold text-white truncate">{ship.name}</h3>
          <span className="bg-primary text-white text-xs px-2 py-1 rounded-full whitespace-nowrap ml-2">
            {ship.memberCount} members
          </span>
        </div>

        {ship.slogan && (
          <p className="text-primary text-sm italic mb-2 line-clamp-1">
            "{ship.slogan}"
          </p>
        )}

        <p className="text-gray-300 text-sm line-clamp-3 mb-3">
          {ship.description}
        </p>

        {/* Tags */}
        {ship.tags && ship.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-3">
            {ship.tags.slice(0, 3).map((tag, index) => (
              <button
                key={index}
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  onTagClick(tag);
                }}
                className="bg-secondary-dark text-gray-300 hover:bg-primary hover:text-white text-xs px-2 py-1 rounded transition-colors cursor-pointer"
              >
                #{tag}
              </button>
            ))}
            {ship.tags.length > 3 && (
              <span className="bg-secondary-dark text-gray-300 text-xs px-2 py-1 rounded">
                +{ship.tags.length - 3} more
              </span>
            )}
          </div>
        )}

        {/* Captain Info */}
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <img
              src={ship.captain.avatar || "/images/avatars/default.png"}
              alt={ship.captain.displayName}
              className="w-6 h-6 rounded-full mr-2"
            />
            <span className="text-gray-400 text-sm">
              Captain: {ship.captain.displayName}
            </span>
          </div>
        </div>

        {/* Action Button */}
        <div className="mt-4">
          <Link href={`/ships/${ship.id}`}>
            <Button variant="primary" size="sm" className="w-full">
              View Ship
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default function ShipsPage() {
  const { user } = useAuth();
  const [ships, setShips] = useState<Ship[]>([]);
  const [loading, setLoading] = useState(true);
  const [search, setSearch] = useState("");
  const [sortBy, setSortBy] = useState("newest");
  const [filterTag, setFilterTag] = useState("");
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 12,
    total: 0,
    pages: 0,
  });

  const fetchShips = async (searchTerm = "", page = 1, sort = sortBy, tag = filterTag) => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        limit: pagination.limit.toString(),
      });

      if (searchTerm) {
        params.append("search", searchTerm);
      }

      if (sort) {
        params.append("sort", sort);
      }

      if (tag) {
        params.append("tag", tag);
      }

      const response = await fetch(`/api/ships?${params}`);
      if (!response.ok) {
        throw new Error("Failed to fetch ships");
      }

      const data: ShipsResponse = await response.json();
      setShips(data.ships);
      setPagination(data.pagination);
    } catch (error) {
      console.error("Error fetching ships:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchShips(search, pagination.page, sortBy, filterTag);
  }, [pagination.page, sortBy, filterTag]);

  const handleSearch = (searchTerm: string) => {
    setSearch(searchTerm);
    setPagination({ ...pagination, page: 1 });
    fetchShips(searchTerm, 1, sortBy, filterTag);
  };

  const handleClearSearch = () => {
    setSearch("");
    setPagination({ ...pagination, page: 1 });
    fetchShips("", 1, sortBy, filterTag);
  };

  const handleSortChange = (newSort: string) => {
    setSortBy(newSort);
    setPagination({ ...pagination, page: 1 });
  };

  const handleTagFilter = (tag: string) => {
    setFilterTag(tag === filterTag ? "" : tag);
    setPagination({ ...pagination, page: 1 });
  };

  const handlePageChange = (newPage: number) => {
    setPagination({ ...pagination, page: newPage });
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Ships</h1>
            <p className="text-gray-400">
              Discover and join ships in the Bank of Styx community
            </p>
          </div>
          
          {user && (
            <div className="mt-4 lg:mt-0">
              <Link href="/ships/apply">
                <Button variant="primary">
                  Apply to be a Captain
                </Button>
              </Link>
            </div>
          )}
        </div>

        {/* Search and Filters */}
        <div className="mb-6 space-y-4">
          <SearchBar
            placeholder="Search ships by name, captain, or tags..."
            onSearch={handleSearch}
            onClear={handleClearSearch}
            initialValue={search}
            showButton={true}
            buttonText="Search"
          />
          
          {/* Filters and Sort */}
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
            <div className="flex items-center gap-2">
              <label className="text-gray-300 text-sm font-medium">Sort by:</label>
              <select
                value={sortBy}
                onChange={(e) => handleSortChange(e.target.value)}
                className="bg-secondary border border-secondary-dark rounded px-3 py-1 text-white text-sm"
              >
                <option value="newest">Newest</option>
                <option value="oldest">Oldest</option>
                <option value="name">Name (A-Z)</option>
                <option value="members">Most Members</option>
              </select>
            </div>
            
            {filterTag && (
              <div className="flex items-center gap-2">
                <span className="text-gray-300 text-sm">Filtering by tag:</span>
                <div className="flex items-center gap-1 bg-primary/20 text-primary px-2 py-1 rounded text-sm">
                  #{filterTag}
                  <button
                    onClick={() => handleTagFilter(filterTag)}
                    className="ml-1 hover:text-white transition-colors"
                  >
                    ×
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Ships Grid */}
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {Array.from({ length: 8 }).map((_, index) => (
              <div
                key={index}
                className="bg-secondary rounded-lg shadow-lg overflow-hidden animate-pulse"
              >
                <div className="h-48 bg-secondary-dark"></div>
                <div className="p-4">
                  <div className="h-6 bg-secondary-dark rounded mb-2"></div>
                  <div className="h-4 bg-secondary-dark rounded mb-2"></div>
                  <div className="h-20 bg-secondary-dark rounded mb-3"></div>
                  <div className="flex gap-2 mb-3">
                    <div className="h-6 w-16 bg-secondary-dark rounded"></div>
                    <div className="h-6 w-12 bg-secondary-dark rounded"></div>
                  </div>
                  <div className="h-8 bg-secondary-dark rounded"></div>
                </div>
              </div>
            ))}
          </div>
        ) : ships.length > 0 ? (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
              {ships.map((ship) => (
                <ShipCard key={ship.id} ship={ship} onTagClick={handleTagFilter} />
              ))}
            </div>

            {/* Pagination */}
            {pagination.pages > 1 && (
              <div className="flex justify-center items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={pagination.page === 1}
                >
                  Previous
                </Button>
                
                {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
                  const pageNum = i + 1;
                  return (
                    <Button
                      key={pageNum}
                      variant={pagination.page === pageNum ? "primary" : "outline"}
                      size="sm"
                      onClick={() => handlePageChange(pageNum)}
                    >
                      {pageNum}
                    </Button>
                  );
                })}
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={pagination.page === pagination.pages}
                >
                  Next
                </Button>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-12">
            <div className="w-24 h-24 bg-secondary rounded-full flex items-center justify-center mx-auto mb-4">
              <svg
                className="w-12 h-12 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">No ships found</h3>
            <p className="text-gray-400 mb-4">
              {search
                ? `No ships match your search for "${search}"`
                : "No ships have been created yet"}
            </p>
            {user && (
              <Link href="/ships/apply">
                <Button variant="primary">
                  Be the first Captain
                </Button>
              </Link>
            )}
          </div>
        )}
      </div>
    </div>
  );
}