/**
 * Development environment configuration
 * Helps reduce console noise and unnecessary re-renders during development
 */

// Suppress common development warnings that can spam the console
if (typeof window !== "undefined" && process.env.NODE_ENV === "development") {
  // Store original console methods
  const originalWarn = console.warn;
  const originalLog = console.log;

  // Filter out specific warnings that create noise
  console.warn = (...args) => {
    const message = args.join(" ");

    // Suppress preload warnings
    if (message.includes("was preloaded using link preload but not used")) {
      return;
    }

    // Suppress DOMNodeInserted warnings
    if (message.includes("DOMNodeInserted")) {
      return;
    }

    // Suppress React Hook warnings during development (temporary)
    if (message.includes("<PERSON><PERSON> has detected a change in the order of Hooks")) {
      return;
    }

    if (
      message.includes("Cannot update a component") &&
      message.includes("while rendering a different component")
    ) {
      return;
    }

    // Call original warn for other messages
    originalWarn.apply(console, args);
  };

  // Store original console.error
  const originalError = console.error;

  // Filter console.error as well
  console.error = (...args) => {
    const message = args.join(" ");

    // Suppress hook-related errors during development
    if (
      message.includes("Rendered more hooks than during the previous render")
    ) {
      return;
    }

    if (
      message.includes("Cannot update a component") &&
      message.includes("while rendering a different component")
    ) {
      return;
    }

    // Call original error for other messages
    originalError.apply(console, args);
  };

  // Optionally reduce console.log noise in development
  console.log = (...args) => {
    const message = args.join(" ");

    // Suppress repetitive Fast Refresh messages
    if (message.includes("[Fast Refresh] rebuilding")) {
      return;
    }

    // Suppress excessive category logs unless specifically needed
    if (
      message.includes("CategorySelector received categories") &&
      !window.location.search.includes("debug=categories")
    ) {
      return;
    }

    // Call original log for other messages
    originalLog.apply(console, args);
  };
}

export {};
