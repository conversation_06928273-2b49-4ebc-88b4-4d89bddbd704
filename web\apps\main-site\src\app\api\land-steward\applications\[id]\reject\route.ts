import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth";

export const dynamic = 'force-dynamic';

export async function POST(request: Request, { params }: { params: { id: string } }) {
  try {
    const currentUser = await getCurrentUser(request);

    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    if (!currentUser.isAdmin && !currentUser.isLandSteward) {
      return NextResponse.json(
        { error: "Land Steward or Admin access required" },
        { status: 403 }
      );
    }

    const applicationId = params.id;
    const { reason } = await request.json();

    if (!reason || reason.trim().length < 10) {
      return NextResponse.json(
        { error: "Rejection reason is required (minimum 10 characters)" },
        { status: 400 }
      );
    }

    // Get the application
    const application = await prisma.captainApplication.findUnique({
      where: { id: applicationId },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
      },
    });

    if (!application) {
      return NextResponse.json(
        { error: "Application not found" },
        { status: 404 }
      );
    }

    if (application.status !== 'pending') {
      return NextResponse.json(
        { error: "Application has already been reviewed" },
        { status: 400 }
      );
    }

    // Update application status
    const updatedApplication = await prisma.captainApplication.update({
      where: { id: applicationId },
      data: {
        status: 'rejected',
        rejectionReason: reason.trim(),
        reviewedAt: new Date(),
        reviewedById: currentUser.id,
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      message: "Application rejected successfully",
      application: {
        id: updatedApplication.id,
        shipName: updatedApplication.shipName,
        status: updatedApplication.status,
        rejectionReason: updatedApplication.rejectionReason,
        reviewedAt: updatedApplication.reviewedAt?.toISOString(),
        user: updatedApplication.user,
      },
    });
  } catch (error) {
    console.error("Error rejecting captain application:", error);
    return NextResponse.json(
      { error: "Failed to reject application" },
      { status: 500 }
    );
  }
}