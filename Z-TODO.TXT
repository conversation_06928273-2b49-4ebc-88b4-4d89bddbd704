**Admin Dashboard**


**Bread crumbs**
- When you're an admin and the admin dashboard the "Admin" Breadcrumb needs to redirect back to the admin dashboard Currently it's a 404 http://192.168.1.87:3000/admin 
- http://192.168.1.87:3000/sales
- Change the breadcrumb name for the events to the event title The same for news articles and products and everything else We should avoid showing UUID links for clickable areas

**Sales & Events**
- Better event management page and information shown specifically about tickets sold revenue and such

- Add a buy ticket button to the event page
- Revamp the date selection and time selection UI elements

**Content Management**


**Tickets**
- Make the ticket tracking system more robust
- Add the availability on the ticket page for the product card



**Bank & Economy**
- Figure out what deposit will be as well as withdrawal
- Remove the deposit page for the nation and change the withdrawal page to a better word a large currency token withdrawal



**UI/UX**
Done- Event edit page color update
- Auto refresh after edit of article Or other card items




**Seed Data**
- Make a set of base categories In the seed And the ability to filter by event in the shop
- Add five users in the seed info with basic login and email

 *Volunteers*
- Add a template for shifts and volunteer positions That automatically creates shifts based on dates of event

**Payments & Orders**
- Add a payment successful page that shows the order number and ETC . ..
- Figure out how to run this Stripe CLI again

**Cashier Tools**
- Better cashier tools for transferring money between users or in and out

**Notifications**
- Revamp the notification system with newer notifications for specific events

**Image Update**
- Resize the image on the event card

The volunteer for a person that does not have a ship or does but that checks the box and wants to associate the hours to a different ship We need to be able to accommodate giving those hours to that ship without having a member attached to those hours maybe a ghost member that all ships so that we can minus there hours remaining for the volunteer total.
In the volunteer sign up sheet if they are associated with a ship already it should pre populate that 
Actually test the volunteer hour ship management system and sh have I volunteered work the shift and complete it Check and see if the hours changed in the dashboards
and the Land Steward dashboard we need to break out each tab into their own individual page Or at least each tab its own separate file 