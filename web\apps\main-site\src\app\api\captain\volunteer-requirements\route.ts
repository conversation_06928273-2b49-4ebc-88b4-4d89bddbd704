import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth";
import prisma from "@/lib/prisma";

export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Find the ship this user captains
    const ship = await prisma.ship.findFirst({
      where: {
        captainId: user.id,
      },
    });

    if (!ship) {
      return NextResponse.json(
        { error: "You are not a captain of any ship" },
        { status: 403 }
      );
    }

    // Get active volunteer requirements for this ship
    const requirements = await prisma.shipVolunteerRequirement.findMany({
      where: {
        shipId: ship.id,
        status: {
          in: ['pending', 'in_progress']
        }
      },
      include: {
        formSubmission: {
          include: {
            form: {
              include: {
                event: {
                  select: {
                    id: true,
                    name: true,
                    startDate: true,
                  }
                }
              }
            }
          }
        },
        ship: {
          select: {
            id: true,
            name: true,
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    return NextResponse.json(requirements);
  } catch (error: any) {
    console.error("Error fetching volunteer requirements:", error);
    return NextResponse.json(
      { error: "Failed to fetch volunteer requirements" },
      { status: 500 }
    );
  }
}