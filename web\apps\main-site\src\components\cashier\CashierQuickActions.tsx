import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";

export const CashierQuickActions: React.FC = () => {
  const pathname = usePathname();

  // Function to check if a path is active
  const isActive = (path: string) => {
    if (path === "/cashier/dashboard") {
      return pathname === "/cashier/dashboard";
    }
    return pathname.startsWith(path);
  };

  // Define all navigation items
  const navItems = [
    {
      name: "Dashboard",
      path: "/cashier/dashboard",
      icon: "M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6",
    },
    {
      name: "Deposits",
      path: "/cashier/dashboard/deposits",
      icon: "M12 4v16m8-8H4",
    },
    {
      name: "Withdrawals",
      path: "/cashier/dashboard/withdrawals",
      icon: "M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z",
    },
    {
      name: "Transactions",
      path: "/cashier/dashboard/transactions",
      icon: "M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2",
    },
    {
      name: "Members",
      path: "/cashier/dashboard/members",
      icon: "M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z",
    },
    {
      name: "Ledger",
      path: "/cashier/dashboard/ledger",
      icon: "M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z",
    },
    {
      name: "Statistics",
      path: "/cashier/dashboard/statistics",
      icon: "M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z",
    },
  ];

  // Define different sets of actions for different screen sizes
  const largeScreenActions = navItems.slice(0, 4); // First 4 items for large screens
  const mediumScreenActions = navItems; // All items for medium screens
  const mobileActions = navItems; // All items for mobile screens

  // Action button component
  const ActionButton = ({
    action,
  }: {
    action: { name: string; path: string; icon: string };
  }) => {
    return (
      <Link
        href={action.path}
        className={`
          flex flex-col items-center justify-center p-1.5 sm:p-3 rounded-md transition-colors
          ${
            isActive(action.path)
              ? "bg-primary text-white font-medium"
              : "bg-secondary-dark text-white hover:bg-secondary-light"
          }
        `}
      >
        <svg
          className="w-4 h-4 sm:w-6 sm:h-6 mb-0.5 sm:mb-2"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d={action.icon}
          />
        </svg>
        <span className="text-[10px] sm:text-sm font-medium text-center leading-tight">
          {action.name}
        </span>
      </Link>
    );
  };

  return (
    <div className="bg-secondary rounded-lg shadow-md p-3 sm:p-4 border border-gray-600">
      <h2 className="text-base sm:text-lg font-semibold mb-2 sm:mb-4 text-white">
        Quick Actions
      </h2>

      {/* Large Desktop view (896px+) - Show only the first 4 actions in 2x2 grid */}
      <div className="hidden min-[896px]:grid min-[896px]:grid-cols-2 gap-3">
        {largeScreenActions.map((action) => (
          <ActionButton key={action.name} action={action} />
        ))}
      </div>

      {/* Medium view (768px-896px) - Show all actions in a 3-column grid */}
      <div className="hidden md:grid md:grid-cols-3 min-[896px]:hidden gap-3">
        {mediumScreenActions.map((action) => (
          <ActionButton key={action.name} action={action} />
        ))}
      </div>

      {/* Mobile view (below 768px) - Show all actions in a 3-column grid */}
      <div className="grid grid-cols-3 gap-1.5 md:hidden">
        {mobileActions.map((action) => (
          <ActionButton key={action.name} action={action} />
        ))}
      </div>
    </div>
  );
};
