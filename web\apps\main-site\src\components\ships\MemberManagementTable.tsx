"use client";

import { useState } from 'react';
import Image from 'next/image';
import { <PERSON><PERSON>, Modal } from '@bank-of-styx/ui';

interface ShipRole {
  id: string;
  name: string;
  description?: string;
}

interface ShipMember {
  id: string;
  role: string;
  status: string;
  joinedAt: string;
  leftAt?: string;
  roleId?: string;
  customRole?: ShipRole;
  user: {
    id: string;
    username: string;
    displayName: string;
    avatar: string;
    email: string;
  };
}

interface MemberManagementTableProps {
  members: ShipMember[];
  roles: ShipRole[];
  captainId: string;
  onUpdateMemberRole: (userId: string, roleId: string | null, roleName: string) => Promise<void>;
  onRemoveMember: (userId: string, reason?: string) => Promise<void>;
  loading?: boolean;
}

export default function MemberManagementTable({
  members,
  roles,
  captainId,
  onUpdateMemberRole,
  onRemoveMember,
  loading = false
}: MemberManagementTableProps) {
  const [selectedMember, setSelectedMember] = useState<ShipMember | null>(null);
  const [showRemoveModal, setShowRemoveModal] = useState(false);
  const [showRoleModal, setShowRoleModal] = useState(false);
  const [selectedRole, setSelectedRole] = useState<string>('');
  const [actionLoading, setActionLoading] = useState(false);

  const activeMembers = Array.isArray(members) ? members.filter(member => member.status === 'active') : [];

  const handleUpdateRole = async () => {
    if (!selectedMember) return;

    setActionLoading(true);
    try {
      const roleId = selectedRole === 'Member' ? null : selectedRole;
      const roleName = selectedRole === 'Member' ? 'Member' : 
        roles.find(r => r.id === selectedRole)?.name || 'Member';
      
      await onUpdateMemberRole(selectedMember.user.id, roleId, roleName);
      setShowRoleModal(false);
      setSelectedMember(null);
      setSelectedRole('');
    } catch (error) {
      console.error('Error updating member role:', error);
    } finally {
      setActionLoading(false);
    }
  };

  const handleRemoveMember = async () => {
    if (!selectedMember) return;

    setActionLoading(true);
    try {
      await onRemoveMember(selectedMember.user.id);
      setShowRemoveModal(false);
      setSelectedMember(null);
    } catch (error) {
      console.error('Error removing member:', error);
    } finally {
      setActionLoading(false);
    }
  };

  const openRoleModal = (member: ShipMember) => {
    setSelectedMember(member);
    setSelectedRole(member.roleId || 'Member');
    setShowRoleModal(true);
  };

  const openRemoveModal = (member: ShipMember) => {
    setSelectedMember(member);
    setShowRemoveModal(true);
  };

  if (loading) {
    return (
      <div className="bg-secondary rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Ship Members</h3>
        <div className="animate-pulse space-y-4">
          {[1, 2, 3].map(i => (
            <div key={i} className="flex items-center space-x-4">
              <div className="w-10 h-10 bg-secondary-dark rounded-full"></div>
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-secondary-dark rounded w-1/4"></div>
                <div className="h-3 bg-secondary-dark rounded w-1/6"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="bg-secondary rounded-lg shadow">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-600">
            <thead className="bg-secondary-dark">
              <tr>
                <th className="px-3 sm:px-4 py-2 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Member
                </th>
                <th className="px-3 sm:px-4 py-2 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Role
                </th>
                <th className="px-3 sm:px-4 py-2 text-left text-xs font-medium text-gray-400 uppercase tracking-wider hidden sm:table-cell">
                  Joined
                </th>
                <th className="px-3 sm:px-4 py-2 text-right text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-secondary divide-y divide-gray-600">
              {activeMembers.map((member) => (
                <tr key={member.id} className="hover:bg-secondary-light">
                  <td className="px-3 sm:px-4 py-2 sm:py-3 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <Image
                          className="h-10 w-10 rounded-full object-cover"
                          src={member.user.avatar}
                          alt={member.user.displayName}
                          width={40}
                          height={40}
                        />
                      </div>
                      <div className="ml-3 sm:ml-4">
                        <div className="text-sm font-medium text-white">
                          {member.user.displayName}
                        </div>
                        <div className="text-xs sm:text-sm text-gray-400">
                          @{member.user.username}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-3 sm:px-4 py-2 sm:py-3 whitespace-nowrap">
                    <div className="flex items-center">
                      {member.user.id === captainId ? (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-900/30 text-yellow-400 border border-yellow-700">
                          Captain
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary/20 text-primary border border-primary/50">
                          {member.customRole?.name || member.role}
                        </span>
                      )}
                    </div>
                  </td>
                  <td className="px-3 sm:px-4 py-2 sm:py-3 whitespace-nowrap text-xs sm:text-sm text-gray-400 hidden sm:table-cell">
                    {new Date(member.joinedAt).toLocaleDateString()}
                  </td>
                  <td className="px-3 sm:px-4 py-2 sm:py-3 whitespace-nowrap text-right text-sm font-medium">
                    {member.user.id !== captainId && (
                      <div className="flex justify-end space-x-1 sm:space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => openRoleModal(member)}
                          className="text-xs px-2 py-1 sm:px-3 sm:py-1.5"
                        >
                          <span className="hidden sm:inline">Change Role</span>
                          <span className="sm:hidden">Role</span>
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => openRemoveModal(member)}
                          className="text-xs px-2 py-1 sm:px-3 sm:py-1.5 text-red-400 hover:text-red-300 hover:bg-red-900/30 border-red-700"
                        >
                          <span className="hidden sm:inline">Remove</span>
                          <span className="sm:hidden">✕</span>
                        </Button>
                      </div>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {loading && activeMembers.length === 0 && (
          <div className="text-center py-6">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto mb-3"></div>
            <p className="text-gray-400 text-sm">Loading members...</p>
          </div>
        )}

        {!loading && activeMembers.length === 0 && (
          <div className="text-center py-8 text-gray-400">
            No members found. Invite some members to get started!
          </div>
        )}
      </div>

      {/* Role Change Modal */}
      <Modal
        isOpen={showRoleModal}
        onClose={() => setShowRoleModal(false)}
        size="md"
      >
        <div className="p-6">
          <h3 className="text-lg font-semibold text-white mb-4">
            Change Role for {selectedMember?.user.displayName}
          </h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Select Role
              </label>
              <select
                value={selectedRole}
                onChange={(e) => setSelectedRole(e.target.value)}
                className="w-full bg-input border border-secondary-dark rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="Member">Member (Default)</option>
                {Array.isArray(roles) ? roles.map((role) => (
                  <option key={role.id} value={role.id}>
                    {role.name}
                  </option>
                )) : null}
              </select>
            </div>
          </div>

          <div className="flex justify-end space-x-3 mt-6">
            <Button
              variant="outline"
              onClick={() => setShowRoleModal(false)}
              disabled={actionLoading}
            >
              Cancel
            </Button>
            <Button
              onClick={handleUpdateRole}
              disabled={actionLoading}
            >
              {actionLoading ? 'Updating...' : 'Update Role'}
            </Button>
          </div>
        </div>
      </Modal>

      {/* Remove Member Modal */}
      <Modal
        isOpen={showRemoveModal}
        onClose={() => setShowRemoveModal(false)}
        size="md"
      >
        <div className="p-6">
          <h3 className="text-lg font-semibold mb-4 text-red-400">
            Remove Member
          </h3>
          
          <p className="text-gray-300 mb-6">
            Are you sure you want to remove <strong className="text-white">{selectedMember?.user.displayName}</strong> 
            from the ship? This action cannot be undone.
          </p>

          <div className="flex justify-end space-x-3">
            <Button
              variant="outline"
              onClick={() => setShowRemoveModal(false)}
              disabled={actionLoading}
            >
              Cancel
            </Button>
            <Button
              onClick={handleRemoveMember}
              disabled={actionLoading}
              className="bg-red-600 hover:bg-red-700 text-white border-red-600"
            >
              {actionLoading ? 'Removing...' : 'Remove Member'}
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
}