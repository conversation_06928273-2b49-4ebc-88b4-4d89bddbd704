"use client";

import React, { ReactNode, useEffect, useState } from "react";
import { useRouter, usePathname } from "next/navigation";
import Link from "next/link";
import { useAuth } from "../../contexts/AuthContext";
import { Toaster } from "react-hot-toast";

interface NewsDashboardLayoutProps {
  children: ReactNode;
  dashboardData?: any;
}

export const NewsDashboardLayout: React.FC<NewsDashboardLayoutProps> = ({
  children,
  dashboardData,
}) => {
  const { user, isLoading: authLoading } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const [isAuthorized, setIsAuthorized] = useState(false);

  // Navigation items
  const navItems = [
    { name: "Dashboard", path: "/news/dashboard" },
    { name: "Articles", path: "/news/dashboard/articles" },
    { name: "Categories", path: "/news/dashboard/categories" },
  ];

  // Function to check if a path is active
  const isActive = (path: string) => {
    if (path === "/news/dashboard") {
      return pathname === "/news/dashboard";
    }
    return pathname.startsWith(path);
  };

  // Check authorization after auth is loaded
  useEffect(() => {
    // Only proceed with checks if auth loading is complete
    if (!authLoading) {
      if (!user || !user.roles?.editor) {
        router.push("/");
      } else {
        // User is authorized
        setIsAuthorized(true);
      }
    }
  }, [user, router, authLoading]);

  // Show loading state or nothing while checking authorization
  if (authLoading || !isAuthorized) {
    return (
      <div className="min-h-screen bg-secondary-dark flex items-center justify-center">
        <div className="bg-secondary-light p-6 rounded-lg shadow-md border border-gray-600">
          <p className="text-white text-lg">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-secondary-dark">
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 3000,
          style: {
            background: "var(--color-secondary)",
            color: "white",
            fontWeight: "500",
          },
          success: {
            style: {
              background: "var(--color-success)",
              color: "white",
              fontWeight: "500",
            },
            icon: "✓",
          },
          error: {
            style: {
              background: "var(--color-error)",
              color: "white",
              fontWeight: "500",
            },
            icon: "✕",
          },
        }}
      />
      <div className="container mx-auto px-1 py-2">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <div className="w-12 h-12 rounded-full mr-4 bg-primary flex items-center justify-center text-white font-bold text-xl">
              N
            </div>
            <div>
              <h1 className="text-2xl font-bold text-white">News Dashboard</h1>
              <p className="text-gray-400">
                Manage articles, categories, and content
              </p>
            </div>
          </div>
        </div>

        <div className="flex flex-col md:flex-row gap-2">
          {/* Sidebar Navigation - Hidden on mobile */}
          <aside className="hidden md:block md:w-40 flex-shrink-0">
            <div className="bg-secondary rounded-lg shadow-md p-4">
              <nav>
                <ul className="space-y-2">
                  {navItems.map((item) => (
                    <li key={item.path}>
                      <Link
                        href={item.path}
                        className={`
                          block px-4 py-2 rounded-md transition-colors
                          ${
                            isActive(item.path)
                              ? "border-2 border-primary font-medium"
                              : "border-2 border-gray-600 hover:bg-secondary-light"
                          }
                        `}
                      >
                        {item.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </nav>
            </div>

            {/* Stats Card */}
            {dashboardData && (
              <div className="bg-secondary rounded-lg shadow-md p-4 mt-1">
                <h2 className="text-lg font-semibold mb-2">Article Stats</h2>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Published:</span>
                    <span className="font-bold text-white">
                      {dashboardData.stats.publishedArticles}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Draft:</span>
                    <span className="font-bold text-white">
                      {dashboardData.stats.draftArticles}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Total Views:</span>
                    <span className="font-bold text-white">
                      {dashboardData.stats.totalViews}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </aside>

          {/* Mobile Navigation - Only visible on mobile */}
          <div className="md:hidden bg-secondary rounded-lg shadow-md p-3 mb-2">
            <nav>
              <ul className="grid grid-cols-3 gap-2">
                {navItems.map((item) => (
                  <li key={item.path}>
                    <Link
                      href={item.path}
                      className={`
                        block px-3 py-2 rounded-md text-center text-sm transition-colors
                        ${
                          isActive(item.path)
                            ? "bg-primary text-white font-medium"
                            : "bg-secondary-light text-white hover:bg-secondary-light"
                        }
                      `}
                    >
                      {item.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </nav>
          </div>

          {/* Mobile Stats Card - Only visible on mobile */}
          {dashboardData && (
            <div className="md:hidden bg-secondary rounded-lg shadow-md p-3 mb-2">
              <h2 className="text-lg font-semibold mb-2">Article Stats</h2>
              <div className="grid grid-cols-3 gap-2">
                <div className="text-center">
                  <p className="text-gray-400 text-sm">Published</p>
                  <p className="text-xl font-bold text-white">
                    {dashboardData.stats.publishedArticles}
                  </p>
                </div>
                <div className="text-center">
                  <p className="text-gray-400 text-sm">Draft</p>
                  <p className="text-xl font-bold text-white">
                    {dashboardData.stats.draftArticles}
                  </p>
                </div>
                <div className="text-center">
                  <p className="text-gray-400 text-sm">Views</p>
                  <p className="text-xl font-bold text-white">
                    {dashboardData.stats.totalViews}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Main Content */}
          <main className="flex-1">{children}</main>
        </div>
      </div>
    </div>
  );
};
