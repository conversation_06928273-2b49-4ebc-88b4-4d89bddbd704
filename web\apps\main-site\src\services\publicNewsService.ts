/**
 * Public News API Service
 * Provides functions for interacting with the public News API endpoints
 */
import fetchClient from "@/lib/fetchClient";

// Types
export interface PublicArticle {
  id: string;
  title: string;
  excerpt: string;
  content?: string;
  image: string | null;
  slug: string;
  publishedAt: string;
  views: number;
  featured: boolean;
  author: {
    displayName: string;
    avatar: string | null;
  };
  category: {
    name: string;
    slug: string;
  };
}

export interface PublicArticleWithRelated extends PublicArticle {
  relatedArticles: RelatedArticle[];
}

export interface RelatedArticle {
  id: string;
  title: string;
  excerpt: string;
  slug: string;
  image: string | null;
  publishedAt: string;
}

export interface PublicCategory {
  name: string;
  slug: string;
}

export interface PublicNewsFilters {
  page?: number;
  limit?: number;
  category?: string;
  search?: string;
  featured?: boolean;
  sortBy?: "publishedAt" | "views" | "title";
  order?: "asc" | "desc";
}

export interface PaginationMeta {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface PublicNewsResponse {
  data: PublicArticle[];
  meta: PaginationMeta;
}

/**
 * Get published articles with filtering and pagination
 */
export async function getPublicArticles(
  filters: PublicNewsFilters = {},
): Promise<PublicNewsResponse> {
  // Convert filters to query params
  const queryParams: Record<string, string> = {};
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined) {
      queryParams[key] = String(value);
    }
  });

  return fetchClient.get<PublicNewsResponse>("/api/news/public", {
    params: queryParams,
  });
}

/**
 * Get a single published article by slug
 */
export async function getPublicArticleBySlug(
  slug: string,
): Promise<PublicArticleWithRelated> {
  return fetchClient.get<PublicArticleWithRelated>(`/api/news/public/${slug}`);
}

/**
 * Get all available categories (from public articles only)
 * This uses the public articles endpoint with a limit of 0 to just get category information
 */
export async function getPublicCategories(): Promise<PublicCategory[]> {
  // Fetch all categories
  const categories = await fetchClient.get<any[]>("/api/news/categories");

  // Filter to only include categories that have articles
  return categories
    .filter((category: any) => category.articlesCount > 0)
    .map((category: any) => ({
      name: category.name,
      slug: category.slug,
    }));
}
