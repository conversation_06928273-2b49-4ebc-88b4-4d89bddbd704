// Type definitions for the unified upload system

export type UploadType = "avatar" | "news" | "event" | "product" | "deposit" | "ship-logo";

export interface UploadResponse {
  success: boolean;
  message?: string;
  file?: {
    id: string;
    filename: string;
    url: string;
    originalUrl: string;
    mimeType: string;
    size: number;
    dimensions?: {
      width: number;
      height: number;
    };
    uploadType: string;
    entityId?: string;
  };
  url?: string; // Legacy support
  error?: string;
}

export interface UploadOptions {
  maxSize?: number;
  allowedTypes?: string[];
  width?: number;
  height?: number;
  quality?: number;
  processImage?: boolean;
  generateThumbnail?: boolean;
}

export interface UniversalImageUploaderProps {
  uploadType: UploadType;
  onUploadComplete: (response: UploadResponse) => void;
  onUploadStart?: () => void;
  entityId?: string;
  options?: UploadOptions;
  inputRef?: React.RefObject<HTMLInputElement>;
  className?: string;
}

export interface UploadProgress {
  id: string;
  progress: number;
  status: "uploading" | "processing" | "complete" | "error";
  message?: string;
}

export interface UploadConfig {
  maxSize: number;
  allowedTypes: string[];
  processImage?: boolean;
  generateThumbnail?: boolean;
  quality?: number;
}

export type UploadConfigMap = {
  [K in UploadType]: UploadConfig;
};
