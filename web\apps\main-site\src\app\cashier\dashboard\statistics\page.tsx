"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "../../../../contexts/AuthContext";
import {
  CashierDashboardLayout,
  StatisticsDisplay,
} from "../../../../components/cashier";
import { useBankStatistics } from "../../../../hooks/useBank";

export default function StatisticsPage() {
  const { user, isLoading: authLoading } = useAuth();
  const router = useRouter();
  const [isAuthorized, setIsAuthorized] = useState(false);

  // Fetch bank statistics using the hook
  const {
    data: statistics,
    isLoading: statsLoading,
    error,
  } = useBankStatistics();

  // Check authorization after auth is loaded
  useEffect(() => {
    // Only proceed with checks if auth loading is complete
    if (!authLoading) {
      if (!user || !user.roles?.banker) {
        router.push("/");
      } else {
        // User is authorized
        setIsAuthorized(true);
      }
    }
  }, [user, router, authLoading]);

  // Show loading state or nothing while checking authorization
  if (authLoading || !isAuthorized) {
    return (
      <div className="min-h-screen bg-secondary-dark flex items-center justify-center">
        <div className="bg-secondary-light p-6 rounded-lg shadow-md border border-gray-600">
          <p className="text-white text-lg">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <CashierDashboardLayout>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-white mb-2">
          Banking Statistics
        </h1>
        <p className="text-gray-400">
          View and analyze transaction activity and banking metrics.
        </p>
      </div>

      {statsLoading ? (
        <div className="bg-secondary-light rounded-lg shadow-md p-4 sm:p-6 border border-gray-600 text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-gray-400">Loading statistics...</p>
        </div>
      ) : error ? (
        <div className="bg-secondary-light rounded-lg shadow-md p-4 sm:p-6 border border-gray-600 text-center py-8">
          <div className="text-error text-4xl mb-2">!</div>
          <p className="text-error font-medium">Error loading statistics</p>
          <p className="text-gray-400 mt-2">
            {error instanceof Error ? error.message : "Unknown error occurred"}
          </p>
        </div>
      ) : statistics ? (
        <div className="bg-secondary-light rounded-lg shadow-md p-4 sm:p-6 border border-gray-600">
          <StatisticsDisplay statistics={statistics} />
        </div>
      ) : (
        <div className="bg-secondary-light rounded-lg shadow-md p-4 sm:p-6 border border-gray-600 text-center py-8">
          <p className="text-gray-400">No statistics data available</p>
        </div>
      )}
    </CashierDashboardLayout>
  );
}
