# Support System

## Overview
Comprehensive customer support and help desk system that manages ticket creation, assignment, resolution tracking, and email notifications. The system enables both authenticated users and guests to submit support tickets, provides admin staff with tools for ticket management, and maintains full communication history with automated email integration.

## Directory Structure
```
src/app/api/support/
├── README.md                    # API overview and feature documentation
├── tickets/                     # Main ticket management endpoints
│   ├── route.ts                # GET (list tickets) + POST (create ticket)
│   └── [id]/                   # Individual ticket operations
│       ├── route.ts            # GET (fetch ticket) + PATCH (update ticket)
│       └── notes/              # Ticket notes and conversation management
│           └── route.ts        # GET (fetch notes) + POST (add note)
```

## Key Files & Components

### API Endpoints
- **`GET /api/support/tickets`** - Retrieve tickets with filtering, search, and pagination (Admin only)
- **`POST /api/support/tickets`** - Create new support ticket (Public + Authenticated)
- **`GET /api/support/tickets/[id]`** - Fetch specific ticket details with notes
- **`PATCH /api/support/tickets/[id]`** - Update ticket status, priority, assignment (Admin only)
- **`GET /api/support/tickets/[id]/notes`** - Retrieve conversation notes for ticket
- **`POST /api/support/tickets/[id]/notes`** - Add public or internal notes to ticket

### Database Models
- **`SupportTicket`** - Main ticket entity with metadata, contact info, and assignments
- **`TicketNote`** - Conversation history with internal/public note distinction

### Email Integration
- **Ticket Creation** - Automatic admin notification email on new ticket submission
- **Ticket Resolution** - User notification email when ticket is marked resolved
- **Note Responses** - Bidirectional email notifications for admin replies and user responses

## Common Tasks

### Task 1: Submit a Support Ticket
1. Send POST request to `/api/support/tickets` with required fields
2. Include subject, message, email (required) and optional name, phone, category
3. System creates ticket and sends admin notification email
4. Returns ticket ID and status for user confirmation

### Task 2: Admin Ticket Management
1. Use GET `/api/support/tickets` with query parameters for filtering:
   - `status` - Filter by open, in_progress, resolved, closed
   - `priority` - Filter by low, medium, high, urgent
   - `assignedToMe=true` - Show only tickets assigned to current admin
   - `search` - Search across subject, message, email, name fields
2. Update tickets using PATCH `/api/support/tickets/[id]` with status/priority changes
3. Add internal notes for staff communication or public responses to users

### Task 3: Ticket Conversation Management
1. Fetch conversation history with GET `/api/support/tickets/[id]/notes`
2. Add public responses via POST with `isInternal: false` (triggers user email)
3. Add internal notes via POST with `isInternal: true` (admin-only, no email)
4. System automatically sends appropriate email notifications

## API Integration

### Authentication Requirements
- **Public Access**: Ticket creation (POST /api/support/tickets)
- **User Access**: View own tickets and add responses
- **Admin Access**: Full ticket management, assignment, internal notes, filtering

### Request/Response Examples
```typescript
// Create Ticket Request
interface CreateTicketRequest {
  subject: string;
  message: string;
  email: string;
  name?: string;
  phone?: string;
  category?: "general" | "account" | "banking" | "technical" | "other";
}

// Ticket Response
interface SupportTicketResponse {
  id: string;
  subject: string;
  message: string;
  status: "open" | "in_progress" | "resolved" | "closed";
  priority: "low" | "medium" | "high" | "urgent";
  category: string;
  name?: string;
  email: string;
  phone?: string;
  userId?: string;
  assignedToId?: string;
  resolvedById?: string;
  resolution?: string;
  createdAt: string;
  assignedAt?: string;
  resolvedAt?: string;
  user?: UserInfo;
  assignedTo?: UserInfo;
  resolvedBy?: UserInfo;
  notes?: TicketNote[];
}

// Add Note Request
interface AddNoteRequest {
  content: string;
  isInternal?: boolean; // Default false, admin-only for true
}
```

## Database Schema

### Primary Models
```sql
-- Support ticket main entity
CREATE TABLE SupportTicket (
  id VARCHAR(191) PRIMARY KEY,
  subject VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  status VARCHAR(50) DEFAULT 'open',
  priority VARCHAR(50) DEFAULT 'medium',
  category VARCHAR(50) DEFAULT 'general',
  
  -- Contact information
  name VARCHAR(255),
  email VARCHAR(255) NOT NULL,
  phone VARCHAR(50),
  
  -- User relationships
  userId VARCHAR(191),
  assignedToId VARCHAR(191),
  resolvedById VARCHAR(191),
  
  -- Resolution info
  resolution TEXT,
  
  -- Timestamps
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  assignedAt DATETIME,
  resolvedAt DATETIME
);

-- Ticket conversation notes
CREATE TABLE TicketNote (
  id VARCHAR(191) PRIMARY KEY,
  content TEXT NOT NULL,
  isInternal TINYINT DEFAULT 0,
  
  -- Relationships
  ticketId VARCHAR(191) NOT NULL,
  authorId VARCHAR(191) NOT NULL,
  
  -- Timestamps
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (ticketId) REFERENCES SupportTicket(id) ON DELETE CASCADE,
  FOREIGN KEY (authorId) REFERENCES User(id)
);
```

### Relationships
- **SupportTicket → User** (userId): Optional authenticated user who submitted ticket
- **SupportTicket → User** (assignedToId): Admin staff member assigned to ticket
- **SupportTicket → User** (resolvedById): Admin staff member who resolved ticket
- **TicketNote → SupportTicket**: Conversation notes belong to specific ticket
- **TicketNote → User** (authorId): User (admin or customer) who wrote the note

## Related Features
- **[Authentication System](./authentication-system.md)** - User identification and admin role validation
- **[Admin Dashboard System](./admin-dashboard-system.md)** - Support ticket management interface
- **Email System** - SMTP integration for automated notifications and responses

## User Roles & Permissions
- **Guest**: Can create tickets via public API endpoint
- **User**: Can create tickets, view own tickets, add responses to own tickets
- **Admin**: Full ticket management - view all tickets, assign, update status/priority, add internal notes
- **Super Admin**: Same as Admin (no additional support-specific permissions)

## Recent Changes
- **v1.0.0** - Initial support system implementation with full email integration
- **Email Notifications** - Comprehensive email system for ticket lifecycle events
- **Note System** - Internal vs public note distinction for staff communication
- **Advanced Filtering** - Comprehensive search and filter capabilities for admin dashboard

## Troubleshooting

### Common Issues
1. **Email Not Sending**: Verify `ADMIN_EMAIL` environment variable and SMTP configuration
2. **Permission Denied**: Ensure proper role validation - admins for management, users for own tickets
3. **Missing Ticket**: Check ticket ID format and user permissions for ticket access

### Debug Information
- **Environment Variables**: `ADMIN_EMAIL` for administrative notifications
- **Email Configuration**: Requires configured SMTP settings in email service
- **Permissions**: Uses `getCurrentUser()` and `userHasRole()` for access control

---

**File Locations:**
- API: `/src/app/api/support/`
- Database Models: `prisma/schema.prisma` (SupportTicket, TicketNote models)
- Email Templates: Inline HTML/text templates in route handlers