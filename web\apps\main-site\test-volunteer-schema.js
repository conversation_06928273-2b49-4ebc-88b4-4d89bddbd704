// Test script to verify volunteer management schema updates
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function testVolunteerSchema() {
  try {
    console.log('Testing volunteer management schema updates...');
    
    // 1. Check User model schema
    const userModel = await prisma.$queryRaw`
      DESCRIBE user;
    `;
    
    const userFields = userModel.map(field => field.Field);
    console.log('\nUser model fields:');
    console.log('- isVolunteerCoordinator:', userFields.includes('isVolunteerCoordinator') ? 'Found ✓' : 'Missing ✗');
    console.log('- isLeadManager:', userFields.includes('isLeadManager') ? 'Found ✓' : 'Missing ✗');
    console.log('- leadManagerCategoryId:', userFields.includes('leadManagerCategoryId') ? 'Found ✓' : 'Missing ✗');
    
    // 2. Check VolunteerCategory model schema
    const categoryModel = await prisma.$queryRaw`
      DESCRIBE volunteer_categories;
    `;
    
    const categoryFields = categoryModel.map(field => field.Field);
    console.log('\nVolunteerCategory model fields:');
    console.log('- leadManagerId:', categoryFields.includes('leadManagerId') ? 'Found ✓' : 'Missing ✗');
    
    // 3. Check foreign key constraints
    const foreignKeys = await prisma.$queryRaw`
      SELECT 
        TABLE_NAME, COLUMN_NAME, REFERENCED_TABLE_NAME, REFERENCED_COLUMN_NAME
      FROM 
        INFORMATION_SCHEMA.KEY_COLUMN_USAGE
      WHERE 
        REFERENCED_TABLE_SCHEMA = DATABASE() AND
        TABLE_NAME = 'user' AND COLUMN_NAME = 'leadManagerCategoryId';
    `;
    
    console.log('\nForeign key constraints:');
    if (foreignKeys.length > 0) {
      console.log('- User.leadManagerCategoryId -> VolunteerCategory.id: Found ✓');
      console.log('  Details:', JSON.stringify(foreignKeys[0], null, 2));
    } else {
      console.log('- User.leadManagerCategoryId -> VolunteerCategory.id: Missing ✗');
    }
    
    console.log('\nSchema verification complete!');
  } catch (error) {
    console.error('Error testing schema:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testVolunteerSchema();