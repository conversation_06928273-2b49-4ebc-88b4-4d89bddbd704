# UI Component Library Documentation

## Overview
The `@bank-of-styx/ui` package provides a comprehensive set of reusable React components built with TypeScript and TailwindCSS. These components follow consistent design patterns, support theming, and include accessibility features for building user interfaces across the Bank of Styx application.

## Installation & Usage

### Import Components
```typescript
import { Button, Modal, Input, Card } from "@bank-of-styx/ui";
```

### Package Configuration
- **Package Name**: `@bank-of-styx/ui`
- **Version**: 0.1.0
- **Built With**: TypeScript, React 18.2.0, TailwindCSS
- **Bundle Formats**: ESM, CJS with TypeScript definitions

## Core Components

### Button Component
Multi-variant button component with loading states and size options.

```typescript
interface ButtonProps {
  variant?: "primary" | "secondary" | "accent" | "outline" | "ghost";
  size?: "sm" | "md" | "lg";
  fullWidth?: boolean;
  disabled?: boolean;
  loading?: boolean;
  children: React.ReactNode;
}
```

**Usage Examples:**
```jsx
// Primary button
<Button variant="primary" size="md">Save Changes</Button>

// Loading button
<Button loading={true}>Processing...</Button>

// Full width outline button
<Button variant="outline" fullWidth>Cancel</Button>
```

**Variants:**
- `primary` - Main action button (primary theme color)
- `secondary` - Secondary actions (secondary theme color)  
- `accent` - Important/attention actions (accent theme color)
- `outline` - Bordered transparent button
- `ghost` - Transparent button with hover effects

### Modal Component
Accessible modal dialog with multiple size options and event handling.

```typescript
interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  footer?: React.ReactNode;
  size?: "sm" | "md" | "lg" | "xl" | "full";
  closeOnClickOutside?: boolean;
  closeOnEsc?: boolean;
}
```

**Usage Examples:**
```jsx
<Modal 
  isOpen={showModal} 
  onClose={() => setShowModal(false)}
  title="Confirm Action"
  size="md"
  footer={
    <div className="flex gap-2">
      <Button variant="outline" onClick={() => setShowModal(false)}>Cancel</Button>
      <Button variant="primary" onClick={handleConfirm}>Confirm</Button>
    </div>
  }
>
  <p>Are you sure you want to proceed?</p>
</Modal>
```

**Features:**
- Keyboard navigation (ESC to close)
- Click outside to close (configurable)
- Body scroll prevention
- Dark theme support
- Multiple size options
- ARIA accessibility attributes

### Input Component
Comprehensive form input with validation, icons, and helper text.

```typescript
interface InputProps {
  label?: string;
  error?: string;
  helperText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  fullWidth?: boolean;
  size?: "sm" | "md" | "lg";
}
```

**Usage Examples:**
```jsx
// Basic input with label
<Input 
  label="Email Address" 
  type="email" 
  placeholder="Enter your email"
  fullWidth 
/>

// Input with validation error
<Input 
  label="Password" 
  type="password" 
  error="Password must be at least 8 characters"
  fullWidth 
/>

// Input with icons
<Input 
  leftIcon={<SearchIcon />}
  rightIcon={<ClearIcon />}
  placeholder="Search..."
/>
```

### Card Component
Flexible content container with consistent spacing and theming.

```typescript
interface CardProps {
  children: React.ReactNode;
  padding?: "sm" | "md" | "lg";
  className?: string;
}
```

### Pagination Component
Data navigation component with page controls and info display.

```typescript
interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  showInfo?: boolean;
  totalItems?: number;
  itemsPerPage?: number;
}
```

**Usage Example:**
```jsx
<Pagination 
  currentPage={currentPage}
  totalPages={totalPages}
  onPageChange={setCurrentPage}
  showInfo={true}
  totalItems={150}
  itemsPerPage={10}
/>
```

### Spinner Component
Loading indicator with configurable size and styling.

```jsx
// Basic spinner
<Spinner />

// Large spinner with custom color
<Spinner size="lg" className="text-primary" />
```

### Search Bar Component
Advanced search input with category filtering and suggestions.

```typescript
interface SearchBarProps {
  onSearch: (query: string, category?: string) => void;
  categories?: CategoryItem[];
  placeholder?: string;
  showCategoryFilter?: boolean;
}
```

## Specialized Components

### Rich Text Editor
Full-featured WYSIWYG editor for content creation and editing.

```jsx
<RichTextEditor 
  value={content}
  onChange={setContent}
  placeholder="Start writing..."
  toolbar={['bold', 'italic', 'link', 'bulletList']}
/>
```

### Form Builder
Dynamic form creation and rendering system.

```typescript
// Form builder for creating forms
<FormBuilder 
  onFormChange={handleFormChange}
  availableFields={fieldTypes}
/>

// Form renderer for displaying forms
<FormRenderer 
  formConfig={formConfig}
  onSubmit={handleSubmit}
  values={formValues}
/>
```

### Content Card
Specialized card for displaying content with metadata.

```jsx
<ContentCard 
  title="Article Title"
  excerpt="Article excerpt..."
  author="John Doe"
  publishDate="2025-01-07"
  category="News"
/>
```

### Featured Content
Highlight component for showcasing important content.

```jsx
<FeaturedContent 
  title="Featured Article"
  description="This is important content"
  imageUrl="/path/to/image.jpg"
  linkUrl="/article/123"
/>
```

### Hero Section
Landing page hero component with call-to-action support.

```jsx
<Hero 
  title="Welcome to Bank of Styx"
  subtitle="Your digital banking solution"
  backgroundImage="/hero-bg.jpg"
  ctaButton={<Button variant="primary">Get Started</Button>}
/>
```

### Navigation Components

#### Sidebar
Responsive navigation sidebar component.

```jsx
<Sidebar 
  items={navigationItems}
  currentPath={router.pathname}
  onNavigate={handleNavigation}
/>
```

#### Scroll to Top
Utility component for page navigation assistance.

```jsx
<ScrollToTop 
  showAfter={300} 
  smooth={true}
/>
```

## Form Components

### Additional Input Types
- **Textarea**: Multi-line text input with auto-resize
- **Checkbox**: Styled checkbox with label support  
- **UserSearchInput**: Specialized user selection input

```jsx
// Textarea
<Textarea 
  label="Description"
  rows={4}
  placeholder="Enter description..."
/>

// Checkbox
<Checkbox 
  label="I agree to the terms"
  checked={agreed}
  onChange={setAgreed}
/>

// User search input
<UserSearchInput 
  onUserSelect={handleUserSelect}
  placeholder="Search users..."
/>
```

## Theme Integration

### Color Scheme Support
All components support the application's dynamic theming system:

- **Primary Colors**: Main brand colors for primary actions
- **Secondary Colors**: Supporting colors for secondary elements
- **Accent Colors**: Attention-grabbing colors for important actions
- **Background Colors**: Dark/light theme backgrounds
- **Text Colors**: Contrast-appropriate text colors

### CSS Custom Properties
Components use CSS custom properties for theming:
```css
--color-primary
--color-secondary  
--color-accent
--color-background
--color-text-primary
```

## Accessibility Features

### Built-in Accessibility
- **ARIA labels and roles** for screen readers
- **Keyboard navigation** support for interactive elements
- **Focus management** for modal and form components
- **High contrast** color combinations
- **Semantic HTML** structure

### Usage Guidelines
- Always provide `label` props for form inputs
- Use descriptive `aria-label` attributes when needed
- Ensure sufficient color contrast ratios
- Test keyboard navigation for all interactive components

## Development Scripts

```bash
# Build the component library
pnpm ui:build

# Development mode with watch
pnpm ui:dev  

# Lint components
pnpm lint
```

## Component Export Structure

```typescript
// Main exports from @bank-of-styx/ui
export {
  Button, Modal, Input, Card, Pagination, Spinner,
  SearchBar, ScrollToTop, Sidebar, Hero,
  RichTextEditor, FormBuilder, FormRenderer,
  ContentCard, FeaturedContent,
  Textarea, Checkbox, UserSearchInput
} from "@bank-of-styx/ui";

// Type exports
export type { CategoryItem } from "@bank-of-styx/ui";
```

## Best Practices

### Component Usage
1. **Import only needed components** to optimize bundle size
2. **Use TypeScript interfaces** for proper type checking
3. **Apply consistent sizing** (`sm`, `md`, `lg`) across components
4. **Handle loading and error states** appropriately
5. **Provide meaningful labels** for accessibility

### Styling Guidelines  
1. **Use theme colors** instead of hardcoded colors
2. **Apply consistent spacing** using component props
3. **Avoid overriding component styles** unless necessary
4. **Test components in both light and dark themes**
5. **Ensure responsive design** for all screen sizes

---

**Package Location**: `/web/packages/ui/`  
**Source Code**: `/web/packages/ui/src/`  
**Built Distribution**: `/web/packages/ui/dist/`  
**Documentation**: This file and inline TypeScript definitions