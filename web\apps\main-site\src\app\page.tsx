"use client";

import { <PERSON>, ContentCard, FeaturedContent } from "@bank-of-styx/ui";
import { useRouter } from "next/navigation";
import { useAuth } from "../contexts/AuthContext";
import { usePublicArticles } from "../hooks/usePublicNews";

export default function Home() {
  const router = useRouter();
  const { openAuthModal, isAuthenticated } = useAuth();

  // Fetch featured news articles from the API
  const {
    data: articlesResponse,
    isLoading: isArticlesLoading,
    error: articlesError,
  } = usePublicArticles({
    featured: true,
    limit: 3,
    sortBy: "publishedAt",
    order: "desc",
  });

  // Extract articles from the response
  const featuredArticles = articlesResponse?.data || [];

  return (
    <main className="flex min-h-screen py-2 flex-col">
      {/* Hero Section - Desktop version (md screens and up) */}
      <div className="hidden md:block">
        <Hero
          title="Bank of Styx"
          subtitle="The trusted financial institution for the Pirate Rinfair community"
          backgroundImage="/images/hero-background.jpg"
          height="300px"
        >
          <div className="flex justify-center">
            {!isAuthenticated && (
              <button
                onClick={openAuthModal}
                className="bg-primary hover:bg-primary-dark text-white font-bold py-2 px-6 rounded-full mr-4"
              >
                Open an Account
              </button>
            )}
            <button
              onClick={() => router.push("/help")}
              className="bg-transparent hover:bg-white/10 text-white font-bold py-2 px-6 rounded-full border border-white"
            >
              Learn More
            </button>
          </div>
        </Hero>
      </div>

      {/* Mobile Hero Section - Visible only on mobile, matches card size */}
      <div className="md:hidden container mx-auto px-2 mb-4">
        <div className="rounded-lg shadow-md overflow-hidden">
          <div className="relative h-48 overflow-hidden">
            {/* Background image */}
            <div
              className="absolute inset-0 bg-cover bg-center z-0"
              style={{ backgroundImage: `url(/images/hero-background.jpg)` }}
            />
            {/* Overlay */}
            <div
              className="absolute inset-0 z-10"
              style={{ backgroundColor: "rgba(0, 0, 0, 0.7)" }}
            />
            {/* Content */}
            <div className="relative z-20 flex flex-col justify-center items-center h-full text-center px-2">
              <h1 className="text-4xl font-bold text-white">Bank of Styx</h1>
              <p className="mt-4 text-base text-white">
                The trusted financial institution for the Pirate Rinfair
                community
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-2 py-4 md:py-8 lg:py-12">
        <div className="max-w-4xl mx-auto">
          {/* News Section */}
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-white mb-4">
              Latest News & Updates
            </h2>

            {isArticlesLoading && (
              <div className="text-center py-8">
                <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
                <p className="mt-2 text-gray-400">Loading news articles...</p>
              </div>
            )}

            {articlesError && (
              <div className="bg-red-900/30 border border-red-700 text-white p-4 rounded-md">
                <p>Failed to load news articles. Please try again later.</p>
              </div>
            )}

            {!isArticlesLoading &&
              !articlesError &&
              featuredArticles.length === 0 && (
                <div className="bg-secondary-light p-4 rounded-md">
                  <p className="text-center text-gray-400">
                    No news articles available at this time.
                  </p>
                </div>
              )}

            {!isArticlesLoading &&
              !articlesError &&
              featuredArticles.length > 0 && (
                <div className="grid grid-cols-1 gap-6">
                  {featuredArticles.map((article) => (
                    <ContentCard
                      key={article.id}
                      title={article.title}
                      content={article.excerpt}
                      image={article.image || "/images/placeholder-news.jpg"}
                      date={new Date(article.publishedAt).toLocaleDateString()}
                      author={article.author.displayName}
                      category={article.category.name}
                      footer={
                        <span className="text-sm text-gray-500">
                          Published on{" "}
                          {new Date(article.publishedAt).toLocaleDateString()}
                        </span>
                      }
                      onClick={() => router.push(`/news/${article.slug}`)}
                      htmlContent={true}
                    />
                  ))}
                </div>
              )}
          </div>

          {/* Banking Services */}
          <div className="bg-secondary rounded-lg p-6 border border-gray-600 mb-8">
            <h3 className="text-xl font-bold mb-4 text-white">Bank Services</h3>
            <ul className="space-y-3 text-gray-400">
              <li className="flex items-center">
                <svg
                  className="w-5 h-5 text-primary mr-2"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                    clipRule="evenodd"
                  />
                </svg>
                <span>Secure Treasure Storage</span>
              </li>
              <li className="flex items-center">
                <svg
                  className="w-5 h-5 text-primary mr-2"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                    clipRule="evenodd"
                  />
                </svg>
                <span>Currency Exchange</span>
              </li>
              <li className="flex items-center">
                <svg
                  className="w-5 h-5 text-primary mr-2"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                    clipRule="evenodd"
                  />
                </svg>
                <span>Loans & Financing</span>
              </li>
              <li className="flex items-center">
                <svg
                  className="w-5 h-5 text-primary mr-2"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                    clipRule="evenodd"
                  />
                </svg>
                <span>Investment Opportunities</span>
              </li>
            </ul>
            <button
              onClick={() => router.push("/help")}
              className="mt-6 w-full bg-primary hover:bg-primary-dark text-white font-medium py-2 px-4 rounded"
            >
              Learn More
            </button>
          </div>
        </div>
      </div>
    </main>
  );
}
