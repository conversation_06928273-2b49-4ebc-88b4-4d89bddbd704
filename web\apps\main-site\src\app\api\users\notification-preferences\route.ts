import { NextRequest, NextResponse } from "next/server";
import prisma from "../../../../lib/prisma";
import { getCurrentUser } from "../../../../lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// GET endpoint to fetch notification preferences
export async function GET(req: NextRequest) {
  try {
    // Get current user
    const currentUser = await getCurrentUser(req);
    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    // Get user preferences from database
    const user = await prisma.user.findUnique({
      where: { id: currentUser.id },
      select: {
        notifyTransfers: true,
        notifyDeposits: true,
        notifyWithdrawals: true,
        notifyNewsEvents: true,
        notifyAuctions: true,
        notifyChat: true,
        notifyAdmin: true,
      },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      preferences: {
        notifyTransfers: user.notifyTransfers,
        notifyDeposits: user.notifyDeposits,
        notifyWithdrawals: user.notifyWithdrawals,
        notifyNewsEvents: user.notifyNewsEvents,
        notifyAuctions: user.notifyAuctions,
        notifyChat: user.notifyChat,
        notifyAdmin: user.notifyAdmin,
      },
    });
  } catch (error) {
    console.error("Error fetching notification preferences:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

// PUT endpoint to update notification preferences
export async function PUT(req: NextRequest) {
  try {
    // Get current user
    const currentUser = await getCurrentUser(req);
    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    // Parse request body
    const { notifications } = await req.json();

    // Validate input
    if (!notifications) {
      return NextResponse.json(
        { error: "Notifications preferences are required" },
        { status: 400 },
      );
    }

    // Update user preferences
    const updatedUser = await prisma.user.update({
      where: { id: currentUser.id },
      data: {
        notifyTransfers: notifications.notifyTransfers ?? undefined,
        notifyDeposits: notifications.notifyDeposits ?? undefined,
        notifyWithdrawals: notifications.notifyWithdrawals ?? undefined,
        notifyNewsEvents: notifications.notifyNewsEvents ?? undefined,
        notifyAuctions: notifications.notifyAuctions ?? undefined,
        notifyChat: notifications.notifyChat ?? undefined,
        notifyAdmin: notifications.notifyAdmin ?? undefined,
      },
      select: {
        notifyTransfers: true,
        notifyDeposits: true,
        notifyWithdrawals: true,
        notifyNewsEvents: true,
        notifyAuctions: true,
        notifyChat: true,
        notifyAdmin: true,
      },
    });

    // Return updated preferences
    return NextResponse.json({
      success: true,
      preferences: {
        notifyTransfers: updatedUser.notifyTransfers,
        notifyDeposits: updatedUser.notifyDeposits,
        notifyWithdrawals: updatedUser.notifyWithdrawals,
        notifyNewsEvents: updatedUser.notifyNewsEvents,
        notifyAuctions: updatedUser.notifyAuctions,
        notifyChat: updatedUser.notifyChat,
        notifyAdmin: updatedUser.notifyAdmin,
      },
    });
  } catch (error) {
    console.error("Error updating notification preferences:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
