import React from "react";
import { VolunteerPayment } from "@/hooks/useVolunteerPayments";
import { formatDate, formatCurrency, formatDateTime } from "@/lib/utils";

interface PaymentHistoryProps {
  payments: VolunteerPayment[];
  isLoading: boolean;
}

export default function PaymentHistory({
  payments,
  isLoading,
}: PaymentHistoryProps) {
  if (isLoading) {
    return (
      <div className="bg-secondary-light rounded-lg shadow-md p-4 border border-gray-600">
        <div className="flex justify-center items-center h-40">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
          <p className="ml-2 text-white">Loading payment history...</p>
        </div>
      </div>
    );
  }

  if (payments.length === 0) {
    return (
      <div className="bg-secondary-light rounded-lg shadow-md p-4 border border-gray-600">
        <p className="text-white text-center py-8">
          No payment history found matching the current filters.
        </p>
      </div>
    );
  }

  return (
    <div className="bg-secondary-light rounded-lg shadow-md border border-gray-600">
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-secondary-dark">
            <tr>
              <th className="p-3 text-left text-white">Volunteer</th>
              <th className="p-3 text-left text-white">Event / Category</th>
              <th className="p-3 text-left text-white">Shift Date</th>
              <th className="p-3 text-left text-white">Hours</th>
              <th className="p-3 text-right text-white">Amount</th>
              <th className="p-3 text-center text-white">Status</th>
              <th className="p-3 text-right text-white">Payment Date</th>
            </tr>
          </thead>
          <tbody>
            {payments.map((payment) => (
              <tr
                key={payment.id}
                className="border-t border-gray-600 hover:bg-secondary-dark/50"
              >
                <td className="p-3">
                  <div className="flex items-center">
                    {payment.user.avatar && (
                      <img
                        src={payment.user.avatar}
                        alt={payment.user.displayName}
                        className="w-8 h-8 rounded-full mr-2"
                      />
                    )}
                    <div>
                      <div className="text-white">
                        {payment.user.displayName}
                      </div>
                      <div className="text-gray-400 text-sm">
                        @{payment.user.username}
                      </div>
                    </div>
                  </div>
                </td>
                <td className="p-3">
                  <div className="text-white">
                    {payment.assignment.shift.event.name}
                  </div>
                  <div className="text-gray-400 text-sm">
                    {payment.assignment.shift.category.name}
                  </div>
                </td>
                <td className="p-3">
                  <div className="text-white">
                    {formatDate(payment.assignment.shift.startTime)}
                  </div>
                  <div className="text-gray-400 text-sm">
                    {payment.assignment.shift.name}
                  </div>
                </td>
                <td className="p-3 text-white">{payment.hoursWorked}</td>
                <td className="p-3 text-white text-right">
                  {formatCurrency(payment.paymentAmount)}
                </td>
                <td className="p-3">
                  <span
                    className={`inline-block px-2 py-1 rounded text-xs ${
                      payment.paymentStatus === "pending"
                        ? "bg-yellow-800 text-yellow-200"
                        : payment.paymentStatus === "processing"
                        ? "bg-blue-800 text-blue-200"
                        : payment.paymentStatus === "paid"
                        ? "bg-green-800 text-green-200"
                        : "bg-red-800 text-red-200"
                    }`}
                  >
                    {payment.paymentStatus.charAt(0).toUpperCase() +
                      payment.paymentStatus.slice(1)}
                  </span>
                </td>
                <td className="p-3 text-right text-white">
                  {payment.transaction
                    ? formatDateTime(payment.transaction.createdAt)
                    : "-"}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
