# Get-FileTree.ps1
# Script to generate feature-based file tree layouts of the Bank of Styx website
# Excludes node_modules and other commonly ignored files/folders
# Creates separate files for each major feature/system based on the documentation structure

# Define directories and files to exclude (based on .gitignore and common patterns)
$excludePatterns = @(
    ".git",
    "node_modules",
    ".pnpm-store",
    ".npm",

    "yarn.lock",
    ".next",
    "build",
    "dist",
    "out",
    ".env",
    ".env.local",
    ".env.development.local",
    ".env.test.local",
    ".env.production.local",
    ".env*.local",
    "*-debug.log*",
    "*-error.log*",
    ".idea",
    ".vscode",
    "*.swp",
    "*.swo",
    "*~",
    ".DS_Store",
    ".DS_Store?",
    "._*",
    ".Spotlight-V100",
    ".Trashes",
    "ehthumbs.db",
    "Thumbs.db",
    "coverage",
    "*.log",
    ".cache",
    ".temp",
    "*.bak",

    "*.tsbuildinfo",
    "public/sw.js",
    "public/workbox-*.js",
    "pnpm-lock.yaml"
)

# Function to check if a path should be excluded
function ShouldExclude {
    param (
        [string]$path
    )

    foreach ($pattern in $excludePatterns) {
        if ($pattern.Contains("*")) {
            # Handle wildcard patterns
            $regexPattern = "^" + [regex]::Escape($pattern).Replace("\*", ".*") + "$"
            if ($path -match $regexPattern) {
                return $true
            }
        } else {
            # Handle exact matches
            if ($path -eq $pattern -or $path.Contains("\$pattern\") -or $path.EndsWith("\$pattern")) {
                return $true
            }
        }
    }

    return $false
}

# Output file paths
$outputFolder = "C:\Users\<USER>\projects\test\docs\feature-trees"
$mainOutputFile = "C:\Users\<USER>\projects\test\docs\website-file-tree.txt"

# Create output folder if it doesn't exist
if (-not (Test-Path -Path $outputFolder)) {
    New-Item -Path $outputFolder -ItemType Directory | Out-Null
    Write-Host "Created output folder: $outputFolder"
}

# Get the current directory
$rootDir = "C:\Users\<USER>\projects\test\web"

# Create a StringBuilder for better performance
$output = New-Object System.Text.StringBuilder

# Function to recursively get files and directories
function Get-FileTreeRecursive {
    param (
        [string]$directory,
        [string]$relativePath = ""
    )

    # Get all items in the current directory
    $items = Get-ChildItem -Path $directory -Force

    foreach ($item in $items) {
        $itemRelativePath = if ($relativePath) { "$relativePath\$($item.Name)" } else { $item.Name }

        # Check if the item should be excluded
        if (ShouldExclude -path $itemRelativePath) {
            continue
        }

        # Add the item to the output with full path
        $fullPath = $item.FullName

        if ($item.PSIsContainer) {
            # It's a directory
            [void]$output.AppendLine("$fullPath\")

            # Recursively process subdirectories
            Get-FileTreeRecursive -directory $item.FullName -relativePath $itemRelativePath
        } else {
            # It's a file
            [void]$output.AppendLine("$fullPath")
        }
    }
}

# Start the recursive process
Get-FileTreeRecursive -directory $rootDir

# Sort the output for better searchability
$sortedOutput = $output.ToString().Split("`n") | Where-Object { $_ -ne "" } | Sort-Object

# Create a StringBuilder for directories
$directoryOutput = New-Object System.Text.StringBuilder

# Function to recursively get directories only
function Get-DirectoriesRecursive {
    param (
        [string]$directory,
        [string]$relativePath = ""
    )

    # Get all items in the current directory
    $items = Get-ChildItem -Path $directory -Force | Where-Object { $_.PSIsContainer }

    foreach ($item in $items) {
        $itemRelativePath = if ($relativePath) { "$relativePath\$($item.Name)" } else { $item.Name }

        # Check if the item should be excluded
        if (ShouldExclude -path $itemRelativePath) {
            continue
        }

        # Add the directory to the output
        [void]$directoryOutput.AppendLine("$($item.FullName)\")

        # Recursively process subdirectories
        Get-DirectoriesRecursive -directory $item.FullName -relativePath $itemRelativePath
    }
}

# Start the recursive process for directories
Get-DirectoriesRecursive -directory $rootDir

# Sort the directory output
$sortedDirectories = $directoryOutput.ToString().Split("`n") | Where-Object { $_ -ne "" } | Sort-Object

# Create a new StringBuilder for the final output
$finalOutput = New-Object System.Text.StringBuilder
[void]$finalOutput.AppendLine("# Bank of Styx Website File Tree (Directories Only)")
[void]$finalOutput.AppendLine("# Generated on $(Get-Date)")
[void]$finalOutput.AppendLine("# Excludes node_modules, .git, and other commonly ignored files/folders")
[void]$finalOutput.AppendLine("# Root directory: $rootDir")
[void]$finalOutput.AppendLine("")

foreach ($line in $sortedDirectories) {
    [void]$finalOutput.Append("$line`n")
}

# Add a separator before the full file tree
[void]$finalOutput.AppendLine("")
[void]$finalOutput.AppendLine("# Full File Tree (Files and Directories)")
[void]$finalOutput.AppendLine("")

# Append the sorted full file tree to the final output
foreach ($line in $sortedOutput) {
    [void]$finalOutput.Append("$line`n")
}

# Save the complete output to the main file
$finalOutput.ToString() | Out-File -FilePath $mainOutputFile -Encoding utf8

Write-Host "Complete file tree has been saved to $mainOutputFile"
Write-Host "Total directories listed: $($sortedDirectories.Count)"
Write-Host "Total files and directories listed: $($sortedOutput.Count)"

# Now create separate files for different feature groups based on the documentation structure
# Define the feature groups based on the Bank of Styx feature documentation
$featureGroups = @(
    @{
        Name = "authentication-system";
        Title = "Authentication System";
        Pattern = "\\apps\\main-site\\src\\app\\auth\\|\\apps\\main-site\\src\\app\\api\\auth\\|\\apps\\api\\auth\\";
        Description = "Complete authentication system including login, registration, Discord integration, and user management";
        Priority = "High";
    },
    @{
        Name = "banking-system";
        Title = "Banking System";
        Pattern = "\\apps\\main-site\\src\\app\\bank\\|\\apps\\main-site\\src\\app\\api\\bank\\|\\apps\\main-site\\src\\app\\cashier\\|\\apps\\main-site\\src\\app\\api\\cashier\\";
        Description = "Core banking functionality including accounts, transactions, pay codes, deposits, withdrawals, and cashier operations";
        Priority = "High";
    },
    @{
        Name = "ship-management-system";
        Title = "Ship Management System";
        Pattern = "\\apps\\main-site\\src\\app\\ships\\|\\apps\\main-site\\src\\app\\api\\ships\\|\\apps\\main-site\\src\\app\\captain\\|\\apps\\main-site\\src\\app\\api\\captain\\";
        Description = "Ship creation, management, captain dashboard, crew management, and ship-related operations";
        Priority = "High";
    },
    @{
        Name = "volunteer-system";
        Title = "Volunteer System";
        Pattern = "\\apps\\main-site\\src\\app\\volunteer\\|\\apps\\main-site\\src\\app\\api\\volunteer\\|\\apps\\main-site\\src\\app\\land-steward\\|\\apps\\main-site\\src\\app\\api\\land-steward\\";
        Description = "Volunteer management, applications, assignments, hour tracking, and land steward operations";
        Priority = "High";
    },
    @{
        Name = "admin-system";
        Title = "Admin System";
        Pattern = "\\apps\\main-site\\src\\app\\admin\\|\\apps\\main-site\\src\\app\\api\\admin\\";
        Description = "Administrative dashboard, user management, system configuration, and administrative tools";
        Priority = "Medium";
    },
    @{
        Name = "shopping-system";
        Title = "Shopping & Sales System";
        Pattern = "\\apps\\main-site\\src\\app\\shop\\|\\apps\\main-site\\src\\app\\sales\\|\\apps\\main-site\\src\\app\\api\\shop\\|\\apps\\main-site\\src\\app\\api\\sales\\|\\apps\\main-site\\src\\app\\api\\cart\\|\\apps\\main-site\\src\\app\\api\\checkout\\|\\apps\\main-site\\src\\app\\api\\orders\\|\\apps\\main-site\\src\\app\\api\\products\\|\\apps\\main-site\\src\\app\\api\\product-categories\\";
        Description = "E-commerce functionality including product catalog, shopping cart, checkout, order management, and sales dashboard";
        Priority = "Medium";
    },
    @{
        Name = "news-system";
        Title = "News & Content System";
        Pattern = "\\apps\\main-site\\src\\app\\news\\|\\apps\\main-site\\src\\app\\api\\news\\";
        Description = "News article management, content publishing, and news dashboard";
        Priority = "Medium";
    },
    @{
        Name = "events-system";
        Title = "Events System";
        Pattern = "\\apps\\main-site\\src\\app\\events\\|\\apps\\main-site\\src\\app\\api\\events\\|\\apps\\main-site\\src\\app\\api\\event-categories\\";
        Description = "Event management, event categories, event registration, and event-related operations";
        Priority = "Medium";
    },
    @{
        Name = "user-settings-system";
        Title = "User Settings & Profile System";
        Pattern = "\\apps\\main-site\\src\\app\\settings\\|\\apps\\main-site\\src\\app\\api\\user\\|\\apps\\main-site\\src\\app\\api\\users\\";
        Description = "User profile management, settings, preferences, and user-related API endpoints";
        Priority = "Low";
    },
    @{
        Name = "support-system";
        Title = "Support System";
        Pattern = "\\apps\\main-site\\src\\app\\api\\support\\|\\apps\\main-site\\src\\app\\help\\|\\apps\\main-site\\src\\app\\api\\contact\\";
        Description = "Support ticket system, help pages, and contact functionality";
        Priority = "Low";
    },
    @{
        Name = "notification-system";
        Title = "Notification System";
        Pattern = "\\apps\\main-site\\src\\app\\api\\notifications\\";
        Description = "Real-time notification system and notification management";
        Priority = "Medium";
    },
    @{
        Name = "core-infrastructure";
        Title = "Core Infrastructure";
        Pattern = "\\apps\\main-site\\src\\components\\|\\packages\\ui\\|\\packages\\config\\|\\apps\\main-site\\src\\lib\\|\\apps\\main-site\\src\\hooks\\|\\apps\\main-site\\src\\utils\\";
        Description = "Shared components, UI library, configuration, utilities, hooks, and core infrastructure";
        Priority = "High";
    },
    @{
        Name = "database-schema";
        Title = "Database Schema & Migrations";
        Pattern = "\\apps\\main-site\\prisma\\";
        Description = "Database schema, migrations, and database-related configuration";
        Priority = "High";
    },
    @{
        Name = "static-pages";
        Title = "Static Pages & Content";
        Pattern = "\\apps\\main-site\\src\\app\\about\\|\\apps\\main-site\\src\\app\\rules\\|\\apps\\main-site\\src\\app\\page\\.tsx|\\apps\\main-site\\src\\app\\layout\\.tsx|\\apps\\main-site\\src\\app\\globals\\.css";
        Description = "Static pages including about, rules, homepage, and global layout files";
        Priority = "Low";
    },
    @{
        Name = "system-utilities";
        Title = "System Utilities & Testing";
        Pattern = "\\apps\\main-site\\src\\app\\test\\|\\apps\\main-site\\src\\app\\api\\test\\|\\apps\\main-site\\src\\app\\api\\cron\\|\\apps\\main-site\\src\\app\\api\\setup\\|\\apps\\main-site\\src\\app\\api\\uploads\\|\\apps\\main-site\\src\\app\\api\\images\\";
        Description = "Testing utilities, cron jobs, system setup, upload handling, and image processing";
        Priority = "Low";
    }
)

# Process each feature group
foreach ($group in $featureGroups) {
    $groupOutput = New-Object System.Text.StringBuilder
    [void]$groupOutput.AppendLine("# Bank of Styx Website - $($group.Title)")
    [void]$groupOutput.AppendLine("# Generated on $(Get-Date)")
    [void]$groupOutput.AppendLine("# Priority: $($group.Priority)")
    [void]$groupOutput.AppendLine("# $($group.Description)")
    [void]$groupOutput.AppendLine("# Root directory: $rootDir")
    [void]$groupOutput.AppendLine("")

    # Filter files and directories for this feature group
    $groupItems = $sortedOutput | Where-Object { $_ -match $group.Pattern }

    # Add a section header for directories
    if ($groupItems.Count -gt 0) {
        [void]$groupOutput.AppendLine("## Directories and Files")
        [void]$groupOutput.AppendLine("")

        # Separate directories and files for better organization
        $directories = $groupItems | Where-Object { $_.EndsWith("\") }
        $files = $groupItems | Where-Object { -not $_.EndsWith("\") }

        if ($directories.Count -gt 0) {
            [void]$groupOutput.AppendLine("### Directories")
            foreach ($line in $directories) {
                [void]$groupOutput.Append("$line`n")
            }
            [void]$groupOutput.AppendLine("")
        }

        if ($files.Count -gt 0) {
            [void]$groupOutput.AppendLine("### Files")
            foreach ($line in $files) {
                [void]$groupOutput.Append("$line`n")
            }
        }
    } else {
        [void]$groupOutput.AppendLine("No files found for this feature group.")
    }

    # Save to feature-specific file
    $groupFilePath = Join-Path -Path $outputFolder -ChildPath "feature-$($group.Name).txt"
    $groupOutput.ToString() | Out-File -FilePath $groupFilePath -Encoding utf8

    Write-Host "Created feature file: $groupFilePath with $($groupItems.Count) items (Priority: $($group.Priority))"
}

# Create a comprehensive summary file with links to all feature files
$summaryOutput = New-Object System.Text.StringBuilder
[void]$summaryOutput.AppendLine("# Bank of Styx Website - Feature-Based File Tree Summary")
[void]$summaryOutput.AppendLine("# Generated on $(Get-Date)")
[void]$summaryOutput.AppendLine("# This file provides organized access to feature-specific file trees")
[void]$summaryOutput.AppendLine("")
[void]$summaryOutput.AppendLine("## Overview")
[void]$summaryOutput.AppendLine("The Bank of Styx website has been organized into feature-based file trees to improve")
[void]$summaryOutput.AppendLine("navigation and understanding of the codebase structure. Each feature contains all")
[void]$summaryOutput.AppendLine("relevant files including pages, API endpoints, components, and related utilities.")
[void]$summaryOutput.AppendLine("")

# Group features by priority
$highPriorityFeatures = $featureGroups | Where-Object { $_.Priority -eq "High" }
$mediumPriorityFeatures = $featureGroups | Where-Object { $_.Priority -eq "Medium" }
$lowPriorityFeatures = $featureGroups | Where-Object { $_.Priority -eq "Low" }

[void]$summaryOutput.AppendLine("## HIGH PRIORITY FEATURES (Core Systems)")
[void]$summaryOutput.AppendLine("These are the essential systems that drive the Bank of Styx functionality:")
[void]$summaryOutput.AppendLine("")

foreach ($group in $highPriorityFeatures) {
    [void]$summaryOutput.AppendLine("### $($group.Title)")
    [void]$summaryOutput.AppendLine("$($group.Description)")
    [void]$summaryOutput.AppendLine("File: [feature-$($group.Name).txt](feature-$($group.Name).txt)")
    [void]$summaryOutput.AppendLine("")
}

[void]$summaryOutput.AppendLine("## MEDIUM PRIORITY FEATURES (Extended Functionality)")
[void]$summaryOutput.AppendLine("Important features that extend the core functionality:")
[void]$summaryOutput.AppendLine("")

foreach ($group in $mediumPriorityFeatures) {
    [void]$summaryOutput.AppendLine("### $($group.Title)")
    [void]$summaryOutput.AppendLine("$($group.Description)")
    [void]$summaryOutput.AppendLine("File: [feature-$($group.Name).txt](feature-$($group.Name).txt)")
    [void]$summaryOutput.AppendLine("")
}

[void]$summaryOutput.AppendLine("## LOW PRIORITY FEATURES (Supporting Systems)")
[void]$summaryOutput.AppendLine("Supporting features and utilities:")
[void]$summaryOutput.AppendLine("")

foreach ($group in $lowPriorityFeatures) {
    [void]$summaryOutput.AppendLine("### $($group.Title)")
    [void]$summaryOutput.AppendLine("$($group.Description)")
    [void]$summaryOutput.AppendLine("File: [feature-$($group.Name).txt](feature-$($group.Name).txt)")
    [void]$summaryOutput.AppendLine("")
}

[void]$summaryOutput.AppendLine("## ADDITIONAL RESOURCES")
[void]$summaryOutput.AppendLine("")
[void]$summaryOutput.AppendLine("### Complete File Tree")
[void]$summaryOutput.AppendLine("The complete file tree with all files and directories (for reference)")
[void]$summaryOutput.AppendLine("File: [website-file-tree.txt](../website-file-tree.txt)")
[void]$summaryOutput.AppendLine("")
[void]$summaryOutput.AppendLine("### Documentation")
[void]$summaryOutput.AppendLine("For detailed feature documentation, see the [docs/features/](../../docs/features/) directory")
[void]$summaryOutput.AppendLine("")
[void]$summaryOutput.AppendLine("### Feature Count Summary")
[void]$summaryOutput.AppendLine("- **High Priority**: $($highPriorityFeatures.Count) features")
[void]$summaryOutput.AppendLine("- **Medium Priority**: $($mediumPriorityFeatures.Count) features")
[void]$summaryOutput.AppendLine("- **Low Priority**: $($lowPriorityFeatures.Count) features")
[void]$summaryOutput.AppendLine("- **Total Features**: $($featureGroups.Count) features")

# Save the summary file
$summaryFilePath = Join-Path -Path $outputFolder -ChildPath "README.md"
$summaryOutput.ToString() | Out-File -FilePath $summaryFilePath -Encoding utf8

Write-Host "Created feature summary file: $summaryFilePath"
Write-Host ""
Write-Host "=== Feature-based file tree processing complete! ==="
Write-Host "Summary:"
Write-Host "   - Generated $($featureGroups.Count) feature-specific file trees"
Write-Host "   - High Priority Features: $($highPriorityFeatures.Count)"
Write-Host "   - Medium Priority Features: $($mediumPriorityFeatures.Count)"
Write-Host "   - Low Priority Features: $($lowPriorityFeatures.Count)"
Write-Host "   - Total files and directories processed: $($sortedOutput.Count)"
Write-Host ""
Write-Host "Output location: $outputFolder"
Write-Host "Start with: $summaryFilePath"
