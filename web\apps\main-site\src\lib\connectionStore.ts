/**
 * Connection Store for Server-Sent Events (SSE)
 *
 * This module provides a store for managing SSE connections.
 * It allows tracking active connections and provides methods for adding and removing connections.
 * It also provides methods for sending messages to specific users or broadcasting to all users.
 */

import crypto from "crypto";
import prisma from "@/lib/prisma";

// Helper function for timestamped logging
function logWithTimestamp(message: string, ...args: any[]) {
  const timestamp = new Date().toISOString().replace("T", " ").replace("Z", "");
  console.log(`[${timestamp}] ${message}`, ...args);
}

// Helper function for timestamped error logging
function errorWithTimestamp(message: string, ...args: any[]) {
  const timestamp = new Date().toISOString().replace("T", " ").replace("Z", "");
  console.error(`[${timestamp}] ${message}`, ...args);
}

// Define the connection type
type Connection = {
  userId: string;
  writer: WritableStreamDefaultWriter<Uint8Array>;
  lastActivity: Date;
  // Track connection statistics
  messagesSent: number;
  messagesReceived: number;
  bytesTransferred: number;
  errors: number;
};

// Define connection statistics type
export type ConnectionStats = {
  totalConnections: number;
  activeUsers: number;
  messagesSent: number;
  messagesReceived: number;
  bytesTransferred: number;
  errors: number;
  connectionsPerUser: Record<string, number>;
};

// Create a connection store class
class ConnectionStore {
  private connections: Map<string, Connection> = new Map();
  // Map to track the active connection for each user (userId -> connectionId)
  private userConnectionMap: Map<string, string> = new Map();
  private totalMessagesSent = 0;
  private totalMessagesReceived = 0;
  private totalBytesTransferred = 0;
  private totalErrors = 0;

  // Add a new connection
  async addConnection(
    connectionId: string,
    userId: string,
    writer: WritableStreamDefaultWriter<Uint8Array>,
  ): Promise<void> {
    // Check if user already has an active connection
    const existingConnectionId = this.userConnectionMap.get(userId);

    if (existingConnectionId) {
      logWithTimestamp(
        `[SSE] User ${userId} already has an active connection (${existingConnectionId}). Transitioning to new connection (${connectionId}).`,
      );

      // Get the existing connection
      const existingConnection = this.connections.get(existingConnectionId);

      if (existingConnection) {
        try {
          // Send a transition message to the existing connection
          const encoder = new TextEncoder();
          const transitionMessage = `data: ${JSON.stringify({
            type: "connection_replaced",
            message: "Your connection has been replaced by a new session",
            timestamp: new Date().toISOString(),
          })}\n\n`;

          // Try to notify the client that their connection is being replaced
          await existingConnection.writer.write(
            encoder.encode(transitionMessage),
          );
          logWithTimestamp(
            `[SSE] Sent transition message to connection ${existingConnectionId}`,
          );
        } catch (error) {
          // If we can't write to the connection, it's probably already broken
          errorWithTimestamp(
            `[SSE] Error sending transition message to connection ${existingConnectionId}:`,
            error,
          );
        }

        // Close the existing connection properly
        try {
          // Attempt to close the writer
          await existingConnection.writer.close();
          logWithTimestamp(
            `[SSE] Closed writer for connection ${existingConnectionId}`,
          );
        } catch (error) {
          errorWithTimestamp(
            `[SSE] Error closing writer for connection ${existingConnectionId}:`,
            error,
          );
        }
      }

      // Remove the existing connection from our maps
      this.connections.delete(existingConnectionId);
      logWithTimestamp(
        `[SSE] Removed old connection ${existingConnectionId} for user ${userId}`,
      );
    }

    // Add the new connection
    this.connections.set(connectionId, {
      userId,
      writer,
      lastActivity: new Date(),
      messagesSent: 0,
      messagesReceived: 0,
      bytesTransferred: 0,
      errors: 0,
    });

    // Update the user connection map
    this.userConnectionMap.set(userId, connectionId);

    logWithTimestamp(
      `[SSE] New connection: ${connectionId} for user ${userId}`,
    );
    logWithTimestamp(`[SSE] Total connections: ${this.connections.size}`);
    logWithTimestamp(
      `[SSE] Total unique users: ${this.userConnectionMap.size}`,
    );
  }

  // Remove a connection
  removeConnection(connectionId: string): void {
    const connection = this.connections.get(connectionId);
    if (connection) {
      logWithTimestamp(
        `[SSE] Closing connection: ${connectionId} for user ${connection.userId}`,
      );

      // Remove from connections map
      this.connections.delete(connectionId);

      // Check if this is the current active connection for the user
      const currentConnectionForUser = this.userConnectionMap.get(
        connection.userId,
      );
      if (currentConnectionForUser === connectionId) {
        // Remove from user connection map
        this.userConnectionMap.delete(connection.userId);
        logWithTimestamp(
          `[SSE] Removed user ${connection.userId} from active user map`,
        );
      }

      logWithTimestamp(`[SSE] Total connections: ${this.connections.size}`);
      logWithTimestamp(
        `[SSE] Total unique users: ${this.userConnectionMap.size}`,
      );
    }
  }

  // Get active connections for a user
  getUserConnectionCount(userId: string): number {
    // With our new architecture, a user will have either 0 or 1 connection
    return this.userConnectionMap.has(userId) ? 1 : 0;
  }

  // Get connection statistics
  getConnectionStats(): ConnectionStats {
    const connectionsPerUser: Record<string, number> = {};

    // With our new architecture, each user should only have one connection
    // But we'll still count them from the connections map for verification
    for (const connection of this.connections.values()) {
      connectionsPerUser[connection.userId] =
        (connectionsPerUser[connection.userId] || 0) + 1;
    }

    return {
      totalConnections: this.connections.size,
      activeUsers: this.userConnectionMap.size,
      messagesSent: this.totalMessagesSent,
      messagesReceived: this.totalMessagesReceived,
      bytesTransferred: this.totalBytesTransferred,
      errors: this.totalErrors,
      connectionsPerUser,
    };
  }

  // Get all connection IDs for a user
  getUserConnectionIds(userId: string): string[] {
    // With our new architecture, a user will have at most one connection
    const connectionId = this.userConnectionMap.get(userId);
    return connectionId ? [connectionId] : [];
  }

  // Get total connection count
  getTotalConnectionCount(): number {
    return this.connections.size;
  }

  // Update connection activity
  updateConnectionActivity(connectionId: string): void {
    const connection = this.connections.get(connectionId);
    if (connection) {
      connection.lastActivity = new Date();
    }
  }

  // Send a message to a specific user
  async sendToUser(userId: string, data: any): Promise<number> {
    let sentCount = 0;
    const encoder = new TextEncoder();
    const message = `data: ${JSON.stringify(data)}\n\n`;
    const encodedMessage = encoder.encode(message);
    const messageSize = encodedMessage.byteLength;

    // Get the active connection ID for this user from the userConnectionMap
    const connectionId = this.userConnectionMap.get(userId);

    if (!connectionId) {
      logWithTimestamp(`[SSE] No active connection for user ${userId}`);
      return 0;
    }

    // Get the connection
    const connection = this.connections.get(connectionId);
    if (!connection) {
      // Connection no longer exists, remove from user map
      this.userConnectionMap.delete(userId);
      logWithTimestamp(
        `[SSE] Connection ${connectionId} for user ${userId} no longer exists, removing from user map`,
      );
      return 0;
    }

    logWithTimestamp(
      `[SSE] Sending message to user ${userId} on connection ${connectionId}`,
    );

    try {
      await connection.writer.write(encodedMessage);

      // Update connection statistics
      connection.lastActivity = new Date();
      connection.messagesSent++;
      connection.bytesTransferred += messageSize;

      // Update global statistics
      this.totalMessagesSent++;
      this.totalBytesTransferred += messageSize;

      sentCount++;
      logWithTimestamp(
        `[SSE] Message sent to user ${userId} on connection ${connectionId}`,
      );
    } catch (error) {
      errorWithTimestamp(
        `[SSE] Error sending to connection ${connectionId}:`,
        error,
      );
      // Update error statistics
      connection.errors++;
      this.totalErrors++;
      // Remove the connection if we can't write to it
      this.removeConnection(connectionId);
    }

    return sentCount;
  }

  // Broadcast a message to all users (one connection per user)
  async broadcast(data: any): Promise<number> {
    let sentCount = 0;
    const encoder = new TextEncoder();
    const message = `data: ${JSON.stringify(data)}\n\n`;
    const encodedMessage = encoder.encode(message);
    const messageSize = encodedMessage.byteLength;

    logWithTimestamp(
      `[SSE] Broadcasting message to ${this.userConnectionMap.size} users`,
    );

    // Only broadcast to the active connection for each user
    for (const [userId, connectionId] of this.userConnectionMap.entries()) {
      const connection = this.connections.get(connectionId);
      if (!connection) {
        // Connection no longer exists, remove from user map
        this.userConnectionMap.delete(userId);
        continue;
      }

      try {
        await connection.writer.write(encodedMessage);

        // Update connection statistics
        connection.lastActivity = new Date();
        connection.messagesSent++;
        connection.bytesTransferred += messageSize;

        // Update global statistics
        this.totalMessagesSent++;
        this.totalBytesTransferred += messageSize;

        sentCount++;
      } catch (error) {
        errorWithTimestamp(
          `[SSE] Error broadcasting to connection ${connectionId} for user ${userId}:`,
          error,
        );
        // Update error statistics
        connection.errors++;
        this.totalErrors++;
        // Remove the connection if we can't write to it
        this.removeConnection(connectionId);
      }
    }

    if (sentCount > 0) {
      logWithTimestamp(
        `[SSE] Message broadcast to ${sentCount} users (one connection per user)`,
      );
    }

    return sentCount;
  }

  // Track the last heartbeat time and service ID to detect duplicate services
  private lastHeartbeatTime = 0;
  private lastHeartbeatServiceId: string | null = null;
  private duplicateHeartbeatCount = 0;

  // Send a heartbeat to all active user connections (one per user) using batch processing
  async sendHeartbeat(serviceId: string | null = null): Promise<number> {
    const now = performance.now();
    let sentCount = 0;
    let duplicatePrevented = 0;
    let inactiveUserCount = 0;

    // Check for duplicate heartbeat services by detecting heartbeats that are too close together
    // but from different service IDs
    if (
      this.lastHeartbeatServiceId &&
      serviceId &&
      this.lastHeartbeatServiceId !== serviceId &&
      now - this.lastHeartbeatTime < 15000
    ) {
      // Less than half the normal interval

      this.duplicateHeartbeatCount++;

      // Log a warning about duplicate services
      logWithTimestamp(
        `[SSE] WARNING: Detected potential duplicate heartbeat service!`,
      );
      logWithTimestamp(
        `[SSE] Current service: ${serviceId}, Previous service: ${this.lastHeartbeatServiceId}`,
      );
      logWithTimestamp(
        `[SSE] Time since last heartbeat: ${Math.round(
          now - this.lastHeartbeatTime,
        )}ms (expected ~30000ms)`,
      );
      logWithTimestamp(
        `[SSE] Duplicate detection count: ${this.duplicateHeartbeatCount}`,
      );

      // If we've detected this multiple times, it's likely a real duplicate
      if (this.duplicateHeartbeatCount >= 3) {
        logWithTimestamp(
          `[SSE] Multiple duplicate heartbeats detected, this is likely a duplicate service instance`,
        );
        logWithTimestamp(
          `[SSE] Consider restarting the server if this persists`,
        );

        // Return 0 to indicate no heartbeats were sent from this service
        // This effectively disables this duplicate service instance
        return 0;
      }
    } else {
      // Reset duplicate count if this is a normal heartbeat
      if (
        this.lastHeartbeatServiceId === serviceId ||
        now - this.lastHeartbeatTime >= 15000
      ) {
        this.duplicateHeartbeatCount = 0;
      }
    }

    // Update tracking
    this.lastHeartbeatTime = now;
    this.lastHeartbeatServiceId = serviceId;

    // Generate a unique batch ID for this heartbeat batch
    const batchId = crypto.randomUUID().substring(0, 8);

    // Create a Set of unique userIds from the userConnectionMap
    const uniqueUserIds = new Set<string>();
    const processedUsers = new Set<string>();

    // Collect all unique userIds
    for (const [userId, _] of this.userConnectionMap.entries()) {
      uniqueUserIds.add(userId);
    }

    logWithTimestamp(
      `[SSE] Starting batch heartbeat (Batch: ${batchId}${
        serviceId ? `, Service: ${serviceId}` : ""
      }) for ${uniqueUserIds.size} unique users`,
    );

    // Prepare the heartbeat message once for all users
    const encoder = new TextEncoder();
    const heartbeat = `data: ${JSON.stringify({
      type: "heartbeat",
      timestamp: new Date().toISOString(),
      batchId: batchId,
      serviceId: serviceId,
    })}\n\n`;
    const encodedHeartbeat = encoder.encode(heartbeat);
    const heartbeatSize = encodedHeartbeat.byteLength;

    // Process each user connection
    for (const [userId, connectionId] of this.userConnectionMap.entries()) {
      // Verification step: check if user already received a heartbeat in this batch
      if (processedUsers.has(userId)) {
        duplicatePrevented++;
        logWithTimestamp(
          `[SSE] Prevented duplicate heartbeat for user ${userId}`,
        );
        continue;
      }

      const connection = this.connections.get(connectionId);
      if (!connection) {
        // Connection no longer exists, remove from user map
        this.userConnectionMap.delete(userId);
        logWithTimestamp(
          `[SSE] Removed stale connection mapping for user ${userId}`,
        );
        continue;
      }

      // Check if the user has been inactive in the application
      // Use a shorter timeout for heartbeat checks (30 minutes) compared to connection cleanup (60 minutes)
      const isInactive = await this.isUserInactive(userId, 30);
      if (isInactive) {
        logWithTimestamp(
          `[SSE] Skipping heartbeat for inactive user: ${userId} (connection ID: ${connectionId})`,
        );
        inactiveUserCount++;
        continue;
      }

      try {
        await connection.writer.write(encodedHeartbeat);

        // Update connection statistics
        connection.lastActivity = new Date();
        connection.messagesSent++;
        connection.bytesTransferred += heartbeatSize;

        // Update global statistics
        this.totalMessagesSent++;
        this.totalBytesTransferred += heartbeatSize;

        // Mark this user as processed
        processedUsers.add(userId);
        sentCount++;
      } catch (error) {
        errorWithTimestamp(
          `[SSE] Error sending heartbeat to connection ${connectionId} for user ${userId}:`,
          error,
        );
        // Update error statistics
        connection.errors++;
        this.totalErrors++;
        // Remove the connection if we can't write to it
        this.removeConnection(connectionId);
      }
    }

    // Calculate processing time
    const endTime = performance.now();
    const processingTime = (endTime - now).toFixed(2);

    // Log detailed statistics
    if (sentCount > 0 || duplicatePrevented > 0 || inactiveUserCount > 0) {
      logWithTimestamp(
        `[SSE] Batch heartbeat (Batch: ${batchId}${
          serviceId ? `, Service: ${serviceId}` : ""
        }) completed in ${processingTime}ms:\n` +
          `  - Total unique users: ${uniqueUserIds.size}\n` +
          `  - Heartbeats sent: ${sentCount}\n` +
          `  - Inactive users skipped: ${inactiveUserCount}\n` +
          `  - Duplicate attempts prevented: ${duplicatePrevented}\n` +
          `  - Failed/removed connections: ${
            uniqueUserIds.size -
            sentCount -
            duplicatePrevented -
            inactiveUserCount
          }`,
      );
    }

    return sentCount;
  }

  // Check if a user has been inactive for longer than the specified threshold
  async isUserInactive(
    userId: string,
    maxInactiveMinutes = 60,
  ): Promise<boolean> {
    try {
      // Get the user's state from the database
      const userState = await prisma.userState.findUnique({
        where: { userId },
        select: { lastActive: true },
      });

      if (!userState || !userState.lastActive) {
        // If we can't find the user state or lastActive is null, consider the user inactive
        logWithTimestamp(
          `[SSE] User ${userId} has no lastActive timestamp, considering inactive`,
        );
        return true;
      }

      const now = new Date();
      const lastActive = new Date(userState.lastActive);
      const diffMs = now.getTime() - lastActive.getTime();
      const diffMinutes = diffMs / (1000 * 60);

      if (diffMinutes > maxInactiveMinutes) {
        logWithTimestamp(
          `[SSE] User ${userId} has been inactive for ${diffMinutes.toFixed(
            2,
          )} minutes (threshold: ${maxInactiveMinutes} minutes)`,
        );
        return true;
      }

      return false;
    } catch (error) {
      // If there's an error checking the user state, log it but don't consider the user inactive
      errorWithTimestamp(
        `[SSE] Error checking user activity for ${userId}:`,
        error,
      );
      return false;
    }
  }

  // Clean up inactive connections
  async cleanupInactiveConnections(
    maxInactiveMinutes = 30,
  ): Promise<void> {
    const now = new Date();
    logWithTimestamp(
      `[SSE] Checking for inactive connections (inactive > ${maxInactiveMinutes} minutes)`,
    );

    // Create an array of promises for checking user activity
    const checkPromises: Promise<void>[] = [];

    for (const [connectionId, connection] of this.connections.entries()) {
      // First check connection activity based on the last message sent
      const diffMs = now.getTime() - connection.lastActivity.getTime();
      const diffMinutes = diffMs / (1000 * 60);

      if (diffMinutes > maxInactiveMinutes) {
        logWithTimestamp(
          `[SSE] Removing inactive connection: ${connectionId} (inactive for ${diffMinutes.toFixed(
            2,
          )} minutes)`,
        );
        this.removeConnection(connectionId);
        continue;
      }

      // If the connection is active, check if the user has been inactive in the application
      const checkPromise = (async () => {
        const isInactive = await this.isUserInactive(connection.userId, 60); // 60 minutes user inactivity threshold
        if (isInactive) {
          logWithTimestamp(
            `[SSE] Removing connection for inactive user: ${connection.userId} (connection ID: ${connectionId})`,
          );
          this.removeConnection(connectionId);
        }
      })();

      checkPromises.push(checkPromise);
    }

    // Wait for all checks to complete
    await Promise.all(checkPromises);
  }
}

// Export a singleton instance
export const connectionStore = new ConnectionStore();
