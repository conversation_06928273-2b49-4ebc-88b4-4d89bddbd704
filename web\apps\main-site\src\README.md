# Source Code

The main source code directory containing all application logic, components, and utilities.

## Structure

- **app/** - Next.js App Router pages and API routes following the new App Router architecture
- **components/** - React components organized by feature and functionality
- **contexts/** - React Context providers for global state management
- **hooks/** - Custom React hooks for reusable logic
- **lib/** - Core utility functions and configurations
- **providers/** - React providers for external services (TanStack Query, etc.)
- **services/** - API service functions for backend communication
- **types/** - TypeScript type definitions and interfaces
- **utils/** - Helper utilities and shared functions
- **scripts/** - Client-side scripts and utilities
- **tests/** - Testing utilities and test helpers

This directory follows Next.js 13+ App Router conventions with a clear separation of concerns between UI components, business logic, and data management.
