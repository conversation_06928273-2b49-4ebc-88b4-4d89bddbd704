import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import prisma from '@/lib/prisma';

export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const captainId = user.id;

    const { userId, message, roleId } = await request.json();

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    // Verify captain owns a ship
    const ship = await prisma.ship.findFirst({
      where: {
        captainId,
        status: 'active'
      }
    });

    if (!ship) {
      return NextResponse.json({ error: 'Ship not found or you are not the captain' }, { status: 404 });
    }

    // Check if user exists
    const invitedUser = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!invitedUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Check if user is already a member or has pending invite/request
    const existingMember = await prisma.shipMember.findUnique({
      where: {
        userId_shipId: {
          userId,
          shipId: ship.id
        }
      }
    });

    if (existingMember) {
      return NextResponse.json({ error: 'User is already a member of this ship' }, { status: 400 });
    }

    const existingRequest = await prisma.shipJoinRequest.findFirst({
      where: {
        userId,
        shipId: ship.id,
        status: 'pending'
      }
    });

    if (existingRequest) {
      return NextResponse.json({ error: 'User already has a pending invite or request' }, { status: 400 });
    }

    // Verify role exists if provided
    if (roleId) {
      const role = await prisma.shipRole.findFirst({
        where: {
          id: roleId,
          shipId: ship.id
        }
      });

      if (!role) {
        return NextResponse.json({ error: 'Role not found' }, { status: 404 });
      }
    }

    // Determine role information for the invitation
    let inviteMessage = message || `You've been invited to join ${ship.name}!`;
    let roleName = 'Member';
    
    // If a role was specified, include it in the message for parsing later
    if (roleId) {
      const role = await prisma.shipRole.findFirst({
        where: {
          id: roleId,
          shipId: ship.id
        }
      });
      
      if (role) {
        roleName = role.name;
        inviteMessage = message || `You've been invited to join ${ship.name} as ${role.name}!`;
      }
    }

    // Create invitation
    const invitation = await prisma.shipJoinRequest.create({
      data: {
        userId,
        shipId: ship.id,
        requestedById: captainId,
        type: 'invite',
        message: inviteMessage,
        status: 'pending'
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true
          }
        },
        ship: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    // Create notification for the invited user
    await prisma.notification.create({
      data: {
        userId,
        category: 'ship',
        type: 'ship_invitation',
        title: 'Ship Invitation',
        message: `You've been invited to join ${ship.name}. Visit the ship page to respond.`,
        read: false
      }
    });

    return NextResponse.json({
      success: true,
      invitation
    });

  } catch (error) {
    console.error('Error inviting user to ship:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}