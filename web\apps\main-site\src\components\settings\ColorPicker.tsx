"use client";

import React, { useState, useEffect } from "react";

interface ColorPickerProps {
  value: string;
  onChange: (value: string) => void;
  label: string;
  description?: string;
  variable: string;
}

// Predefined color palette
const colorPalette = [
  // Discord-inspired colors
  "#5865F2",
  "#4752C4",
  "#7984F5", // Primary blues
  "#2C2F33",
  "#23272A",
  "#36393F", // Secondary grays
  "#ED4245",
  "#D03A3D",
  "#F25D60", // Accent reds

  // Status colors
  "#43B581", // Success green
  "#FAA61A", // Warning orange
  "#F04747", // Error red

  // Text colors
  "#FFFFFF", // White
  "#99AAB5", // Gray 400
  "#72767d", // Gray 500

  // Additional colors
  "#000000", // Black
  "#4B5563", // Border dark
  "#474B52", // Hover background
];

export const ColorPicker: React.FC<ColorPickerProps> = ({
  value,
  onChange,
  label,
  description,
  variable,
}) => {
  const [hexValue, setHexValue] = useState(value);

  // Update hex input when color changes
  useEffect(() => {
    setHexValue(value);
  }, [value]);

  // Handle hex input change
  const handleHexChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setHexValue(newValue);

    // Only update the actual color if it's a valid hex
    if (/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(newValue)) {
      onChange(newValue);
    }
  };

  // Handle color input change
  const handleColorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setHexValue(newValue);
    onChange(newValue);
  };

  // Handle palette color selection
  const handlePaletteSelect = (color: string) => {
    setHexValue(color);
    onChange(color);
  };

  return (
    <div className="space-y-3">
      <div className="flex justify-between items-center">
        <label htmlFor={variable} className="font-medium">
          {label}
        </label>
        <div
          className="w-8 h-8 rounded-full border border-gray-600"
          style={{ backgroundColor: value }}
        ></div>
      </div>

      {/* Color input */}
      <div className="flex space-x-2">
        <input
          id={variable}
          type="color"
          value={value}
          onChange={handleColorChange}
          className="w-12 h-10 rounded cursor-pointer bg-secondary border border-gray-600"
        />
        <input
          type="text"
          value={hexValue}
          onChange={handleHexChange}
          placeholder="#RRGGBB"
          className="flex-1 px-3 py-2 bg-secondary border border-gray-600 rounded focus:border-primary focus:outline-none"
        />
      </div>

      {/* Color palette */}
      <div className="flex flex-wrap gap-2 mt-2">
        {colorPalette.map((color) => (
          <button
            key={color}
            type="button"
            className={`w-6 h-6 rounded-full border ${
              color === value ? "border-primary border-2" : "border-gray-600"
            }`}
            style={{ backgroundColor: color }}
            onClick={() => handlePaletteSelect(color)}
            aria-label={`Select color ${color}`}
          />
        ))}
      </div>

      {description && (
        <p className="text-sm text-text-secondary">{description}</p>
      )}
    </div>
  );
};
