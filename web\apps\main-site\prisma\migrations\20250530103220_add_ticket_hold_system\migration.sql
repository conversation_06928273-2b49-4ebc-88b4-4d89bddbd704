/*
  Warnings:

  - A unique constraint covering the columns `[slotId]` on the table `volunteer_assignments` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE `volunteer_assignments` ADD COLUMN `slotId` VARCHAR(191) NULL;

-- CreateTable
CREATE TABLE `volunteer_slots` (
    `id` VARCHAR(191) NOT NULL,
    `shiftId` VARCHAR(191) NOT NULL,
    `status` ENUM('AVAILABLE', 'HELD', 'SOLD', 'CANCELLED') NOT NULL DEFAULT 'AVAILABLE',
    `holdId` VARCHAR(191) NULL,
    `assignmentId` VARCHAR(191) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `volunteer_slots_assignmentId_key`(`assignmentId`),
    INDEX `volunteer_slots_shiftId_idx`(`shiftId`),
    INDEX `volunteer_slots_status_idx`(`status`),
    INDEX `volunteer_slots_holdId_idx`(`holdId`),
    INDEX `volunteer_slots_assignmentId_idx`(`assignmentId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `volunteer_slot_holds` (
    `id` VARCHAR(191) NOT NULL,
    `userId` VARCHAR(191) NOT NULL,
    `expiresAt` DATETIME(3) NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    INDEX `volunteer_slot_holds_userId_idx`(`userId`),
    INDEX `volunteer_slot_holds_expiresAt_idx`(`expiresAt`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `tickets` (
    `id` VARCHAR(191) NOT NULL,
    `productId` VARCHAR(191) NOT NULL,
    `status` ENUM('AVAILABLE', 'HELD', 'SOLD', 'CANCELLED') NOT NULL DEFAULT 'AVAILABLE',
    `holdId` VARCHAR(191) NULL,
    `orderId` VARCHAR(191) NULL,
    `seatInfo` JSON NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    INDEX `tickets_productId_idx`(`productId`),
    INDEX `tickets_status_idx`(`status`),
    INDEX `tickets_holdId_idx`(`holdId`),
    INDEX `tickets_orderId_idx`(`orderId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ticket_holds` (
    `id` VARCHAR(191) NOT NULL,
    `userId` VARCHAR(191) NOT NULL,
    `cartItemId` VARCHAR(191) NULL,
    `expiresAt` DATETIME(3) NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `ticket_holds_cartItemId_key`(`cartItemId`),
    INDEX `ticket_holds_userId_idx`(`userId`),
    INDEX `ticket_holds_expiresAt_idx`(`expiresAt`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateIndex
CREATE UNIQUE INDEX `volunteer_assignments_slotId_key` ON `volunteer_assignments`(`slotId`);

-- CreateIndex
CREATE INDEX `volunteer_assignments_slotId_idx` ON `volunteer_assignments`(`slotId`);

-- AddForeignKey
ALTER TABLE `volunteer_assignments` ADD CONSTRAINT `volunteer_assignments_slotId_fkey` FOREIGN KEY (`slotId`) REFERENCES `volunteer_slots`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `volunteer_slots` ADD CONSTRAINT `volunteer_slots_shiftId_fkey` FOREIGN KEY (`shiftId`) REFERENCES `volunteer_shifts`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `volunteer_slots` ADD CONSTRAINT `volunteer_slots_holdId_fkey` FOREIGN KEY (`holdId`) REFERENCES `volunteer_slot_holds`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `volunteer_slot_holds` ADD CONSTRAINT `volunteer_slot_holds_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- Note: The problematic foreign key constraint has been removed
-- We'll add it later after the products table is created

-- AddForeignKey
ALTER TABLE `tickets` ADD CONSTRAINT `tickets_holdId_fkey` FOREIGN KEY (`holdId`) REFERENCES `ticket_holds`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `tickets` ADD CONSTRAINT `tickets_orderId_fkey` FOREIGN KEY (`orderId`) REFERENCES `orders`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ticket_holds` ADD CONSTRAINT `ticket_holds_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ticket_holds` ADD CONSTRAINT `ticket_holds_cartItemId_fkey` FOREIGN KEY (`cartItemId`) REFERENCES `cart_items`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
