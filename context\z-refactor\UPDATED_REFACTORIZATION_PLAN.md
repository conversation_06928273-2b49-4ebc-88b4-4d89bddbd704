# Updated Image Upload Refactorization Plan
## Incremental Approach for Pre-Launch Development

## Executive Summary

This updated plan takes a simpler, incremental approach to consolidating image upload functionality. Since we're still in pre-launch development with a single developer (with AI assistance), we'll build the new system alongside the existing one, then gradually switch components over. This approach minimizes risk while still allowing for rapid development progress.

## Strategy Overview

### Phase 1: Build New System (Non-Destructive)
- Create unified API alongside existing endpoints
- Build universal components without removing old ones
- Implement new services while keeping existing ones functional
- Test each piece as it's built

### Phase 2: Gradual Migration (Component by Component)
- Switch one upload type at a time
- Test each migration immediately
- Simple rollback if needed (just switch back to old component)

### Phase 3: Clean Up
- Remove old components after new ones are working
- Delete redundant APIs
- Final cleanup

## Implementation Steps

### Phase 1: Build New Unified System (Parallel Implementation)
C:\Users\<USER>\projects\test\web\apps\main-site\src\app\api The actual directory
#### 1.1 Create New Unified API (Keep Existing APIs)
- [x] Create `/api/uploads/v2/route.ts` (new unified endpoint)
- [x] Keep existing `/api/uploads/route.ts` and `/api/uploads/avatar/route.ts` functional
- [x] Implement type-based routing in v2 endpoint
- [x] Add upload configuration system web\apps\main-site\src\lib\uploadConfig.ts

**New API Structure:**
```typescript
// /api/uploads/v2/route.ts - New unified endpoint
POST /api/uploads/v2
{
  file: File,
  type: 'avatar' | 'news' | 'event' | 'product' | 'deposit',
  entityId?: string,
  options?: UploadOptions
}
```

#### 1.2 Build New Backend Services (Alongside Existing)
- [x] Create `services/uploadServiceV2.ts` (keep existing services)
- [x] Create `lib/uploadConfig.ts` (configuration definitions)
- [x] Create `lib/imageProcessingV2.ts` (enhanced utilities)
- [x] Implement progress tracking and validation

#### 1.3 Create Universal Frontend Components (Non-Destructive)
- [x] Create `components/common/UniversalImageUploader.tsx`
- [x] Create wrapper components:
  - [x] `components/upload/AvatarUploaderV2.tsx`
  - [x] `components/upload/NewsImageUploaderV2.tsx`
  - [x] `components/upload/ProductImageUploader.tsx` (new)
  - [x] `components/upload/DepositReceiptUploader.tsx` (new)
- [x] Keep all existing components functional

#### 1.4 Database Schema Enhancement (Additive Only)
- [x] Add new columns to existing `UploadedImage` model:
  ```sql
  ALTER TABLE uploaded_image ADD COLUMN upload_type VARCHAR(50) DEFAULT 'general';
  ALTER TABLE uploaded_image ADD COLUMN upload_config JSON;
  ALTER TABLE uploaded_image ADD COLUMN dimensions JSON;
  ```
- [x] Create indexes for performance
- [x] No data deletion or breaking changes

### Phase 2: Gradual Component Migration


#### 2.1 Deposit System Migration (First Migration)
- [x] Create `DepositReceiptUploader` using universal system
- [x] Replace custom deposit upload implementation
- [x] Add PDF support for receipts
- [x] Keep old deposit upload as fallback (removed in this migration)
- [x] Test deposit receipt functionality

**Rollback Plan:** Switch back to old components if issues arise

#### 2.2 News System Migration (Second Migration)
**Why Second:** Well-defined use case with existing patterns

- [x] Create `NewsImageUploaderV2` using universal system
- [x] Migrate news featured images:
  - [x] News creation form
  - [x] News editing form
  - [ ] Rich text editor inline images
- [x] Keep `FeaturedImageUploader` as fallback during transition
- [ ] Test all news image functionality

#### 2.3 Event System Migration (Third Migration)
- [ ] Replace existing `ImageUploader` with `UniversalImageUploader`
- [ ] Update event creation/editing forms
- [ ] Keep old event upload logic as fallback
- [ ] Test event image management

#### 2.4 Product System Implementation (Fourth Migration)
**Why Fourth:** New functionality, no existing system to break

- [ ] Implement `ProductImageUploader` component
- [ ] Add to product creation/editing forms
- [ ] Implement product image gallery
- [ ] No fallback needed (new feature)
- [ ] Test functionality


#### 2.5 Avatar System Migration (Final Migration)

- [x] Create `AvatarUploaderV2` component using new universal system
- [ ] Update one avatar upload location at a time:
  - [ ] User settings page (test with single page)
  - [ ] Profile creation form
  - [ ] Admin user management
- [ ] Keep `SimpleAvatarUpload` and `AvatarUploadModal` as fallback
- [ ] Test avatar functionality works correctly

### Phase 3: Cleanup

#### 3.1 Component Cleanup
- [ ] Remove `SimpleAvatarUpload` (after avatar migration works)
- [ ] Remove `AvatarUploadModal` (after avatar migration works)
- [ ] Remove `FeaturedImageUploader` (after news migration works)
- [ ] Remove old `ImageUploader` (after event migration works)
- [ ] Remove custom deposit upload logic (after deposit migration works)

#### 3.2 Service Cleanup
- [ ] Remove `avatarService.ts` (after avatar components removed)
- [ ] Remove old `upload-service.ts` (after all components migrated)
- [ ] Rename `uploadServiceV2.ts` to `uploadService.ts`

#### 3.3 API Cleanup (Final Step)
- [ ] Remove `/api/uploads/avatar/route.ts`
- [ ] Update `/api/uploads/route.ts` to redirect to v2 or replace entirely
- [ ] Rename `/api/uploads/v2/route.ts` to `/api/uploads/route.ts`

## File Structure During Transition

### Phase 1 - Parallel Implementation:
```
src/
├── services/
│   ├── uploadService.ts (existing - keep)
│   ├── uploadServiceV2.ts (new - unified)
│   └── avatarService.ts (existing - keep)
├── components/
│   ├── common/
│   │   ├── ImageUploader.tsx (existing - keep)
│   │   └── UniversalImageUploader.tsx (new)
│   ├── upload/ (new directory)
│   │   ├── AvatarUploaderV2.tsx (new)
│   │   ├── NewsImageUploaderV2.tsx (new)
│   │   ├── ProductImageUploader.tsx (new)
│   │   └── DepositReceiptUploader.tsx (new)
│   ├── news/
│   │   └── FeaturedImageUploader.tsx (existing - keep)
│   └── user/
│       ├── SimpleAvatarUpload.tsx (existing - keep)
│       └── AvatarUploadModal.tsx (existing - keep)

api/uploads/
├── route.ts (existing - keep)
├── avatar/route.ts (existing - keep)
├── v2/route.ts (new - unified)
└── [type]/[filename]/route.ts (existing - update)
```

### Phase 3 - After Cleanup:
```
src/
├── services/
│   └── uploadService.ts (renamed from V2)
├── components/
│   ├── common/
│   │   └── UniversalImageUploader.tsx
│   └── upload/
│       ├── AvatarUploader.tsx
│       ├── NewsImageUploader.tsx
│       ├── ProductImageUploader.tsx
│       └── DepositReceiptUploader.tsx

api/uploads/
├── route.ts (unified endpoint)
└── [type]/[filename]/route.ts
```

## Simple Testing Checklist

For each component migration, check:
- [ ] Component renders correctly
- [ ] Upload works
- [ ] Image appears after upload
- [ ] Error handling works
- [ ] Basic validation works

## Benefits of This Approach

### 1. Risk Reduction
- No destructive changes until new system is proven
- Easy rollback at any stage
- Test each piece individually

### 2. Development Flexibility
- Continue development while refactoring
- No need to complete everything at once
- Focus on one component at a time

### 3. Learning Opportunity
- Build understanding of the system piece by piece
- Easier to debug smaller changes
- More manageable for a single developer

## Next Steps

1. Start with Phase 1: Build the new unified API and components
2. Test each piece as you build it
3. Begin migrating components one by one
4. Clean up old code after new system is working

---

*This updated plan provides a practical, incremental approach for a pre-launch project with a single developer, focusing on safety and simplicity while still achieving the goal of a unified upload system.*