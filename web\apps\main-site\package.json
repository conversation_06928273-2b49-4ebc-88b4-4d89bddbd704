{"name": "main-site", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "check": "npx tsc --noEmit", "build": "next build", "start": "next start", "lint": "next lint", "prisma:seed": "node prisma/seed.js", "prisma:studio": "prisma studio"}, "dependencies": {"@bank-of-styx/ui": "workspace:*", "@prisma/client": "^6.6.0", "@radix-ui/react-tabs": "^1.1.11", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.0", "@tanstack/react-query": "^5.17.19", "@tanstack/react-query-devtools": "^5.74.7", "@types/event-source-polyfill": "^1.0.5", "@types/recharts": "^2.0.1", "bcryptjs": "^3.0.2", "date-fns": "^4.1.0", "dompurify": "^3.2.5", "event-source-polyfill": "^1.0.31", "jsonwebtoken": "^9.0.2", "next": "^13.4.2", "nodemailer": "^6.10.1", "react": "18.2.0", "react-dom": "18.2.0", "react-hot-toast": "^2.5.2", "react-image-crop": "10.1.8", "react-quill": "^2.0.0", "recharts": "^2.15.3", "sharp": "^0.34.2", "stripe": "^18.1.1", "uuid": "^11.1.0"}, "devDependencies": {"@bank-of-styx/config": "workspace:*", "@types/bcryptjs": "^3.0.0", "@types/dompurify": "^3.2.0", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^18.16.0", "@types/nodemailer": "^6.4.17", "@types/react": "18.2.0", "@types/react-dom": "18.2.0", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.14", "chalk": "^5.4.1", "eslint": "^8.40.0", "eslint-config-next": "^13.4.2", "postcss": "^8.4.23", "prisma": "^6.6.0", "tailwindcss": "^3.3.2", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}