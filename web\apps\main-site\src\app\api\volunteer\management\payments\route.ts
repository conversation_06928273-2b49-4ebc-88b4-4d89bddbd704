import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser, userHasRole } from "@/lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// GET /api/volunteer/management/payments - Get pending volunteer payments
export async function GET(req: NextRequest) {
  try {
    // Check if user is authenticated and has volunteer coordinator role
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 },
      );
    }

    const hasCoordinatorRole = await userHasRole(req, "volunteerCoordinator");
    if (!hasCoordinatorRole) {
      return NextResponse.json(
        { error: "Unauthorized - Coordinator role required" },
        { status: 403 },
      );
    }

    // Get query parameters for filtering
    const url = new URL(req.url);
    const eventId = url.searchParams.get("eventId");
    const categoryId = url.searchParams.get("categoryId");
    const startDate = url.searchParams.get("startDate");
    const endDate = url.searchParams.get("endDate");
    const userId = url.searchParams.get("userId");
    const status = url.searchParams.get("status") || "pending"; // Default to pending

    // Build the where clause for filtering
    const where: any = {
      paymentStatus: status,
    };

    // Add optional filters if provided
    if (eventId) {
      where.assignment = {
        ...where.assignment,
        shift: {
          ...where.assignment?.shift,
          eventId,
        },
      };
    }

    if (categoryId) {
      where.assignment = {
        ...where.assignment,
        shift: {
          ...where.assignment?.shift,
          categoryId,
        },
      };
    }

    if (startDate) {
      where.assignment = {
        ...where.assignment,
        shift: {
          ...where.assignment?.shift,
          startTime: {
            gte: new Date(startDate),
          },
        },
      };
    }

    if (endDate) {
      where.assignment = {
        ...where.assignment,
        shift: {
          ...where.assignment?.shift,
          endTime: {
            lte: new Date(endDate),
          },
        },
      };
    }

    if (userId) {
      where.userId = userId;
    }

    // Fetch volunteer hours with payment status matching the filter
    const payments = await prisma.volunteerHours.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
        verifiedBy: {
          select: {
            id: true,
            username: true,
            displayName: true,
          },
        },
        transaction: {
          select: {
            id: true,
            amount: true,
            status: true,
            createdAt: true,
          },
        },
        assignment: {
          include: {
            shift: {
              include: {
                event: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
                category: {
                  select: {
                    id: true,
                    name: true,
                    payRate: true,
                  },
                },
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return NextResponse.json(payments);
  } catch (error) {
    console.error("Error fetching volunteer payments:", error);
    return NextResponse.json(
      { error: "Failed to fetch volunteer payments" },
      { status: 500 },
    );
  }
}
