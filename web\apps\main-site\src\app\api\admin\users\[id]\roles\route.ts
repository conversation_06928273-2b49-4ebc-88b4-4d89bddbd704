import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser, userHasRole } from "@/lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
interface Params {
  params: {
    id: string;
  };
}

export async function PATCH(request: Request, { params }: Params) {
  const { id } = params;

  try {
    // Check if user is authenticated and has admin role
    const currentUser = await getCurrentUser(request);
    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    const isAdmin = await userHasRole(request, "admin");
    if (!isAdmin) {
      return NextResponse.json(
        { error: "Admin privileges required" },
        { status: 403 },
      );
    }

    // Get roles from request body
    const {
      admin,
      editor,
      banker,
      chatModerator,
      volunteerCoordinator,
      leadManager,
      salesManager,
      landSteward,
    } = await request.json();

    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { id },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Update user roles
    const updatedUser = await prisma.user.update({
      where: { id },
      data: {
        isAdmin: admin !== undefined ? admin : user.isAdmin,
        isEditor: editor !== undefined ? editor : user.isEditor,
        isBanker: banker !== undefined ? banker : user.isBanker,
        isChatModerator:
          chatModerator !== undefined ? chatModerator : user.isChatModerator,
        isVolunteerCoordinator:
          volunteerCoordinator !== undefined
            ? volunteerCoordinator
            : user.isVolunteerCoordinator,
        isLeadManager:
          leadManager !== undefined ? leadManager : user.isLeadManager,
        isSalesManager:
          salesManager !== undefined ? salesManager : user.isSalesManager,
        isLandSteward:
          landSteward !== undefined ? landSteward : user.isLandSteward,
      },
      select: {
        id: true,
        username: true,
        displayName: true,
        avatar: true,
        email: true,
        isEmailVerified: true,
        isAdmin: true,
        isEditor: true,
        isBanker: true,
        isChatModerator: true,
        isSalesManager: true,
        isVolunteerCoordinator: true,
        isLeadManager: true,
        isLandSteward: true,
        createdAt: true,
      },
    });

    // Transform user to match the AdminUser interface
    const transformedUser = {
      id: updatedUser.id,
      username: updatedUser.username,
      displayName: updatedUser.displayName,
      avatar: updatedUser.avatar,
      email: updatedUser.email,
      isEmailVerified: updatedUser.isEmailVerified,
      status: "active", // This would need to be mapped from your actual schema
      roles: {
        admin: updatedUser.isAdmin,
        editor: updatedUser.isEditor,
        banker: updatedUser.isBanker,
        chatModerator: updatedUser.isChatModerator,
        volunteerCoordinator: updatedUser.isVolunteerCoordinator,
        leadManager: updatedUser.isLeadManager,
        salesManager: updatedUser.isSalesManager,
        landSteward: updatedUser.isLandSteward,
      },
      createdAt: updatedUser.createdAt.toISOString(),
    };

    return NextResponse.json(transformedUser);
  } catch (error) {
    console.error("Error updating user roles:", error);
    return NextResponse.json(
      { error: "Failed to update user roles" },
      { status: 500 },
    );
  }
}
