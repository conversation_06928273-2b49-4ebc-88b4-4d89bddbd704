import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser, userHasRole } from "@/lib/auth";

export const dynamic = 'force-dynamic';

interface Params {
  params: {
    id: string;
  };
}

export async function PATCH(request: Request, { params }: Params) {
  try {
    const { id } = params;
    const currentUser = await getCurrentUser(request);

    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    const isLandSteward = await userHasRole(request, "landSteward");
    if (!isLandSteward) {
      return NextResponse.json(
        { error: "Land Steward privileges required" },
        { status: 403 }
      );
    }

    const { action, rejectionReason } = await request.json();

    if (!['approve', 'reject'].includes(action)) {
      return NextResponse.json(
        { error: "Invalid action. Must be 'approve' or 'reject'" },
        { status: 400 }
      );
    }

    if (action === 'reject' && !rejectionReason) {
      return NextResponse.json(
        { error: "Rejection reason is required" },
        { status: 400 }
      );
    }

    // Get the application
    const application = await prisma.captainApplication.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            displayName: true,
          },
        },
      },
    });

    if (!application) {
      return NextResponse.json(
        { error: "Application not found" },
        { status: 404 }
      );
    }

    if (application.status !== 'pending') {
      return NextResponse.json(
        { error: "Application has already been reviewed" },
        { status: 400 }
      );
    }

    if (action === 'approve') {
      // Check if ship name is still available
      const existingShip = await prisma.ship.findFirst({
        where: {
          name: application.shipName,
          status: { not: 'deleted' },
        },
      });

      if (existingShip) {
        return NextResponse.json(
          { error: "Ship name is no longer available" },
          { status: 400 }
        );
      }

      // Use transaction to create ship and update application
      const result = await prisma.$transaction(async (tx) => {
        // Update application
        const updatedApplication = await tx.captainApplication.update({
          where: { id },
          data: {
            status: 'approved',
            reviewedAt: new Date(),
            reviewedById: currentUser.id,
          },
        });

        // Create the ship
        const ship = await tx.ship.create({
          data: {
            name: application.shipName,
            description: application.description,
            logo: application.logoPath,
            tags: application.tags as any, // Cast to InputJsonValue
            captainId: application.userId,
            status: 'active',
          },
        });

        // Add captain as first member
        await tx.shipMember.create({
          data: {
            userId: application.userId,
            shipId: ship.id,
            role: 'Captain',
            status: 'active',
          },
        });

        return { application: updatedApplication, ship };
      });

      return NextResponse.json({
        success: true,
        message: `Application approved. Ship "${application.shipName}" created successfully.`,
        application: {
          id: result.application.id,
          status: result.application.status,
          reviewedAt: result.application.reviewedAt?.toISOString(),
        },
        ship: {
          id: result.ship.id,
          name: result.ship.name,
        },
      });
    } else {
      // Reject application
      const updatedApplication = await prisma.captainApplication.update({
        where: { id },
        data: {
          status: 'rejected',
          rejectionReason,
          reviewedAt: new Date(),
          reviewedById: currentUser.id,
        },
      });

      return NextResponse.json({
        success: true,
        message: `Application rejected.`,
        application: {
          id: updatedApplication.id,
          status: updatedApplication.status,
          rejectionReason: updatedApplication.rejectionReason,
          reviewedAt: updatedApplication.reviewedAt?.toISOString(),
        },
      });
    }
  } catch (error) {
    console.error("Error reviewing captain application:", error);
    return NextResponse.json(
      { error: "Failed to review application" },
      { status: 500 }
    );
  }
}