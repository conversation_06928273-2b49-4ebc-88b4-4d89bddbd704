# TypeScript Type Definitions

TypeScript type definitions, interfaces, and type utilities for the Bank of Styx application.

## Type Categories

This directory contains:

- **API Types** - Request/response interfaces for backend communication
- **Database Models** - Types matching Prisma schema models
- **Component Props** - Interface definitions for React components
- **Form Types** - Form validation and input type definitions
- **State Types** - Application state and context type definitions
- **Utility Types** - Generic helper types and transformations
- **Third-party Types** - Custom type definitions for external libraries

Strong typing ensures code reliability, enables better IDE support, and helps catch errors during development rather than runtime.
