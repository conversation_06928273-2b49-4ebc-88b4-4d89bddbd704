-- CreateTable
CREATE TABLE `ship_deletion_requests` (
    `id` VARCHAR(191) NOT NULL,
    `reason` TEXT NULL,
    `status` VARCHAR(191) NOT NULL DEFAULT 'pending',
    `message` TEXT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `reviewedAt` DATETIME(3) NULL,
    `shipId` VARCHAR(191) NOT NULL,
    `requestedById` VARCHAR(191) NOT NULL,
    `reviewedById` VARCHAR(191) NULL,

    INDEX `ship_deletion_requests_shipId_idx`(`shipId`),
    INDEX `ship_deletion_requests_requestedById_idx`(`requestedById`),
    INDEX `ship_deletion_requests_reviewedById_idx`(`reviewedById`),
    INDEX `ship_deletion_requests_status_idx`(`status`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `ship_deletion_requests` ADD CONSTRAINT `ship_deletion_requests_shipId_fkey` FOREIGN KEY (`shipId`) REFERENCES `ships`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ship_deletion_requests` ADD CONSTRAINT `ship_deletion_requests_requestedById_fkey` FOREIGN KEY (`requestedById`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ship_deletion_requests` ADD CONSTRAINT `ship_deletion_requests_reviewedById_fkey` FOREIGN KEY (`reviewedById`) REFERENCES `user`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
