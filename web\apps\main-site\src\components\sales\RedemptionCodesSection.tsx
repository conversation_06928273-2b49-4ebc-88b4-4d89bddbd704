"use client";

import React, { useState } from "react";
import { <PERSON>, But<PERSON> } from "@bank-of-styx/ui";
import { Badge } from "@/components/shared";
import { toast } from "react-hot-toast";
import fetchClient from "@/lib/fetchClient";

// Copy icon component
const CopyIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
      d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
  </svg>
);

// Plus icon component
const PlusIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
  </svg>
);

// Trash icon component
const TrashIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
  </svg>
);

interface RedemptionCode {
  id: string;
  code: string;
  isActive: boolean;
  createdAt: string;
}

interface RedemptionCodesSectionProps {
  productId: string;
  productName: string;
  isFree: boolean;
  redemptionCodes: RedemptionCode[];
  productInventory: number | null;
  onRefresh: () => void;
}

export const RedemptionCodesSection: React.FC<RedemptionCodesSectionProps> = ({
  productId,
  productName,
  isFree,
  redemptionCodes,
  productInventory,
  onRefresh,
}) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [deletingCodeId, setDeletingCodeId] = useState<string | null>(null);

  // Copy code to clipboard
  const copyToClipboard = async (code: string) => {
    try {
      await navigator.clipboard.writeText(code);
      toast.success("Code copied to clipboard!");
    } catch (error) {
      console.error("Failed to copy:", error);
      toast.error("Failed to copy code");
    }
  };

  // Generate additional redemption code
  const generateAdditionalCode = async () => {
    setIsGenerating(true);
    try {
      await fetchClient.post(`/api/sales/products/${productId}/redemption-codes`, {});
      toast.success("New redemption code generated!");
      onRefresh();
    } catch (error) {
      console.error("Error generating code:", error);
      toast.error("Failed to generate redemption code");
    } finally {
      setIsGenerating(false);
    }
  };

  // Toggle code active status
  const toggleCodeStatus = async (codeId: string, isActive: boolean) => {
    try {
      await fetchClient.put(`/api/sales/products/${productId}/redemption-codes/${codeId}`, {
        isActive: !isActive,
      });
      toast.success(`Code ${!isActive ? 'activated' : 'deactivated'}!`);
      onRefresh();
    } catch (error) {
      console.error("Error updating code:", error);
      toast.error("Failed to update code status");
    }
  };

  // Delete redemption code
  const deleteCode = async (codeId: string) => {
    if (!confirm("Are you sure you want to delete this redemption code? This action cannot be undone.")) {
      return;
    }

    setDeletingCodeId(codeId);
    try {
      await fetchClient.delete(`/api/sales/products/${productId}/redemption-codes/${codeId}`);
      toast.success("Redemption code deleted!");
      onRefresh();
    } catch (error) {
      console.error("Error deleting code:", error);
      toast.error("Failed to delete redemption code");
    } finally {
      setDeletingCodeId(null);
    }
  };

  // Don't show section if product is not free
  if (!isFree) {
    return null;
  }

  const formatDate = (dateString: string) => new Date(dateString).toLocaleDateString();

  // Check if we can generate more codes
  const canGenerateMoreCodes = () => {
    if (productInventory === null) return true; // Unlimited inventory
    return redemptionCodes.length < productInventory;
  };

  return (
    <Card className="mt-8">
      <div className="p-6 border-b border-border-subtle">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold">Redemption Codes</h3>
            <p className="text-sm text-text-muted">
              Codes for free access to "{productName}"
            </p>
          </div>
          {canGenerateMoreCodes() && (
            <Button
              variant="primary"
              size="sm"
              onClick={generateAdditionalCode}
              loading={isGenerating}
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Generate Code
            </Button>
          )}
        </div>
      </div>

      <div className="p-6">
        {redemptionCodes.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-text-muted mb-4">No redemption codes generated yet</p>
            {canGenerateMoreCodes() && (
              <Button
                variant="primary"
                onClick={generateAdditionalCode}
                loading={isGenerating}
              >
                Generate First Code
              </Button>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            {redemptionCodes.map((code) => (
              <div
                key={code.id}
                className="flex items-center justify-between p-4 bg-secondary-light rounded-lg"
              >
                <div className="flex items-center space-x-4">
                  <div className="font-mono text-lg font-semibold bg-background px-3 py-1 rounded border">
                    {code.code}
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant={code.isActive ? "success" : "secondary"}>
                      {code.isActive ? "Active" : "Inactive"}
                    </Badge>
                    <span className="text-sm text-text-muted">
                      Created {formatDate(code.createdAt)}
                    </span>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(code.code)}
                  >
                    <CopyIcon className="h-4 w-4 mr-1" />
                    Copy
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => toggleCodeStatus(code.id, code.isActive)}
                  >
                    {code.isActive ? "Deactivate" : "Activate"}
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => deleteCode(code.id)}
                    loading={deletingCodeId === code.id}
                  >
                    <TrashIcon className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}

            <div className="mt-6 space-y-4">
              {!canGenerateMoreCodes() && productInventory !== null && (
                <div className="p-4 bg-warning-light border border-warning rounded-lg">
                  <h4 className="font-medium text-warning-dark mb-2">Code Limit Reached</h4>
                  <p className="text-sm text-warning-dark">
                    All {productInventory} codes have been generated for this product. 
                    Delete a code to generate new ones.
                  </p>
                </div>
              )}
              
              <div className="p-4 bg-info-light border border-info rounded-lg">
                <h4 className="font-medium text-info-dark mb-2">Usage Instructions</h4>
                <p className="text-sm text-info-dark">
                  Share these codes with customers who should receive free access to "{productName}". 
                  Customers can enter the code on the shop page to add the product to their cart at $0.00.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </Card>
  );
};