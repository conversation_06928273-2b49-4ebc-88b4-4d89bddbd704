/**
 * Utility for handling image uploads in React Quill
 */
import fetchClient from "@/lib/fetchClient";

/**
 * Upload an image to the server with database tracking
 * @param file - The image file to upload
 * @param entityType - Optional entity type (e.g., 'news', 'avatar')
 * @param entityId - Optional entity ID (e.g., article ID, user ID)
 * @returns Promise resolving to the uploaded image data
 */
export async function uploadImage(
  file: File,
  entityType?: string,
  entityId?: string,
): Promise<{
  id: string;
  url: string;
  filename: string;
  originalUrl: string;
}> {
  try {
    // Create form data
    const formData = new FormData();
    formData.append("file", file);

    if (entityType) {
      formData.append("entityType", entityType);
    }

    if (entityId) {
      formData.append("entityId", entityId);
    }

    // Upload the file using the fetch wrapper
    return fetchClient.request<{
      id: string;
      url: string;
      filename: string;
      originalUrl: string;
    }>("/api/uploads", {
      method: "POST",
      body: formData,
      // Don't set Content-Type header for FormData as the browser will set it with the boundary
    });
  } catch (error) {
    console.error("Error uploading image:", error);
    throw error;
  }
}

/**
 * Handler for React Quill image uploads
 * @param quillInstance - The Quill instance
 * @param articleId - Optional article ID for tracking
 */
export function imageHandler(quillInstance: any, articleId?: string) {
  const input = document.createElement("input");
  input.setAttribute("type", "file");
  input.setAttribute("accept", "image/jpeg,image/png,image/gif,image/webp");
  input.click();

  // Handle file selection
  input.onchange = async () => {
    const file = input.files?.[0];
    if (!file) return;

    // Validate file type
    const allowedTypes = ["image/jpeg", "image/png", "image/gif", "image/webp"];
    if (!allowedTypes.includes(file.type)) {
      alert(
        "File type not supported. Please upload a JPEG, PNG, GIF or WEBP image.",
      );
      return;
    }

    // Validate file size (10MB max)
    const maxSize = 10 * 1024 * 1024; // 10MB in bytes
    if (file.size > maxSize) {
      alert("File size exceeds 10MB limit");
      return;
    }

    // Get editor selection to insert the image
    const range = quillInstance.getEditorSelection();

    try {
      // Show loading indicator
      quillInstance.insertEmbed(
        range.index,
        "image",
        "/images/loading-spinner.gif",
      );

      // Upload image with entity info
      const result = await uploadImage(file, "news-content", articleId);

      // Remove loading indicator and insert the uploaded image with data-image-id attribute
      quillInstance.deleteText(range.index, 1);

      // Insert with data-image-id attribute
      const imageElement = `<img src="${result.url}" data-image-id="${result.id}" alt="${file.name}" />`;
      quillInstance.clipboard.dangerouslyPasteHTML(range.index, imageElement);

      // Move cursor after the inserted image
      quillInstance.setSelection(range.index + 1);
    } catch (error) {
      // Alert and remove loading indicator on error
      alert("Failed to upload image. Please try again.");
      quillInstance.deleteText(range.index, 1);
    }
  };
}
