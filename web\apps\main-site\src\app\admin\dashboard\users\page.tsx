"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "../../../../contexts/AuthContext";
import {
  AdminDashboardLayout,
  UserProfileCard,
  UserStatusBadge,
  UserRoleBadges,
  UserActionButtons,
  UserProfileModal,
  AddUserModal,
} from "../../../../components/admin";
import {
  getUsers,
  getUserById,
  AdminUser,
  updateUserRoles,
  updateUserStatus,
  updateUserEmail,
  resetUserPassword,
  createUser,
} from "../../../../services/adminService";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";

export default function UserManagementPage() {
  const { user, isLoading } = useAuth();
  const router = useRouter();
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [roleFilter, setRoleFilter] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [page, setPage] = useState(1);
  const [limit] = useState(10);
  const [viewMode, setViewMode] = useState<"table" | "grid">("grid");
  const [selectedUser, setSelectedUser] = useState<AdminUser | null>(null);
  const [isProfileModalOpen, setIsProfileModalOpen] = useState(false);
  const [isAddUserModalOpen, setIsAddUserModalOpen] = useState(false);
  const queryClient = useQueryClient();

  // Fetch users
  const {
    data,
    isLoading: isLoadingUsers,
    error,
  } = useQuery({
    queryKey: ["adminUsers", page, limit, searchTerm, roleFilter, statusFilter],
    queryFn: () =>
      getUsers({
        page,
        limit,
        search: searchTerm,
        role: roleFilter,
        status: statusFilter,
      }),
    enabled: !!user?.roles?.admin, // Only fetch if user is admin
  });

  // Mutation for updating user roles
  const updateRolesMutation = useMutation({
    mutationFn: ({ userId, roles }: { userId: string; roles: any }) =>
      updateUserRoles(userId, roles),
    onSuccess: () => {
      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: ["adminUsers"] });
      if (selectedUser) {
        // Refresh the selected user data
        getUserById(selectedUser.id).then((updatedUser) => {
          setSelectedUser(updatedUser);
        });
      }
    },
  });

  // Mutation for updating user status
  const updateStatusMutation = useMutation({
    mutationFn: ({ userId, status }: { userId: string; status: string }) =>
      updateUserStatus(userId, status as any),
    onSuccess: () => {
      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: ["adminUsers"] });
      if (selectedUser) {
        // Refresh the selected user data
        getUserById(selectedUser.id).then((updatedUser) => {
          setSelectedUser(updatedUser);
        });
      }
    },
  });

  // Mutation for updating user email
  const updateEmailMutation = useMutation({
    mutationFn: ({ userId, email }: { userId: string; email: string }) =>
      updateUserEmail(userId, email),
    onSuccess: () => {
      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: ["adminUsers"] });
      if (selectedUser) {
        // Refresh the selected user data
        getUserById(selectedUser.id).then((updatedUser) => {
          setSelectedUser(updatedUser);
        });
      }
    },
  });

  // Mutation for resetting user password
  const resetPasswordMutation = useMutation({
    mutationFn: (userId: string) => resetUserPassword(userId),
    onSuccess: (data) => {
      if (data.emailSent) {
        alert(
          `Password has been reset and emailed to the user. For your reference, the temporary password is: ${data.tempPassword}`,
        );
      } else {
        alert(
          `Password has been reset but there was an issue sending the email. Please provide this temporary password to the user manually: ${data.tempPassword}`,
        );
      }

      if (selectedUser) {
        // Refresh the selected user data
        getUserById(selectedUser.id).then((updatedUser) => {
          setSelectedUser(updatedUser);
        });
      }
    },
  });

  // Mutation for creating a new user
  const createUserMutation = useMutation({
    mutationFn: (userData: {
      username: string;
      displayName: string;
      email: string;
      password: string;
      roles: {
        admin: boolean;
        editor: boolean;
        banker: boolean;
        chatModerator: boolean;
        volunteerCoordinator: boolean;
        leadManager: boolean;
        salesManager: boolean;
        landSteward: boolean;
      };
      status: string;
    }) => createUser(userData),
    onSuccess: () => {
      // Close modal and invalidate users query
      setIsAddUserModalOpen(false);
      queryClient.invalidateQueries({ queryKey: ["adminUsers"] });
    },
    onError: (error) => {
      alert(
        `Error creating user: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
      );
    },
  });

  // Check authorization after auth is loaded
  useEffect(() => {
    // Only proceed with checks if auth loading is complete
    if (!isLoading) {
      if (!user || !user.roles?.admin) {
        router.push("/");
      } else {
        // User is authorized
        setIsAuthorized(true);
      }
    }
  }, [user, router, isLoading]);

  // Handle search input
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setPage(1); // Reset to first page when searching
  };

  // Handle role filter change
  const handleRoleFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setRoleFilter(e.target.value);
    setPage(1); // Reset to first page when filtering
  };

  // Handle status filter change
  const handleStatusFilterChange = (
    e: React.ChangeEvent<HTMLSelectElement>,
  ) => {
    setStatusFilter(e.target.value);
    setPage(1); // Reset to first page when filtering
  };

  // Handle role toggle
  const handleRoleToggle = (
    userId: string,
    roles: { [key: string]: boolean },
  ) => {
    updateRolesMutation.mutate({
      userId,
      roles,
    });
  };

  // Handle user selection for detailed view
  const handleUserSelect = async (userId: string) => {
    try {
      const userData = await getUserById(userId);
      setSelectedUser(userData);
      setIsProfileModalOpen(true);
    } catch (error) {
      console.error("Error fetching user details:", error);
      alert("Failed to load user details. Please try again.");
    }
  };

  // Handle status change
  const handleStatusChange = (userId: string, newStatus: string) => {
    updateStatusMutation.mutate({ userId, status: newStatus });
  };

  // Handle email change
  const handleEmailChange = (userId: string, newEmail: string) => {
    updateEmailMutation.mutate({ userId, email: newEmail });
  };

  // Handle password reset
  const handlePasswordReset = (userId: string) => {
    if (confirm("Are you sure you want to reset this user's password?")) {
      resetPasswordMutation.mutate(userId);
    }
  };

  // Handle user creation
  const handleCreateUser = (userData: {
    username: string;
    displayName: string;
    email: string;
    password: string;
    roles: {
      admin: boolean;
      editor: boolean;
      banker: boolean;
      chatModerator: boolean;
      volunteerCoordinator: boolean;
      leadManager: boolean;
      salesManager: boolean;
      landSteward: boolean;
    };
    status: string;
  }) => {
    createUserMutation.mutate(userData);
  };

  // Show loading state or nothing while checking authorization
  if (isLoading || !isAuthorized) {
    return (
      <div className="min-h-screen bg-secondary-dark flex items-center justify-center">
        <div className="bg-secondary-light p-6 rounded-lg shadow-md border border-gray-600">
          <p className="text-white text-lg">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <AdminDashboardLayout>
      <div className="space-y-6">
        <div className="bg-secondary-light rounded-lg p-2">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 mb-4">
            <h2 className="text-2xl font-bold text-white">User Management</h2>
            <div className="flex gap-2 w-full sm:w-auto">
              {/* Hide table/grid buttons on mobile, show on desktop */}
              <div className="hidden sm:flex gap-2">
                <button
                  onClick={() => setViewMode("table")}
                  className={`px-3 py-1 rounded-md ${
                    viewMode === "table"
                      ? "bg-primary text-white"
                      : "bg-secondary text-white hover:bg-secondary-dark"
                  }`}
                >
                  Table
                </button>
                <button
                  onClick={() => setViewMode("grid")}
                  className={`px-3 py-1 rounded-md ${
                    viewMode === "grid"
                      ? "bg-primary text-white"
                      : "bg-secondary text-white hover:bg-secondary-dark"
                  }`}
                >
                  Grid
                </button>
              </div>
              <button
                onClick={() => setIsAddUserModalOpen(true)}
                className="bg-primary hover:bg-primary-dark text-white px-3 py-1 rounded-md text-sm font-medium w-full sm:w-auto"
              >
                Add New User
              </button>
            </div>
          </div>

          <div className="mb-4 grid grid-cols-1 sm:grid-cols-4 gap-3">
            <div className="sm:col-span-2">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search users..."
                  className="w-full px-4 py-2 bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary"
                  value={searchTerm}
                  onChange={handleSearchChange}
                />
                <svg
                  className="absolute right-3 top-2.5 h-5 w-5 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              </div>
            </div>
            <div>
              <select
                value={roleFilter}
                onChange={handleRoleFilterChange}
                className="w-full px-4 py-2 bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="">All Roles</option>
                <option value="admin">Admin</option>
                <option value="editor">Editor</option>
                <option value="banker">Banker</option>
                <option value="chatModerator">Chat Moderator</option>
                <option value="volunteerCoordinator">
                  Volunteer Coordinator
                </option>
                <option value="leadManager">Lead Manager</option>
              </select>
            </div>
            <div>
              <select
                value={statusFilter}
                onChange={handleStatusFilterChange}
                className="w-full px-4 py-2 bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="">All Statuses</option>
                <option value="active">Active</option>
                <option value="pending">Pending</option>
                <option value="inactive">Inactive</option>
                <option value="suspended">Suspended</option>
                <option value="frozen">Frozen</option>
              </select>
            </div>
          </div>

          {isLoadingUsers ? (
            <div className="text-center py-8">
              <p className="text-white">Loading users...</p>
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <p className="text-red-400">
                Error loading users. Please try again later.
              </p>
            </div>
          ) : (
            <>
              {/* Always show grid view on mobile, respect viewMode on desktop */}
              <div className={`sm:${viewMode === "grid" ? "block" : "hidden"}`}>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                  {data?.users.map((u: AdminUser) => (
                    <UserProfileCard
                      key={u.id}
                      user={u}
                      onClick={() => handleUserSelect(u.id)}
                    />
                  ))}
                </div>
              </div>

              {/* Hide table view on mobile, show on desktop if selected */}
              <div
                className={`hidden sm:${
                  viewMode === "table" ? "block" : "hidden"
                }`}
              >
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-600">
                    <thead className="bg-secondary">
                      <tr>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"
                        >
                          User
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"
                        >
                          Username
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"
                        >
                          Roles
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"
                        >
                          Status
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"
                        >
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-secondary-light divide-y divide-gray-600">
                      {data?.users.map((u: AdminUser) => (
                        <tr key={u.id}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="flex-shrink-0 h-10 w-10">
                                <img
                                  className="h-10 w-10 rounded-full object-cover"
                                  src={
                                    u.avatar || "/images/avatars/default.png"
                                  }
                                  alt={u.displayName}
                                />
                              </div>
                              <div className="ml-4">
                                <div className="text-sm font-medium text-white">
                                  {u.displayName}
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-white">
                              @{u.username}
                            </div>
                            <div className="text-sm text-gray-400">
                              {u.email}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <UserRoleBadges roles={u.roles} size="sm" />
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <UserStatusBadge
                              status={u.status as any}
                              size="sm"
                            />
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <UserActionButtons
                              compact={true}
                              status={u.status as any}
                              onView={() => handleUserSelect(u.id)}
                              onFreeze={() =>
                                handleStatusChange(u.id, "frozen")
                              }
                              onUnfreeze={() =>
                                handleStatusChange(u.id, "active")
                              }
                              onSuspend={() =>
                                handleStatusChange(u.id, "suspended")
                              }
                              onReactivate={() =>
                                handleStatusChange(u.id, "active")
                              }
                              onResetPassword={() => handlePasswordReset(u.id)}
                            />
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Pagination */}
              {data && data.pages > 1 && (
                <div className="flex flex-col sm:flex-row justify-between items-center gap-3 mt-4">
                  <div className="text-sm text-gray-400 text-center sm:text-left">
                    Showing {(page - 1) * limit + 1} to{" "}
                    {Math.min(page * limit, data.total)} of {data.total} users
                  </div>
                  <div className="flex gap-2 w-full sm:w-auto">
                    <button
                      onClick={() => setPage((p) => Math.max(1, p - 1))}
                      disabled={page === 1}
                      className={`px-3 py-1 rounded-md w-full sm:w-auto ${
                        page === 1
                          ? "bg-gray-700 text-gray-500 cursor-not-allowed"
                          : "bg-secondary text-white hover:bg-secondary-dark"
                      }`}
                    >
                      Previous
                    </button>
                    <button
                      onClick={() =>
                        setPage((p) => Math.min(data.pages, p + 1))
                      }
                      disabled={page === data.pages}
                      className={`px-3 py-1 rounded-md w-full sm:w-auto ${
                        page === data.pages
                          ? "bg-gray-700 text-gray-500 cursor-not-allowed"
                          : "bg-secondary text-white hover:bg-secondary-dark"
                      }`}
                    >
                      Next
                    </button>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* User Profile Modal */}
      {selectedUser && (
        <UserProfileModal
          user={selectedUser}
          isOpen={isProfileModalOpen}
          onClose={() => setIsProfileModalOpen(false)}
          onStatusChange={handleStatusChange}
          onEmailChange={handleEmailChange}
          onPasswordReset={handlePasswordReset}
          onRoleChange={handleRoleToggle}
        />
      )}

      {/* Add User Modal */}
      <AddUserModal
        isOpen={isAddUserModalOpen}
        onClose={() => setIsAddUserModalOpen(false)}
        onSubmit={handleCreateUser}
        isLoading={createUserMutation.isPending}
      />
    </AdminDashboardLayout>
  );
}
