# Products API Routes

Product catalog management and e-commerce functionality with inventory tracking.

## Core Operations

- **route.ts** - Main product operations (list, create, update products)
- **[id]/** - Individual product operations (view, edit, delete specific products)
- **search/** - Product search and filtering functionality

## Features

The product system provides:

- Product catalog management
- Inventory tracking and availability
- Category-based organization
- Search and filtering capabilities
- Integration with cart and checkout systems
- Ticket hold system for inventory protection

These endpoints support the complete e-commerce functionality within the Bank of Styx platform, ensuring proper inventory management and seamless shopping experience.
