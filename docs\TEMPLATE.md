# [Feature Name] System

## Overview
Brief description of what this feature/system does and its purpose within the Bank of Styx application.

## Directory Structure
```
feature-root/
├── components/           # React components specific to this feature
│   ├── [ComponentName]/  # Individual component folders
│   └── shared/          # Shared components within feature
├── api/                 # API endpoints for this feature
│   ├── [endpoint]/      # Individual endpoint folders
│   └── middleware/      # Feature-specific middleware
├── hooks/               # Custom React hooks
├── services/            # Business logic and external API calls
├── types/               # TypeScript type definitions
└── utils/               # Utility functions
```

## Key Files & Components

### Frontend Components
- **`ComponentName.tsx`** - Description of main component and its purpose
- **`ComponentName.tsx`** - Another key component description
- **`index.ts`** - Export file for the feature components

### API Endpoints
- **`GET /api/feature-name`** - Description of what this endpoint does
- **`POST /api/feature-name`** - Description of what this endpoint does
- **`PUT /api/feature-name/[id]`** - Description of what this endpoint does
- **`DELETE /api/feature-name/[id]`** - Description of what this endpoint does

### Database Models
- **`ModelName`** - Description of the main data model
- **`RelatedModel`** - Description of related models and relationships

### Services
- **`featureService.ts`** - Description of business logic service
- **`externalApiService.ts`** - Description of external integrations

### Hooks
- **`useFeatureName()`** - Description of custom hook functionality
- **`useFeatureData()`** - Description of data management hook

## Common Tasks

### Task 1: How to [Do Something]
1. Step-by-step instructions
2. Code examples if needed
3. Expected outcomes

### Task 2: How to [Do Something Else]
1. Step-by-step instructions
2. Important considerations
3. Troubleshooting tips

## API Integration

### Authentication Requirements
- Required permissions/roles
- Token requirements
- Rate limiting considerations

### Request/Response Examples
```typescript
// Request example
interface CreateFeatureRequest {
  name: string;
  description: string;
  // other fields
}

// Response example
interface FeatureResponse {
  id: string;
  name: string;
  status: string;
  createdAt: string;
}
```

## Database Schema

### Primary Models
```sql
-- Main table structure
CREATE TABLE feature_name (
  id VARCHAR(191) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  -- other fields
);
```

### Relationships
- Describes how this feature connects to other parts of the system
- Foreign key relationships
- Junction tables if applicable

## Related Features
- **[Related Feature 1](./related-feature-1.md)** - How it integrates
- **[Related Feature 2](./related-feature-2.md)** - Shared components or data
- **[Authentication System](./authentication-system.md)** - Permission requirements

## User Roles & Permissions
- **Admin**: Full access to all features
- **Manager**: Limited management capabilities
- **User**: Basic user access
- **Guest**: Public access only

## Recent Changes
- **v1.2.0** - Description of recent updates
- **v1.1.0** - Previous version changes
- **v1.0.0** - Initial implementation

## Troubleshooting

### Common Issues
1. **Issue Description**: Solution steps
2. **Another Issue**: How to resolve
3. **Performance Concerns**: Optimization tips

### Debug Information
- Log locations
- Important environment variables
- Useful debugging commands

---

**File Locations:**
- Pages: `/src/app/feature-name/`
- Components: `/src/components/feature-name/`
- API: `/src/app/api/feature-name/`
- Types: `/src/types/feature.ts`