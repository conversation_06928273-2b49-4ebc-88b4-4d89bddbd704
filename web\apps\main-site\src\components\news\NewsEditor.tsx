"use client";

import React, { useState, useEffect } from "react";
import dynamic from "next/dynamic";

// Dynamically import ReactQuill to avoid SSR issues
const ReactQuill = dynamic(() => import("react-quill"), {
  ssr: false,
  loading: () => (
    <div className="min-h-[200px] bg-secondary-dark border border-gray-600 rounded-md p-4">
      <div className="animate-pulse h-6 w-24 bg-gray-700 rounded mb-4"></div>
      <div className="animate-pulse h-4 w-full bg-gray-700 rounded mb-2"></div>
      <div className="animate-pulse h-4 w-3/4 bg-gray-700 rounded"></div>
    </div>
  ),
});

// Import CSS for ReactQuill
import "react-quill/dist/quill.snow.css";

interface NewsEditorProps {
  value: string;
  onChange: (content: string) => void;
  error?: boolean;
  errorMessage?: string;
  disabled?: boolean;
  articleId?: string; // For future inline image support
}

export const NewsEditor: React.FC<NewsEditorProps> = ({
  value,
  onChange,
  error = false,
  errorMessage = "Content is required",
  disabled = false,
  articleId,
}) => {
  // Fix for hydration issues - don't render until component has mounted
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Enhanced toolbar with better organization and more visible buttons
  const modules = {
    toolbar: [
      [{ header: [1, 2, 3, false] }], // Simplified header options
      ["bold", "italic", "underline", "strike"],
      [{ list: "ordered" }, { list: "bullet" }],
      [{ indent: "-1" }, { indent: "+1" }],
      ["link"],
      ["clean"],
    ],
  };

  const formats = [
    "header",
    "bold",
    "italic",
    "underline",
    "strike",
    "list",
    "bullet",
    "indent",
    "link",
  ];

  if (!mounted) {
    // Return a placeholder with the same dimensions to prevent layout shift
    return (
      <div className="min-h-[250px] bg-secondary-dark border border-gray-600 rounded-md p-4">
        <div className="animate-pulse h-6 w-24 bg-gray-700 rounded mb-4"></div>
        <div className="animate-pulse h-4 w-full bg-gray-700 rounded mb-2"></div>
        <div className="animate-pulse h-4 w-3/4 bg-gray-700 rounded"></div>
      </div>
    );
  }

  return (
    <div className="styx-editor-container">
      <div
        className={`
        ${error ? "border-error" : ""}
        rounded-md overflow-hidden styx-editor
      `}
      >
        <ReactQuill
          theme="snow"
          value={value}
          onChange={onChange}
          modules={modules}
          formats={formats}
          readOnly={disabled}
          placeholder="Write your article content here..."
          className="min-h-[250px]"
        />
      </div>

      {error && <p className="text-error text-sm mt-1">{errorMessage}</p>}

      {articleId && (
        <div className="mt-2 text-xs text-gray-500">
          💡 Inline image support coming soon! For now, use the featured image
          above.
        </div>
      )}
    </div>
  );
};
