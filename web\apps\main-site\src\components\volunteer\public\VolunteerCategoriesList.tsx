"use client";

import React from "react";
import {
  useVolunteerEvent,
  useVolunteerCategoriesByEvent,
} from "@/hooks/usePublicVolunteer";
import { VolunteerCategoryCard } from "./VolunteerCategoryCard";

interface VolunteerCategoriesListProps {
  eventId: string;
  onCategorySelect: (categoryId: string) => void;
  onBack: () => void;
}

export const VolunteerCategoriesList: React.FC<
  VolunteerCategoriesListProps
> = ({ eventId, onCategorySelect, onBack }) => {
  const { data: eventData } = useVolunteerEvent(eventId);
  const { data, isLoading, error } = useVolunteerCategoriesByEvent(eventId);

  const event = eventData?.event;
  const categories = data?.categories || [];

  return (
    <div>
      {/* Header with back button */}
      <div className="flex items-center mb-4">
        <button
          onClick={onBack}
          className="mr-3 p-2 rounded-full bg-secondary hover:bg-secondary-dark"
        >
          <svg
            className="w-5 h-5 text-white"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 19l-7-7 7-7"
            />
          </svg>
        </button>
        <h2 className="text-xl font-bold text-white">
          {event?.name || "Loading..."}
        </h2>
      </div>

      {/* Categories grid */}
      {isLoading ? (
        <div className="flex items-center justify-center h-40">
          <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-primary"></div>
        </div>
      ) : error ? (
        <div className="bg-accent bg-opacity-20 border border-accent rounded-md p-4 text-accent">
          <p>{error instanceof Error ? error.message : "An error occurred"}</p>
        </div>
      ) : categories.length === 0 ? (
        <div className="text-gray-400 p-4 text-center">
          <p className="mb-2">
            No volunteer categories available for this event.
          </p>
          <p>Check back later for upcoming volunteer opportunities.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {categories.map((category) => (
            <VolunteerCategoryCard
              key={category.id}
              category={category}
              onClick={() => onCategorySelect(category.id)}
            />
          ))}
        </div>
      )}
    </div>
  );
};
